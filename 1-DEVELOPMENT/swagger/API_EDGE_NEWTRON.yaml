swagger: '2.0'
info:
  description: Mapfre API EDGE Guatemala - API_EDGE
  version: '${project.version}'
  title: Mapfre API EDGE Guatemala  - API_EDGE
basePath: /newtron/api
host: api.mapfre.com
securityDefinitions:
  basicAuth:
    type: basic
security:
- basicAuth: []
schemes:
- http
- https
tags:
- name: "API_EDGE"
  description: "Servicios definidos para API_EDGE"
- name: "API_EDGE"
  description: "Endpoints para la integración de Apps en Guatemala"
paths:
  /cache/clear:
    post:
      tags:
      - Cache
      summary: Clear cache
      description: Clears the application's cache.
      operationId: clearCache
      responses:
        '200':
          description: Cache cleared successfully!
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /oficinaDigital/busquedaAsegurados:
    get:
      tags:
        - Oficina Digital
      summary: Endpoint Get Asegurados
      operationId: busquedaAsegurados
      produces:
        - 'application/json'
      parameters:
        - name: codCia
          in: query
          description: 'Codigo de Empresa'
          type: integer
          format: int32
          required: true
        - name: nombreAseg
          in: query
          description: 'Nombre de Asegurado'
          type: string
          required: true
        - name: codAgente
          in: query
          description: 'Codigo de agente'
          type: integer
          format: int32
      responses:
        '200':
          description: Success
          schema:
            type: array
            items:
              $ref: '#/definitions/BusquedaAsegurado'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /planilla_electronica/buscar_medios_pago_planilla:
    get:
      tags:
        - Planilla Electronica
      summary: Buscar medios de pago de planilla
      operationId: buscarMediosPagoPlanilla
      produces:
        - 'application/json'
      parameters:
        - name: idPlanilla
          in: query
          description: 'ID de la planilla'
          type: integer
          format: int32
          required: true
      responses:
        '200':
          description: Success
          schema:
            type: array
            items:
              $ref: '#/definitions/PLE_MedioPagoPlanilla'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /firmaElectronica/buscarDepositosPlanilla:
    get:
      tags:
        - Firma Electronica
      summary: Buscar depósitos de planilla
      operationId: buscarDepositosPlanilla
      produces:
        - 'application/json'
      parameters:
        - name: idPlanilla
          in: query
          description: 'ID de la planilla'
          type: integer
          format: int32
          required: true
      responses:
        '200':
          description: Success
          schema:
            type: array
            items:
              $ref: '#/definitions/PLE_DepositoPlanilla'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /firmaElectronica/buscarIdentificadorMedioPago:
    get:
      tags:
        - Firma Electronica
      summary: Buscar identificador de medio de pago
      operationId: buscarIdentificadorMedioPago
      produces:
        - 'application/json'
      parameters:
        - name: sistema
          in: query
          description: 'Sistema (A/T)'
          type: string
          required: true
        - name: moneda
          in: query
          description: 'Código de moneda'
          type: string
          required: true
        - name: medio
          in: query
          description: 'Medio de pago'
          type: string
          required: true
        - name: tipo
          in: query
          description: 'Tipo (N/I)'
          type: string
          required: true
      responses:
        '200':
          description: Success
          schema:
            type: array
            items:
              $ref: '#/definitions/PLE_IdentificadorMedioPago'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /firmaElectronica/buscarDetallePlanilla:
    get:
      tags:
        - Firma Electronica
      summary: Buscar detalle de planilla
      operationId: buscarDetallePlanilla
      produces:
        - 'application/json'
      parameters:
        - name: idPlanilla
          in: query
          description: 'ID de la planilla'
          type: integer
          format: int32
          required: true
      responses:
        '200':
          description: Success
          schema:
            type: array
            items:
              $ref: '#/definitions/PLE_DetallePlanilla'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /firmaElectronica/getCuentasBancos:
    get:
      tags:
        - Firma Electronica
      summary: Obtener cuentas de bancos
      operationId: getCuentasBancos
      produces:
        - 'application/json'
      parameters:
        - name: moneda
          in: query
          description: 'Código de moneda'
          type: string
          required: true
        - name: entidad
          in: query
          description: 'Código de entidad'
          type: string
          required: true
      responses:
        '200':
          description: Success
          schema:
            type: array
            items:
              $ref: '#/definitions/PLE_CuentaBanco'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /firmaElectronica/obtenerTotalPagosPlanilla:
    get:
      tags:
        - Firma Electronica
      summary: Obtener total de pagos de planilla
      operationId: obtenerTotalPagosPlanilla
      produces:
        - 'application/json'
      parameters:
        - name: idPlanilla
          in: query
          description: 'ID de la planilla'
          type: integer
          format: int32
          required: true
      responses:
        '200':
          description: Success
          schema:
            type: array
            items:
              $ref: '#/definitions/PLE_TotalPagoPlanilla'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /firmaElectronica/obtenerMedioPago:
    get:
      tags:
        - Firma Electronica
      summary: Obtener medios de pago disponibles
      operationId: obtenerMedioPago
      produces:
        - 'application/json'
      responses:
        '200':
          description: Success
          schema:
            type: array
            items:
              $ref: '#/definitions/PLE_MedioPago'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /firmaElectronica/obtenerTipoPlanilla:
    get:
      tags:
        - Firma Electronica
      summary: Obtener tipos de planilla disponibles
      operationId: obtenerTipoPlanilla
      produces:
        - 'application/json'
      responses:
        '200':
          description: Success
          schema:
            type: array
            items:
              $ref: '#/definitions/PLE_TipoPlanilla'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
definitions:
  BusquedaAsegurado:
    type: object
    properties:
      Nombre:
        type: string
      tipDocum:
        type: string
      codDocum:
        type: string
    additionalProperties: false
  PLE_MedioPagoPlanilla:
    type: object
    properties:
      idplanilla:
        type: integer
      requerimientos:
        type: string
      idmedio_pago:
        type: integer
      medio_pago:
        type: string
      codmoneda:
        type: string
      monto:
        type: number
      fecha_cheque:
        type: string
        format: date
      fecha_deposito:
        type: string
        format: date
      codbanco:
        type: string
      nombre_banco:
        type: string
      documento:
        type: string
      comentario:
        type: string
      identificador:
        type: string
      estado_mediopago:
        type: string
      correlativo:
        type: integer
      grupo:
        type: string
      enviar_mensajero:
        type: string
      cuenta_codigo:
        type: string
      cuenta_numero:
        type: string
      cobro_anticipado:
        type: string
    additionalProperties: false
  PLE_DepositoPlanilla:
    type: object
    properties:
      id:
        type: integer
      idplanilla:
        type: integer
      codmedio:
        type: string
      medio_deposito:
        type: string
      monto:
        type: number
      documento:
        type: string
      fecha_deposito:
        type: string
        format: date
      codbanco:
        type: string
      nombre_banco:
        type: string
      descarga:
        type: string
      registro:
        type: string
        format: date-time
    additionalProperties: false
  PLE_IdentificadorMedioPago:
    type: object
    properties:
      sistema:
        type: string
      idsistema:
        type: integer
      medio:
        type: string
      idmedio:
        type: integer
      identificador:
        type: string
      moneda:
        type: string
    additionalProperties: false
  PLE_DetallePlanilla:
    type: object
    properties:
      idplanilla_detalle:
        type: integer
      poliza:
        type: string
      requerimiento:
        type: string
      factura:
        type: string
      nombre_pagador:
        type: string
      nit_pagador:
        type: string
      no_cuota:
        type: string
      tipo_moneda:
        type: string
      prima:
        type: number
      tipo_pago:
        type: string
      comentario:
        type: string
      estado_poliza:
        type: string
      sistema:
        type: string
      fecha_vig:
        type: string
        format: date
      estado_cobro:
        type: string
      esaviso:
        type: string
    additionalProperties: false
  PLE_CuentaBanco:
    type: object
    properties:
      codigo:
        type: string
      cuenta:
        type: string
      tronCode:
        type: string
      acselCode:
        type: string
      nombre:
        type: string
    additionalProperties: false
  PLE_TotalPagoPlanilla:
    type: object
    properties:
      medio_pago:
        type: string
      tipo_moneda:
        type: string
      monto:
        type: number
      documento:
        type: string
      banco:
        type: string
      nombre_banco:
        type: string
      fecha_cheque:
        type: string
        format: date
      fecha_deposito:
        type: string
        format: date
      estado:
        type: string
      procesado:
        type: number
      no_procesado:
        type: number
    additionalProperties: false
  PLE_MedioPago:
    type: object
    properties:
      codigo:
        type: integer
      descripcion:
        type: string
    additionalProperties: false
  PLE_TipoPlanilla:
    type: object
    properties:
      codigo:
        type: integer
      descripcion:
        type: string
    additionalProperties: false