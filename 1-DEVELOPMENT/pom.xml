<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.mapfre.tron.gt</groupId>
    <artifactId>nwt_api_gt_be</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>
  <artifactId>nwt_api_gt_be.DEVELOPMENT</artifactId>
  <packaging>pom</packaging>
  <name>${project.artifactId}:${project.version}</name>
  <properties>
    <gaia.folder>1-DEVELOPMENT</gaia.folder>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.mapfre.tron.gt</groupId>
        <artifactId>nwt_api_gt_be-web</artifactId>
        <version>${project.version}</version>
        <type>war</type>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron.gt</groupId>
        <artifactId>nwt_api_gt_be-web-dependencies</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron.gt</groupId>
        <artifactId>nwt_api_gt_be.zeroConfig</artifactId>
        <version>${project.version}</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron.gt</groupId>
        <artifactId>nwt_api_gt_be-bo</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron.gt</groupId>
        <artifactId>nwt_api_gt_be-commons</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron.gt</groupId>
        <artifactId>nwt_api_gt_be-config</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron.gt</groupId>
        <artifactId>nwt_api_gt_be-sr-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron.gt</groupId>
        <artifactId>nwt_api_gt_be-sr-impl</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron.gt</groupId>
        <artifactId>nwt_api_gt_be-bl-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron.gt</groupId>
        <artifactId>nwt_api_gt_be-bl-impl</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron.gt</groupId>
        <artifactId>nwt_api_gt_be-dl-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron.gt</groupId>
        <artifactId>nwt_api_gt_be-dl-impl</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- External dependencies -->
      <dependency>
        <groupId>com.mapfre.dgtp</groupId>
        <artifactId>mapfre-discovery-service-delivery</artifactId>
        <version>1.0.1</version>
        <type>pom</type>
        <scope>provided</scope>
      </dependency>
      <!-- API Clients -->
      <dependency>
        <groupId>com.mapfre.tron</groupId>
        <artifactId>nwt_cmn_api_be-client</artifactId>
        <version>${NWT_CMN_API_BE-CLIENT_VERSION}</version>
        <exclusions>
          <exclusion>
            <groupId>*</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron</groupId>
        <artifactId>nwt_btc_api_be-client</artifactId>
        <version>${NWT_BTC_API_BE-CLIENT_VERSION}</version>
        <exclusions>
          <exclusion>
            <groupId>*</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron</groupId>
        <artifactId>nwt_isu_api_be-client</artifactId>
        <version>${NWT_ISU_API_BE-CLIENT_VERSION}</version>
        <exclusions>
          <exclusion>
            <groupId>*</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron</groupId>
        <artifactId>nwt_lss_api_be-client</artifactId>
        <version>${NWT_LSS_API_BE-CLIENT_VERSION}</version>
        <exclusions>
          <exclusion>
            <groupId>*</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron</groupId>
        <artifactId>nwt_spl_api_be-client</artifactId>
        <version>${NWT_SPL_API_BE-CLIENT_VERSION}</version>
        <exclusions>
          <exclusion>
            <groupId>*</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron</groupId>
        <artifactId>nwt_thp_api_be-client</artifactId>
        <version>${NWT_THP_API_BE-CLIENT_VERSION}</version>
        <exclusions>
          <exclusion>
            <groupId>*</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.mapfre.tron</groupId>
        <artifactId>nwt_tsy_api_be-client</artifactId>
        <version>${NWT_TSY_API_BE-CLIENT_VERSION}</version>
        <exclusions>
          <exclusion>
            <groupId>*</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <modules>
    <module>javaApp</module>
    <module>jeeApp</module>
    <module>securityDef</module>
    <module>zeroConfig</module>
    <module>swagger</module>
  </modules>
</project>