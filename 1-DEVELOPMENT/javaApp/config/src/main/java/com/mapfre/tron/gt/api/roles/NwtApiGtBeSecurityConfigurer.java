package com.mapfre.tron.gt.api.roles;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;
import org.springframework.stereotype.Component;

@Component
public class NwtApiGtBeSecurityConfigurer {
	
	/**
	 * application.properties variable that activates/deactivates roles
	 */
	@Value("${rol.apinwt.active:false}")
	private boolean active;
	
	@Value("${rol.apinwt.swagger:${rol.apinwt.default:ROLAPINWT_ALL}}")
	private String swaggerRol;


	
	public ExpressionUrlAuthorizationConfigurer<HttpSecurity>.ExpressionInterceptUrlRegistry roleAuthorizations(
			HttpSecurity http) throws Exception {
		if(!active) {
			return http.authorizeRequests().anyRequest().authenticated();
		} else {
			return http
	        .authorizeRequests()
	        .antMatchers("/api/fw/**").permitAll()
	        .antMatchers("/api/life/**").authenticated()
	        .antMatchers("/swagger-ui/**").hasRole(swaggerRol)
			;
		}
	}

}
