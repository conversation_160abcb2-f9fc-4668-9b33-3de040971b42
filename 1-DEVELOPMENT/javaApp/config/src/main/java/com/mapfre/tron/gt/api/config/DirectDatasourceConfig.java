package com.mapfre.tron.gt.api.config;

import java.sql.SQLException;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.mapfre.dgtp.gaia.commons.env.EnvironmentAttributes;
import com.mapfre.dgtp.gaia.dl.connector.datasource.DataSourceManager;

import lombok.extern.slf4j.Slf4j;
import oracle.jdbc.pool.OracleDataSource;

@Profile(EnvironmentAttributes.SPRING_DIRECT_CONNECTION_PROFILE)
@Configuration
@Slf4j
public class DirectDatasourceConfig implements DatasourceConfig {

	// Access data to local DB.
	// Tronweb DL
	@Value("${app.env.dl.datasource.url}")
	private String dlDataSourceUrl;
	@Value("${app.env.dl.datasource.u}")
	private String dlDataSourceU;
	@Value("${app.env.dl.datasource.p}")
	private String dlDataSourceP;

	@Bean
	DataSourceManager dataSourceManager() {
		DataSourceManager dsManager = new DataSourceManager();
		dsManager.setDefaultDataSourceName("dataSource");
		return dsManager;
	}

	@Override
	@SuppressWarnings("deprecation")
	@Bean(name = "dataSource")
	public DataSource dataSource() {
		OracleDataSource dataSource = null;
		try {
			dataSource = new OracleDataSource();
			dataSource.setURL(dlDataSourceUrl);
			dataSource.setUser(dlDataSourceU);
			dataSource.setPassword(dlDataSourceP);
			dataSource.setConnectionCachingEnabled(true);
		} catch (SQLException e) {
			log.error("There was an error while creating the datasource (dataSource):" + e.getMessage());
		}
		return dataSource;
	}

	@Override
	@Bean(name = "transactionManager")
	public DataSourceTransactionManager transactionManager() {
		return new DataSourceTransactionManager(dataSource());
	}

}
