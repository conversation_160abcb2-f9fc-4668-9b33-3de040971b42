package com.mapfre.tron.gt.api.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.jdbc.datasource.lookup.JndiDataSourceLookup;

import com.mapfre.dgtp.gaia.commons.env.EnvironmentAttributes;
import com.mapfre.dgtp.gaia.dl.connector.datasource.DataSourceManager;

@Profile({ EnvironmentAttributes.SPRING_JNDI_CONNECTION_PROFILE })
@Configuration
public class RealDatasourceConfig implements DatasourceConfig {


	@Value("${app.env.dl.datasource.ref.jndi}")
	private String dlRefJNDI;

	@Bean
	DataSourceManager dataSourceManager() {
		DataSourceManager dsManager = new DataSourceManager();
		dsManager.setDefaultDataSourceName("dataSource");
		return dsManager;
	}

	@Bean(name = "dataSource", destroyMethod = "")
	public DataSource dataSource() {
		JndiDataSourceLookup lookup = new JndiDataSourceLookup();
		return lookup.getDataSource(dlRefJNDI);
	}

	@Bean(name = "transactionManager")
	@Override
	public DataSourceTransactionManager transactionManager() {
		return new DataSourceTransactionManager(dataSource());
	}

}
