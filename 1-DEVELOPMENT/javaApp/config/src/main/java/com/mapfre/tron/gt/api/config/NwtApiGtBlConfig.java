package com.mapfre.tron.gt.api.config;

import java.util.Collection;
import java.util.Collections;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.ehcache.EhCacheCacheManager;
import org.springframework.cache.ehcache.EhCacheManagerFactoryBean;
import org.springframework.cache.interceptor.CacheOperationInvocationContext;
import org.springframework.cache.interceptor.CacheResolver;
import org.springframework.cache.interceptor.SimpleCacheResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.mapfre.dgtp.gaia.commons.beans.factory.support.CustomBeanNameGenerator;
import com.mapfre.dgtp.gaia.commons.config.GaiaBlConfiguration;
import com.mapfre.dgtp.gaia.remote.annotation.HttpInvokerProtocol;

@Configuration
@GaiaBlConfiguration
@EnableCaching
@EnableScheduling
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableTransactionManagement
@ComponentScan(basePackages = { 
		"com.mapfre.tron.gt.api", "com.mapfre.tron.gt.api.cache" 
	}, 
	nameGenerator = CustomBeanNameGenerator.class, 
	excludeFilters = {
				@ComponentScan.Filter(type = FilterType.ANNOTATION, value = Controller.class),
				@ComponentScan.Filter(type = FilterType.ANNOTATION, value = HttpInvokerProtocol.class) 
				})
public class NwtApiGtBlConfig {

	@Bean
	@Primary
	public CacheManager cacheManager() {
		net.sf.ehcache.CacheManager cm = ehCacheCacheManager().getObject();
		if (cm != null) {
			return new EhCacheCacheManager(cm);
		}
		return null;
	}

	@Bean
	public EhCacheManagerFactoryBean ehCacheCacheManager() {
		EhCacheManagerFactoryBean cmfb = new EhCacheManagerFactoryBean();
		cmfb.setConfigLocation(new ClassPathResource("ehcache-config.xml"));
		cmfb.setShared(true);
		return cmfb;
	}

	@Bean
	public CacheResolver customCacheResolver(CacheManager cacheManager) {
		return new SimpleCacheResolver(cacheManager) {
			@Override
			protected Collection<String> getCacheNames(CacheOperationInvocationContext<?> context) {
				return Collections.singletonList("defaultCache");
			}
		};
	}

}
