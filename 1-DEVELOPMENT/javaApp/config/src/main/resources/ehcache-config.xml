<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="ehcache.xsd" updateCheck="true"
	monitoring="autodetect" dynamicConfig="true">

	<diskStore path="java.io.tmpdir" />

	<defaultCache maxElementsInMemory="3000" eternal="false"
		timeToIdleSeconds="1200" timeToLiveSeconds="1200"
		overflowToDisk="true" maxElementsOnDisk="10000" diskPersistent="false"
		diskExpiryThreadIntervalSeconds="120" memoryStoreEvictionPolicy="LRU" />

	<!-- Example (Copy from this example) -->
	<cache name="getLanguagesCacheable"
		maxElementsInMemory="2500" eternal="true" overflowToDisk="false"
		memoryStoreEvictionPolicy="LRU" />

	
</ehcache>