package com.mapfre.tron.gt.api.sr;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mapfre.tron.gt.api.bl.IBlOficinaDigital;
import com.mapfre.tron.gt.api.model.BusquedaAsegurado;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
@Api(tags = { "Oficina Digital" })
public class OficinaDigitalController implements OficinaDigitalApi {
	
	@Autowired
	private IBlOficinaDigital iOficinaDigitalService;
	
	@Override
	public ResponseEntity<List<BusquedaAsegurado>> busquedaAsegurados(
			@NotNull @ApiParam(value = "Codigo de Empresa", required = true) @Valid @RequestParam(value = "codCia", required = true) Integer codCia,
			@NotNull @ApiParam(value = "Nombre de Asegurado", required = true) @Valid @RequestParam(value = "nombreAseg", required = true) String nombreAseg,
			@ApiParam(value = "Codigo de agente") @Valid @RequestParam(value = "codAgente", required = false) Integer codAgente) {
		log.info("The busquedaAsegurados rest operation had been called!");
		
		List<BusquedaAsegurado> response = iOficinaDigitalService.busquedaAsegurados(codCia, nombreAseg, codAgente);
	
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

}
