package com.mapfre.tron.gt.api.sr;

import java.time.LocalDate;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import com.mapfre.tron.gt.api.bl.IBlTracking;
import com.mapfre.tron.gt.api.model.DetalleSiniestro;
import com.mapfre.tron.gt.api.model.EncuestaSiniestro;
import com.mapfre.tron.gt.api.model.EncuestaSiniestroRequest;
import com.mapfre.tron.gt.api.model.EncuestaSiniestroResponse;
import com.mapfre.tron.gt.api.model.EtapaSiniestro;
import com.mapfre.tron.gt.api.model.RamoSiniestro;
import com.mapfre.tron.gt.api.model.Siniestro;
import com.mapfre.tron.gt.api.model.SiniestroNit;
import com.mapfre.tron.gt.api.model.SiniestroVehiculo;
import com.mapfre.tron.gt.api.model.UrlSiniestro;
import com.mapfre.tron.gt.api.model.ValidaIngresoEncuestaResponse;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * Controlador REST para operaciones de tracking de siniestros.
 */
@RestController
@Slf4j
@Api(tags = {"Tracking"})
public class TrackingController implements TrackingApi {

    @Autowired
    private IBlTracking blTracking;

    @Override
    public ResponseEntity<List<Siniestro>> getSiniestros(Integer codRamo, Integer codInter, String numPoliza,
                                                         String numSini, LocalDate fechaInicio, LocalDate fechaFin) {
        log.info("Recibida solicitud de búsqueda de siniestros con codRamo={}, codInter={}, numPoliza={}, numSini={}, fechaInicio={}, fechaFin={}",
                 codRamo, codInter, numPoliza, numSini, fechaInicio, fechaFin);

        try {
            // Llamar al servicio de negocio
            List<Siniestro> siniestros = blTracking.getSiniestros(codRamo, codInter, numPoliza, numSini, fechaInicio, fechaFin);

            // Determinar el código HTTP basado en los resultados
            if (siniestros == null || siniestros.isEmpty()) {
                log.info("No se encontraron siniestros para los criterios especificados");
                return new ResponseEntity<>(siniestros, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} siniestros", siniestros.size());
            return new ResponseEntity<>(siniestros, HttpStatus.OK);

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<RamoSiniestro> getRamoSiniestro(String numSiniestro) {
        log.info("Recibida solicitud para obtener el ramo del siniestro: {}", numSiniestro);

        try {
            // Llamar al servicio de negocio
            Integer ramoValue = blTracking.getRamoSiniestro(numSiniestro);

            // Determinar el código HTTP basado en los resultados
            if (ramoValue == null) {
                log.info("No se encontró ramo para el siniestro especificado");
                return new ResponseEntity<>(HttpStatus.NO_CONTENT);
            }

            // Crear el objeto de respuesta
            RamoSiniestro ramoSiniestro = new RamoSiniestro();
            ramoSiniestro.setRamo(ramoValue);

            log.info("Se encontró el ramo {} para el siniestro {}", ramoValue, numSiniestro);
            return new ResponseEntity<>(ramoSiniestro, HttpStatus.OK);

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<EtapaSiniestro>> getTrackingEtapaSiniestro(String numSiniestro, Integer codRamo) {
        log.info("Recibida solicitud para obtener las etapas del siniestro: {} con ramo: {}", numSiniestro, codRamo);

        try {
            // Llamar al servicio de negocio
            List<EtapaSiniestro> etapas = blTracking.getTrackingEtapaSiniestro(numSiniestro, codRamo);

            // Determinar el código HTTP basado en los resultados
            if (etapas == null || etapas.isEmpty()) {
                log.info("No se encontraron etapas para el siniestro especificado");
                return new ResponseEntity<>(etapas, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} etapas para el siniestro {}", etapas.size(), numSiniestro);
            return new ResponseEntity<>(etapas, HttpStatus.OK);

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<SiniestroNit>> getSiniestroByDoc(String numSiniestro, String numDoc, String tipoDoc) {
        log.info("Recibida solicitud para obtener siniestro por documento: {} de tipo: {} con número de siniestro: {}", numDoc, tipoDoc, numSiniestro);

        try {
            // Llamar al servicio de negocio
            List<SiniestroNit> siniestros = blTracking.getSiniestroByDoc(numSiniestro, numDoc, tipoDoc);

            // Determinar el código HTTP basado en los resultados
            if (siniestros == null || siniestros.isEmpty()) {
                log.info("No se encontraron siniestros para el documento y número de siniestro especificados");
                return new ResponseEntity<>(siniestros, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} siniestros para el documento {} de tipo {} y número de siniestro {}",
                     siniestros.size(), numDoc, tipoDoc, numSiniestro);
            return new ResponseEntity<>(siniestros, HttpStatus.OK);

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<UrlSiniestro> getUrlSiniestro(String url, String numSiniestro, String tipoCliente) {
        log.info("Recibida solicitud para obtener URL encriptada con url={}, numSiniestro={}, tipoCliente={}", url, numSiniestro, tipoCliente);

        try {
            // Llamar al servicio de negocio
            UrlSiniestro urlSiniestro = blTracking.getUrlSiniestro(url, numSiniestro, tipoCliente);

            // Determinar el código HTTP basado en los resultados
            if (urlSiniestro == null) {
                log.info("No se pudo generar la URL encriptada");
                return new ResponseEntity<>(HttpStatus.NO_CONTENT);
            }

            log.info("URL encriptada generada correctamente: {}", urlSiniestro.getUrl());
            return new ResponseEntity<>(urlSiniestro, HttpStatus.OK);

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<DetalleSiniestro>> getTrackingDetalleSiniestro(String numSiniestro, Integer codRamo) {
        log.info("Recibida solicitud para obtener detalle de siniestro con numSiniestro={}, codRamo={}", numSiniestro, codRamo);

        try {
            // Llamar al servicio de negocio
            List<DetalleSiniestro> detalles = blTracking.getTrackingDetalleSiniestro(numSiniestro, codRamo);

            // Determinar el código HTTP basado en los resultados
            if (detalles == null || detalles.isEmpty()) {
                log.info("No se encontraron detalles para el siniestro especificado");
                return new ResponseEntity<>(detalles, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} detalles para el siniestro {}", detalles.size(), numSiniestro);
            return new ResponseEntity<>(detalles, HttpStatus.OK);

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<EncuestaSiniestro>> getEncuestaSiniestro() {
        log.info("Recibida solicitud para obtener encuesta de siniestro");

        try {
            // Llamar al servicio de negocio
            List<EncuestaSiniestro> encuestas = blTracking.getEncuestaSiniestro();

            // Determinar el código HTTP basado en los resultados
            if (encuestas == null || encuestas.isEmpty()) {
                log.info("No se encontraron encuestas");
                return new ResponseEntity<>(encuestas, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} encuestas", encuestas.size());
            return new ResponseEntity<>(encuestas, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<EncuestaSiniestroResponse> saveEncuesta(EncuestaSiniestroRequest body) {
        log.info("Recibida solicitud para guardar encuesta de siniestro con numSiniestro={}, idEncuesta={}",
                 body.getNumSiniestro(), body.getIdEncuesta());

        try {
            // Llamar al servicio de negocio
            String resultado = blTracking.saveEncuesta(body.getNumSiniestro(), body.getIdEncuesta(), body.getArchXml());

            // Crear objeto de respuesta
            EncuestaSiniestroResponse response = new EncuestaSiniestroResponse();
            response.setResultado(resultado);

            // Determinar el código HTTP basado en los resultados
            if ("OK".equals(resultado)) {
                log.info("Encuesta guardada correctamente");
                return new ResponseEntity<>(response, HttpStatus.OK);
            } else {
                log.info("No se pudo guardar la encuesta: {}", resultado);
                return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
            }

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            EncuestaSiniestroResponse response = new EncuestaSiniestroResponse();
            response.setResultado(e.getMessage());
            return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<ValidaIngresoEncuestaResponse> validaIngresoEncuesta(String numSiniestro) {
        log.info("Recibida solicitud para validar ingreso de encuesta con numSiniestro={}", numSiniestro);

        try {
            // Llamar al servicio de negocio
            String resultado = blTracking.validaIngresoEncuesta(numSiniestro);

            // Crear objeto de respuesta
            ValidaIngresoEncuestaResponse response = new ValidaIngresoEncuestaResponse();
            response.setResultado(resultado);

            // Determinar el código HTTP basado en los resultados
            if ("OK".equals(resultado)) {
                log.info("Validación de ingreso de encuesta exitosa");
                return new ResponseEntity<>(response, HttpStatus.OK);
            } else {
                log.info("Validación de ingreso de encuesta fallida: {}", resultado);
                return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
            }

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            ValidaIngresoEncuestaResponse response = new ValidaIngresoEncuestaResponse();
            response.setResultado(e.getMessage());
            return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<SiniestroVehiculo>> getSiniestroVehiculo(String numSiniestro, Integer codCia, String numPoliza, String codFase, Integer codRamo) {
        log.info("Recibida solicitud para obtener información de siniestro de vehículo con numSiniestro={}, codCia={}, numPoliza={}, codFase={}, codRamo={}",
                 numSiniestro, codCia, numPoliza, codFase, codRamo);

        try {
            // Llamar al servicio de negocio
            List<SiniestroVehiculo> siniestros = blTracking.getSiniestroVehiculo(numSiniestro, codCia, numPoliza, codFase, codRamo);

            // Determinar el código HTTP basado en los resultados
            if (siniestros == null || siniestros.isEmpty()) {
                log.info("No se encontraron siniestros de vehículo");
                return new ResponseEntity<>(siniestros, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} siniestros de vehículo", siniestros.size());
            return new ResponseEntity<>(siniestros, HttpStatus.OK);

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
