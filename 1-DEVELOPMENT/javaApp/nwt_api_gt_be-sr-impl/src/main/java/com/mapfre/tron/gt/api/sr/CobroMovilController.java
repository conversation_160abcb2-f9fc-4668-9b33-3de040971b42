package com.mapfre.tron.gt.api.sr;


import com.mapfre.tron.gt.api.bl.IBlCobroMovil;
import com.mapfre.tron.gt.api.model.*;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;


@RestController
@Slf4j
@Api(tags = {"CobroMovil"})
public class CobroMovilController implements CobroMovilApi {
    @Autowired
    private IBlCobroMovil iBlCobroMovil;

    @Override
    public ResponseEntity<EntidadResponse> obtenerEntidadesBancarias(Integer sistema, String moneda) {
        log.info("Iniciando endpoint obtenerEntidadesBancarias - sistema: {}, moneda: {}",
                sistema, moneda);

        EntidadResponse entidades = iBlCobroMovil.ListaEntidad(sistema, moneda);
        HttpStatus httpStatus = HttpStatus.OK;
        if (entidades.getCodigo().equals("404")) {
            httpStatus = HttpStatus.NOT_FOUND;
        } else if (entidades.getCodigo().equals("400")) {
            httpStatus = HttpStatus.BAD_REQUEST;
        } else if (entidades.getCodigo().equals("500")) {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        }
        return new ResponseEntity<>(entidades, httpStatus);
    }

    @Override
    public ResponseEntity<CobroResponse> obtenerOpcionesDeCobro() {
        log.info("Iniciando endpoint obtener-operaciones-de-cobro ");

        CobroResponse opcionesCobro = iBlCobroMovil.ListaOpcionesCobro();

        HttpStatus httpStatus = HttpStatus.OK;
        if (opcionesCobro.getCodigo().equals("404")) {
            httpStatus = HttpStatus.NOT_FOUND;
        } else if (opcionesCobro.getCodigo().equals("400")) {
            httpStatus = HttpStatus.BAD_REQUEST;
        } else if (opcionesCobro.getCodigo().equals("500")) {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        }
        return new ResponseEntity<>(opcionesCobro, httpStatus);
    }

    @Override
    public ResponseEntity<RutaDiaria> crearNuevaRutaDiaria(RutaDiariaRequest rutaDiariaRequest) {
        log.info("Iniciando endpoint crearNuevaRutaDiaria - idUsuarioCrea: {}, idUsuarioAsignado: {}",
                rutaDiariaRequest.getIdUsuarioCrea(), rutaDiariaRequest.getIdUsuarioAsignado());

        RutaDiaria rutaDiaria = iBlCobroMovil.CrearRutaDiaria(rutaDiariaRequest.getIdUsuarioCrea(), rutaDiariaRequest.getIdUsuarioAsignado());
        HttpStatus httpStatus = HttpStatus.CREATED;
        if (rutaDiaria.getCodigo().equals("400")) {
            httpStatus = HttpStatus.BAD_REQUEST;
        } else if (rutaDiaria.getCodigo().equals("500")) {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        }
        return new ResponseEntity<>(rutaDiaria, httpStatus);
    }

    @Override
    public ResponseEntity<RutaDiaria> crearNuevaRuta(RutaDiariaRequest rutaDiariaRequest) {
        log.info("Iniciando endpoint crear-ruta - idUsuarioCrea: {}, idUsuarioAsignado: {}",
                rutaDiariaRequest.getIdUsuarioCrea(), rutaDiariaRequest.getIdUsuarioAsignado());

        RutaDiaria rutaDiaria = iBlCobroMovil.CrearRuta(rutaDiariaRequest.getIdUsuarioCrea(),
                rutaDiariaRequest.getIdUsuarioAsignado());
        HttpStatus httpStatus = HttpStatus.CREATED;
        if (rutaDiaria.getCodigo().equals("400")) {
            httpStatus = HttpStatus.BAD_REQUEST;
        } else if (rutaDiaria.getCodigo().equals("500")) {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        }
        return new ResponseEntity<>(rutaDiaria, httpStatus);
    }

    @Override
    public ResponseEntity<ModificarRutaResponse> modificarRutaCobro(ModificarRutaRequest modificarRutaRequest) {
        log.info("Iniciando endpoint modificar-ruta - idUsuarioCrea: {}, idUsuarioAsignado: {}, idRuta: {}",
                modificarRutaRequest.getIdUsuarioCrea(), modificarRutaRequest.getIdUsuarioAsignado(),
                modificarRutaRequest.getIdRuta());

        ModificarRutaResponse response = iBlCobroMovil.ModificarRuta(modificarRutaRequest.getIdUsuarioCrea(),
                modificarRutaRequest.getIdUsuarioAsignado(), modificarRutaRequest.getIdRuta());

        HttpStatus httpStatus = HttpStatus.NO_CONTENT;

        // Mapeo de códigos de respuesta a HTTP Status
        switch (response.getCodigo()) {
            case "400":
                httpStatus = HttpStatus.BAD_REQUEST;
                log.warn("Bad Request - {}", response.getMensaje());
                break;
            case "404":
                httpStatus = HttpStatus.NOT_FOUND;
                log.warn("Not Found - {}", response.getMensaje());
                break;
            case "500":
                httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
                log.error("Internal Server Error - {}", response.getMensaje());
                break;
            case "200":
                httpStatus = HttpStatus.OK;
                log.info("Operación exitosa - idRuta modificada: {}", response.getIdRuta());
                break;
            default:
                log.warn("Código de respuesta no esperado: {}", response.getCodigo());
                break;
        }

        log.info("Endpoint modificar-ruta completado - código: {}, httpStatus: {}",
                response.getCodigo(), httpStatus);

        return new ResponseEntity<>(response, httpStatus);
    }

    @Override
    public ResponseEntity<InsertarDetalleRutaDiarioResponse> insertarDetalleRutaDiario(InsertarDetalleRutaDiarioRequest body) {
        log.info("Iniciando endpoint insertar-detalle-ruta-diario - ruta: {}, idpol: {}, numpol: {}",
                body.getRuta(), body.getIdpol(), body.getNumpol());
        InsertarDetalleRutaDiarioResponse response = iBlCobroMovil.InsertarDetalleRutaDiario(body);
        HttpStatus httpStatus = HttpStatus.CREATED;

        // Mapeo de códigos de respuesta a HTTP Status
        if (response.getCodigo().equals("400")) {
            httpStatus = HttpStatus.BAD_REQUEST;
        } else if (response.getCodigo().equals("500")) {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        } else if (response.getCodigo().equals("201")) {
            httpStatus = HttpStatus.CREATED;
        }
        log.info("Endpoint insertar-detalle-ruta-diario completado - código: {}, httpStatus: {}",
                response.getCodigo(), httpStatus);
        return new ResponseEntity<>(response, httpStatus);
    }

    @Override
    public ResponseEntity<InsertarDetalleRuta2Response> insertarDetalleRuta2(InsertarDetalleRuta2Request body) {
        log.info("Iniciando endpoint insertar-detalle-ruta2 - ruta: {}, idPol: {}, numPol: {}, correo: {}",
                body.getRuta(), body.getIdPol(), body.getNumPol(), body.getCorreo());

        InsertarDetalleRuta2Response response = iBlCobroMovil.InsertarDetalleRuta2(body);
        HttpStatus httpStatus = HttpStatus.CREATED;

        // Mapeo de códigos de respuesta a HTTP Status
        if (response.getCodigo().equals("400")) {
            httpStatus = HttpStatus.BAD_REQUEST;
        } else if (response.getCodigo().equals("500")) {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        } else if (response.getCodigo().equals("201")) {
            httpStatus = HttpStatus.CREATED;
        }

        log.info("Finalizando endpoint insertar-detalle-ruta2 - código respuesta: {}, HTTP status: {}",
                response.getCodigo(), httpStatus);
        return new ResponseEntity<>(response, httpStatus);
    }

    @Override
    public ResponseEntity<TipoMedioPagoResponse> insertarTipoMedioPago(TipoMedioPagoRequest body) {
        log.info("Iniciando endpoint insertar-tipo-medio-pago - idRuta: {}, tipo: {}, entidadFinanciera: {}, monto: {}",
                body.getIdRuta(), body.getTipo(), body.getEntidadFinanciera(), body.getMonto());

        TipoMedioPagoResponse response = iBlCobroMovil.InsertarTipoMedioPago(body);
        HttpStatus httpStatus = HttpStatus.CREATED;

        // Mapeo de códigos de respuesta a HTTP Status
        if (response.getCodigo().equals("400")) {
            httpStatus = HttpStatus.BAD_REQUEST;
        } else if (response.getCodigo().equals("500")) {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        } else if (response.getCodigo().equals("201")) {
            httpStatus = HttpStatus.CREATED; 
        }

        log.info("Finalizando endpoint insertar-tipo-medio-pago - código respuesta: {}, HTTP status: {}",
                response.getCodigo(), httpStatus);
        return new ResponseEntity<>(response, httpStatus);
    }

    @Override
    public ResponseEntity<AvisoReciboRutaResponse> updateAvisoReciboRuta(AvisoReciboRutaRequest body) {
        log.info("Iniciando endpoint update-aviso-recibo-ruta - ruta: {}, recibo: {}, usuario: {}",
                body.getRuta(), body.getRecibo(), body.getUsuario());

        AvisoReciboRutaResponse response = iBlCobroMovil.UpdateAvisoReciboRuta(body);
        HttpStatus httpStatus = HttpStatus.NO_CONTENT;

        // Mapeo de códigos de respuesta a HTTP Status
        if (response.getCodigo().equals("400")) {
            httpStatus = HttpStatus.BAD_REQUEST;
        } else if (response.getCodigo().equals("404")) {
            httpStatus = HttpStatus.NOT_FOUND;
        } else if (response.getCodigo().equals("500")) {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        } else if (response.getCodigo().equals("200")) {
            httpStatus = HttpStatus.OK; //
        }

        log.info("Finalizando endpoint update-aviso-recibo-ruta - código respuesta: {}, HTTP status: {}",
                response.getCodigo(), httpStatus);
        return new ResponseEntity<>(response, httpStatus);
    }

    @Override
    public ResponseEntity<UpdateReciboRutaResponse> updateReciboRuta(ReciboRutaRequest body) {
        log.info("Iniciando endpoint update-recibo-ruta - ruta: {}, recibo: {}, usuario: {}",
                body.getRuta(), body.getRecibo(), body.getUsuario());

        UpdateReciboRutaResponse response = iBlCobroMovil.UpdateReciboRuta(body);
        HttpStatus httpStatus = HttpStatus.OK;

        // Mapeo de códigos de respuesta a HTTP Status
        if (response.getCodigo().equals("400")) {
            httpStatus = HttpStatus.BAD_REQUEST;
        } else if (response.getCodigo().equals("404")) {
            httpStatus = HttpStatus.NOT_FOUND;
        } else if (response.getCodigo().equals("500")) {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        } else if (response.getCodigo().equals("200")) {
            httpStatus = HttpStatus.OK;
        }

        log.info("Finalizando endpoint update-recibo-ruta - código respuesta: {}, HTTP status: {}",
                response.getCodigo(), httpStatus);
        return new ResponseEntity<>(response, httpStatus);
    }

    @Override
    public ResponseEntity<AutenticarUsuarioResponse> autenticarUsuario(String usuario, String clave) {
        log.info("Iniciando endpoint autenticar-usuario - usuario: {}", usuario);

        AutenticarUsuarioResponse response = iBlCobroMovil.AutenticarUsuario(usuario, clave);
        HttpStatus httpStatus = HttpStatus.OK;

        // Mapeo de códigos de respuesta a HTTP Status
        if (response.getCodigo().equals("400")) {
            httpStatus = HttpStatus.BAD_REQUEST;
        } else if (response.getCodigo().equals("401")) {
            httpStatus = HttpStatus.UNAUTHORIZED;
        } else if (response.getCodigo().equals("404")) {
            httpStatus = HttpStatus.OK;
        } else if (response.getCodigo().equals("500")) {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        } else if (response.getCodigo().equals("200")) {
            httpStatus = HttpStatus.OK;
        }

        log.info("Finalizando endpoint autenticar-usuario - código respuesta: {}, HTTP status: {}",
                response.getCodigo(), httpStatus);
        return new ResponseEntity<>(response, httpStatus);
    }

    @Override
    public ResponseEntity<ObtenerRolesUsuarioResponse> obtenerRolesUsuario(Integer idUsuario) {
        log.info("Iniciando endpoint obtener-roles-usuario - idUsuario: {}", idUsuario);

        ObtenerRolesUsuarioResponse response = iBlCobroMovil.ObtenerRolesUsuario(idUsuario);
        HttpStatus httpStatus = HttpStatus.OK;

        // Mapeo de códigos de respuesta a HTTP Status
        if (response.getCodigo().equals("400")) {
            httpStatus = HttpStatus.BAD_REQUEST;
        } else if (response.getCodigo().equals("401")) {
            httpStatus = HttpStatus.UNAUTHORIZED;
        } else if (response.getCodigo().equals("404")) {
            httpStatus = HttpStatus.NOT_FOUND;
        } else if (response.getCodigo().equals("500")) {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        } else if (response.getCodigo().equals("200")) {
            httpStatus = HttpStatus.OK;
        }

        log.info("Finalizando endpoint obtener-roles-usuario - código respuesta: {}, HTTP status: {}, roles encontrados: {}",
                response.getCodigo(), httpStatus, response.getRoles() != null ? response.getRoles().size() : 0);
        return new ResponseEntity<>(response, httpStatus);
    }

    @Override
    public ResponseEntity<CajeroUsuarioResponse> listarCajeroUsuario(Integer usuario) {
        log.info("Iniciando endpoint listar-cajero-usuario - usuario: {}", usuario);

        CajeroUsuarioResponse response = iBlCobroMovil.ListarCajeroUsuario(usuario);
        HttpStatus httpStatus = HttpStatus.OK;

        // Mapeo de códigos de respuesta a HTTP Status
        if (response.getCodigo().equals("400")) {
            httpStatus = HttpStatus.BAD_REQUEST;
        } else if (response.getCodigo().equals("401")) {
            httpStatus = HttpStatus.UNAUTHORIZED;
        } else if (response.getCodigo().equals("404")) {
            httpStatus = HttpStatus.NOT_FOUND;
        } else if (response.getCodigo().equals("500")) {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        } else if (response.getCodigo().equals("200")) {
            httpStatus = HttpStatus.OK;
        }

        log.info("Finalizando endpoint listar-cajero-usuario - código respuesta: {}, HTTP status: {}, cajeros encontrados: {}",
                response.getCodigo(), httpStatus, response.getCajeros() != null ? response.getCajeros().size() : 0);
        return new ResponseEntity<>(response, httpStatus);
    }

    @Override
    public ResponseEntity<AccionesPorRolesResponse> obtenerAccionesPorRoles(String roles) {
        log.info("Iniciando endpoint obtener-acciones-por-roles - roles: {}", roles);

        AccionesPorRolesResponse response = iBlCobroMovil.ObtenerAccionesPorRoles(roles);
        HttpStatus httpStatus = HttpStatus.OK;

        // Mapeo de códigos de respuesta a HTTP Status
        if (response.getCodigo().equals("400")) {
            httpStatus = HttpStatus.BAD_REQUEST;
        } else if (response.getCodigo().equals("401")) {
            httpStatus = HttpStatus.UNAUTHORIZED;
        } else if (response.getCodigo().equals("404")) {
            httpStatus = HttpStatus.NOT_FOUND;
        } else if (response.getCodigo().equals("500")) {
            httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        } else if (response.getCodigo().equals("200")) {
            httpStatus = HttpStatus.OK;
        }

        log.info("Finalizando endpoint obtener-acciones-por-roles - código respuesta: {}, HTTP status: {}, acciones encontradas: {}",
                response.getCodigo(), httpStatus, response.getAcciones() != null ? response.getAcciones().size() : 0);
        return new ResponseEntity<>(response, httpStatus);
    }
    

    @Override
    public ResponseEntity<RutasCobradasResponse> getRutasCobradas() {
        log.info("Iniciando obtención de rutas cobradas");

        try {
            // Llamar al servicio de negocio
            RutasCobradasResponse response = iBlCobroMovil.getRutasCobradas();

            // Asegurarse de que idRutas nunca sea null
            if (response.getIdRutas() == null) {
                response.setIdRutas(new ArrayList<>());
            }

            log.info("Se encontraron {} rutas cobradas", response.getIdRutas().size());
            log.debug("Respuesta: {}", response);

            // Usar ResponseEntity.ok() para asegurar que se establezcan los headers correctos
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error al obtener rutas cobradas: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @Override
    public ResponseEntity<List<RutaLocalizacionResponse>> obtenerRutasLocalizacion(
            String fechaInicio,
            String fechaFin,
            Integer idRuta) {

        try {
            LocalDate fechaInicioDate = LocalDate.parse(fechaInicio);
            LocalDate fechaFinDate = LocalDate.parse(fechaFin);



            if (iBlCobroMovil == null) {
                return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
            }

            List<RutaLocalizacionResponse> rutas = iBlCobroMovil.obtenerRutasLocalizacion(
                    fechaInicioDate, fechaFinDate, idRuta);


            // Verificar el resultado
            if (rutas == null) {
                log.info("El resultado es null, devolviendo lista vacía");
                return ResponseEntity.ok(new ArrayList<>());
            }

            log.info("Se encontraron {} rutas", rutas.size());
            return ResponseEntity.ok(rutas);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @Override
    public ResponseEntity<List<LocalizacionPagosResponse>> obtenerLocalizacionPagos(
            String fechaInicio,
            String fechaFin,
            Integer idRuta) {

        log.info("Iniciando obtención de localización de pagos");
        log.info("Parámetros recibidos: fechaInicio={}, fechaFin={}, idRuta={}",
                fechaInicio, fechaFin, idRuta);

        try {
            // Convertir las fechas de String a LocalDate
            LocalDate fechaInicioDate = LocalDate.parse(fechaInicio);
            LocalDate fechaFinDate = LocalDate.parse(fechaFin);

            log.info("Fechas convertidas: fechaInicioDate={}, fechaFinDate={}",
                    fechaInicioDate, fechaFinDate);

            // Llamar al servicio de negocio
            List<LocalizacionPagosResponse> pagos = iBlCobroMovil.obtenerLocalizacionPagos(
                    fechaInicioDate, fechaFinDate, idRuta);

            // Verificar el resultado
            if (pagos == null) {
                log.info("El resultado es null, devolviendo lista vacía");
                return ResponseEntity.ok(new ArrayList<>());
            }

            log.info("Se encontraron {} pagos", pagos.size());
            return ResponseEntity.ok(pagos);

        } catch (Exception e) {
            log.error("Error al obtener localización de pagos: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<LlenarDetalleCierreCajaResponse>> llenarDetalleCierreCaja(Integer idUsuario) {
        log.info("Iniciando obtención de detalle de cierre de caja");
        log.info("Parámetro recibido: idUsuario={}", idUsuario);

        try {
            // Validar parámetro
            if (idUsuario == null) {
                log.error("El ID de usuario es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio
            List<LlenarDetalleCierreCajaResponse> detalles = iBlCobroMovil.llenarDetalleCierreCaja(idUsuario);

            // Verificar el resultado
            if (detalles == null) {
                log.info("El resultado es null, devolviendo lista vacía");
                return ResponseEntity.ok(new ArrayList<>());
            }

            log.info("Se encontraron {} detalles", detalles.size());
            return ResponseEntity.ok(detalles);

        } catch (Exception e) {
            log.error("Error al obtener detalle de cierre de caja: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<InfoRutaCierreCajaResponse>> getInfoRutaCierreCaja(Integer idUsuario) {
        log.info("Iniciando obtención de información de rutas para cierre de caja");
        log.info("Parámetro recibido: idUsuario={}", idUsuario);

        try {
            // Validar parámetro
            if (idUsuario == null) {
                log.error("El ID de usuario es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio
            List<InfoRutaCierreCajaResponse> rutas = iBlCobroMovil.getInfoRutaCierreCaja(idUsuario);

            // Verificar el resultado
            if (rutas == null) {
                log.info("El resultado es null, devolviendo lista vacía");
                return ResponseEntity.ok(new ArrayList<>());
            }

            log.info("Se encontraron {} rutas", rutas.size());
            return ResponseEntity.ok(rutas);

        } catch (Exception e) {
            log.error("Error al obtener información de rutas para cierre de caja: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<InfoRutaResponse>> getInfoRuta(Integer idUsuario, String fecha) {
        log.info("Iniciando obtención de información de rutas");
        log.info("Parámetros recibidos: idUsuario={}, fecha={}", idUsuario, fecha);

        try {
            // Validar parámetros
            if (idUsuario == null) {
                log.error("El ID de usuario es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (fecha == null || fecha.trim().isEmpty()) {
                log.error("La fecha es obligatoria");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio
            List<InfoRutaResponse> rutas = iBlCobroMovil.getInfoRuta(idUsuario, fecha);

            // Verificar el resultado
            if (rutas == null) {
                log.info("El resultado es null, devolviendo lista vacía");
                return ResponseEntity.ok(new ArrayList<>());
            }

            log.info("Se encontraron {} rutas", rutas.size());
            return ResponseEntity.ok(rutas);

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al obtener información de rutas: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<InfoPromedioResponse> getPromedios(Integer idUsuario, Integer ruta, Integer tipo, String fecha) {
        log.info("Iniciando obtención de promedios");
        log.info("Parámetros recibidos: idUsuario={}, ruta={}, tipo={}, fecha={}", 
                 idUsuario, ruta, tipo, fecha);

        try {
            // Validar parámetros
            if (idUsuario == null) {
                log.error("El ID de usuario es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (ruta == null) {
                log.error("El ID de ruta es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (tipo == null) {
                log.error("El tipo es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }



            // Llamar al servicio de negocio
            InfoPromedioResponse response = iBlCobroMovil.getPromedios(idUsuario, ruta, tipo, fecha);

            // Verificar el resultado
            if (response == null) {
                log.info("El resultado es null, creando respuesta por defecto");
                response = new InfoPromedioResponse();
                response.setPromedio("0");
            }

            log.info("Promedio obtenido: {}", response.getPromedio());
            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al obtener promedios: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<ModRutaPolizaFacturasResponse>> getModRutaPolizaFacturas(Integer idRuta) {
        log.info("Iniciando obtención de pólizas y facturas");
        log.info("Parámetros recibidos: idRuta={}", idRuta);

        try {
            // Validar parámetros
            if (idRuta == null) {
                log.error("El ID de ruta es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio
            List<ModRutaPolizaFacturasResponse> polizas = iBlCobroMovil.getModRutaPolizaFacturas(idRuta);

            // Verificar el resultado
            if (polizas == null) {
                log.info("El resultado es null, devolviendo lista vacía");
                return ResponseEntity.ok(new ArrayList<>());
            }

            log.info("Se encontraron {} pólizas", polizas.size());
            return ResponseEntity.ok(polizas);

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al obtener pólizas y facturas: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<ModRutaPolizaResponse>> getModRutaPoliza(Integer idRuta) {
        log.info("Iniciando obtención de pólizas");
        log.info("Parámetros recibidos: idRuta={}", idRuta);

        try {
            // Validar parámetros
            if (idRuta == null) {
                log.error("El ID de ruta es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio
            List<ModRutaPolizaResponse> polizas = iBlCobroMovil.getModRutaPoliza(idRuta);

            // Verificar el resultado
            if (polizas == null) {
                log.info("El resultado es null, devolviendo lista vacía");
                return ResponseEntity.ok(new ArrayList<>());
            }

            log.info("Se encontraron {} pólizas", polizas.size());
            return ResponseEntity.ok(polizas);

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al obtener pólizas: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<InfoRecibosPolizaRutaCobradosResponse>> getInfoRecibosPolizaRutaCobrados(Integer idRuta, String idePol, String numCertis) {
        log.info("Iniciando obtención de recibos cobrados");
        log.info("Parámetros recibidos: idRuta={}, idePol={}, numCertis={}", idRuta, idePol, numCertis);

        try {
            // Validar parámetros
            if (idRuta == null) {
                log.error("El ID de ruta es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio
            List<InfoRecibosPolizaRutaCobradosResponse> recibos = iBlCobroMovil.getInfoRecibosPolizaRutaCobrados(idRuta, idePol, numCertis);

            // Verificar el resultado
            if (recibos == null) {
                log.info("El resultado es null, devolviendo lista vacía");
                return ResponseEntity.ok(new ArrayList<>());
            }

            log.info("Se encontraron {} recibos cobrados", recibos.size());
            return ResponseEntity.ok(recibos);

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al obtener recibos cobrados: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<InfoRecibosPolizaRutaResponse>> getInfoRecibosPolizaRuta(Integer idRuta, String idePol, String numCertis) {
        log.info("Iniciando obtención de recibos");
        log.info("Parámetros recibidos: idRuta={}, idePol={}, numCertis={}", idRuta, idePol, numCertis);

        try {
            // Validar parámetros
            if (idRuta == null) {
                log.error("El ID de ruta es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idePol == null || idePol.isEmpty()) {
                log.error("El ID de póliza es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (numCertis == null || numCertis.isEmpty()) {
                log.error("El número de certificado es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio
            List<InfoRecibosPolizaRutaResponse> recibos = iBlCobroMovil.getInfoRecibosPolizaRuta(idRuta, idePol, numCertis);

            // Verificar el resultado
            if (recibos == null) {
                log.info("El resultado es null, devolviendo lista vacía");
                return ResponseEntity.ok(new ArrayList<>());
            }

            log.info("Se encontraron {} recibos", recibos.size());
            return ResponseEntity.ok(recibos);

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al obtener recibos: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<InfoFirmaReciboResponse>> getInfoFirmaRecibo(String idRuta, String recibo) {
        log.info("Iniciando obtención de información de firma");
        log.info("Parámetros recibidos: idRuta={}, recibo={}", idRuta, recibo);

        try {
            // Validar parámetros
            if (idRuta == null || idRuta.trim().isEmpty()) {
                log.error("El ID de ruta es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (recibo == null || recibo.trim().isEmpty()) {
                log.error("El número de recibo es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio
            List<InfoFirmaReciboResponse> firmas = iBlCobroMovil.getInfoFirmaRecibo(idRuta, recibo);

            // Verificar el resultado
            if (firmas == null) {
                log.info("El resultado es null, devolviendo lista vacía");
                return ResponseEntity.ok(new ArrayList<>());
            }

            log.info("Se encontraron {} registros de firma", firmas.size());
            return ResponseEntity.ok(firmas);

        } catch (IllegalArgumentException e) {
            log.error("Error de validación: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("Error al obtener información de firma: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<CobradoresResponse>> getCobradores() {
        log.info("Iniciando obtención de lista de cobradores");

        try {
            // Llamar al servicio de negocio
            List<CobradoresResponse> cobradores = iBlCobroMovil.getCobradores();

            // Verificar el resultado
            if (cobradores == null) {
                log.info("El resultado es null, devolviendo lista vacía");
                return ResponseEntity.ok(new ArrayList<>());
            }

            log.info("Se encontraron {} cobradores", cobradores.size());
            return ResponseEntity.ok(cobradores);

        } catch (Exception e) {
            log.error("Error al obtener lista de cobradores: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<TipoPagoResponse>> getTipoPago() {
        log.info("Iniciando obtención de lista de tipos de pago");

        try {
            // Llamar al servicio de negocio
            List<TipoPagoResponse> tiposPago = iBlCobroMovil.getTipoPago();

            // Verificar el resultado
            if (tiposPago == null) {
                log.info("El resultado es null, devolviendo lista vacía");
                return ResponseEntity.ok(new ArrayList<>());
            }

            log.info("Se encontraron {} tipos de pago", tiposPago.size());
            return ResponseEntity.ok(tiposPago);

        } catch (Exception e) {
            log.error("Error al obtener lista de tipos de pago: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
