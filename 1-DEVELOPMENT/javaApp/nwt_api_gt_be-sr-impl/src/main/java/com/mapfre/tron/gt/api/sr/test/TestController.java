package com.mapfre.tron.gt.api.sr.test;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mapfre.tron.api.cmn.client.model.OCmnLngS;
import com.mapfre.tron.gt.api.bl.cache.ICacheableMethods;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Validated
@RestController
@RequestMapping("/test")
@Api(tags = "Test", description = "Test Controller for testing services.")
public class TestController {

	@Autowired
	private ICacheableMethods iCacheableMethods;

	@ApiOperation(value = "Fetch cached languages from the CMN API", notes = "Fetches the list of languages based on the provided application language and user identifier.", response = List.class)
	@GetMapping("/languages")
	public ResponseEntity<List<OCmnLngS>> getLanguages(
			@ApiParam(value = "Application language", required = true) @RequestHeader(value = "Accept-Language", required = true) String acceptLanguage,
			@ApiParam(value = "User identifier", required = true) @RequestHeader(value = "userBK", required = true) String userBK) {
		log.info("Test getCmnApiLanguages called!");

		List<OCmnLngS> rsOCmnLngST = iCacheableMethods.getLanguagesCacheable(acceptLanguage, userBK);

		return new ResponseEntity<>(rsOCmnLngST, HttpStatus.OK);
	}

}
