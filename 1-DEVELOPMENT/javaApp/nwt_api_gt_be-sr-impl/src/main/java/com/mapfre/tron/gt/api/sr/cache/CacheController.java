package com.mapfre.tron.gt.api.sr.cache;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import com.mapfre.tron.gt.api.bl.cache.config.ICacheService;
import com.mapfre.tron.gt.api.sr.CacheApi;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.sf.ehcache.CacheException;

@Slf4j
@RestController
@Api(tags = { "Cache" })
public class CacheController implements CacheApi {

	@Autowired
	private ICacheService iCacheService;

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public ResponseEntity<Void> clearCache() {
		log.info("The clearCache rest operation had been called!");

		try {
			iCacheService.evictAllCaches();
			return new ResponseEntity("Operation successfully done!", HttpStatus.OK);
		} catch (Exception e) {
			log.error("Error cleaning all caches", e);
			throw new CacheException("Error during cache deletion!", e);
		}
	}

}
