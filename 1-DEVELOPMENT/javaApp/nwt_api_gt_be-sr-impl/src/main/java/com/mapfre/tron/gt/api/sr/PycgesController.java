package com.mapfre.tron.gt.api.sr;

import java.time.LocalDate;
import java.util.List;

import com.mapfre.tron.gt.api.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import com.mapfre.tron.gt.api.bl.IBlPycges;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * Controlador REST para operaciones de Pycges.
 */
@RestController
@Slf4j
@Api(tags = {"Pycges"})
public class PycgesController implements PycgesApi {

    @Autowired
    private IBlPycges blPycges;

    @Override
    public ResponseEntity<List<PycUsuario>> getUsuarios() {
        log.info("Recibida solicitud para obtener lista completa de usuarios");

        try {
            // Llamar al servicio de negocio para obtener la lista de usuarios
            List<PycUsuario> listaUsuarios = blPycges.getUsuarios();

            // Determinar el código HTTP basado en los resultados
            if (listaUsuarios == null || listaUsuarios.isEmpty()) {
                log.info("No se encontraron usuarios");
                return new ResponseEntity<>(listaUsuarios, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} usuarios", listaUsuarios.size());
            return new ResponseEntity<>(listaUsuarios, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycAplicacion>> getAplicaciones(Integer idProceso) {
        log.info("Recibida solicitud para obtener aplicaciones del proceso {}", idProceso);

        try {
            // Llamar al servicio de negocio
            List<PycAplicacion> aplicaciones = blPycges.getAplicaciones(idProceso);

            // Determinar el código HTTP basado en los resultados
            if (aplicaciones == null || aplicaciones.isEmpty()) {
                log.info("No se encontraron aplicaciones para el proceso {}", idProceso);
                return new ResponseEntity<>(aplicaciones, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} aplicaciones para el proceso {}", aplicaciones.size(), idProceso);
            return new ResponseEntity<>(aplicaciones, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycInfoProceso>> getInfoProceso(Integer idProceso) {
        log.info("Recibida solicitud para obtener información del proceso {}", idProceso);

        try {
            // Validar parámetros obligatorios
            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio
            List<PycInfoProceso> infoProceso = blPycges.getInfoProceso(idProceso);

            // Determinar el código HTTP basado en los resultados
            if (infoProceso == null || infoProceso.isEmpty()) {
                log.info("No se encontró información para el proceso {}", idProceso);
                return new ResponseEntity<>(infoProceso, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontró información del proceso {}. Total de registros: {}", idProceso, infoProceso.size());
            return new ResponseEntity<>(infoProceso, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycControlProc>> getControlesProceso(Integer idProceso, String tipoControl) {
        log.info("Recibida solicitud para obtener controles del proceso {} con tipo de control {}", idProceso, tipoControl);

        try {
            // Validar parámetros obligatorios
            if (idProceso == null || tipoControl == null || tipoControl.trim().isEmpty()) {
                log.error("Parámetros obligatorios faltantes: idProceso y tipoControl son requeridos");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio
            List<PycControlProc> controlesProceso = blPycges.getControlesProceso(idProceso, tipoControl);

            // Determinar el código HTTP basado en los resultados
            if (controlesProceso == null || controlesProceso.isEmpty()) {
                log.info("No se encontraron controles para el proceso {} con tipo de control {}", idProceso, tipoControl);
                return new ResponseEntity<>(controlesProceso, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron controles del proceso {}. Total de registros: {}", idProceso, controlesProceso.size());
            return new ResponseEntity<>(controlesProceso, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycEstadoPerfil>> getEstadoPerfil(Integer idPerfil, Integer idProceso) {
        log.info("Recibida solicitud para obtener estados del perfil {} en el proceso {}", idPerfil, idProceso);

        try {
            // Validar parámetros obligatorios
            if (idPerfil == null || idProceso == null) {
                log.error("Parámetros obligatorios faltantes: idPerfil y idProceso son requeridos");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio
            List<PycEstadoPerfil> estadosPerfil = blPycges.getEstadoPerfil(idPerfil, idProceso);

            // Determinar el código HTTP basado en los resultados
            if (estadosPerfil == null || estadosPerfil.isEmpty()) {
                log.info("No se encontraron estados para el perfil {} en el proceso {}", idPerfil, idProceso);
                return new ResponseEntity<>(estadosPerfil, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron estados del perfil {}. Total de registros: {}", idPerfil, estadosPerfil.size());
            return new ResponseEntity<>(estadosPerfil, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycProcesoPorTipo>> getProcesosPorTipo(Integer idTipo) {
        log.info("Recibida solicitud para obtener procesos del tipo de petición {}", idTipo);

        try {
            // Llamar al servicio de negocio
            List<PycProcesoPorTipo> procesos = blPycges.getProcesosPorTipo(idTipo);

            // Determinar el código HTTP basado en los resultados
            if (procesos == null || procesos.isEmpty()) {
                log.info("No se encontraron procesos para el tipo de petición {}", idTipo);
                return new ResponseEntity<>(procesos, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} procesos para el tipo de petición {}", procesos.size(), idTipo);
            return new ResponseEntity<>(procesos, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }



    @Override
    public ResponseEntity<List<PycUsuarioPerfil>> getUsuariosPerfil(Integer idPerfil) {
        log.info("Recibida solicitud para obtener usuarios por perfil. ID Perfil: {}", idPerfil);

        try {
            // Llamar al servicio de negocio para obtener la lista de usuarios por perfil
            List<PycUsuarioPerfil> listaUsuariosPerfil = blPycges.getUsuariosPerfil(idPerfil);

            // Determinar el código HTTP basado en los resultados
            if (listaUsuariosPerfil == null || listaUsuariosPerfil.isEmpty()) {
                log.info("No se encontraron usuarios para el perfil especificado");
                return new ResponseEntity<>(listaUsuariosPerfil, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} usuarios para el perfil especificado", listaUsuariosPerfil.size());
            return new ResponseEntity<>(listaUsuariosPerfil, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycListadoAsigna>> getListadoAsigna(Integer idPerfil) {
        log.info("Recibida solicitud para obtener listado de asignaciones por perfil. ID Perfil: {}", idPerfil);

        try {
            // Llamar al servicio de negocio para obtener el listado de asignaciones
            List<PycListadoAsigna> listadoAsigna = blPycges.getListadoAsigna(idPerfil);

            // Determinar el código HTTP basado en los resultados
            if (listadoAsigna == null || listadoAsigna.isEmpty()) {
                log.info("No se encontraron asignaciones para el perfil especificado");
                return new ResponseEntity<>(listadoAsigna, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} asignaciones para el perfil especificado", listadoAsigna.size());
            return new ResponseEntity<>(listadoAsigna, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycGetQueryDatVar> getQueryDatVar(Integer idProceso, Integer idSeccion, Integer idDatoVar) {
        log.info("Recibida solicitud para obtener consulta SQL de dato variable. ID Proceso: {}, ID Sección: {}, ID Dato Variable: {}",
                idProceso, idSeccion, idDatoVar);

        try {
            // Llamar al servicio de negocio para obtener la consulta SQL
            PycGetQueryDatVar queryDatVar = blPycges.getQueryDatVar(idProceso, idSeccion, idDatoVar);

            // Determinar el código HTTP basado en los resultados
            if (queryDatVar == null || queryDatVar.getQuery() == null || queryDatVar.getQuery().isEmpty()) {
                log.info("No se encontró consulta SQL para los parámetros especificados");
                return new ResponseEntity<>(queryDatVar, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontró consulta SQL para los parámetros especificados");
            return new ResponseEntity<>(queryDatVar, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycSrcMultipleOption> getSrcMultipleOption(Integer idPerfil) {
        log.info("Recibida solicitud para verificar opción de selección múltiple. ID Perfil: {}", idPerfil);

        try {
            // Llamar al servicio de negocio para verificar la opción de selección múltiple
            PycSrcMultipleOption srcMultipleOption = blPycges.getSrcMultipleOption(idPerfil);

            // Determinar el código HTTP basado en los resultados
            if (srcMultipleOption == null || srcMultipleOption.getHasMultipleOption() == null) {
                log.info("No se encontró información de selección múltiple para el perfil especificado");
                return new ResponseEntity<>(srcMultipleOption, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontró información de selección múltiple para el perfil especificado. Resultado: {}",
                    srcMultipleOption.getHasMultipleOption());
            return new ResponseEntity<>(srcMultipleOption, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycGetUsuariosAsig>> getUsuariosAsig(Integer idProceso, Integer idPerfil, Integer idAplicacion, Integer idEstado) {
        log.info("Recibida solicitud para obtener usuarios asignados. ID Proceso: {}, ID Perfil: {}, ID Aplicación: {}, ID Estado: {}",
                idProceso, idPerfil, idAplicacion, idEstado);

        try {
            // Llamar al servicio de negocio para obtener los usuarios asignados
            List<PycGetUsuariosAsig> usuariosAsig = blPycges.getUsuariosAsig(idProceso, idPerfil, idAplicacion, idEstado);

            // Determinar el código HTTP basado en los resultados
            if (usuariosAsig == null || usuariosAsig.isEmpty()) {
                log.info("No se encontraron usuarios asignados para los parámetros especificados");
                return new ResponseEntity<>(usuariosAsig, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} usuarios asignados para los parámetros especificados", usuariosAsig.size());
            return new ResponseEntity<>(usuariosAsig, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycUsuPerfilProceso>> getUsuPerfilProceso(Integer idPerfil, Integer idProceso) {
        log.info("Recibida solicitud para obtener usuarios por perfil y proceso. ID Perfil: {}, ID Proceso: {}", idPerfil, idProceso);

        try {
            // Llamar al servicio de negocio para obtener los usuarios por perfil y proceso
            List<PycUsuPerfilProceso> usuariosPerfilProceso = blPycges.getUsuPerfilProceso(idPerfil, idProceso);

            // Determinar el código HTTP basado en los resultados
            if (usuariosPerfilProceso == null || usuariosPerfilProceso.isEmpty()) {
                log.info("No se encontraron usuarios para el perfil {} y proceso {}", idPerfil, idProceso);
                return new ResponseEntity<>(usuariosPerfilProceso, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} usuarios para el perfil {} y proceso {}", usuariosPerfilProceso.size(), idPerfil, idProceso);
            return new ResponseEntity<>(usuariosPerfilProceso, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycUsuProcesos>> getUsuarioProcesos(String usuario) {
        log.info("Recibida solicitud para obtener procesos del usuario: {}", usuario);

        try {
            // Llamar al servicio de negocio para obtener los procesos del usuario
            List<PycUsuProcesos> usuarioProcesos = blPycges.getUsuarioProcesos(usuario);

            // Determinar el código HTTP basado en los resultados
            if (usuarioProcesos == null || usuarioProcesos.isEmpty()) {
                log.info("No se encontraron procesos para el usuario {}", usuario);
                return new ResponseEntity<>(usuarioProcesos, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} procesos para el usuario {}", usuarioProcesos.size(), usuario);
            return new ResponseEntity<>(usuarioProcesos, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycEstadoUsu>> getEstadoUsuario(String usuario, String clave) {
        log.info("Recibida solicitud para obtener estado de usuario: {}", usuario);

        try {
            // Validar parámetros obligatorios
            if (usuario == null || usuario.trim().isEmpty() || clave == null || clave.trim().isEmpty()) {
                log.error("Parámetros obligatorios faltantes: usuario y clave son requeridos");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener el estado del usuario
            List<PycEstadoUsu> estadoUsuario = blPycges.getEstadoUsuario(usuario, clave);

            // Determinar el código HTTP basado en los resultados
            if (estadoUsuario == null || estadoUsuario.isEmpty()) {
                log.info("No se encontró información de estado para el usuario {}", usuario);
                return new ResponseEntity<>(estadoUsuario, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontró información de estado para el usuario {}", usuario);
            return new ResponseEntity<>(estadoUsuario, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycUsuarioResponse> updatePerfil(PycUpdPerfil perfilData) {
        log.info("Recibida solicitud para actualizar perfil. ID Perfil: {}, ID Usuario: {}",
                perfilData.getIdPerfil(), perfilData.getIdUsuario());

        try {
            // Validar datos de entrada
            if (perfilData.getIdPerfil() == null || perfilData.getIdUsuario() == null) {
                log.error("Datos de entrada inválidos: idPerfil y idUsuario son obligatorios");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para actualizar el perfil
            Integer idUsuario = blPycges.updatePerfil(perfilData);

            // Crear objeto de respuesta
            PycUsuarioResponse response = new PycUsuarioResponse();
            response.setIdUsuario(idUsuario);

            log.info("Perfil actualizado correctamente. ID Usuario: {}", idUsuario);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycUsuarioResponse> updateUsuario(PycUpdUsuario usuarioData) {
        log.info("Recibida solicitud para actualizar usuario. Nombre: {}, Numa o Usuario: {}",
                usuarioData.getPrimerNombre(), usuarioData.getNumaOUsuario());

        try {
            // Validar datos de entrada
            if (usuarioData.getPrimerNombre() == null || usuarioData.getPrimerApellido() == null ||
                usuarioData.getNumaOUsuario() == null || usuarioData.getEstado() == null) {
                log.error("Datos de entrada inválidos: primerNombre, primerApellido, numaOUsuario y estado son obligatorios");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para actualizar el usuario
            Integer idUsuario = blPycges.updateUsuario(usuarioData);

            // Crear objeto de respuesta
            PycUsuarioResponse response = new PycUsuarioResponse();
            response.setIdUsuario(idUsuario);

            log.info("Usuario actualizado correctamente. ID Usuario: {}", idUsuario);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycSolicitudResponse> creaSolicitud(PycCreaSolicitud solicitudData) {
        log.info("Recibida solicitud para crear solicitud. Plataforma: {}, Tipo Seguro: {}, Cliente: {} {}",
                solicitudData.getPlataforma(), solicitudData.getTipoSeguro(),
                solicitudData.getNombres(), solicitudData.getApellidos());

        try {
            // Validar datos de entrada
            if (solicitudData.getPlataforma() == null || solicitudData.getTipoSeguro() == null ||
                solicitudData.getNombres() == null || solicitudData.getApellidos() == null ||
                solicitudData.getTelefono() == null || solicitudData.getEmail() == null) {
                log.error("Datos de entrada inválidos: plataforma, tipoSeguro, nombres, apellidos, telefono y email son obligatorios");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }
            // El XML puede ser null

            // Llamar al servicio de negocio para crear la solicitud
            Integer idSolicitud = blPycges.creaSolicitud(solicitudData);

            // Crear objeto de respuesta
            PycSolicitudResponse response = new PycSolicitudResponse();
            response.setIdSolicitud(idSolicitud);

            log.info("Solicitud creada correctamente. ID Solicitud: {}", idSolicitud);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    @Override
    public ResponseEntity<PycPeticionResponse> insertPeticion(PycInsertPeticion peticionData) {
        log.info("Recibida solicitud para insertar petición: {}", peticionData.getNombrePeticion());

        try {
            // Validar datos de entrada obligatorios
            if (peticionData.getNombrePeticion() == null || peticionData.getDescripcionPeticion() == null ||
                peticionData.getIdUsuarioSolicitante() == null || peticionData.getIdPerfil() == null ||
                peticionData.getIdTipo() == null || peticionData.getIdProceso() == null ||
                peticionData.getCodcia() == null || peticionData.getCodcia().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: nombrePeticion, descripcionPeticion, idUsuarioSolicitante, idPerfil, idTipo, idProceso y codcia son obligatorios");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para insertar la petición
            Integer idPeticion = blPycges.insertPeticion(peticionData);

            // Crear objeto de respuesta
            PycPeticionResponse response = new PycPeticionResponse();
            response.setIdPeticion(idPeticion);

            log.info("Petición insertada correctamente. ID Petición: {}", idPeticion);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la petición: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    @Override
    public ResponseEntity<List<PycPeticionPerfil>> getPeticionPerfil(Integer idPerfil, Integer idProceso, String numaOUsuario,
                                                                     String idEstado, Integer idPeticion, Integer idCanal,
                                                                     Integer idUsuario, String indSubs, Integer idOficina) {
        log.info("Recibida solicitud para obtener peticiones por perfil. ID Perfil: {}, ID Proceso: {}, Numa O Usuario: {}, ID Estado: {}, ID Petición: {}, ID Canal: {}, ID Usuario: {}, Ind Subs: {}, ID Oficina: {}",
                idPerfil, idProceso, numaOUsuario, idEstado, idPeticion, idCanal, idUsuario, indSubs, idOficina);

        try {
            // Validar parámetros obligatorios
            if (idPerfil == null || idProceso == null ||
                numaOUsuario == null || numaOUsuario.trim().isEmpty()) {
                log.error("Parámetros obligatorios faltantes: idPerfil, idProceso y numaOUsuario son requeridos");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener las peticiones por perfil
            List<PycPeticionPerfil> peticionesPerfil = blPycges.getPeticionPerfil(idPerfil, idProceso, idEstado,
                    idPeticion, idCanal, idUsuario, indSubs, idOficina, numaOUsuario);

            // Determinar el código HTTP basado en los resultados
            if (peticionesPerfil == null || peticionesPerfil.isEmpty()) {
                log.info("No se encontraron peticiones para los criterios especificados");
                return new ResponseEntity<>(peticionesPerfil, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} peticiones para los criterios especificados", peticionesPerfil.size());
            return new ResponseEntity<>(peticionesPerfil, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycPetSinProgramador>> getPeticionSinProgramador() {
        log.info("Recibida solicitud para obtener peticiones sin programador");

        try {
            // Llamar al servicio de negocio para obtener las peticiones sin programador
            List<PycPetSinProgramador> peticionesSinProgramador = blPycges.getPeticionSinProgramador();

            // Determinar el código HTTP basado en los resultados
            if (peticionesSinProgramador == null || peticionesSinProgramador.isEmpty()) {
                log.info("No se encontraron peticiones sin programador");
                return new ResponseEntity<>(peticionesSinProgramador, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} peticiones sin programador", peticionesSinProgramador.size());
            return new ResponseEntity<>(peticionesSinProgramador, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycAreaPeti>> getConArea() {
        log.info("Recibida solicitud para obtener áreas con peticiones");

        try {
            // Llamar al servicio de negocio para obtener las áreas con peticiones
            List<PycAreaPeti> areasConPeticiones = blPycges.getConArea();

            // Determinar el código HTTP basado en los resultados
            if (areasConPeticiones == null || areasConPeticiones.isEmpty()) {
                log.info("No se encontraron áreas con peticiones");
                return new ResponseEntity<>(areasConPeticiones, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} áreas con peticiones", areasConPeticiones.size());
            return new ResponseEntity<>(areasConPeticiones, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycDepartamento>> getConDepartamento(Integer idArea) {
        log.info("Recibida solicitud para obtener departamentos. ID Área: {}", idArea);

        try {
            // Llamar al servicio de negocio para obtener los departamentos
            List<PycDepartamento> departamentos = blPycges.getConDepartamento(idArea);

            // Determinar el código HTTP basado en los resultados
            if (departamentos == null || departamentos.isEmpty()) {
                log.info("No se encontraron departamentos para el área especificada");
                return new ResponseEntity<>(departamentos, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} departamentos para el área especificada", departamentos.size());
            return new ResponseEntity<>(departamentos, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycAreaUsuario>> getConSolicitante(Integer idArea, Integer idDepartamento) {
        log.info("Recibida solicitud para obtener usuarios solicitantes. ID Área: {}, ID Departamento: {}", idArea, idDepartamento);

        try {
            // Llamar al servicio de negocio para obtener los usuarios solicitantes
            List<PycAreaUsuario> usuariosSolicitantes = blPycges.getConSolicitante(idArea, idDepartamento);

            // Determinar el código HTTP basado en los resultados
            if (usuariosSolicitantes == null || usuariosSolicitantes.isEmpty()) {
                log.info("No se encontraron usuarios solicitantes para los parámetros especificados");
                return new ResponseEntity<>(usuariosSolicitantes, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} usuarios solicitantes para los parámetros especificados", usuariosSolicitantes.size());
            return new ResponseEntity<>(usuariosSolicitantes, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycAnalista>> getConAnalista(Integer idSolicitante, String prioridad) {
        log.info("Recibida solicitud para obtener analistas. ID Solicitante: {}, Prioridad: {}", idSolicitante, prioridad);

        try {
            // Llamar al servicio de negocio para obtener los analistas
            List<PycAnalista> analistas = blPycges.getConAnalista(idSolicitante, prioridad);

            // Determinar el código HTTP basado en los resultados
            if (analistas == null || analistas.isEmpty()) {
                log.info("No se encontraron analistas para los parámetros especificados");
                return new ResponseEntity<>(analistas, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} analistas para los parámetros especificados", analistas.size());
            return new ResponseEntity<>(analistas, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycConEstado>> getConEstado(Integer idSolicitante, String prioridad, Integer idAnalista) {
        log.info("Recibida solicitud para obtener estados. ID Solicitante: {}, Prioridad: {}, ID Analista: {}",
                idSolicitante, prioridad, idAnalista);

        try {
            // Validar parámetros obligatorios
            if (idSolicitante == null) {
                log.error("Parámetro obligatorio faltante: idSolicitante es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (prioridad == null) {
                log.error("Parámetro obligatorio faltante: prioridad es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los estados
            List<PycConEstado> estados = blPycges.getConEstado(idSolicitante, prioridad, idAnalista);

            // Determinar el código HTTP basado en los resultados
            if (estados == null || estados.isEmpty()) {
                log.info("No se encontraron estados para los parámetros especificados");
                return new ResponseEntity<>(estados, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} estados para los parámetros especificados", estados.size());
            return new ResponseEntity<>(estados, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycPrioridad>> getConPrioridad(Integer idSolicitante, Integer idArea, Integer idDepartamento) {
        log.info("Recibida solicitud para obtener prioridades. ID Solicitante: {}, ID Área: {}, ID Departamento: {}",
                idSolicitante, idArea, idDepartamento);

        try {
            // Llamar al servicio de negocio para obtener las prioridades
            List<PycPrioridad> prioridades = blPycges.getConPrioridad(idSolicitante, idArea, idDepartamento);

            // Determinar el código HTTP basado en los resultados
            if (prioridades == null || prioridades.isEmpty()) {
                log.info("No se encontraron prioridades para los parámetros especificados");
                return new ResponseEntity<>(prioridades, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} prioridades para los parámetros especificados", prioridades.size());
            return new ResponseEntity<>(prioridades, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycTabAnios>> getTabAnios(Integer idProceso) {
        log.info("Recibida solicitud para obtener años. ID Proceso: {}", idProceso);

        try {
            // Llamar al servicio de negocio para obtener los años
            List<PycTabAnios> anios = blPycges.getTabAnios(idProceso);

            // Determinar el código HTTP basado en los resultados
            if (anios == null || anios.isEmpty()) {
                log.info("No se encontraron años para el proceso especificado");
                return new ResponseEntity<>(anios, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} años para el proceso especificado", anios.size());
            return new ResponseEntity<>(anios, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycTabArea>> getTabArea(Integer idProceso, String anio) {
        log.info("Recibida solicitud para obtener áreas. ID Proceso: {}, Año: {}", idProceso, anio);

        try {
            // Validar parámetro obligatorio
            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener las áreas
            List<PycTabArea> areas = blPycges.getTabArea(idProceso, anio);

            // Determinar el código HTTP basado en los resultados
            if (areas == null || areas.isEmpty()) {
                log.info("No se encontraron áreas para el proceso especificado");
                return new ResponseEntity<>(areas, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} áreas para el proceso especificado", areas.size());
            return new ResponseEntity<>(areas, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycTabEstado>> getTabEstado(Integer idProceso, String anio, Integer idArea) {
        log.info("Recibida solicitud para obtener estados. ID Proceso: {}, Año: {}, ID Área: {}", idProceso, anio, idArea);

        try {
            // Validar parámetro obligatorio
            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los estados
            List<PycTabEstado> estados = blPycges.getTabEstado(idProceso, anio, idArea);

            // Determinar el código HTTP basado en los resultados
            if (estados == null || estados.isEmpty()) {
                log.info("No se encontraron estados para los parámetros especificados");
                return new ResponseEntity<>(estados, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} estados para los parámetros especificados", estados.size());
            return new ResponseEntity<>(estados, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycPetAvance>> getPeticionAvance(Integer idProceso, Integer idArea, String estado, String codcia, Integer anio) {
        log.info("Recibida solicitud para obtener avance de peticiones. ID Proceso: {}, ID Área: {}, Estado: {}, Codcia: {}, Año: {}",
                idProceso, idArea, estado, codcia, anio);

        try {
            // Llamar al servicio de negocio para obtener el avance de peticiones
            List<PycPetAvance> avancePeticiones = blPycges.getPeticionAvance(idProceso, idArea, estado, codcia, anio);

            // Determinar el código HTTP basado en los resultados
            if (avancePeticiones == null || avancePeticiones.isEmpty()) {
                log.info("No se encontraron avances de peticiones para los criterios especificados");
                return new ResponseEntity<>(avancePeticiones, HttpStatus.NO_CONTENT);
            }

            log.info("Avance de peticiones obtenido exitosamente. Total de registros: {}", avancePeticiones.size());
            return new ResponseEntity<>(avancePeticiones, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycUsuPerfilPeti>> getUsuariosPerfilPeticion(Integer idPeticion, Integer idPerfil, Integer idAplicacion) {
        log.info("Recibida solicitud para obtener usuarios por perfil y petición. ID Petición: {}, ID Perfil: {}, ID Aplicación: {}",
                idPeticion, idPerfil, idAplicacion);

        try {
            // Llamar al servicio de negocio para obtener los usuarios
            List<PycUsuPerfilPeti> usuarios = blPycges.getUsuariosPerfilPeticion(idPeticion, idPerfil, idAplicacion);

            // Determinar el código HTTP basado en los resultados
            if (usuarios == null || usuarios.isEmpty()) {
                log.info("No se encontraron usuarios para los parámetros especificados");
                return new ResponseEntity<>(usuarios, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} usuarios para los parámetros especificados", usuarios.size());
            return new ResponseEntity<>(usuarios, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycPetiFiltro>> getPeticionFiltros(Integer idArea, Integer idDepartamento, Integer idPerfil,
                                                                  Integer idEstado, Integer idUsuarioSolicitante, Integer idTipo,
                                                                  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
                                                                  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
                                                                  Integer idProceso) {
        log.info("Recibida solicitud para obtener peticiones filtradas. ID Área: {}, ID Departamento: {}, ID Perfil: {}, ID Estado: {}, ID Usuario Solicitante: {}, ID Tipo: {}, Fecha Inicio: {}, Fecha Fin: {}, ID Proceso: {}",
                idArea, idDepartamento, idPerfil, idEstado, idUsuarioSolicitante, idTipo, fechaInicio, fechaFin, idProceso);

        try {
            // Convertir LocalDate a String para la capa de negocio
            String fechaInicioStr = fechaInicio != null ? fechaInicio.toString() : null;
            String fechaFinStr = fechaFin != null ? fechaFin.toString() : null;

            // Llamar al servicio de negocio para obtener las peticiones filtradas
            List<PycPetiFiltro> peticionesFiltradas = blPycges.getPeticionFiltros(idArea, idDepartamento, idPerfil,
                    idEstado, idUsuarioSolicitante, idTipo, fechaInicioStr, fechaFinStr, idProceso);

            // Determinar el código HTTP basado en los resultados
            if (peticionesFiltradas == null || peticionesFiltradas.isEmpty()) {
                log.info("No se encontraron peticiones para los criterios de filtrado especificados");
                return new ResponseEntity<>(peticionesFiltradas, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} peticiones para los criterios de filtrado especificados", peticionesFiltradas.size());
            return new ResponseEntity<>(peticionesFiltradas, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycReportePeti>> getReportePeticion(Integer idArea, Integer idDepartamento, Integer idSolicitante,
                                                                   String prioridad, Integer idAnalista, String estado) {
        log.info("Recibida solicitud para obtener reporte de peticiones. ID Área: {}, ID Departamento: {}, ID Solicitante: {}, Prioridad: {}, ID Analista: {}, Estado: {}",
                idArea, idDepartamento, idSolicitante, prioridad, idAnalista, estado);

        try {
            // Llamar al servicio de negocio para obtener el reporte de peticiones
            List<PycReportePeti> reportePeticiones = blPycges.getReportePeticion(idArea, idDepartamento, idSolicitante,
                    prioridad, idAnalista, estado);

            // Determinar el código HTTP basado en los resultados
            if (reportePeticiones == null || reportePeticiones.isEmpty()) {
                log.info("No se encontraron peticiones para los criterios de reporte especificados");
                return new ResponseEntity<>(reportePeticiones, HttpStatus.NO_CONTENT);
            }

            log.info("Reporte de peticiones obtenido exitosamente. Total de registros: {}", reportePeticiones.size());
            return new ResponseEntity<>(reportePeticiones, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al obtener el reporte de peticiones: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycUsuarioResponse> insertUserProceso(PycInsertUserPro userData) {
        log.info("Recibida solicitud para insertar/actualizar usuario proceso. ID Usuario: {}, ID Proceso: {}, Estado: {}",
                userData.getIdUsuario(), userData.getIdProceso(), userData.getEstado());

        try {
            // Validar datos de entrada
            if (userData.getIdUsuario() == null || userData.getIdProceso() == null || userData.getEstado() == null) {
                log.error("Datos de entrada inválidos: idUsuario, idProceso y estado son obligatorios");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para insertar/actualizar usuario proceso
            Integer idUsuario = blPycges.insertUserProceso(userData);

            // Crear objeto de respuesta
            PycUsuarioResponse response = new PycUsuarioResponse();
            response.setIdUsuario(idUsuario);

            log.info("Usuario proceso insertado/actualizado correctamente. ID Usuario: {}", idUsuario);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycUsuarioResponse> insertUsuario(PycInsertUsuario usuarioData) {
        log.info("Recibida solicitud para insertar usuario. Primer Nombre: {}, Primer Apellido: {}, Numa o Usuario: {}, Email: {}",
                usuarioData.getPrimerNombre(), usuarioData.getPrimerApellido(),
                usuarioData.getNumaOUsuario(), usuarioData.getEmail());

        try {
            // Validar datos de entrada
            if (usuarioData.getPrimerNombre() == null || usuarioData.getPrimerApellido() == null ||
                usuarioData.getNumaOUsuario() == null || usuarioData.getEmail() == null ||
                usuarioData.getGenero() == null) {
                log.error("Datos de entrada inválidos: primerNombre, primerApellido, numaOUsuario, email y genero son obligatorios");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para insertar usuario
            Integer idUsuario = blPycges.insertUsuario(usuarioData);

            // Crear objeto de respuesta
            PycUsuarioResponse response = new PycUsuarioResponse();
            response.setIdUsuario(idUsuario);

            log.info("Usuario insertado correctamente. ID Usuario: {}", idUsuario);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycPeticionResponse> actDocumento(PycActDocumento documentoData) {
        log.info("Recibida solicitud para actualizar documento. ID Petición: {}, ID Documento: {}",
                documentoData.getIdPeticion(), documentoData.getIdDocumento());

        try {
            // Validar parámetros obligatorios
            if (documentoData.getIdPeticion() == null ||
                documentoData.getIdDocumento() == null ||
                documentoData.getLocalizacion() == null || documentoData.getLocalizacion().trim().isEmpty()) {
                log.error("Parámetros obligatorios faltantes: idPeticion, idDocumento y localizacion son requeridos");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para actualizar el documento
            Integer idPeticion = blPycges.actDocumento(documentoData);

            // Crear objeto de respuesta
            PycPeticionResponse response = new PycPeticionResponse();
            response.setIdPeticion(idPeticion);

            log.info("Documento actualizado correctamente. ID Petición: {}", idPeticion);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycObserPet>> getObserPeticion(Integer idPeticion, String numaOUsuario) {
        log.info("Recibida solicitud para obtener observaciones de la petición {}. Numa O Usuario: {}", idPeticion, numaOUsuario);

        try {
            // Validar parámetro obligatorio
            if (idPeticion == null) {
                log.error("Parámetro obligatorio faltante: idPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener las observaciones
            List<PycObserPet> observaciones = blPycges.getObserPeticion(idPeticion, numaOUsuario);

            // Determinar el código HTTP basado en los resultados
            if (observaciones == null || observaciones.isEmpty()) {
                log.info("No se encontraron observaciones para la petición {}", idPeticion);
                return new ResponseEntity<>(observaciones, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} observaciones para la petición {}", observaciones.size(), idPeticion);
            return new ResponseEntity<>(observaciones, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycResumenActi>> getResumenActividad(Integer idPeticion, Integer idCategoria) {
        log.info("Recibida solicitud para obtener resumen de actividades - petición: {}, categoría: {}", idPeticion, idCategoria);

        try {
            // Validar parámetros obligatorios
            if (idPeticion == null) {
                log.error("Parámetro obligatorio faltante: idPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idCategoria == null) {
                log.error("Parámetro obligatorio faltante: idCategoria es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener el resumen de actividades
            List<PycResumenActi> resumen = blPycges.getResumenActividad(idPeticion, idCategoria);

            // Determinar el código HTTP basado en los resultados
            if (resumen == null || resumen.isEmpty()) {
                log.info("No se encontró resumen de actividades para la petición {} y categoría {}", idPeticion, idCategoria);
                return new ResponseEntity<>(resumen, HttpStatus.NO_CONTENT);
            }

            log.info("Resumen de actividades obtenido exitosamente para petición {} y categoría {}", idPeticion, idCategoria);
            return new ResponseEntity<>(resumen, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycPetAvanceSig>> getPeticionAvanceSiguiente(Integer idProceso, Integer idArea, String estado, String codcia, Integer anio) {
        log.info("Recibida solicitud para obtener peticiones con avance siguiente. ID Proceso: {}, ID Área: {}, Estado: {}, Codcia: {}, Año: {}",
                idProceso, idArea, estado, codcia, anio);

        try {
            // Llamar al servicio de negocio para obtener las peticiones con avance siguiente
            List<PycPetAvanceSig> peticionesAvanceSiguiente = blPycges.getPeticionAvanceSiguiente(idProceso, idArea, estado, codcia, anio);

            // Determinar el código HTTP basado en los resultados
            if (peticionesAvanceSiguiente == null || peticionesAvanceSiguiente.isEmpty()) {
                log.info("No se encontraron peticiones con avance siguiente para los criterios especificados");
                return new ResponseEntity<>(peticionesAvanceSiguiente, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} peticiones con avance siguiente para los criterios especificados", peticionesAvanceSiguiente.size());
            return new ResponseEntity<>(peticionesAvanceSiguiente, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycListVal>> getListaVal(String tipo, Integer idProceso) {
        log.info("Recibida solicitud para obtener lista de valores. Tipo: {}, ID Proceso: {}", tipo, idProceso);

        try {
            // Validar parámetros obligatorios
            if (tipo == null || tipo.trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: tipo es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener la lista de valores
            List<PycListVal> listaValores = blPycges.getListaVal(tipo, idProceso);

            // Determinar el código HTTP basado en los resultados
            if (listaValores == null || listaValores.isEmpty()) {
                log.info("No se encontraron valores para el tipo '{}' y proceso '{}'", tipo, idProceso);
                return new ResponseEntity<>(listaValores, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} valores para el tipo '{}' y proceso '{}'", listaValores.size(), tipo, idProceso);
            return new ResponseEntity<>(listaValores, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycOficina>> getOficinas(Integer idProceso, Integer idUsuario) {
        log.info("Recibida solicitud para obtener oficinas. ID Proceso: {}, ID Usuario: {}", idProceso, idUsuario);

        try {
            // Validar parámetros obligatorios
            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idUsuario == null) {
                log.error("Parámetro obligatorio faltante: idUsuario es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener las oficinas
            List<PycOficina> oficinas = blPycges.getOficinas(idProceso, idUsuario);

            // Determinar el código HTTP basado en los resultados
            if (oficinas == null || oficinas.isEmpty()) {
                log.info("No se encontraron oficinas para el proceso '{}' y usuario '{}'", idProceso, idUsuario);
                return new ResponseEntity<>(oficinas, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} oficinas para el proceso '{}' y usuario '{}'", oficinas.size(), idProceso, idUsuario);
            return new ResponseEntity<>(oficinas, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycCanal>> getCanales(Integer idProceso, Integer idUsuario, Integer idOficina) {
        log.info("Recibida solicitud para obtener canales. ID Proceso: {}, ID Usuario: {}, ID Oficina: {}",
                idProceso, idUsuario, idOficina);

        try {
            // Validar parámetros obligatorios
            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idUsuario == null) {
                log.error("Parámetro obligatorio faltante: idUsuario es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idOficina == null) {
                log.error("Parámetro obligatorio faltante: idOficina es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los canales
            List<PycCanal> canales = blPycges.getCanales(idProceso, idUsuario, idOficina);

            // Determinar el código HTTP basado en los resultados
            if (canales == null || canales.isEmpty()) {
                log.info("No se encontraron canales para el proceso '{}', usuario '{}' y oficina '{}'",
                        idProceso, idUsuario, idOficina);
                return new ResponseEntity<>(canales, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} canales para el proceso '{}', usuario '{}' y oficina '{}'",
                    canales.size(), idProceso, idUsuario, idOficina);
            return new ResponseEntity<>(canales, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycSubordinados>> getSubordinados(String canal, String oficina, String idSupervisor, String indLogin, String proceso) {
        log.info("Recibida solicitud para obtener subordinados. Canal: {}, Oficina: {}, ID Supervisor: {}, Ind Login: {}, Proceso: {}",
                canal, oficina, idSupervisor, indLogin, proceso);

        try {
            // Validar parámetros obligatorios
            if (canal == null || canal.trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: canal es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (oficina == null || oficina.trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: oficina es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idSupervisor == null || idSupervisor.trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: idSupervisor es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (indLogin == null || indLogin.trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: indLogin es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (proceso == null || proceso.trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: proceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los subordinados
            List<PycSubordinados> subordinados = blPycges.getSubordinados(canal, oficina, idSupervisor, indLogin, proceso);

            // Determinar el código HTTP basado en los resultados
            if (subordinados == null || subordinados.isEmpty()) {
                log.info("No se encontraron subordinados para los parámetros especificados");
                return new ResponseEntity<>(subordinados, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} subordinados para los parámetros especificados", subordinados.size());
            return new ResponseEntity<>(subordinados, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycInfoUsuario>> getInfoUsuario(String usuario) {
        log.info("Recibida solicitud para obtener información del usuario. Usuario: {}", usuario);

        try {
            // Validar parámetro obligatorio
            if (usuario == null || usuario.trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: usuario es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener la información del usuario
            List<PycInfoUsuario> infoUsuario = blPycges.getInfoUsuario(usuario);

            // Determinar el código HTTP basado en los resultados
            if (infoUsuario == null || infoUsuario.isEmpty()) {
                log.info("No se encontró información para el usuario especificado");
                return new ResponseEntity<>(infoUsuario, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} registros de información para el usuario '{}'", infoUsuario.size(), usuario);
            return new ResponseEntity<>(infoUsuario, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycRamo>> obtenerRamo(String codRamo, String codCia, Integer codModalidad) {
        log.info("Recibida solicitud para obtener información del ramo. Código Ramo: {}, Código Modalidad: {}, Código Compañía: {}",
                codRamo, codModalidad, codCia);

        try {
            // Validar parámetros obligatorios
            if (codRamo == null || codRamo.trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: codRamo es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (codCia == null || codCia.trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: codCia es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // El parámetro codModalidad es opcional y tiene valor por defecto
            // Si no se proporciona, se usará el valor por defecto en la capa de datos
            // codModalidad ahora es Integer con valor por defecto 999

            // Llamar al servicio de negocio para obtener la información del ramo
            List<PycRamo> ramos = blPycges.obtenerRamo(codRamo, codModalidad, codCia);

            // Determinar el código HTTP basado en los resultados
            if (ramos == null || ramos.isEmpty()) {
                log.info("No se encontró información para el ramo especificado");
                return new ResponseEntity<>(ramos, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} registros de información para el ramo '{}'", ramos.size(), codRamo);
            return new ResponseEntity<>(ramos, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycDatoVarEquiv>> obtenerDatVarEquiv(String codRamo, String codCia, Integer codModalidad) {
        log.info("Recibida solicitud para obtener datos de variables equivalentes. Código Ramo: {}, Código Modalidad: {}, Código Compañía: {}",
                codRamo, codModalidad, codCia);

        try {
            // Validar parámetros obligatorios
            if (codRamo == null || codRamo.trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: codRamo es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (codCia == null || codCia.trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: codCia es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // El parámetro codModalidad es opcional y tiene valor por defecto
            // Si no se proporciona, se usará el valor por defecto en la capa de datos
            // codModalidad ahora es Integer con valor por defecto 999

            // Llamar al servicio de negocio para obtener los datos de variables equivalentes
            List<PycDatoVarEquiv> datosVarEquiv = blPycges.obtenerDatVarEquiv(codRamo, codModalidad, codCia);

            // Determinar el código HTTP basado en los resultados
            if (datosVarEquiv == null || datosVarEquiv.isEmpty()) {
                log.info("No se encontraron datos de variables equivalentes para el ramo especificado");
                return new ResponseEntity<>(datosVarEquiv, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} registros de datos de variables equivalentes para el ramo '{}'", datosVarEquiv.size(), codRamo);
            return new ResponseEntity<>(datosVarEquiv, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycPerfilUsu>> perfilesUsuario(Integer idUsuario, Integer idProceso) {
        log.info("Recibida solicitud para obtener perfiles de usuario. ID Usuario: {}, ID Proceso: {}",
                idUsuario, idProceso);

        try {
            // Validar parámetros obligatorios
            if (idUsuario == null) {
                log.error("Parámetro obligatorio faltante: idUsuario es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los perfiles del usuario
            List<PycPerfilUsu> perfilesUsuario = blPycges.perfilesUsuario(idUsuario, idProceso);

            // Determinar el código HTTP basado en los resultados
            if (perfilesUsuario == null || perfilesUsuario.isEmpty()) {
                log.info("No se encontraron perfiles para el usuario {} en el proceso {}", idUsuario, idProceso);
                return new ResponseEntity<>(perfilesUsuario, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} perfiles para el usuario {} en el proceso {}",
                    perfilesUsuario.size(), idUsuario, idProceso);
            return new ResponseEntity<>(perfilesUsuario, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycCategoriaPet>> getCategoriaPeticion(Integer idPeticion) {
        log.info("Recibida solicitud para obtener categorías de petición. ID Petición: {}", idPeticion);

        try {
            // Validar parámetro obligatorio
            if (idPeticion == null) {
                log.error("Parámetro obligatorio faltante: idPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener las categorías de la petición
            List<PycCategoriaPet> categoriasPeticion = blPycges.getCategoriaPeticion(idPeticion);

            // Determinar el código HTTP basado en los resultados
            if (categoriasPeticion == null || categoriasPeticion.isEmpty()) {
                log.info("No se encontraron categorías para la petición {}", idPeticion);
                return new ResponseEntity<>(categoriasPeticion, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} categorías para la petición {}", categoriasPeticion.size(), idPeticion);
            return new ResponseEntity<>(categoriasPeticion, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycPeticionTab>> getPeticionesTablero() {
        log.info("Recibida solicitud para obtener peticiones del tablero");

        try {
            // Llamar al servicio de negocio para obtener las peticiones del tablero
            List<PycPeticionTab> peticionesTablero = blPycges.getPeticionesTablero();

            // Determinar el código HTTP basado en los resultados
            if (peticionesTablero == null || peticionesTablero.isEmpty()) {
                log.info("No se encontraron peticiones para el tablero");
                return new ResponseEntity<>(peticionesTablero, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} peticiones para el tablero", peticionesTablero.size());
            return new ResponseEntity<>(peticionesTablero, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycOpcionUsu>> getOpcionUsuario(Integer idPerfil, Integer idProceso, String numaOUsuario) {
        log.info("Recibida solicitud para obtener opciones de usuario. ID Perfil: {}, ID Proceso: {}, Numa O Usuario: {}", idPerfil, idProceso, numaOUsuario);

        try {
            // Validar parámetros obligatorios
            if (idPerfil == null) {
                log.error("Parámetro obligatorio faltante: idPerfil es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (numaOUsuario == null || numaOUsuario.trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: numaOUsuario es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener las opciones del usuario
            List<PycOpcionUsu> opcionesUsuario = blPycges.getOpcionUsuario(idPerfil, idProceso, numaOUsuario);

            // Determinar el código HTTP basado en los resultados
            if (opcionesUsuario == null || opcionesUsuario.isEmpty()) {
                log.info("No se encontraron opciones para el perfil {} en el proceso {}", idPerfil, idProceso);
                return new ResponseEntity<>(opcionesUsuario, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} opciones para el perfil {} en el proceso {}", opcionesUsuario.size(), idPerfil, idProceso);
            return new ResponseEntity<>(opcionesUsuario, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycReportePetProg>> getReportePeticionProgramacion(Integer anio, Integer idProceso) {
        log.info("Recibida solicitud para obtener reporte de peticiones con programación. Año: {}, ID Proceso: {}", anio, idProceso);

        try {
            // Validar parámetros obligatorios
            if (anio == null) {
                log.error("Parámetro obligatorio faltante: anio es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener el reporte de peticiones con programación
            List<PycReportePetProg> reportePeticiones = blPycges.getReportePeticionProgramacion(anio, idProceso);

            // Determinar el código HTTP basado en los resultados
            if (reportePeticiones == null || reportePeticiones.isEmpty()) {
                log.info("No se encontraron peticiones para el año {} y proceso {}", anio, idProceso);
                return new ResponseEntity<>(reportePeticiones, HttpStatus.NO_CONTENT);
            }

            log.info("Reporte de peticiones con programación obtenido exitosamente. Total de registros: {} para año {} y proceso {}",
                    reportePeticiones.size(), anio, idProceso);
            return new ResponseEntity<>(reportePeticiones, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycEstadisticaEstado>> getEstadisticaEstados(Integer proceso, Integer area, Integer anio, String estado, String codCia) {
        log.info("Recibida solicitud para obtener estadísticas de estados. Proceso: {}, Área: {}, Año: {}, Estado: {}, CodCia: {}",
                proceso, area, anio, estado, codCia);

        try {
            // Validar parámetros obligatorios
            if (proceso == null) {
                log.error("Parámetro obligatorio faltante: proceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (area == null) {
                log.error("Parámetro obligatorio faltante: area es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Nota: estado puede ser null o vacío, en cuyo caso se pasa null a la función de BD

            if (anio == null) {
                log.error("Parámetro obligatorio faltante: anio es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener las estadísticas de estados
            List<PycEstadisticaEstado> estadisticasEstados = blPycges.getEstadisticaEstados(proceso, area, anio, estado, codCia);

            // Determinar el código HTTP basado en los resultados
            if (estadisticasEstados == null || estadisticasEstados.isEmpty()) {
                log.info("No se encontraron estadísticas para los parámetros especificados: proceso={}, area={}, anio={}, estado={}",
                        proceso, area, anio, estado);
                return new ResponseEntity<>(estadisticasEstados, HttpStatus.NO_CONTENT);
            }

            log.info("Estadísticas de estados obtenidas exitosamente. Total de registros: {} para proceso={}, area={}, anio={}, estado={}",
                    estadisticasEstados.size(), proceso, area, anio, estado);
            return new ResponseEntity<>(estadisticasEstados, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycEstadistEstadoSig>> getEstadisticaEstadosSiguientes(Integer proceso, Integer area, Integer anio, String estado, String codCia) {
        log.info("Recibida solicitud para obtener estadísticas de estados siguientes. Proceso: {}, Área: {}, Año: {}, Estado: {}, CodCia: {}",
                proceso, area, anio, estado, codCia);

        try {
            // Validar parámetros obligatorios
            if (proceso == null) {
                log.error("Parámetro obligatorio faltante: proceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (area == null) {
                log.error("Parámetro obligatorio faltante: area es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (anio == null) {
                log.error("Parámetro obligatorio faltante: anio es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Nota: estado y codCia pueden ser null o vacío, en cuyo caso se pasa null a la función de BD

            // Llamar al servicio de negocio para obtener las estadísticas de estados siguientes
            List<PycEstadistEstadoSig> estadisticasEstadosSiguientes = blPycges.getEstadisticaEstadosSiguientes(proceso, area, anio, estado, codCia);

            // Determinar el código HTTP basado en los resultados
            if (estadisticasEstadosSiguientes == null || estadisticasEstadosSiguientes.isEmpty()) {
                log.info("No se encontraron estadísticas de estados siguientes para los parámetros especificados: proceso={}, area={}, anio={}, estado={}",
                        proceso, area, anio, estado);
                return new ResponseEntity<>(estadisticasEstadosSiguientes, HttpStatus.NO_CONTENT);
            }

            log.info("Estadísticas de estados siguientes obtenidas exitosamente. Total de registros: {} para proceso={}, area={}, anio={}, estado={}",
                    estadisticasEstadosSiguientes.size(), proceso, area, anio, estado);
            return new ResponseEntity<>(estadisticasEstadosSiguientes, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycEstadoPeticionResponse> updateEstadoPeticion(PycUpdEstadoPet estadoData) {
        log.info("Recibida solicitud para actualizar estado de petición: {}", estadoData.getIdPeticion());

        try {
            // Validar datos de entrada
            if (estadoData.getIdPeticion() == null || estadoData.getIdPeticion().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: idPeticion es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (estadoData.getNuevoEstado() == null) {
                log.error("Datos de entrada inválidos: nuevoEstado es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (estadoData.getObservacion() == null || estadoData.getObservacion().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: observacion es obligatoria");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para actualizar el estado de la petición
            Integer resultado = blPycges.updateEstadoPeticion(estadoData);

            // Crear objeto de respuesta
            PycEstadoPeticionResponse response = new PycEstadoPeticionResponse();
            response.setResultado(resultado);

            log.info("Estado de petición actualizado correctamente. Resultado: {}", resultado);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycPathPerfilResponse> updatePathPerfil(PycUpdPathPerfil perfilData) {
        log.info("Recibida solicitud para actualizar path del perfil. ID Usuario: {}", perfilData.getIdUsuario());

        try {
            // Validar datos de entrada
            if (perfilData.getIdUsuario() == null) {
                log.error("Datos de entrada inválidos: idUsuario es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (perfilData.getUrlPerfil() == null || perfilData.getUrlPerfil().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: urlPerfil es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (perfilData.getPathPerfil() == null || perfilData.getPathPerfil().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: pathPerfil es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para actualizar el path del perfil
            Integer resultado = blPycges.updatePathPerfil(perfilData);

            // Crear objeto de respuesta
            PycPathPerfilResponse response = new PycPathPerfilResponse();
            response.setResultado(resultado);

            log.info("Path del perfil actualizado correctamente. Resultado: {}", resultado);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycSesionResponse> updateSesion(PycUpdSesion sesionData) {
        log.info("Recibida solicitud para actualizar sesión. ID Usuario: {}, Estado: {}",
                sesionData.getIdUsuario(), sesionData.getEstado());

        try {
            // Validar datos de entrada
            if (sesionData.getIdUsuario() == null) {
                log.error("Datos de entrada inválidos: idUsuario es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (sesionData.getEstado() == null) {
                log.error("Datos de entrada inválidos: estado es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Validar que el estado sea 0 o 1
            if (sesionData.getEstado() != 0 && sesionData.getEstado() != 1) {
                log.error("Datos de entrada inválidos: estado debe ser 0 (logout) o 1 (login)");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para actualizar la sesión
            Integer resultado = blPycges.updateSesion(sesionData);

            // Crear objeto de respuesta
            PycSesionResponse response = new PycSesionResponse();
            response.setResultado(resultado);

            log.info("Sesión actualizada correctamente. Resultado: {}", resultado);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycAnalistaActi>> getAnalistaActividad(Integer peticion, Integer actividad) {
        log.info("Recibida solicitud para obtener analistas por actividad. Petición: {}, Actividad: {}", peticion, actividad);

        try {
            // Validar parámetros obligatorios
            if (peticion == null || actividad == null) {
                log.error("Parámetros obligatorios faltantes: peticion y actividad son requeridos");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los analistas
            List<PycAnalistaActi> analistas = blPycges.getAnalistaActividad(peticion, actividad);

            log.info("Analistas obtenidos correctamente. Total: {}", analistas.size());
            return new ResponseEntity<>(analistas, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al obtener los analistas por actividad: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycInsertObs> insertObservacion(PycInsertObservacion observacionData) {
        log.info("Recibida solicitud para insertar observación en petición: {}", observacionData.getIdPeticion());

        try {
            // Validar parámetros obligatorios
            if (observacionData.getIdPeticion() == null ||
                observacionData.getObservacion() == null || observacionData.getObservacion().trim().isEmpty() ||
                observacionData.getUsuarioPet() == null ||
                observacionData.getEstado() == null || observacionData.getEstado().trim().isEmpty()) {
                log.error("Parámetros obligatorios faltantes: idPeticion, observacion, usuarioPet y estado son requeridos");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para insertar la observación
            Integer idObservacion = blPycges.insertObservacion(observacionData);

            // Crear objeto de respuesta
            PycInsertObs response = new PycInsertObs();
            response.setIdObservacion(idObservacion);
            response.setMensaje("Observación insertada exitosamente");
            response.setEjecutado(true);

            log.info("Observación insertada correctamente. ID Observación: {}", idObservacion);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la observación: {}", e.getMessage(), e);

            // Crear respuesta de error
            PycInsertObs response = new PycInsertObs();
            response.setIdObservacion(null);
            response.setMensaje("Error al insertar la observación: " + e.getMessage());
            response.setEjecutado(false);

            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycBitacoraResponse> bitacoraAccion(PycBitacoraAccion bitacoraData) {
        log.info("Recibida solicitud para registrar acción en bitácora. ID Proceso: {}, Acción: {}",
                bitacoraData.getIdProceso(), bitacoraData.getAccion());

        try {
            // Validar datos de entrada
            if (bitacoraData.getIdProceso() == null) {
                log.error("Datos de entrada inválidos: idProceso es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (bitacoraData.getAccion() == null || bitacoraData.getAccion().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: accion es obligatoria");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (bitacoraData.getDescripcion() == null || bitacoraData.getDescripcion().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: descripcion es obligatoria");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (bitacoraData.getEstado() == null || bitacoraData.getEstado().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: estado es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (bitacoraData.getRefEntidad() == null || bitacoraData.getRefEntidad().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: refEntidad es obligatoria");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (bitacoraData.getRefEntidadId() == null || bitacoraData.getRefEntidadId().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: refEntidadId es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para registrar en bitácora
            boolean resultado = blPycges.bitacoraAccion(bitacoraData);

            // Crear objeto de respuesta
            PycBitacoraResponse response = new PycBitacoraResponse();
            response.setEjecutado(resultado);

            if (resultado) {
                response.setMensaje("Acción registrada exitosamente en la bitácora");
                log.info("Acción registrada exitosamente en bitácora");
            } else {
                response.setMensaje("Error al registrar la acción en la bitácora");
                log.warn("Error al registrar la acción en bitácora");
            }

            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);

            // Crear respuesta de error
            PycBitacoraResponse response = new PycBitacoraResponse();
            response.setEjecutado(false);
            response.setMensaje("Error interno al procesar la solicitud");

            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycGestUsrAsigAutoResponse> gestionUsrAsignacionAuto(PycGestUsrAsigAuto asignacionData) {
        log.info("Recibida solicitud para gestionar asignación automática de usuario. ID Proceso: {}, ID Perfil: {}, ID Aplicación: {}, ID Estado: {}, Estado: {}, ID Usuario: {}",
                asignacionData.getIdProceso(), asignacionData.getIdPerfil(), asignacionData.getIdAplicacion(),
                asignacionData.getIdEstado(), asignacionData.getEstado(), asignacionData.getIdUsuario());

        try {
            // Validar parámetros obligatorios
            if (asignacionData.getIdProceso() == null ||
                asignacionData.getIdPerfil() == null ||
                asignacionData.getIdAplicacion() == null ||
                asignacionData.getIdEstado() == null ||
                asignacionData.getEstado() == null || asignacionData.getEstado().trim().isEmpty() ||
                asignacionData.getIdUsuario() == null) {
                log.error("Parámetros obligatorios faltantes: idProceso, idPerfil, idAplicacion, idEstado, estado e idUsuario son requeridos");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para gestionar la asignación automática
            boolean resultado = blPycges.gestionUsrAsignacionAuto(asignacionData);

            // Crear objeto de respuesta
            PycGestUsrAsigAutoResponse response = new PycGestUsrAsigAutoResponse();
            response.setEjecutado(resultado);

            if (resultado) {
                response.setMensaje("Asignación automática de usuario gestionada exitosamente");
                log.info("Asignación automática gestionada exitosamente");
            } else {
                response.setMensaje("Error al gestionar la asignación automática de usuario");
                log.warn("Error al gestionar la asignación automática");
            }

            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la gestión de asignación automática: {}", e.getMessage(), e);

            // Crear respuesta de error
            PycGestUsrAsigAutoResponse response = new PycGestUsrAsigAutoResponse();
            response.setEjecutado(false);
            response.setMensaje("Error al gestionar la asignación automática de usuario: " + e.getMessage());

            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycGetDatVarFormSec>> getDatVarFormSecc(Integer idProceso, Integer idFormulario, Integer idSeccion, Integer idPeticion) {
        log.info("Recibida solicitud para obtener datos variables de formulario por sección. ID Proceso: {}, ID Formulario: {}, ID Sección: {}, ID Petición: {}",
                idProceso, idFormulario, idSeccion, idPeticion);

        try {
            // Validar parámetros obligatorios
            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idFormulario == null) {
                log.error("Parámetro obligatorio faltante: idFormulario es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idSeccion == null) {
                log.error("Parámetro obligatorio faltante: idSeccion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // idPeticion es opcional, no requiere validación

            // Llamar al servicio de negocio para obtener los datos variables del formulario
            List<PycGetDatVarFormSec> datosVariables = blPycges.getDatVarFormSecc(idProceso, idFormulario, idSeccion, idPeticion);

            // Determinar el código HTTP basado en los resultados
            if (datosVariables == null || datosVariables.isEmpty()) {
                log.info("No se encontraron datos variables para el proceso {}, formulario {}, sección {} y petición {}",
                        idProceso, idFormulario, idSeccion, idPeticion);
                return new ResponseEntity<>(datosVariables, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} datos variables para el proceso {}, formulario {}, sección {} y petición {}",
                    datosVariables.size(), idProceso, idFormulario, idSeccion, idPeticion);
            return new ResponseEntity<>(datosVariables, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycGetBitacoraPet>> getBitacoraPeticion(Integer idPeticion) {
        log.info("Recibida solicitud para obtener bitácora de petición. ID Petición: {}", idPeticion);

        try {
            // Validar parámetro obligatorio
            if (idPeticion == null) {
                log.error("Parámetro obligatorio faltante: idPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener la bitácora de la petición
            List<PycGetBitacoraPet> bitacoraPeticion = blPycges.getBitacoraPeticion(idPeticion);

            // Determinar el código HTTP basado en los resultados
            if (bitacoraPeticion == null || bitacoraPeticion.isEmpty()) {
                log.info("No se encontraron registros de bitácora para la petición {}", idPeticion);
                return new ResponseEntity<>(bitacoraPeticion, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} registros de bitácora para la petición {}", bitacoraPeticion.size(), idPeticion);
            return new ResponseEntity<>(bitacoraPeticion, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycTipoPeticion>> getTipoPeticion(Integer idProceso) {
        log.info("Recibida solicitud para obtener tipos de petición. ID Proceso: {}", idProceso);

        try {
            // Validar parámetro obligatorio
            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los tipos de petición
            List<PycTipoPeticion> tiposPeticion = blPycges.getTipoPeticion(idProceso);

            // Determinar el código HTTP basado en los resultados
            if (tiposPeticion == null || tiposPeticion.isEmpty()) {
                log.info("No se encontraron tipos de petición para el proceso {}", idProceso);
                return new ResponseEntity<>(tiposPeticion, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} tipos de petición para el proceso {}", tiposPeticion.size(), idProceso);
            return new ResponseEntity<>(tiposPeticion, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycEstadoTransc>> getCambioEstado(Integer idPerfil, Integer idProceso, Integer idEstado) {
        log.info("Recibida solicitud para obtener estados de transición. ID Perfil: {}, ID Proceso: {}, ID Estado: {}", idPerfil, idProceso, idEstado);

        try {
            // Validar parámetros obligatorios
            if (idPerfil == null) {
                log.error("Parámetro obligatorio faltante: idPerfil es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idEstado == null) {
                log.error("Parámetro obligatorio faltante: idEstado es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los estados de transición
            List<PycEstadoTransc> estadosTransicion = blPycges.getCambioEstado(idPerfil, idProceso, idEstado);

            // Determinar el código HTTP basado en los resultados
            if (estadosTransicion == null || estadosTransicion.isEmpty()) {
                log.info("No se encontraron estados de transición para perfil {}, proceso {} y estado {}", idPerfil, idProceso, idEstado);
                return new ResponseEntity<>(estadosTransicion, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} estados de transición para perfil {}, proceso {} y estado {}", estadosTransicion.size(), idPerfil, idProceso, idEstado);
            return new ResponseEntity<>(estadosTransicion, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycDocPeticion>> getDocumentoPeticion(Integer idPeticion, Integer perfilAct) {
        log.info("Recibida solicitud para obtener documentos de petición. ID Petición: {}, Perfil Actual: {}", idPeticion, perfilAct);

        try {
            // Validar parámetro obligatorio
            if (idPeticion == null) {
                log.error("Parámetro obligatorio faltante: idPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los documentos de la petición
            List<PycDocPeticion> documentosPeticion = blPycges.getDocumentoPeticion(idPeticion, perfilAct);

            // Determinar el código HTTP basado en los resultados
            if (documentosPeticion == null || documentosPeticion.isEmpty()) {
                log.info("No se encontraron documentos para la petición {}", idPeticion);
                return new ResponseEntity<>(documentosPeticion, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} documentos para la petición {}", documentosPeticion.size(), idPeticion);
            return new ResponseEntity<>(documentosPeticion, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycDocXPeticion>> getDocXPeticion(Integer idTipo) {
        log.info("Recibida solicitud para obtener documentos por tipo de petición. ID Tipo: {}", idTipo);

        try {
            // Validar parámetro obligatorio
            if (idTipo == null) {
                log.error("Parámetro obligatorio faltante: idTipo es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los documentos por tipo de petición
            List<PycDocXPeticion> documentosXPeticion = blPycges.getDocXPeticion(idTipo);

            // Determinar el código HTTP basado en los resultados
            if (documentosXPeticion == null || documentosXPeticion.isEmpty()) {
                log.info("No se encontraron documentos para el tipo de petición {}", idTipo);
                return new ResponseEntity<>(documentosXPeticion, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} documentos para el tipo de petición {}", documentosXPeticion.size(), idTipo);
            return new ResponseEntity<>(documentosXPeticion, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycDatVarForSeccTippe>> getDatVarFormSeccTippe(Integer idProceso, Integer idTipo) {
        log.info("Recibida solicitud para obtener datos variables de formulario por tipo. ID Proceso: {}, ID Tipo: {}", idProceso, idTipo);

        try {
            // Validar parámetros obligatorios
            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idTipo == null) {
                log.error("Parámetro obligatorio faltante: idTipo es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los datos variables del formulario por tipo
            List<PycDatVarForSeccTippe> datosVariablesTippe = blPycges.getDatVarFormSeccTippe(idProceso, idTipo);

            // Determinar el código HTTP basado en los resultados
            if (datosVariablesTippe == null || datosVariablesTippe.isEmpty()) {
                log.info("No se encontraron datos variables para el proceso {} y tipo {}", idProceso, idTipo);
                return new ResponseEntity<>(datosVariablesTippe, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} datos variables para el proceso {} y tipo {}", datosVariablesTippe.size(), idProceso, idTipo);
            return new ResponseEntity<>(datosVariablesTippe, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycDatVarPetResponse> newDatVarPeticion(PycNewDatVarPet datVarData) {
        log.info("Recibida solicitud para crear/actualizar dato variable de petición. ID Petición: {}, ID Formulario: {}, ID Sección: {}, ID Dato Variable: {}",
                datVarData.getIdPeticion(), datVarData.getIdFormulario(), datVarData.getIdSeccion(), datVarData.getIdDatoVar());

        try {
            // Validar parámetros obligatorios
            if (datVarData.getIdPeticion() == null) {
                log.error("Parámetro obligatorio faltante: idPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (datVarData.getIdFormulario() == null) {
                log.error("Parámetro obligatorio faltante: idFormulario es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (datVarData.getIdSeccion() == null) {
                log.error("Parámetro obligatorio faltante: idSeccion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (datVarData.getIdDatoVar() == null) {
                log.error("Parámetro obligatorio faltante: idDatoVar es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (datVarData.getValor() == null || datVarData.getValor().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: valor es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para crear/actualizar el dato variable
            Integer idRegistro = blPycges.newDatVarPeticion(datVarData);

            // Crear objeto de respuesta
            PycDatVarPetResponse response = new PycDatVarPetResponse();
            response.setIdRegistro(idRegistro);
            response.setMensaje("Dato variable de petición guardado exitosamente");
            response.setEjecutado(true);

            log.info("Dato variable de petición guardado correctamente. ID Registro: {}", idRegistro);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);

            // Crear respuesta de error
            PycDatVarPetResponse response = new PycDatVarPetResponse();
            response.setIdRegistro(null);
            response.setMensaje("Error al guardar el dato variable de petición: " + e.getMessage());
            response.setEjecutado(false);

            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycGetPerfiles>> getPerfilesPorProceso(Integer idProceso) {
        log.info("Recibida solicitud para obtener perfiles por proceso. ID Proceso: {}", idProceso);

        try {
            // Validar parámetro obligatorio
            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los perfiles por proceso
            List<PycGetPerfiles> perfilesProceso = blPycges.getPerfilesPorProceso(idProceso);

            // Determinar el código HTTP basado en los resultados
            if (perfilesProceso == null || perfilesProceso.isEmpty()) {
                log.info("No se encontraron perfiles para el proceso {}", idProceso);
                return new ResponseEntity<>(perfilesProceso, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} perfiles para el proceso {}", perfilesProceso.size(), idProceso);
            return new ResponseEntity<>(perfilesProceso, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycGetEstadoPerfilCod>> getEstadoPerfilCod(Integer idProceso, Integer idPerfil) {
        log.info("Recibida solicitud para obtener estados por perfil. ID Proceso: {}, ID Perfil: {}", idProceso, idPerfil);

        try {
            // Validar parámetros obligatorios
            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (idPerfil == null) {
                log.error("Parámetro obligatorio faltante: idPerfil es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los estados por perfil
            List<PycGetEstadoPerfilCod> estadosPerfil = blPycges.getEstadoPerfilCod(idProceso, idPerfil);

            // Determinar el código HTTP basado en los resultados
            if (estadosPerfil == null || estadosPerfil.isEmpty()) {
                log.info("No se encontraron estados para el proceso {} y perfil {}", idProceso, idPerfil);
                return new ResponseEntity<>(estadosPerfil, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} estados para el proceso {} y perfil {}", estadosPerfil.size(), idProceso, idPerfil);
            return new ResponseEntity<>(estadosPerfil, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycAsignaPetDesResponse> asignaPeticionDesa(PycAsignaPeticionDesa asignacionData) {
        log.info("Recibida solicitud para asignar petición a desarrollador. ID Aplicación: {}, ID Desarrollador: {}, ID Petición: {}, ID Perfil: {}, numaOUsuario: {}",
                asignacionData.getIdAplicacion(), asignacionData.getIdDesarrollador(),
                asignacionData.getIdPeticion(), asignacionData.getIdPerfil(), asignacionData.getNumaOUsuario());

        try {
            // Validar parámetros obligatorios
            if (asignacionData.getIdAplicacion() == null) {
                log.error("Parámetro obligatorio faltante: idAplicacion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (asignacionData.getIdDesarrollador() == null) {
                log.error("Parámetro obligatorio faltante: idDesarrollador es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (asignacionData.getIdPeticion() == null) {
                log.error("Parámetro obligatorio faltante: idPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (asignacionData.getIdPerfil() == null) {
                log.error("Parámetro obligatorio faltante: idPerfil es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para asignar la petición al desarrollador
            Integer idPeticion = blPycges.asignaPeticionDesa(asignacionData);

            // Crear objeto de respuesta
            PycAsignaPetDesResponse response = new PycAsignaPetDesResponse();
            response.setIdPeticion(idPeticion);
            response.setMensaje("Petición asignada exitosamente al desarrollador");
            response.setEjecutado(true);

            log.info("Petición asignada correctamente. ID Petición: {}", idPeticion);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);

            // Crear respuesta de error
            PycAsignaPetDesResponse response = new PycAsignaPetDesResponse();
            response.setIdPeticion(null);
            response.setMensaje("Error al asignar la petición al desarrollador: " + e.getMessage());
            response.setEjecutado(false);

            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycRespPeti>> getResponsablePeticion(Integer idPeticion, String numaOUsuario) {
        log.info("Recibida solicitud para obtener responsables de petición. ID Petición: {}, Usuario: {}",
                idPeticion, numaOUsuario);

        try {
            // Validar parámetros obligatorios
            if (idPeticion == null) {
                log.error("Parámetro obligatorio faltante: idPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los responsables
            List<PycRespPeti> responsables = blPycges.getResponsablePeticion(idPeticion, numaOUsuario);

            log.info("Responsables obtenidos correctamente. ID Petición: {}, Usuario: {}, Cantidad: {}",
                    idPeticion, numaOUsuario, responsables.size());
            return new ResponseEntity<>(responsables, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud de responsables: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycActPeticion>> getActPeticion(Integer idPeticion, Integer idActividad) {
        log.info("Recibida solicitud para obtener actividades de petición. ID Petición: {}, ID Actividad: {}", idPeticion, idActividad);

        try {
            // Validar parámetro obligatorio
            if (idPeticion == null) {
                log.error("Parámetro obligatorio faltante: idPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener las actividades de la petición
            List<PycActPeticion> actividadesPeticion = blPycges.getActPeticion(idPeticion, idActividad);

            // Determinar el código HTTP basado en los resultados
            if (actividadesPeticion == null || actividadesPeticion.isEmpty()) {
                log.info("No se encontraron actividades para la petición {} y actividad {}", idPeticion, idActividad);
                return new ResponseEntity<>(actividadesPeticion, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} actividades para la petición {} y actividad {}", actividadesPeticion.size(), idPeticion, idActividad);
            return new ResponseEntity<>(actividadesPeticion, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycUpdPeticionResponse> updatePeticion(PycUpdPeticion peticionData) {
        log.info("Recibida solicitud para actualizar petición. ID Petición: {}, Nombre: {}, ID Usuario Solicitante: {}, numaOUsuario: {}",
                peticionData.getIdPeticion(), peticionData.getNomPeticion(),
                peticionData.getIdSolUser(), peticionData.getNumaOUsuario());

        try {
            // Validar parámetros obligatorios
            if (peticionData.getIdPeticion() == null) {
                log.error("Parámetro obligatorio faltante: idPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (peticionData.getIdSolUser() == null) {
                log.error("Parámetro obligatorio faltante: idSolUser es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (peticionData.getNomPeticion() == null || peticionData.getNomPeticion().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: nomPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (peticionData.getDescPeticion() == null || peticionData.getDescPeticion().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: descPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (peticionData.getIdTipo() == null) {
                log.error("Parámetro obligatorio faltante: idTipo es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (peticionData.getObservacion() == null || peticionData.getObservacion().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: observacion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (peticionData.getCodCia() == null || peticionData.getCodCia().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: codCia es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para actualizar la petición
            Integer idPeticion = blPycges.updatePeticion(peticionData);

            // Crear la respuesta
            PycUpdPeticionResponse response = new PycUpdPeticionResponse();
            response.setIdPeticion(idPeticion);
            response.setMensaje("Petición actualizada exitosamente");
            response.setEjecutado(true);

            log.info("Petición actualizada exitosamente. ID Petición: {}", idPeticion);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud de actualización de petición: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycUpdPeticionResponse> actualizarPeticion(PycUpdPeticion peticionData) {
        log.info("Recibida solicitud para actualizar petición (POST). ID Petición: {}, Nombre: {}, ID Usuario Solicitante: {}, numaOUsuario: {}",
                peticionData.getIdPeticion(), peticionData.getNomPeticion(),
                peticionData.getIdSolUser(), peticionData.getNumaOUsuario());

        try {
            // Validar parámetros obligatorios
            if (peticionData.getIdPeticion() == null) {
                log.error("Parámetro obligatorio faltante: idPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (peticionData.getIdSolUser() == null) {
                log.error("Parámetro obligatorio faltante: idSolUser es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (peticionData.getNomPeticion() == null || peticionData.getNomPeticion().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: nomPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (peticionData.getDescPeticion() == null || peticionData.getDescPeticion().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: descPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (peticionData.getIdTipo() == null) {
                log.error("Parámetro obligatorio faltante: idTipo es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (peticionData.getObservacion() == null || peticionData.getObservacion().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: observacion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (peticionData.getCodCia() == null || peticionData.getCodCia().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: codCia es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para actualizar la petición
            Integer idPeticion = blPycges.actualizarPeticion(peticionData);

            // Crear la respuesta
            PycUpdPeticionResponse response = new PycUpdPeticionResponse();
            response.setIdPeticion(idPeticion);
            response.setMensaje("Petición actualizada exitosamente");
            response.setEjecutado(true);

            log.info("Petición actualizada exitosamente (POST). ID Petición: {}", idPeticion);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud de actualización (POST): {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // @Override - Comentado temporalmente hasta regenerar la interfaz API
    public ResponseEntity<PycCreaSoliRHResponse> creaSolicitudRH(PycCreaSoliRH solicitudData) {
        log.info("Recibida solicitud para crear solicitud RH. Cliente: {}, Teléfono: {}, Email: {}, DPI: {}",
                solicitudData.getNomCli(), solicitudData.getTelCli(),
                solicitudData.getEmailCli(), solicitudData.getDpi());

        try {
            // Validar parámetros obligatorios
            if (solicitudData.getNomCli() == null || solicitudData.getNomCli().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: nomCli es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getTelCli() == null || solicitudData.getTelCli().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: telCli es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getEmailCli() == null || solicitudData.getEmailCli().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: emailCli es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getFecNac() == null || solicitudData.getFecNac().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: fecNac es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getDpi() == null || solicitudData.getDpi().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: dpi es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getNit() == null || solicitudData.getNit().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: nit es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getDescEsc() == null || solicitudData.getDescEsc().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: descEsc es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getCodEsc() == null || solicitudData.getCodEsc().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: codEsc es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getTitulo() == null || solicitudData.getTitulo().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: titulo es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getDireccion() == null || solicitudData.getDireccion().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: direccion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getCodDpto() == null || solicitudData.getCodDpto().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: codDpto es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getDescDpto() == null || solicitudData.getDescDpto().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: descDpto es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getCodTrb() == null || solicitudData.getCodTrb().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: codTrb es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getDescTrb() == null || solicitudData.getDescTrb().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: descTrb es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getCodSeg() == null || solicitudData.getCodSeg().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: codSeg es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getDescSeg() == null || solicitudData.getDescSeg().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: descSeg es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getPreten() == null || solicitudData.getPreten().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: preten es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getCodPlaza() == null || solicitudData.getCodPlaza().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: codPlaza es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (solicitudData.getDescPlaz() == null || solicitudData.getDescPlaz().trim().isEmpty()) {
                log.error("Parámetro obligatorio faltante: descPlaz es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para crear la solicitud RH
            Integer idSolicitud = blPycges.creaSolicitudRH(solicitudData);

            // Crear objeto de respuesta
            PycCreaSoliRHResponse response = new PycCreaSoliRHResponse();
            response.setIdSolicitud(idSolicitud);
            response.setMensaje("Solicitud de Recursos Humanos creada exitosamente");
            response.setEjecutado(true);

            log.info("Solicitud RH creada correctamente. ID Solicitud: {}", idSolicitud);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud de creación RH: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycDocNoObli>> getDocNoObligatorios(Integer idPeticion) {
        log.info("Iniciando obtención de documentos no obligatorios para la petición {}", idPeticion);

        try {
            // Validar parámetro obligatorio
            if (idPeticion == null) {
                log.error("Parámetro obligatorio faltante: idPeticion es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener los documentos no obligatorios
            List<PycDocNoObli> documentosNoObligatorios = blPycges.getDocNoObligatorios(idPeticion);

            // Determinar el código HTTP basado en los resultados
            if (documentosNoObligatorios == null || documentosNoObligatorios.isEmpty()) {
                log.info("No se encontraron documentos no obligatorios para la petición {}", idPeticion);
                return new ResponseEntity<>(documentosNoObligatorios, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} documentos no obligatorios para la petición {}", documentosNoObligatorios.size(), idPeticion);
            return new ResponseEntity<>(documentosNoObligatorios, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<List<PycCatAct>> getCategoriaAct(Integer idProceso) {
        log.info("Recibida solicitud para obtener categorías activas del tablero. ID Proceso: {}", idProceso);

        try {
            // Validar parámetro obligatorio
            if (idProceso == null) {
                log.error("Parámetro obligatorio faltante: idProceso es requerido");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para obtener las categorías activas
            List<PycCatAct> categoriasActivas = blPycges.getCategoriaAct(idProceso);

            // Determinar el código HTTP basado en los resultados
            if (categoriasActivas == null || categoriasActivas.isEmpty()) {
                log.info("No se encontraron categorías activas para el proceso {}", idProceso);
                return new ResponseEntity<>(categoriasActivas, HttpStatus.NO_CONTENT);
            }

            log.info("Se encontraron {} categorías activas para el proceso {}", categoriasActivas.size(), idProceso);
            return new ResponseEntity<>(categoriasActivas, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycInserActResponse> insertActividad(PycInserAct actividadData) {
        log.info("Recibida solicitud para insertar actividad: {}", actividadData.getNombAct());

        try {
            // Validar datos de entrada
            if (actividadData.getIdPeti() == null) {
                log.error("Datos de entrada inválidos: idPeti es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (actividadData.getNombAct() == null || actividadData.getNombAct().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: nombAct es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (actividadData.getDescAct() == null || actividadData.getDescAct().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: descAct es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (actividadData.getUserPet() == null) {
                log.error("Datos de entrada inválidos: userPet es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para insertar la actividad
            Integer idActividad = blPycges.insertActividad(actividadData);

            // Crear objeto de respuesta
            PycInserActResponse response = new PycInserActResponse();
            response.setIdActividad(idActividad);
            response.setMensaje("Actividad creada exitosamente");
            response.setEjecutado(true);

            log.info("Actividad insertada correctamente. ID Actividad: {}", idActividad);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycEditActResponse> editActividad(PycEditAct actividadData) {
        log.info("Recibida solicitud para editar actividad: {}", actividadData.getNomActividad());

        try {
            // Validar datos de entrada
            if (actividadData.getIdPeticion() == null) {
                log.error("Datos de entrada inválidos: idPeticion es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (actividadData.getIdActividad() == null) {
                log.error("Datos de entrada inválidos: idActividad es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (actividadData.getNomActividad() == null || actividadData.getNomActividad().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: nomActividad es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (actividadData.getDescActividad() == null || actividadData.getDescActividad().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: descActividad es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para editar la actividad
            Integer idPeticion = blPycges.editActividad(actividadData);

            // Crear objeto de respuesta
            PycEditActResponse response = new PycEditActResponse();
            response.setIdPeticion(idPeticion);
            response.setMensaje("Actividad actualizada exitosamente");
            response.setEjecutado(true);

            log.info("Actividad editada correctamente. ID Petición: {}", idPeticion);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycUpdActResponse> updateActividad(PycUpdAct actividadData) {
        log.info("Recibida solicitud para actualizar actividad. ID Petición: {}, ID Actividad: {}, Horas Reales: {}",
                actividadData.getIdPeticion(), actividadData.getIdActividad(), actividadData.getHrsReal());

        try {
            // Validar datos de entrada
            if (actividadData.getIdPeticion() == null) {
                log.error("Datos de entrada inválidos: idPeticion es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (actividadData.getIdActividad() == null) {
                log.error("Datos de entrada inválidos: idActividad es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (actividadData.getHrsReal() == null || actividadData.getHrsReal().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: hrsReal es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (actividadData.getObservacion() == null || actividadData.getObservacion().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: observacion es obligatoria");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (actividadData.getInd() == null) {
                log.error("Datos de entrada inválidos: ind es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para actualizar la actividad
            Integer idPeticion = blPycges.updateActividad(actividadData);

            // Crear objeto de respuesta
            PycUpdActResponse response = new PycUpdActResponse();
            response.setIdPeticion(idPeticion);
            response.setMensaje("Actividad actualizada exitosamente y observación registrada");
            response.setEjecutado(true);

            log.info("Actividad actualizada correctamente. ID Petición: {}", idPeticion);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<PycUpdEncResponse> updateEncuesta(PycUpdEnc encuestaData) {
        log.info("Recibida solicitud para actualizar encuesta de satisfacción. ID Petición: {}, Calificación: {}",
                encuestaData.getIdPeticion(), encuestaData.getEncuCalifi());

        try {
            // Validar datos de entrada
            if (encuestaData.getIdPeticion() == null || encuestaData.getIdPeticion().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: idPeticion es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (encuestaData.getEncuCalifi() == null || encuestaData.getEncuCalifi().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: encuCalifi es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            if (encuestaData.getEncuComent() == null || encuestaData.getEncuComent().trim().isEmpty()) {
                log.error("Datos de entrada inválidos: encuComent es obligatorio");
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }

            // Llamar al servicio de negocio para actualizar la encuesta
            String idPeticion = blPycges.updateEncuesta(encuestaData);

            // Crear objeto de respuesta
            PycUpdEncResponse response = new PycUpdEncResponse();
            response.setIdPeticion(idPeticion);
            response.setMensaje("Encuesta de satisfacción actualizada exitosamente");
            response.setEjecutado(true);

            log.info("Encuesta actualizada correctamente. ID Petición: {}", idPeticion);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al procesar la solicitud: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
