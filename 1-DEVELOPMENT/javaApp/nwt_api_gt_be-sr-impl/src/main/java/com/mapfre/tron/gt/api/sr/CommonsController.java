package com.mapfre.tron.gt.api.sr;

import com.mapfre.tron.gt.api.bl.IBlCommons;
import com.mapfre.tron.gt.api.model.Message;
import com.mapfre.tron.gt.api.model.CargaArchivoRequest;
import com.mapfre.tron.gt.api.model.CargaArchivoResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

@RestController
@Slf4j
@Api(tags = {"Commons"})
public class CommonsController implements CommonsApi{

    @Autowired
    private IBlCommons _iblCommons;

    @Override
    public ResponseEntity<Message> actualizacionDeTasaDeCambio() {
        log.info("The actualizacionDeTasaDeCambio rest operation had been called!");

        Message response = _iblCommons.actualizacionDeTasaDeCambio();

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<CargaArchivoResponse> cargaDeArchivos(CargaArchivoRequest request) {
        log.info("The cargaDeArchivos rest operation had been called!");

        CargaArchivoResponse response = _iblCommons.cargarArchivos(request);

        return new ResponseEntity<>(response, HttpStatus.OK);
    }


}
