package com.mapfre.tron.gt.api.sr;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mapfre.tron.gt.api.bl.IBlFirmaElectronica;
import com.mapfre.tron.gt.api.model.PLE_MedioPagoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_DepositoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_IdentificadorMedioPago;
import com.mapfre.tron.gt.api.model.PLE_DetallePlanilla;
import com.mapfre.tron.gt.api.model.PLE_CuentaBanco;
import com.mapfre.tron.gt.api.model.PLE_TotalPagoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_MedioPago;
import com.mapfre.tron.gt.api.model.PLE_TipoPlanilla;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * Controlador REST para operaciones de firma electrónica.
 */
@RestController
@Slf4j
@Api(tags = {"Firma Electronica"})
public class FirmaElectronicaController implements FirmaElectronicaApi {

    @Autowired
    private IBlFirmaElectronica blFirmaElectronica;

    @Override
    public ResponseEntity<List<PLE_MedioPagoPlanilla>> buscarMediosPagoPlanilla(
            @NotNull @ApiParam(value = "ID de la planilla", required = true) @Valid @RequestParam(value = "idPlanilla", required = true) Integer idPlanilla) {
        log.info("The buscarMediosPagoPlanilla rest operation had been called!");
        
        List<PLE_MedioPagoPlanilla> response = blFirmaElectronica.buscarMediosPagoPlanilla(idPlanilla);
    
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<List<PLE_DepositoPlanilla>> buscarDepositosPlanilla(
            @NotNull @ApiParam(value = "ID de la planilla", required = true) @Valid @RequestParam(value = "idPlanilla", required = true) Integer idPlanilla) {
        log.info("The buscarDepositosPlanilla rest operation had been called!");
        
        List<PLE_DepositoPlanilla> response = blFirmaElectronica.buscarDepositosPlanilla(idPlanilla);
    
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<List<PLE_IdentificadorMedioPago>> buscarIdentificadorMedioPago(
            @NotNull @ApiParam(value = "Sistema (A/T)", required = true) @Valid @RequestParam(value = "sistema", required = true) String sistema,
            @NotNull @ApiParam(value = "Código de moneda", required = true) @Valid @RequestParam(value = "moneda", required = true) String moneda,
            @NotNull @ApiParam(value = "Medio de pago", required = true) @Valid @RequestParam(value = "medio", required = true) String medio,
            @NotNull @ApiParam(value = "Tipo (N/I)", required = true) @Valid @RequestParam(value = "tipo", required = true) String tipo) {
        log.info("The buscarIdentificadorMedioPago rest operation had been called!");
        
        List<PLE_IdentificadorMedioPago> response = blFirmaElectronica.buscarIdentificadorMedioPago(sistema, moneda, medio, tipo);
    
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<List<PLE_DetallePlanilla>> buscarDetallePlanilla(
            @NotNull @ApiParam(value = "ID de la planilla", required = true) @Valid @RequestParam(value = "idPlanilla", required = true) Integer idPlanilla) {
        log.info("The buscarDetallePlanilla rest operation had been called!");
        
        List<PLE_DetallePlanilla> response = blFirmaElectronica.buscarDetallePlanilla(idPlanilla);
    
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<List<PLE_CuentaBanco>> getCuentasBancos(
            @NotNull @ApiParam(value = "Código de moneda", required = true) @Valid @RequestParam(value = "moneda", required = true) String moneda,
            @NotNull @ApiParam(value = "Código de entidad", required = true) @Valid @RequestParam(value = "entidad", required = true) String entidad) {
        log.info("The getCuentasBancos rest operation had been called!");
        
        List<PLE_CuentaBanco> response = blFirmaElectronica.getCuentasBancos(moneda, entidad);
    
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<List<PLE_TotalPagoPlanilla>> obtenerTotalPagosPlanilla(
            @NotNull @ApiParam(value = "ID de la planilla", required = true) @Valid @RequestParam(value = "idPlanilla", required = true) Integer idPlanilla) {
        log.info("The obtenerTotalPagosPlanilla rest operation had been called!");
        
        List<PLE_TotalPagoPlanilla> response = blFirmaElectronica.obtenerTotalPagosPlanilla(idPlanilla);
    
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<List<PLE_MedioPago>> obtenerMedioPago() {
        log.info("The obtenerMedioPago rest operation had been called!");
        
        List<PLE_MedioPago> response = blFirmaElectronica.obtenerMedioPago();
    
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<List<PLE_TipoPlanilla>> obtenerTipoPlanilla() {
        log.info("The obtenerTipoPlanilla rest operation had been called!");
        
        List<PLE_TipoPlanilla> response = blFirmaElectronica.obtenerTipoPlanilla();
    
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

}
