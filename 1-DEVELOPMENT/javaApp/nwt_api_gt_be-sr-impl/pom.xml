<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.mapfre.tron.gt</groupId>
    <artifactId>nwt_api_gt_be.javaApp</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>

  <artifactId>nwt_api_gt_be-sr-impl</artifactId>
  <packaging>jar</packaging>

  <name>${project.artifactId}:${project.version}</name>
  <description>${project.artifactId}:${project.version}</description>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifest>
              <addClasspath>true</addClasspath>
            </manifest>
            <manifestEntries>
              <Class-Path>./ META-INF/</Class-Path>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <dependency>
      <groupId>com.mapfre.tron.gt</groupId>
      <artifactId>nwt_api_gt_be-sr-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.tron.gt</groupId>
      <artifactId>nwt_api_gt_be-bl-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.tron.gt</groupId>
      <artifactId>nwt_api_gt_be-dl-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.tron.gt</groupId>
      <artifactId>nwt_api_gt_be-commons</artifactId>
    </dependency>
  </dependencies>

</project>