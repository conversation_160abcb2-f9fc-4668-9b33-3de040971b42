<?xml version="1.0" encoding="UTF-8"?><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.mapfre.tron.gt</groupId>
    <artifactId>nwt_api_gt_be.DEVELOPMENT</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>
  
  <artifactId>nwt_api_gt_be.javaApp</artifactId>
  <packaging>pom</packaging>
  <name>${project.artifactId}:${project.version}</name>

  <properties>
    <gaia.folder>1-DEVELOPMENT/javaApp</gaia.folder>
  </properties>

  <modules>
    <module>bo</module>
    <module>commons</module>
    <module>config</module>
    <module>nwt_api_gt_be-sr-api</module>
    <module>nwt_api_gt_be-sr-impl</module>
    <module>nwt_api_gt_be-bl-api</module>
    <module>nwt_api_gt_be-bl-impl</module>
    <module>nwt_api_gt_be-dl-api</module>
    <module>nwt_api_gt_be-dl-impl</module>
  </modules>
</project>
