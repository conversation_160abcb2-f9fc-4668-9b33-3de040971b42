package com.mapfre.tron.gt.api.dl;

import java.time.LocalDate;
import java.util.List;

import com.mapfre.tron.gt.api.model.DetalleSiniestro;
import com.mapfre.tron.gt.api.model.EncuestaSiniestro;
import com.mapfre.tron.gt.api.model.EtapaSiniestro;
import com.mapfre.tron.gt.api.model.Siniestro;
import com.mapfre.tron.gt.api.model.SiniestroNit;
import com.mapfre.tron.gt.api.model.SiniestroVehiculo;
import com.mapfre.tron.gt.api.model.UrlSiniestro;

/**
 * Interfaz para operaciones de tracking en la capa de acceso a datos.
 *
 * Proporciona métodos para interactuar con funcionalidades de tracking
 * en la base de datos.
 */
public interface IDlTracking {

    /**
     * Obtiene la lista de siniestros según los criterios de búsqueda.
     * Utiliza la función TRON2000_GT.dc_k_utils_web_mgt.f_siniestros.
     *
     * @param codRamo Código de ramo (obligatorio)
     * @param codInter Código de intermediario (opcional)
     * @param numPoliza Número de póliza (opcional)
     * @param numSini Número de siniestro (opcional)
     * @param fechaInicio Fecha de inicio para la búsqueda (opcional)
     * @param fechaFin Fecha de fin para la búsqueda (opcional)
     * @return Lista de objetos Siniestro con la información de los siniestros encontrados
     */
    public List<Siniestro> getSiniestros(Integer codRamo, Integer codInter, String numPoliza,
                                         String numSini, LocalDate fechaInicio, LocalDate fechaFin);

    /**
     * Obtiene el código de ramo de un siniestro específico.
     * Utiliza la función TRON2000_GT.dc_k_utils_web_mgt.f_sin_ramo.
     *
     * @param numSiniestro Número de siniestro (obligatorio)
     * @return Código de ramo del siniestro o null si no se encuentra
     */
    public Integer getRamoSiniestro(String numSiniestro);

    /**
     * Obtiene las etapas de un siniestro específico.
     * Utiliza la función TRON2000_GT.dc_k_utils_web_mgt.f_etapa_siniestro.
     *
     * @param numSiniestro Número de siniestro (obligatorio)
     * @param codRamo Código de ramo (obligatorio)
     * @return Lista de objetos EtapaSiniestro con la información de las etapas del siniestro
     */
    public List<EtapaSiniestro> getTrackingEtapaSiniestro(String numSiniestro, Integer codRamo);

    /**
     * Obtiene la información de un siniestro según su número, documento y tipo de documento.
     * Utiliza la función TRON2000_GT.dc_k_utils_web_mgt.f_get_sntros.
     *
     * @param numSiniestro Número de siniestro (obligatorio)
     * @param numDoc Número de documento del tomador (obligatorio)
     * @param tipoDoc Tipo de documento (NIT, DPI, etc.) (obligatorio)
     * @return Lista de objetos SiniestroNit con la información del siniestro
     */
    public List<SiniestroNit> getSiniestroByDoc(String numSiniestro, String numDoc, String tipoDoc);

    /**
     * Obtiene una URL encriptada para acceder a un siniestro.
     * Utiliza la función TRON2000_GT.dc_k_utils_web_mgt.f_sin_crypt.
     *
     * @param url URL base (obligatorio)
     * @param numSiniestro Número de siniestro (obligatorio)
     * @param tipoCliente Tipo de cliente (T, A, etc.) (obligatorio)
     * @return Objeto UrlSiniestro con la URL encriptada
     */
    public UrlSiniestro getUrlSiniestro(String url, String numSiniestro, String tipoCliente);

    /**
     * Obtiene el detalle de un siniestro con sus etapas, descripciones e imágenes.
     * Utiliza la función TRON2000_GT.dc_k_utils_web_mgt.f_desc_siniestros.
     *
     * @param numSiniestro Número de siniestro (obligatorio)
     * @param codRamo Código de ramo (obligatorio)
     * @return Lista de objetos DetalleSiniestro con la información detallada del siniestro
     */
    public List<DetalleSiniestro> getTrackingDetalleSiniestro(String numSiniestro, Integer codRamo);

    /**
     * Obtiene la información de encuesta de siniestro con sus preguntas y respuestas.
     * Utiliza la función TRON2000_GT.dc_k_utils_web_mgt.f_get_encuesta.
     *
     * @return Lista de objetos EncuestaSiniestro con la información de la encuesta
     */
    public List<EncuestaSiniestro> getEncuestaSiniestro();

    /**
     * Guarda las respuestas de una encuesta de siniestro.
     * Utiliza la función TRON2000_GT.dc_k_utils_web_mgt.f_save_encuestas.
     *
     * @param numSiniestro Número de siniestro (obligatorio)
     * @param idEncuesta Identificador de la encuesta (obligatorio)
     * @param archXml XML con las respuestas de la encuesta (obligatorio)
     * @return Resultado de la operación
     */
    public String saveEncuesta(String numSiniestro, Integer idEncuesta, String archXml);

    /**
     * Valida si un siniestro puede tener una encuesta.
     * Utiliza la función TRON2000_GT.dc_k_utils_web_mgt.f_valid_sini.
     *
     * @param numSiniestro Número de siniestro (obligatorio)
     * @return Resultado de la validación
     */
    public String validaIngresoEncuesta(String numSiniestro);

    /**
     * Obtiene la información de seguimiento de un siniestro de vehículo.
     * Utiliza la función TRON2000_GT.ts_k_mod_peritacion_mgt.f_sub_tracking_etapa.
     *
     * @param numSiniestro Número de siniestro (obligatorio)
     * @param codCia Código de compañía (obligatorio)
     * @param numPoliza Número de póliza (obligatorio)
     * @param codFase Código de fase (obligatorio)
     * @param codRamo Código de ramo (obligatorio)
     * @return Lista de objetos SiniestroVehiculo con la información de seguimiento
     */
    public List<SiniestroVehiculo> getSiniestroVehiculo(String numSiniestro, Integer codCia, String numPoliza, String codFase, Integer codRamo);
}
