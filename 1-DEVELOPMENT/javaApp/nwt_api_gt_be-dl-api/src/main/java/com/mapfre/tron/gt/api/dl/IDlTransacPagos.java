package com.mapfre.tron.gt.api.dl;

import com.mapfre.tron.gt.api.model.ValidaEstadoDeducibleResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleFacResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleResponse;
import com.mapfre.tron.gt.api.model.InfoDeducibleResponse;
import com.mapfre.tron.gt.api.model.AvisoPagoPolizaGrupoResponse;
import com.mapfre.tron.gt.api.model.ContratosPolizaGrupoResponse;
import com.mapfre.tron.gt.api.model.ResponsablePagoResponse;
import com.mapfre.tron.gt.api.model.InfoReciboResponse;
import com.mapfre.tron.gt.api.model.TipoDocumentoPago;
import java.util.List;

/**
 * Interfaz para operaciones de transacciones de pagos en la capa de acceso a datos.
 *
 * Proporciona métodos para interactuar con funcionalidades de transacciones de pagos
 * en la base de datos.
 */
public interface IDlTransacPagos {

    /**
     * Valida el estado del deducible para un siniestro específico.
     * Utiliza la función TRON2000_GT.co_k_transac_linea_mgt.f_dev_validacion.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numSini Número de siniestro (obligatorio)
     * @return Objeto ValidaEstadoDeducibleResponse con la respuesta de la validación
     */
    public ValidaEstadoDeducibleResponse validaEstadoDeducible(Integer codCia, String numSini);

    /**
     * Obtiene el monto del deducible para facturación de un siniestro específico.
     * Utiliza la función TRON2000_GT.co_k_transac_linea_mgt.f_dev_calculo_dedu.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numSini Número de siniestro (obligatorio)
     * @param mcaFactura Marca de factura ('S' o 'N') (obligatorio)
     * @param numCuota Número de cuota (obligatorio)
     * @return Objeto MontoDeducibleFacResponse con los montos calculados
     */
    public MontoDeducibleFacResponse getMontoDeducibleFac(Integer codCia, String numSini, String mcaFactura, Integer numCuota);

    /**
     * Obtiene el monto del deducible de un siniestro específico.
     * Utiliza la función TRON2000_GT.co_k_transac_linea_mgt.fp_obtiene_deducible.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numSini Número de siniestro (obligatorio)
     * @return Objeto MontoDeducibleResponse con el monto del deducible
     */
    public MontoDeducibleResponse getMontoDeducible(Integer codCia, String numSini);

    /**
     * Obtiene la información de liquidación del deducible.
     * Utiliza la función TRON2000_GT.co_k_transac_linea_mgt.f_info_liquidacion.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numLiq Número de liquidación (obligatorio)
     * @return Objeto InfoDeducibleResponse con la información de liquidación
     */
    public InfoDeducibleResponse getInfoDeducible(Integer codCia, String numLiq);

    /**
     * Obtiene los avisos de pago de póliza grupo.
     * Utiliza la función TRON2000_GT.co_k_transac_linea_mgt.f_dev_aviso_poliza_grupo.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numContrato Número de contrato (opcional)
     * @param numPolizaGrupo Número de póliza grupo (obligatorio)
     * @return Lista de objetos AvisoPagoPolizaGrupoResponse con la información de los avisos de pago
     */
    public List<AvisoPagoPolizaGrupoResponse> getAvisoPagoPolizaGrupo(Integer codCia, Integer numContrato, String numPolizaGrupo);

    /**
     * Valida los tipos de documentos de pago disponibles según el requerimiento.
     * Utiliza la función TRON2000_GT.co_k_pasarela_pago_mgt.f_valida_tip_docto_pago.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param requerimiento Código de requerimiento (obligatorio)
     * @return Lista de objetos TipoDocumentoPago con la información de los tipos de documentos de pago
     */
    public List<TipoDocumentoPago> validaTipoDocumentoPago(Integer codCia, String requerimiento);

    /**
     * Obtiene los contratos de póliza grupo.
     * Utiliza la función TRON2000_GT.co_k_transac_linea_mgt.f_dev_contratos_poliza.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numPolizaGrupo Número de póliza grupo (obligatorio)
     * @return Lista de objetos ContratosPolizaGrupoResponse con la información de los contratos
     */
    public List<ContratosPolizaGrupoResponse> getContratosPolizaGrupo(Integer codCia, String numPolizaGrupo);

    /**
     * Obtiene el responsable de pago.
     * Utiliza la función TRON2000_GT.co_k_transac_linea_mgt.f_dev_responsable_pago.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param tipDocum Tipo de documento (obligatorio)
     * @param codDocum Código de documento (obligatorio)
     * @param codActTercero Código de actividad del tercero (opcional)
     * @return Objeto ResponsablePagoResponse con la información del responsable de pago
     */
    public ResponsablePagoResponse getResponsablePago(Integer codCia, String tipDocum, String codDocum, Integer codActTercero);

    /**
     * Obtiene la información del recibo.
     * Utiliza la función TRON2000_GT.co_k_transac_linea_mgt.f_dev_info_recibo.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numRecibo Número de recibo (obligatorio)
     * @return Objeto InfoReciboResponse con la información del recibo
     */
    public InfoReciboResponse getInfoRecibo(Integer codCia, Integer numRecibo);
}
