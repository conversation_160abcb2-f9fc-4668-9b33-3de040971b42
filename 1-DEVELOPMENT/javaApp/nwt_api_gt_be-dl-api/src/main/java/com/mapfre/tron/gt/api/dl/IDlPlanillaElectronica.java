package com.mapfre.tron.gt.api.dl;

import java.util.List;

import com.mapfre.tron.gt.api.model.Ple_MedioPagoPlanilla;
import com.mapfre.tron.gt.api.model.Ple_DepositoPlanilla;
import com.mapfre.tron.gt.api.model.Ple_IdentificadorMedioPago;
import com.mapfre.tron.gt.api.model.Ple_DetallePlanilla;
import com.mapfre.tron.gt.api.model.Ple_CuentaBanco;
import com.mapfre.tron.gt.api.model.Ple_TotalPagoPlanilla;
import com.mapfre.tron.gt.api.model.Ple_MedioPago;
import com.mapfre.tron.gt.api.model.Ple_TipoPlanilla;

public interface IDlPlanillaElectronica {

    /**
     * Buscar medios de pago de planilla
     * @param idPlanilla ID de la planilla
     * @return Lista de medios de pago
     */
    public List<Ple_MedioPagoPlanilla> buscarMediosPagoPlanilla(Integer idPlanilla);

    /**
     * Buscar depósitos de planilla
     * @param idPlanilla ID de la planilla
     * @return Lista de depósitos
     */
    public List<Ple_DepositoPlanilla> buscarDepositosPlanilla(Integer idPlanilla);

    /**
     * Buscar identificador de medio de pago
     * @param sistema Sistema (A/T)
     * @param moneda Código de moneda
     * @param medio Medio de pago
     * @param tipo Tipo (N/I)
     * @return Lista de identificadores
     */
    public List<Ple_IdentificadorMedioPago> buscarIdentificadorMedioPago(String sistema, String moneda, String medio, String tipo);

    /**
     * Buscar detalle de planilla
     * @param idPlanilla ID de la planilla
     * @return Lista de detalles
     */
    public List<Ple_DetallePlanilla> buscarDetallePlanilla(Integer idPlanilla);

    /**
     * Obtener cuentas de bancos
     * @param moneda Código de moneda
     * @param entidad Código de entidad
     * @return Lista de cuentas bancarias
     */
    public List<Ple_CuentaBanco> getCuentasBancos(String moneda, String entidad);

    /**
     * Obtener total de pagos de planilla
     * @param idPlanilla ID de la planilla
     * @return Lista de totales de pagos
     */
    public List<Ple_TotalPagoPlanilla> obtenerTotalPagosPlanilla(Integer idPlanilla);

    /**
     * Obtener medios de pago disponibles
     * @return Lista de medios de pago
     */
    public List<Ple_MedioPago> obtenerMedioPago();

    /**
     * Obtener tipos de planilla disponibles
     * @return Lista de tipos de planilla
     */
    public List<Ple_TipoPlanilla> obtenerTipoPlanilla();

}
