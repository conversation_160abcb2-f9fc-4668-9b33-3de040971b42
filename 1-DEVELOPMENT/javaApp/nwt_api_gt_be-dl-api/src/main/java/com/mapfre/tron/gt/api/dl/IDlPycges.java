package com.mapfre.tron.gt.api.dl;

import java.util.List;

import com.mapfre.tron.gt.api.model.*;

/**
 * Interfaz para operaciones de Pycges en la capa de acceso a datos.
 *
 * Proporciona métodos para interactuar con funcionalidades de Pycges
 * en la base de datos.
 */
public interface IDlPycges {

    /**
     * Obtiene la lista de usuarios.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_USUARIO.
     *
     * @return Lista de objetos PycUsuario con la información de los usuarios
     */
    public List<PycUsuario> getUsuarios();

    /**
     * Obtiene la lista de aplicaciones asociadas a un proceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_APLICACION.
     *
     * @param idProceso ID del proceso
     * @return Lista de objetos PycAplicacion con la información de las aplicaciones
     */
    public List<PycAplicacion> getAplicaciones(Integer idProceso);

    /**
     * Obtiene la información completa de un proceso específico.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_INFO_PROCESO.
     *
     * @param idProceso ID del proceso
     * @return Lista de objetos PycInfoProceso con la información del proceso
     */
    public List<PycInfoProceso> getInfoProceso(Integer idProceso);

    /**
     * Obtiene la lista de controles asociados a un proceso específico y tipo de control.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBTENER_CONTROLES_PRO.
     *
     * @param idProceso ID del proceso
     * @param tipoControl Código del tipo de control
     * @return Lista de objetos PycControlProc con la información de los controles
     */
    public List<PycControlProc> getControlesProceso(Integer idProceso, String tipoControl);

    /**
     * Obtiene la lista de estados disponibles para un perfil específico en un proceso determinado.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBTENER_ESTADO_PERFIL.
     *
     * @param idPerfil ID del perfil
     * @param idProceso ID del proceso
     * @return Lista de objetos PycEstadoPerfil con la información de los estados
     */
    public List<PycEstadoPerfil> getEstadoPerfil(Integer idPerfil, Integer idProceso);

    /**
     * Obtiene la lista de procesos asociados a un tipo de petición.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PROCESO_POR_TIPO.
     *
     * @param idTipo ID del tipo de petición
     * @return Lista de objetos PycProcesoPorTipo con la información de los procesos
     */
    public List<PycProcesoPorTipo> getProcesosPorTipo(Integer idTipo);

    /**
     * Obtiene la lista de perfiles activos.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PERFIL.
     *
     * @return Lista de objetos PycPerfil con la información de los perfiles
     */
    public List<PycPerfil> getPerfiles();

    /**
     * Obtiene la lista de usuarios asociados a un perfil.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_USUARIO_PERFIL.
     *
     * @param idPerfil ID del perfil (opcional, si es null devuelve todos los usuarios con sus perfiles)
     * @return Lista de objetos PycUsuarioPerfil con la información de los usuarios y sus perfiles
     */
    public List<PycUsuarioPerfil> getUsuariosPerfil(Integer idPerfil);

    /**
     * Obtiene el listado de asignaciones por perfil.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_LISTADO_ASIGNA.
     *
     * @param idPerfil ID del perfil
     * @return Lista de objetos PycListadoAsigna con la información de los usuarios asignados al perfil
     */
    public List<PycListadoAsigna> getListadoAsigna(Integer idPerfil);

    /**
     * Obtiene la consulta SQL asociada a un dato variable.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_QUERY_DAT_VAR.
     *
     * @param idProceso ID del proceso
     * @param idSeccion ID de la sección
     * @param idDatoVar ID del dato variable
     * @return Objeto PycGetQueryDatVar con la consulta SQL
     */
    public PycGetQueryDatVar getQueryDatVar(Integer idProceso, Integer idSeccion, Integer idDatoVar);

    /**
     * Verifica si un perfil tiene la opción de selección múltiple.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_SRC_MULTIPLE_OPTION.
     *
     * @param idPerfil ID del perfil
     * @return Objeto PycSrcMultipleOption con el resultado de la verificación
     */
    public PycSrcMultipleOption getSrcMultipleOption(Integer idPerfil);

    /**
     * Obtiene la lista de usuarios asignados según proceso, perfil, aplicación y estado.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_USUARIOS_ASIG.
     *
     * @param idProceso ID del proceso
     * @param idPerfil ID del perfilPycGetUsuariosAsig
     * @param idAplicacion ID de la aplicación
     * @param idEstado ID del estado
     * @return Lista de objetos PycGetUsuariosAsig con la información de los usuarios asignados
     */
    public List<PycGetUsuariosAsig> getUsuariosAsig(Integer idProceso, Integer idPerfil, Integer idAplicacion, Integer idEstado);

    /**
     * Obtiene la lista de usuarios asociados a un perfil y proceso específicos.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_USU_PERFIL_PROCESO.
     *
     * @param idPerfil ID del perfil
     * @param idProceso ID del proceso
     * @return Lista de objetos PycUsuPerfilProceso con la información de los usuarios
     */
    public List<PycUsuPerfilProceso> getUsuPerfilProceso(Integer idPerfil, Integer idProceso);

    /**
     * Obtiene la lista de procesos asociados a un usuario específico.
     * Utiliza la función TRON2000_GT.GC_K_PYC_AUTH_MGT.FN_USUARIO_PROCESOS.
     *
     * @param usuario Nombre de usuario en la base de datos
     * @return Lista de objetos PycUsuProcesos con la información de los procesos del usuario
     */
    public List<PycUsuProcesos> getUsuarioProcesos(String usuario);

    /**
     * Actualiza o crea un registro de perfil para un usuario específico.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.F_MOD_PERFIL.
     *
     * @param perfilData Datos del perfil a actualizar o crear
     * @return ID del usuario actualizado o creado
     */
    public Integer updatePerfil(PycUpdPerfil perfilData);

    /**
     * Actualiza o crea un registro de usuario con sus datos personales y departamento.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.F_MOD_USUARIO.
     *
     * @param usuarioData Datos del usuario a actualizar o crear
     * @return ID del usuario actualizado o creado
     */
    public Integer updateUsuario(PycUpdUsuario usuarioData);

    /**
     * Crea una nueva solicitud en el sistema PYCGES.
     * Utiliza el procedimiento TRON2000_GT.GC_K_PYC_COMERCIAL_MGT.P_CREA_SOLICITUD.
     *
     * @param solicitudData Datos de la solicitud a crear
     * @return ID de la solicitud creada
     */
    public Integer creaSolicitud(PycCreaSolicitud solicitudData);

    /**
     * Inserta una nueva petición en el sistema PYCGES.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_INSERT_PETICION.
     *
     * @param peticionData Datos de la petición a insertar
     * @return ID de la petición creada
     */
    public Integer insertPeticion(PycInsertPeticion peticionData);

    /**
     * Obtiene la lista de peticiones filtradas por perfil, proceso, estado y otros criterios.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PETICION_PERFIL.
     *
     * @param idPerfil ID del perfil
     * @param idProceso ID del proceso
     * @param idEstado ID del estado
     * @param idPeticion ID de la petición (opcional)
     * @param idCanal ID del canal (opcional)
     * @param idUsuario ID del usuario (opcional)
     * @param indSubs Indicador de substitución (opcional)
     * @param idOficina ID de la oficina (opcional)
     * @param numaOUsuario Número o usuario que realiza la consulta
     * @return Lista de objetos PycPeticionPerfil con la información de las peticiones
     */
    public List<PycPeticionPerfil> getPeticionPerfil(Integer idPerfil, Integer idProceso, String idEstado,
                                                     Integer idPeticion, Integer idCanal, Integer idUsuario,
                                                     String indSubs, Integer idOficina, String numaOUsuario);

    /**
     * Obtiene la lista de peticiones que no tienen programador asignado.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PETICION_SIN_PROGRAMADOR.
     *
     * @return Lista de objetos PycPetSinProgramador con la información de las peticiones sin programador
     */
    public List<PycPetSinProgramador> getPeticionSinProgramador();

    /**
     * Obtiene la lista de áreas que tienen usuarios con peticiones asociadas.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_CON_AREA.
     *
     * @return Lista de objetos PycAreaPeti con la información de las áreas con peticiones
     */
    public List<PycAreaPeti> getConArea();

    /**
     * Obtiene la lista de departamentos que tienen usuarios con peticiones, filtrados por área.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_CON_DEPARTAMENTO.
     *
     * @param idArea ID del área (opcional)
     * @return Lista de objetos PycDepartamento con la información de los departamentos
     */
    public List<PycDepartamento> getConDepartamento(Integer idArea);

    /**
     * Obtiene la lista de usuarios que han realizado peticiones, filtrados por área y/o departamento.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_CON_SOLICITANTE.
     *
     * @param idArea ID del área (opcional)
     * @param idDepartamento ID del departamento (opcional)
     * @return Lista de objetos PycAreaUsuario con la información de los usuarios solicitantes
     */
    public List<PycAreaUsuario> getConSolicitante(Integer idArea, Integer idDepartamento);

    /**
     * Obtiene la lista de analistas disponibles filtrados por solicitante y prioridad de petición.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_CON_ANALISTA.
     *
     * @param idSolicitante ID del usuario solicitante
     * @param prioridad Prioridad de la petición (opcional)
     * @return Lista de objetos PycAnalista con la información de los analistas
     */
    public List<PycAnalista> getConAnalista(Integer idSolicitante, String prioridad);

    /**
     * Obtiene la lista de estados de peticiones filtrados por solicitante, prioridad y analista.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_CON_ESTADO.
     *
     * @param idSolicitante ID del usuario solicitante
     * @param prioridad Prioridad de la petición (usar 'null' para peticiones sin prioridad)
     * @param idAnalista ID del analista asignado (opcional)
     * @return Lista de objetos PycConEstado con la información de los estados
     */
    public List<PycConEstado> getConEstado(Integer idSolicitante, String prioridad, Integer idAnalista);

    /**
     * Obtiene la lista de prioridades de peticiones filtradas por solicitante, área y departamento.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_CON_PRIORIDAD.
     *
     * @param idSolicitante ID del usuario solicitante (opcional)
     * @param idArea ID del área (opcional)
     * @param idDepartamento ID del departamento (opcional)
     * @return Lista de objetos PycPrioridad con la información de las prioridades
     */
    public List<PycPrioridad> getConPrioridad(Integer idSolicitante, Integer idArea, Integer idDepartamento);

    /**
     * Obtiene la lista de años distintos de las peticiones filtrados por proceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_TAB_ANIOS.
     *
     * @param idProceso ID del proceso
     * @return Lista de objetos PycTabAnios con los años disponibles
     */
    public List<PycTabAnios> getTabAnios(Integer idProceso);

    /**
     * Obtiene la lista de áreas distintas de las peticiones filtradas por proceso y año.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_TAB_AREA.
     *
     * @param idProceso ID del proceso
     * @param anio Año de filtro (formato YYYY) (opcional)
     * @return Lista de objetos PycTabArea con las áreas disponibles
     */
    public List<PycTabArea> getTabArea(Integer idProceso, String anio);

    /**
     * Obtiene la lista de estados distintos de las peticiones filtradas por proceso, año y área.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_TAB_ESTADO.
     *
     * @param idProceso ID del proceso
     * @param anio Año de filtro (formato YYYY) (opcional)
     * @param idArea ID del área (opcional)
     * @return Lista de objetos PycTabEstado con los estados disponibles
     */
    public List<PycTabEstado> getTabEstado(Integer idProceso, String anio, Integer idArea);

    /**
     * Obtiene la lista de usuarios asociados a un perfil específico para una petición y aplicación determinada.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_USUARIOS_PERFIL_PETICION.
     *
     * @param idPeticion ID de la petición
     * @param idPerfil ID del perfil
     * @param idAplicacion ID de la aplicación
     * @return Lista de objetos PycUsuPerfilPeti con la información de los usuarios
     */
    public List<PycUsuPerfilPeti> getUsuariosPerfilPeticion(Integer idPeticion, Integer idPerfil, Integer idAplicacion);

    /**
     * Obtiene la lista de peticiones filtradas por múltiples criterios.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PETICION_FILTROS.
     *
     * @param idArea ID del área (opcional)
     * @param idDepartamento ID del departamento (opcional)
     * @param idPerfil ID del perfil (opcional)
     * @param idEstado ID del estado (opcional)
     * @param idUsuarioSolicitante ID del usuario solicitante (opcional)
     * @param idTipo ID del tipo de petición (opcional)
     * @param fechaInicio Fecha de inicio del filtro (opcional)
     * @param fechaFin Fecha de fin del filtro (opcional)
     * @param idProceso ID del proceso (opcional)
     * @return Lista de objetos PycPetiFiltro con la información de las peticiones filtradas
     */
    public List<PycPetiFiltro> getPeticionFiltros(Integer idArea, Integer idDepartamento, Integer idPerfil,
                                                  Integer idEstado, Integer idUsuarioSolicitante, Integer idTipo,
                                                  String fechaInicio, String fechaFin, Integer idProceso);

    /**
     * Obtiene el reporte de peticiones filtrado por múltiples criterios.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_REPORTE_PETICION.
     *
     * @param idArea ID del área (opcional)
     * @param idDepartamento ID del departamento (opcional)
     * @param idSolicitante ID del usuario solicitante (opcional)
     * @param prioridad Prioridad de la petición (opcional, usar 'null' para peticiones sin prioridad)
     * @param idAnalista ID del analista asignado (opcional)
     * @param estado IDs de estados separados por coma (opcional)
     * @return Lista de objetos PycReportePeti con la información del reporte de peticiones
     */
    public List<PycReportePeti> getReportePeticion(Integer idArea, Integer idDepartamento, Integer idSolicitante,
                                                   String prioridad, Integer idAnalista, String estado);

    /**
     * Inserta o actualiza la relación entre un usuario y un proceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_INSERT_USERPRO.
     *
     * @param userData Datos del usuario proceso a insertar/actualizar
     * @return ID del usuario procesado
     */
    public Integer insertUserProceso(PycInsertUserPro userData);

    /**
     * Inserta un nuevo usuario en el sistema PYCGES.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_INSERT_USUARIO.
     *
     * @param usuarioData Datos del usuario a insertar
     * @return ID del usuario creado
     */
    public Integer insertUsuario(PycInsertUsuario usuarioData);

    /**
     * Actualiza la información de un documento asociado a una petición.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_ACT_DOCUMENTO.
     *
     * @param documentoData Datos del documento a actualizar
     * @return ID de la petición actualizada
     */
    public Integer actDocumento(PycActDocumento documentoData);

    /**
     * Obtiene la lista de observaciones asociadas a una petición específica.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBSER_PETICION.
     * Ahora requiere el parámetro numaOUsuario para identificar al usuario específico.
     *
     * @param idPeticion ID de la petición
     * @param numaOUsuario Nombre o número de usuario (opcional)
     * @return Lista de observaciones de la petición
     */
    public List<PycObserPet> getObserPeticion(Integer idPeticion, String numaOUsuario);

    /**
     * Obtiene el resumen estadístico de actividades de una petición específica por categoría.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_RESUMEN_ACTIVIDAD.
     *
     * @param idPeticion ID de la petición
     * @param idCategoria ID de la categoría del tablero
     * @return Lista con el resumen de actividades
     */
    public List<PycResumenActi> getResumenActividad(Integer idPeticion, Integer idCategoria);

    /**
     * Obtiene el avance detallado de peticiones con información de observaciones, usuarios y estados.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PETICION_AVANCE.
     *
     * @param idProceso ID del proceso (opcional)
     * @param idArea ID del área (opcional)
     * @param estado IDs de estados separados por coma (opcional)
     * @param codcia Código de compañía (opcional)
     * @param anio Año de filtro (opcional)
     * @return Lista de objetos PycPetAvance con el avance de peticiones
     */
    public List<PycPetAvance> getPeticionAvance(Integer idProceso, Integer idArea, String estado, String codcia, Integer anio);

    /**
     * Obtiene la lista de peticiones con información de su petición siguiente asociada.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PETICION_AVANCE_SIGUIENTE.
     *
     * @param idProceso ID del proceso (opcional)
     * @param idArea ID del área (opcional)
     * @param estado Lista de estados separados por comas (opcional)
     * @param codcia Código de compañía (opcional)
     * @param anio Año de filtro (opcional)
     * @return Lista de objetos PycPetAvanceSig con la información de peticiones y sus siguientes
     */
    public List<PycPetAvanceSig> getPeticionAvanceSiguiente(Integer idProceso, Integer idArea, String estado, String codcia, Integer anio);

    /**
     * Obtiene la lista de valores activos filtrados por tipo y proceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_LISTA_VAL.
     *
     * @param tipo Tipo de valor a consultar
     * @param idProceso ID del proceso
     * @return Lista de objetos PycListVal con los valores y descripciones
     */
    public List<PycListVal> getListaVal(String tipo, Integer idProceso);

    /**
     * Obtiene la lista de oficinas disponibles para un usuario específico en un proceso determinado.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.F_GET_OFICINAS.
     * La función considera el indicador de oficina del usuario para determinar si retorna
     * todas las oficinas del proceso o solo las asignadas al usuario.
     *
     * @param idProceso ID del proceso
     * @param idUsuario ID del usuario
     * @return Lista de objetos PycOficina con las oficinas disponibles
     */
    public List<PycOficina> getOficinas(Integer idProceso, Integer idUsuario);

    /**
     * Obtiene la lista de canales disponibles para un usuario específico en un proceso y oficina determinados.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.F_GET_CANALES.
     * La función implementa lógica condicional compleja basada en los indicadores ind_canal e ind_oficina:
     * - Si ind_canal = 'T': Retorna todos los canales activos del proceso para la oficina especificada
     * - Si ind_canal != 'T' AND ind_oficina = 'T': Retorna solo canales asignados al usuario para el proceso
     * - Si ind_canal != 'T' AND ind_oficina != 'T': Retorna solo canales asignados al usuario para la oficina y proceso específicos
     *
     * @param idProceso ID del proceso
     * @param idUsuario ID del usuario
     * @param idOficina ID de la oficina
     * @return Lista de objetos PycCanal con los canales disponibles
     */
    public List<PycCanal> getCanales(Integer idProceso, Integer idUsuario, Integer idOficina);

    /**
     * Obtiene la lista de subordinados disponibles para un supervisor específico considerando canal, oficina, indicador de login y proceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.F_GET_SUBORDINADOS.
     * La función implementa lógica condicional extremadamente compleja basada en múltiples indicadores (ind_admin, ind_oficina, ind_canal):
     * - Validaciones iniciales: Si indLogin = 'S' obtiene id_proceso del canal y ind_admin del usuario
     * - Obtiene ind_oficina e ind_canal del supervisor para el proceso
     * - ESCENARIO 1: ind_admin = 'S' OR ind_oficina = 'T' (Supervisor con privilegios administrativos)
     *   - SUB-ESCENARIO 1A: ind_canal = 'T' → Todos los subordinados de la oficina (incluye ID quemado 21)
     *   - SUB-ESCENARIO 1B: ind_canal != 'T' → Subordinados de la oficina filtrados por canal específico
     * - ESCENARIO 2: ind_admin != 'S' AND ind_oficina != 'T' (Supervisor con acceso restringido)
     *   - SUB-ESCENARIO 2A: ind_canal = 'T' → Subordinados directos del supervisor en la oficina
     *   - SUB-ESCENARIO 2B: ind_canal != 'T' → Subordinados directos filtrados por canal y oficina específicos
     * - FALLBACK: Si no se encuentran subordinados, retorna al propio supervisor como único resultado
     *
     * @param canal ID del canal
     * @param oficina ID de la oficina
     * @param idSupervisor ID del supervisor
     * @param indLogin Indicador de login (S/N)
     * @param proceso ID del proceso
     * @return Lista de objetos PycSubordinados con los subordinados disponibles
     */
    public List<PycSubordinados> getSubordinados(String canal, String oficina, String idSupervisor, String indLogin, String proceso);

    /**
     * Obtiene la información completa de un usuario específico incluyendo datos personales, área, departamento, perfil y configuraciones.
     * Utiliza la función TRON2000_GT.GC_K_PYC_AUTH_MGT.FN_INFO_USUARIO.
     * La función realiza múltiples joins entre tablas de usuario, área, departamento, perfil y proceso para obtener información detallada:
     * - Datos básicos del usuario: nombres, apellidos, email, género, fechas, estado
     * - Información organizacional: área y departamento del usuario
     * - Perfiles y procesos: múltiples perfiles con indicador de perfil por defecto
     * - Configuraciones: URLs de perfil e inicio
     * - Filtros: solo registros activos en todas las tablas relacionadas (excepto usuario)
     * - Búsqueda: case-insensitive por nombre de usuario en la base de datos
     * - Ordenamiento: por IND_DEFAULT DESC (perfil por defecto primero)
     *
     * @param usuario Nombre de usuario en la base de datos
     * @return Lista de objetos PycInfoUsuario con la información completa del usuario
     */
    public List<PycInfoUsuario> getInfoUsuario(String usuario);

    /**
     * Obtiene la información de un ramo específico basado en el código de ramo, modalidad y compañía.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBTENER_RAMO.
     * La función consulta la tabla PYC_TIPO_PETICION con parámetros que tienen valores por defecto:
     * - P_COD_RAMO: Código del ramo (obligatorio)
     * - P_COD_MODALIDAD: Código de modalidad (opcional, valor por defecto '999')
     * - P_COD_CIA: Código de compañía (opcional, valor por defecto '2')
     * - Consulta directa en tabla PYC_TIPO_PETICION sin joins complejos
     * - Filtros específicos por COD_RAMO, COD_MODALIDAD y COD_CIA
     * - Retorna información del tipo de petición asociado al ramo
     *
     * @param codRamo Código del ramo (obligatorio)
     * @param codModalidad Código de modalidad (opcional, default '999')
     * @param codCia Código de compañía (opcional, default '2')
     * @return Lista de objetos PycRamo con la información del ramo
     */
    public List<PycRamo> obtenerRamo(String codRamo, Integer codModalidad, String codCia);

    /**
     * Obtiene los datos de variables equivalentes basado en el código de ramo, modalidad y compañía.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBTENER_DAT_VAR_EQUIV.
     * La función consulta la tabla PYC_DATO_VAR_EQUIV con parámetros que tienen valores por defecto y filtros avanzados:
     * - P_COD_RAMO: Código del ramo (obligatorio, se usa con LIKE para búsquedas flexibles)
     * - P_COD_MODALIDAD: Código de modalidad (opcional, valor por defecto '999')
     * - P_COD_CIA: Código de compañía (opcional, valor por defecto '2')
     * - Filtros especiales: LIKE en COD_RAMO, NVL para valores por defecto, ESTADO = 'ACT'
     * - Consulta directa en tabla PYC_DATO_VAR_EQUIV sin joins complejos
     * - Retorna mapeo entre variables PYC y REEF para formularios y secciones específicas
     *
     * @param codRamo Código del ramo (obligatorio, se usa con LIKE)
     * @param codModalidad Código de modalidad (opcional, default '999')
     * @param codCia Código de compañía (opcional, default '2')
     * @return Lista de objetos PycDatoVarEquiv con los datos de variables equivalentes
     */
    public List<PycDatoVarEquiv> obtenerDatVarEquiv(String codRamo, Integer codModalidad, String codCia);

    /**
     * Obtiene los perfiles asignados a un usuario específico para un proceso determinado.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PERFILES_USUARIO.
     * La función realiza un JOIN entre las tablas PYC_USUARIO_PERFIL y PYC_PERFIL con filtros específicos:
     * - pIdUsuario: ID del usuario (obligatorio, se convierte a UPPER)
     * - pIdProceso: ID del proceso (obligatorio)
     * - JOIN: A.ID_PERFIL = B.ID_PERFIL entre PYC_USUARIO_PERFIL (A) y PYC_PERFIL (B)
     * - Filtros: Solo registros activos (ESTADO = 'ACT') en ambas tablas
     * - Filtros específicos: Por ID_PROCESO e ID_USUARIO
     * - Ordenamiento: Por A.ORDEN y B.NOMBRE_PERFIL
     * - Retorna información completa del perfil y su asignación al usuario
     *
     * @param idUsuario ID del usuario (obligatorio)
     * @param idProceso ID del proceso (obligatorio)
     * @return Lista de objetos PycPerfilUsu con los perfiles del usuario para el proceso
     */
    public List<PycPerfilUsu> perfilesUsuario(Integer idUsuario, Integer idProceso);

    /**
     * Obtiene la lista de categorías de actividades asociadas a una petición específica con estadísticas de avance.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBTENER_CATE_X_PET.
     * La función realiza consultas complejas con múltiples JOINs y subconsultas para obtener estadísticas detalladas:
     * - Consulta principal: JOIN entre PYC_ACTIVIDAD_PETICION y PYC_CATEGORIA_TABLERO
     * - Subconsulta interna: Calcula porcentajes de actividad y avance por categoría
     * - LEFT JOINs: Para actividades terminadas (TER) y activas (ACT)
     * - Agregaciones: COUNT, SUM, MIN, MAX para estadísticas por categoría
     * - Cálculos complejos: Porcentajes de avance basados en estado y horas
     * - Filtros: Por ID_PETICION específico
     * - Agrupamiento: Por petición y categoría
     * - Ordenamiento: Por ID_PETICION e ID_CATEGORIA_TABLERO
     *
     * @param idPeticion ID de la petición (obligatorio)
     * @return Lista de objetos PycCategoriaPet con las categorías y estadísticas de la petición
     */
    public List<PycCategoriaPet> getCategoriaPeticion(Integer idPeticion);

    /**
     * Obtiene la lista completa de peticiones con información básica para mostrar en el tablero.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBTENER_PETICIONES_TABLERO.
     * La función realiza JOINs entre las tablas principales del sistema de peticiones:
     * - PYC_PETICION: Información básica de la petición (ID, nombre)
     * - PYC_TIPO_PETICION: Descripción del tipo de petición
     * - PYC_USUARIO: Información del usuario solicitante (primer nombre)
     * - PYC_ESTADO: Nombre del estado actual de la petición
     * - Ordenamiento: Por ID_PETICION DESC (peticiones más recientes primero)
     * - Sin filtros: Retorna todas las peticiones del sistema
     *
     * @return Lista de objetos PycPeticionTab con la información básica de todas las peticiones
     */
    public List<PycPeticionTab> getPeticionesTablero();

    /**
     * Obtiene la lista jerárquica de opciones de menú disponibles para un perfil específico en un proceso determinado.
     * Utiliza la función TRON2000_GT.GC_K_PYC_AUTH_MGT.FN_OPCION_USUARIO.
     * La función realiza consultas complejas con múltiples JOINs y estructura jerárquica:
     * - PYC_OPCION: Información básica de las opciones de menú (ID, nombre, logo, ruta, funciones, estado)
     * - PYC_OPCION_PERFIL: Relación entre opciones y perfiles con orden y permisos
     * - PYC_USUARIO_PERFIL: Asignación de perfiles a usuarios con multiperfil
     * - PYC_PROCESO_OPCION: Personalización de opciones por proceso
     * - Estructura jerárquica: START WITH ID_PADRE = 0 CONNECT BY PRIOR ID_OPCION = ID_PADRE
     * - Filtros: Solo registros activos, usuario específico (NUMA_O_USUARIO), perfil y proceso específicos
     * - Permisos: Considera ADMIN según MULTIPERFIL del usuario
     * - Ordenamiento: Por ORDEN definido en PYC_OPCION_PERFIL
     * - Actualización: Ahora requiere PNumaOUsuario que reemplaza la búsqueda por USER
     *
     * @param idPerfil ID del perfil (obligatorio)
     * @param idProceso ID del proceso (obligatorio)
     * @param numaOUsuario Nombre o número de usuario (obligatorio)
     * @return Lista de objetos PycOpcionUsu con las opciones de menú jerárquicas del usuario
     */
    public List<PycOpcionUsu> getOpcionUsuario(Integer idPerfil, Integer idProceso, String numaOUsuario);

    /**
     * Obtiene un reporte detallado de peticiones con información de programación filtradas por año y proceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_REPORTE_PETICION_PROG.
     * La función realiza consultas complejas con múltiples JOINs y subconsultas:
     * - PYC_PETICION: Información básica de peticiones (ID, nombre, descripción, fechas, estado)
     * - PYC_TIPO_PETICION: Descripción del tipo de petición
     * - PYC_ESTADO: Nombre del estado actual
     * - PYC_ACTIVIDAD_PETICION: Fechas y horas de desarrollo (categoría 3) y análisis (categoría 2)
     * - PYC_LIST_VAL: Descripción de prioridades por proceso
     * - PYC_USUARIO: Información de solicitantes y analistas
     * - PYC_ANALISTA_PETICION: Asignación de analistas
     * - PYC_OBSERVACION_PETICION: Última observación de cada petición
     * - Área y Departamento: Información organizacional del solicitante
     * - Cálculos: Días totales, semanas, días inhábiles y días reales de trabajo
     * - Filtros: Año de creación y proceso específico
     * - Subconsultas: MIN/MAX fechas, SUM horas por categoría de actividad
     *
     * @param anio Año de filtro para las peticiones (obligatorio)
     * @param idProceso ID del proceso (obligatorio)
     * @return Lista de objetos PycReportePetProg con el reporte detallado de peticiones
     */
    public List<PycReportePetProg> getReportePeticionProgramacion(Integer anio, Integer idProceso);

    /**
     * Obtiene estadísticas agrupadas por estado de peticiones con conteos, totales y porcentajes.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_ESTADISTICA_ESTADOS.
     * La función construye dinámicamente una consulta SQL con múltiples JOINs y agregaciones:
     * - PYC_PETICION: Información básica de peticiones (ID, estado, fecha creación, proceso, usuario solicitante)
     * - PYC_ESTADO: Información del estado (ID, nombre, color)
     * - PYC_USUARIO_DEPARTAMENTO: Relación usuario-departamento-área para filtrado organizacional
     * - Agregaciones: COUNT(*) por estado, SUM(COUNT(*)) OVER() para total, cálculo de porcentajes
     * - Filtros dinámicos: Proceso, área, año de creación, estados específicos (lista), código de compañía
     * - Ordenamiento: Por PORCENTAJE_ESTADO DESC (estados con mayor porcentaje primero)
     * - Query dinámica: Construye SQL con concatenación de strings y condiciones condicionales
     *
     * @param proceso ID del proceso (obligatorio)
     * @param area ID del área (obligatorio)
     * @param anio Año de filtro (obligatorio)
     * @param estado Lista de IDs de estados separados por coma (opcional, ej. "1,2,3")
     * @param codCia Código de compañía (opcional, puede ser null)
     * @return Lista de objetos PycEstadisticaEstado con las estadísticas por estado
     */
    public List<PycEstadisticaEstado> getEstadisticaEstados(Integer proceso, Integer area, Integer anio, String estado, String codCia);

    /**
     * Obtiene estadísticas agrupadas por estado de peticiones siguientes con conteos, totales y porcentajes.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_ESTADISTICA_ESTADOS_SIGU.
     * La función construye dinámicamente una consulta SQL con múltiples JOINs y agregaciones:
     * - PYC_PETICION A: Información básica de peticiones principales (ID, estado, fecha creación, proceso, usuario solicitante)
     * - PYC_PETICION S: Información de peticiones siguientes (ID_PETICION_SIGUIENTE, estado)
     * - PYC_ESTADO: Información del estado de las peticiones siguientes (ID, nombre, color)
     * - PYC_USUARIO_DEPARTAMENTO: Relación usuario-departamento-área para filtrado organizacional
     * - Agregaciones: COUNT(*) por estado siguiente, SUM(COUNT(*)) OVER() para total, cálculo de porcentajes
     * - Filtros dinámicos: Proceso, área, año de creación, estados de petición principal (lista), código de compañía
     * - Ordenamiento: Por PORCENTAJE_ESTADO DESC (estados con mayor porcentaje primero)
     * - Query dinámica: Construye SQL con concatenación de strings y condiciones condicionales
     * - Relación: A.ID_PETICION_SIGUIENTE = S.ID_PETICION para obtener estadísticas de peticiones siguientes
     *
     * @param proceso ID del proceso (obligatorio)
     * @param area ID del área (obligatorio)
     * @param anio Año de filtro (obligatorio)
     * @param estado Lista de IDs de estados de petición principal separados por coma (opcional, ej. "1,2,3")
     * @param codCia Código de compañía (opcional, puede ser null)
     * @return Lista de objetos PycEstadistEstadoSig con las estadísticas por estado de peticiones siguientes
     */
    public List<PycEstadistEstadoSig> getEstadisticaEstadosSiguientes(Integer proceso, Integer area, Integer anio, String estado, String codCia);

    /**
     * Actualiza el estado de una o múltiples peticiones en el sistema PYCGES.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_UPDATE_PETICION_ESTADO.
     * La función maneja lógica compleja para actualización de estados con validaciones y transiciones:
     * - Soporte para múltiples peticiones: Acepta IDs separados por coma
     * - Validación de transiciones: Verifica estados válidos según proceso y perfil
     * - Equivalencias de estado: Mapea estados de API externa a estados internos
     * - Asignación automática: Asigna perfil según el nuevo estado
     * - Observaciones: Registra observaciones del cambio con complementos automáticos
     * - Programas de transición: Ejecuta programas específicos según la transición
     * - Actualización de solicitudes: Maneja indicadores especiales de actualización
     * - Transacciones: Confirma cambios con COMMIT automático
     * - Usuario personalizado: Utiliza PNumaOUsuario en lugar de USER para operaciones de auditoría
     *
     * Parámetros de la función Oracle:
     * - P_IDPETICION: IDs de peticiones (separados por coma para múltiples)
     * - P_NEWESTADO: Nuevo estado a asignar
     * - p_OBSER: Observación del cambio de estado
     * - P_NEWESTADO_API: Estado equivalente desde API externa (opcional)
     * - P_MOTIVO: Motivo del cambio (opcional)
     * - PNumaOUsuario: Usuario que realiza la operación (opcional, usa g_k_def_usuario por defecto)
     *
     * @param estadoData Datos para actualizar el estado de la petición (incluye numaOUsuario opcional)
     * @return ID de la petición procesada (último ID en caso de múltiples peticiones)
     */
    public Integer updateEstadoPeticion(PycUpdEstadoPet estadoData);

    /**
     * Actualiza la URL y PATH del perfil de un usuario específico.
     * Utiliza la función TRON2000_GT.GC_K_PYC_AUTH_MGT.FN_ACTUALIZA_PERFIL.
     * La función actualiza los campos URL_PERFIL y PATH_PERFIL en la tabla TRC_GT_DL.PYC_USUARIO
     * para el usuario especificado y confirma los cambios con COMMIT automático.
     *
     * @param perfilData Datos para actualizar el path y URL del perfil del usuario
     * @return Resultado de la operación (1=éxito, 0=error)
     */
    public Integer updatePathPerfil(PycUpdPathPerfil perfilData);

    /**
     * Actualiza las fechas de login/logout de un usuario específico según el estado de la sesión.
     * Utiliza la función TRON2000_GT.GC_K_PYC_AUTH_MGT.FN_ACTUALIZA_SESION.
     * La función actualiza los campos FECHA_LOGIN y FECHA_LOGOUT en la tabla TRC_GT_DL.PYC_USUARIO:
     * - Si estado = 1 (login): actualiza FECHA_LOGIN = SYSDATE, mantiene FECHA_LOGOUT
     * - Si estado = 0 (logout): mantiene FECHA_LOGIN, actualiza FECHA_LOGOUT = SYSDATE
     *
     * @param sesionData Datos para actualizar la sesión del usuario
     * @return Resultado de la operación (1=éxito, 0=error)
     */
    public Integer updateSesion(PycUpdSesion sesionData);

    /**
     * Registra una acción específica en la bitácora del sistema PYCGES.
     * Utiliza el procedimiento TRON2000_GT.GC_K_PYC_AUTH_MGT.P_BITACORA_ACCION.
     * El procedimiento es autónomo y registra automáticamente información de auditoría:
     * - MAQUINA_HOST, MAQUINA_OS, MAQUINA_IP (del contexto del sistema)
     * - FECHA (SYSDATE) y USUARIO (P_NUMA_O_USUARIO)
     * - Genera ID automático con secuencia SQ_PYC_BITACORA_ACCION
     *
     * @param bitacoraData Datos de la acción a registrar en la bitácora (incluye numaOUsuario)
     * @return true si la operación se ejecutó correctamente, false en caso de error
     */
    public boolean bitacoraAccion(PycBitacoraAccion bitacoraData);

    /**
     * Gestiona la asignación automática de usuarios para procesos, perfiles, aplicaciones y estados específicos.
     * Utiliza el procedimiento TRON2000_GT.GC_K_PYC_AUTH_MGT.FN_GESTION_USR_ASIGNACION_AUTO.
     * El procedimiento realiza operaciones UPSERT en la tabla PYC_ASIGNACION_AUTOMATICA_USU:
     * - Si existe el registro, actualiza el estado, usuario y fecha_hora
     * - Si no existe, inserta un nuevo registro con todos los datos
     * - Incluye validación automática y manejo de parámetros opcionales
     *
     * @param asignacionData Datos de la asignación automática a gestionar (incluye numaOUsuario opcional)
     * @return true si la operación se ejecutó correctamente, false en caso de error
     */
    public boolean gestionUsrAsignacionAuto(PycGestUsrAsigAuto asignacionData);

    /**
     * Obtiene el estado de un usuario específico mediante sus credenciales de acceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_AUTH_MGT.FN_ESTADO_USUARIO.
     * La función consulta la tabla TRC_GT_DL.pyc_usuario para validar credenciales:
     * - Búsqueda case-insensitive por nombre de usuario (UPPER)
     * - Validación de clave encriptada (UPPER)
     * - Retorna información básica del usuario si las credenciales son válidas
     *
     * @param usuario Nombre de usuario
     * @param clave Clave del usuario
     * @return Lista de objetos PycEstadoUsu con la información del estado del usuario
     */
    public List<PycEstadoUsu> getEstadoUsuario(String usuario, String clave);

    /**
     * Inserta una nueva observación en una petición específica del sistema PYCGES.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_INSERT_OBSERVACION.
     * La función realiza las siguientes operaciones:
     * - Genera un nuevo ID de observación usando la secuencia SQ_PYC_OBSERVACION_PETICION
     * - Inserta el registro en la tabla PYC_OBSERVACION_PETICION con todos los datos proporcionados
     * - Utiliza PRAGMA AUTONOMOUS_TRANSACTION para asegurar la transacción
     * - Utiliza P_NUMA_O_USUARIO en lugar de USER para el campo USUARIO
     * - Retorna el ID de la observación generada
     *
     * Parámetros de la función Oracle:
     * - P_IDPETICION: ID de la petición
     * - P_OBSERVACION: Texto de la observación
     * - P_USUARIO_PET: ID del usuario que realiza la petición
     * - P_ESTADO: Estado de la observación
     * - P_PUBLICO: Indicador público (DEFAULT 'S')
     * - P_NUMA_O_USUARIO: Número o usuario que realiza la operación (DEFAULT g_k_def_usuario)
     *
     * @param observacionData Datos de la observación a insertar
     * @return ID de la observación creada
     */
    public Integer insertObservacion(PycInsertObservacion observacionData);

    /**
     * Obtiene la lista de analistas disponibles para una actividad específica de una petición.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_ANALISTA_ACTIVIDAD.
     * La función realiza una consulta UNION compleja que incluye:
     * - Primera consulta: Analistas asignados a la petición desde PYC_ANALISTA_PETICION
     *   - JOIN con PYC_USUARIO para obtener datos del analista
     *   - LEFT JOIN con PYC_ACTIVIDAD_PETICION para verificar asignación a la actividad
     *   - Filtros: ANA.ID_PETICION = P_PETICION AND ANA.ESTADO = 'ACT'
     * - Segunda consulta: Usuario solicitante de la petición desde PYC_PETICION
     *   - JOIN con PYC_USUARIO para obtener datos del solicitante
     *   - LEFT JOIN con PYC_ACTIVIDAD_PETICION para verificar asignación a la actividad
     *   - Filtro: PET.ID_PETICION = P_PETICION
     * - Campos calculados:
     *   - NOMBRE: Concatenación de PRIMER_NOMBRE + ' ' + PRIMER_APELLIDO
     *   - URL_PERFIL: NVL con DECODE para imagen por defecto según género
     *   - ASIGNA: DECODE que retorna '1' si está asignado a la actividad, '0' si no
     * - Ordenamiento: Por ESTADO_USUARIO, NOMBRE
     *
     * @param peticion ID de la petición
     * @param actividad ID de la actividad
     * @return Lista de objetos PycAnalistaActi con la información de los analistas
     */
    public List<PycAnalistaActi> getAnalistaActividad(Integer peticion, Integer actividad);

    /**
     * Obtiene los datos variables de un formulario específico filtrados por proceso, formulario y sección.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_DAT_VAR_FORM_SECC.
     * La función realiza consultas complejas con múltiples JOINs entre las tablas:
     * - PYC_HTML_FORM_SECC: Relación entre formularios y secciones con orden
     * - PYC_HTML_SECC_DAT_VAR: Datos variables específicos de cada sección
     * - PYC_DATO_VARIABLE_PETICION: Valores específicos de petición (LEFT JOIN)
     * - PYC_DATO_VARIABLE: Definición base de variables
     * - PYC_DATO_VARIABLE_PROCESO: Relación variable-proceso
     * - PYC_HTML_FORM: Información del formulario
     * - PYC_HTML_SECC: Información de la sección
     * Retorna información completa de controles de formulario incluyendo configuración HTML,
     * validaciones, dependencias y valores por defecto o específicos de petición.
     *
     * @param idProceso ID del proceso
     * @param idFormulario ID del formulario
     * @param idSeccion ID de la sección
     * @param idPeticion ID de la petición (opcional)
     * @return Lista de objetos PycGetDatVarFormSec con la información de datos variables del formulario
     */
    public List<PycGetDatVarFormSec> getDatVarFormSecc(Integer idProceso, Integer idFormulario, Integer idSeccion, Integer idPeticion);

    /**
     * Obtiene la bitácora completa de una petición con información detallada de cambios de estado.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBTENER_BITACORA_PETICION.
     * La función realiza consultas complejas con múltiples JOINs y subconsultas para obtener:
     * - Información de cambios de estado: perfiles y estados inicial/final con códigos y nombres
     * - Responsables asignados: Lista concatenada de analistas responsables por petición
     * - Información temporal: Fechas de inicio/fin formateadas y cálculos de tiempo transcurrido
     * - Contenido HTML: Formato complejo que incluye cambios de estado, observaciones y tiempos
     * - Datos de usuario: Usuario que realizó el cambio y URL de perfil con lógica de género
     * - Cálculos temporales: Años, meses, días, horas, minutos y segundos entre cambios
     * La función utiliza LEAD() para obtener fechas de fin, múltiples JOINs entre tablas PYC_*,
     * y genera contenido HTML formateado con información completa de la bitácora.
     *
     * @param idPeticion ID de la petición
     * @return Lista de objetos PycGetBitacoraPet con la información completa de la bitácora
     */
    public List<PycGetBitacoraPet> getBitacoraPeticion(Integer idPeticion);

    /**
     * Obtiene la lista de tipos de petición disponibles para un proceso específico.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_TIPO_PETICION.
     * La función realiza un JOIN entre las tablas:
     * - PYC_TIPO_PETICION: Información básica de los tipos de petición
     * - PYC_PROCESO_TIPO_PETIC: Relación entre procesos y tipos de petición
     * Filtra por estado activo ('ACT') en ambas tablas y por proceso específico.
     * Ordena los resultados por descripción del tipo de petición.
     *
     * @param idProceso ID del proceso
     * @return Lista de objetos PycTipoPeticion con la información de los tipos de petición
     */
    public List<PycTipoPeticion> getTipoPeticion(Integer idProceso);

    /**
     * Obtiene la lista de estados de transición disponibles para un perfil, proceso y estado específicos.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_CAMBIO_ESTADO.
     * La función realiza un JOIN entre las tablas:
     * - PYC_TRANSICION: Configuración de transiciones de estado permitidas
     * - PYC_ESTADO: Información de los estados finales disponibles
     * Filtra por proceso, estado inicial, perfil y visibilidad de la transición.
     * Ordena los resultados por orden de prioridad y nombre del estado.
     *
     * @param idPerfil ID del perfil
     * @param idProceso ID del proceso
     * @param idEstado ID del estado inicial
     * @return Lista de objetos PycEstadoTransc con la información de los estados de transición
     */
    public List<PycEstadoTransc> getCambioEstado(Integer idPerfil, Integer idProceso, Integer idEstado);

    /**
     * Obtiene la lista de documentos asociados a una petición específica.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_DOCUMENTO_PETICION.
     * La función realiza una consulta UNION ALL que combina:
     * - Primera parte: Documentos ya subidos a la petición con información completa
     *   JOIN entre PYC_DOCUMENTO_PETICION, PYC_DOCUMENTO y PYC_USUARIO
     * - Segunda parte: Documentos disponibles para agregar (multi_archivos = 'S')
     *   JOIN entre PYC_PETICION, PYC_DOCUMENTO_TIPO_PETICION y PYC_DOCUMENTO
     *   Excluye documentos ya existentes sin localización
     *   Control especial para perfil de consulta (perfil 13)
     * Ordena los resultados por fecha y localización.
     *
     * @param idPeticion ID de la petición
     * @param perfilAct ID del perfil actual (opcional)
     * @return Lista de objetos PycDocPeticion con la información de los documentos
     */
    public List<PycDocPeticion> getDocumentoPeticion(Integer idPeticion, Integer perfilAct);

    /**
     * Obtiene la lista de documentos requeridos para un tipo de petición específico.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_DOC_X_PETICION.
     * La función realiza un JOIN entre las tablas:
     * - PYC_DOCUMENTO_TIPO_PETICION: Relación entre tipos de petición y documentos con configuración
     * - PYC_DOCUMENTO: Información básica de los documentos
     * Filtra por tipo de petición específico y estado activo en ambas tablas.
     * Ordena los resultados por obligatoriedad (obligatorios primero) y orden configurado.
     *
     * @param idTipo ID del tipo de petición
     * @return Lista de objetos PycDocXPeticion con la información de los documentos requeridos
     */
    public List<PycDocXPeticion> getDocXPeticion(Integer idTipo);

    /**
     * Obtiene los datos variables de formulario basándose en el proceso y tipo de petición.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_DAT_VAR_FORM_SECC_TIPPE.
     * La función internamente:
     * 1. Busca ID_FORMULARIO e ID_SECCION en PYC_PROCESO_TIPO_PETIC basándose en proceso y tipo
     * 2. Llama a FN_GET_DAT_VAR_FORM_SECC con los parámetros resueltos y P_ID_PETICION = NULL
     * 3. Retorna la misma estructura de datos que FN_GET_DAT_VAR_FORM_SECC
     * Simplifica la obtención de datos variables sin necesidad de conocer formulario/sección específicos.
     *
     * @param idProceso ID del proceso
     * @param idTipo ID del tipo de petición
     * @return Lista de objetos PycDatVarForSeccTippe con la información de datos variables del formulario
     */
    public List<PycDatVarForSeccTippe> getDatVarFormSeccTippe(Integer idProceso, Integer idTipo);

    /**
     * Crea o actualiza un dato variable específico de una petición.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_NEW_DAT_VAR_PETICION.
     * La función maneja operaciones UPSERT en la tabla PYC_DATO_VARIABLE_PETICION:
     * - Inserta un nuevo registro si no existe la combinación petición+formulario+sección+dato_variable
     * - Actualiza el registro existente si ya existe la combinación
     * - Valida la integridad referencial de petición, formulario, sección y dato variable
     * - Mantiene auditoría de cambios con usuario y fecha
     *
     * Parámetros de la función Oracle:
     * - P_ID_PETICION: ID de la petición (Int32)
     * - P_ID_FORMULARIO: ID del formulario (Int32)
     * - P_ID_SECCION: ID de la sección (Int32)
     * - P_ID_DATO_VAR: ID del dato variable (Int32)
     * - P_VALOR: Valor del dato variable (Varchar2)
     * - P_DESCRIPCION: Descripción adicional (Varchar2, opcional)
     *
     * @param datVarData Datos del variable de petición a crear o actualizar
     * @return ID del registro creado o actualizado (NUMBER)
     */
    public Integer newDatVarPeticion(PycNewDatVarPet datVarData);

    /**
     * Obtiene la lista de perfiles asociados a un proceso específico.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_PERFIL.
     * La función realiza JOINs entre las tablas:
     * - PYC_proceso_perfil: Relación proceso-perfil (tabla principal)
     * - PYC_proceso: Información del proceso (nombre)
     * - PYC_perfil: Información del perfil (nombre)
     * - PYC_list_val: Configuración de exclusiones (tipo 'ND-PERFIL-EXCLUYE')
     * Filtra por proceso específico, estado activo y excluye perfiles configurados.
     *
     * @param idProceso ID del proceso
     * @return Lista de objetos PycGetPerfiles con la información de perfiles del proceso
     */
    public List<PycGetPerfiles> getPerfilesPorProceso(Integer idProceso);

    /**
     * Obtiene la lista de estados asociados a un perfil específico dentro de un proceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_ESTADO_PERFIL.
     * La función realiza JOINs entre las tablas:
     * - PYC_perfil_estado: Relación perfil-estado (tabla principal)
     * - PYC_perfil: Información del perfil (nombre)
     * - PYC_estado: Información del estado (nombre)
     * - PYC_list_val: Configuración de exclusiones (tipo 'ND-PERFIL-EXCLUYE')
     * Filtra por proceso y perfil específicos, excluyendo perfiles configurados.
     *
     * @param idProceso ID del proceso
     * @param idPerfil ID del perfil
     * @return Lista de objetos PycGetEstadoPerfilCod con la información de estados del perfil
     */
    public List<PycGetEstadoPerfilCod> getEstadoPerfilCod(Integer idProceso, Integer idPerfil);

    /**
     * Asigna una petición específica a un desarrollador/analista.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_ASIGNA_PETICION_DESA.
     * La función maneja lógica compleja de asignación:
     * - Verifica configuración de cambio de solicitante (mca_cambio_sol)
     * - Actualiza solicitante y responsable según configuración
     * - Maneja operaciones UPSERT en tabla PYC_ANALISTA_PETICION
     * - Inactiva asignaciones previas si es necesario
     * - Registra operación en PYC_LOG para auditoría
     *
     * Parámetros de la función Oracle:
     * - P_IDAPLIC: ID de la aplicación (Integer)
     * - P_IDDESA: ID del desarrollador/analista (Integer)
     * - P_IDPETICION: ID de la petición (Integer)
     * - P_PERFIL: ID del perfil (Integer)
     * - p_numa_o_usuario: Número de usuario para identificación (VARCHAR2, opcional, default NULL)
     *
     * @param asignacionData Datos de la asignación de petición a desarrollador (incluye numaOUsuario opcional)
     * @return ID de la petición procesada (NUMBER)
     */
    public Integer asignaPeticionDesa(PycAsignaPeticionDesa asignacionData);

    /**
     * Obtiene la lista de responsables asociados a una petición específica.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_RESPONSABLE_PETICION.
     * La función retorna información de:
     * - Solicitante de la petición con datos de usuario y fechas
     * - Usuarios asignados con sus perfiles y aplicaciones
     * - Estado de conexión de cada usuario
     * - Información detallada de asignaciones y fechas
     *
     * Parámetros de la función Oracle:
     * - pIdPeticion: ID de la petición (NUMBER)
     * - P_NUMA_O_USUARIO: Número o usuario que realiza la consulta (VARCHAR2, opcional)
     *
     * @param idPeticion ID de la petición
     * @param numaOUsuario Número o usuario que realiza la consulta (opcional)
     * @return Lista de objetos PycRespPeti con información de responsables
     */
    public List<PycRespPeti> getResponsablePeticion(Integer idPeticion, String numaOUsuario);

    /**
     * Obtiene la lista de actividades asociadas a una petición específica.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_ACT_PETICION.
     * La función realiza JOINs entre las tablas:
     * - PYC_ACTIVIDAD_PETICION: Información principal de actividades (tabla principal)
     * - PYC_CATEGORIA_TABLERO: Información de categorías del tablero
     * - PYC_USUARIO: Información del usuario asignado
     * Retorna información detallada de actividades incluyendo:
     * - Datos básicos de la actividad (ID, nombre, descripción)
     * - Información de horas (base, real, pendientes calculadas)
     * - Fechas formateadas (inicio y fin en formato DD/MM/YYYY)
     * - Usuario asignado con nombre completo y URL de perfil
     * - Estado y categoría del tablero
     *
     * Parámetros de la función Oracle:
     * - nIdPeticion: ID de la petición (NUMBER, requerido)
     * - PIdActividad: ID de la actividad (NUMBER, opcional)
     *
     * @param idPeticion ID de la petición
     * @param idActividad ID de la actividad (opcional)
     * @return Lista de objetos PycActPeticion con información de actividades
     */
    public List<PycActPeticion> getActPeticion(Integer idPeticion, Integer idActividad);

    /**
     * Actualiza los datos de una petición existente en el sistema PYCGES.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_UPDATE_PETICION.
     * Permite actualizar información básica, datos del cliente y parámetros adicionales.
     * Automáticamente inserta una observación de la actualización.
     *
     * Parámetros de la función Oracle:
     * - P_IDPETICION: ID de la petición a actualizar (Integer, obligatorio)
     * - P_IDSOL_USER: ID del usuario solicitante (Integer, obligatorio)
     * - P_NOM_PETICION: Nombre de la petición (VARCHAR2, obligatorio)
     * - P_DESC_PETICION: Descripción de la petición (VARCHAR2, obligatorio)
     * - P_IDTIPO: ID del tipo de petición (Integer, obligatorio)
     * - P_OBSERVACION: Observación de la actualización (VARCHAR2, obligatorio)
     * - P_CODCIA: Código de la compañía (VARCHAR2, obligatorio)
     * - P_NOMCLIE: Nombre del cliente (VARCHAR2, opcional)
     * - P_TELCLIE: Teléfono del cliente (VARCHAR2, opcional)
     * - P_EMAILCLIE: Correo electrónico del cliente (VARCHAR2, opcional)
     * - P_ORIGEN: Origen de la petición (VARCHAR2, opcional)
     * - P_CLARITY: Código Clarity asociado (VARCHAR2, opcional)
     * - P_PRIORIDAD: Prioridad de la petición (VARCHAR2, opcional)
     * - P_TIPO_CLIENTE: Tipo de cliente (VARCHAR2, opcional)
     * - P_COD_CLIENTE: Código del cliente (VARCHAR2, opcional)
     * - P_NO_POLIZA: Número de póliza (VARCHAR2, opcional)
     * - P_TIP_SERVICIO: Tipo de servicio (VARCHAR2, opcional)
     * - P_CAUSA: Causa de la petición (VARCHAR2, opcional)
     * - P_GRAVEDAD: Gravedad de la petición (VARCHAR2, opcional)
     * - P_NUMA_O_USUARIO: Usuario para auditoría (VARCHAR2, opcional)
     *
     * @param peticionData Datos de la petición a actualizar (incluye todos los parámetros)
     * @return ID de la petición actualizada (NUMBER)
     */
    public Integer updatePeticion(PycUpdPeticion peticionData);

    /**
     * Crea una nueva solicitud de Recursos Humanos en el sistema PYCGES.
     * Utiliza la función TRON2000_GT.GC_K_PYC_RRHH_MGT.p_crea_solicitud.
     *
     * Parámetros de la función Oracle:
     * - p_nomcli: Nombre del cliente (VARCHAR2, obligatorio)
     * - p_telcli: Teléfono del cliente (VARCHAR2, obligatorio)
     * - p_emailcli: Correo electrónico del cliente (VARCHAR2, obligatorio)
     * - p_fec_nac: Fecha de nacimiento (VARCHAR2, obligatorio)
     * - p_DPI: Número de DPI (VARCHAR2, obligatorio)
     * - p_nit: Número de NIT (VARCHAR2, obligatorio)
     * - p_desc_esc: Descripción de escolaridad (VARCHAR2, obligatorio)
     * - p_cod_esc: Código de escolaridad (VARCHAR2, obligatorio)
     * - p_titulo: Título académico (VARCHAR2, obligatorio)
     * - p_direccion: Dirección (VARCHAR2, obligatorio)
     * - p_cod_dpto: Código de departamento (VARCHAR2, obligatorio)
     * - p_desc_dpto: Descripción de departamento (VARCHAR2, obligatorio)
     * - p_cod_trb: Código de trabajo (VARCHAR2, obligatorio)
     * - P_desc_trb: Descripción de trabajo (VARCHAR2, obligatorio)
     * - P_cod_seg: Código de seguro (VARCHAR2, obligatorio)
     * - P_desc_seg: Descripción de seguro (VARCHAR2, obligatorio)
     * - P_preten: Pretensión salarial (VARCHAR2, obligatorio)
     * - P_cod_plaza: Código de plaza (VARCHAR2, obligatorio)
     * - P_desc_plaz: Descripción de plaza (VARCHAR2, obligatorio)
     *
     * @param solicitudData Datos de la solicitud de RH a crear
     * @return ID de la solicitud creada (NUMBER)
     */
    public Integer creaSolicitudRH(PycCreaSoliRH solicitudData);

    /**
     * Obtiene la lista de documentos no obligatorios que faltan por adjuntar a una petición.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_DOC_NOOBLIGATORIOS.
     * La función realiza JOINs entre las tablas:
     * - PYC_DOCUMENTO_TIPO_PETICION: Relación entre tipos de petición y documentos
     * - PYC_DOCUMENTO: Información básica de los documentos
     * Filtra documentos no obligatorios que no han sido adjuntados a la petición específica.
     * Excluye documentos ya presentes en PYC_DOCUMENTO_PETICION para la petición dada.
     *
     * @param idPeticion ID de la petición
     * @return Lista de objetos PycDocNoObli con la información de los documentos no obligatorios faltantes
     */
    public List<PycDocNoObli> getDocNoObligatorios(Integer idPeticion);

    /**
     * Obtiene la lista de categorías activas del tablero asociadas a un proceso específico.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBTENER_CATEGORIA_ACT.
     *
     * @param idProceso ID del proceso
     * @return Lista de objetos PycCatAct con las categorías activas
     */
    public List<PycCatAct> getCategoriaAct(Integer idProceso);

    /**
     * Inserta una nueva actividad en una petición específica.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_INSERT_ACTIVIDAD.
     * Automáticamente actualiza las fechas de inicio y fin de la petición.
     *
     * @param actividadData Datos de la actividad a insertar
     * @return ID de la actividad creada
     */
    public Integer insertActividad(PycInserAct actividadData);

    /**
     * Edita una actividad existente de una petición específica.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_EDIT_ACTIVIDAD.
     * Automáticamente recalcula las fechas de inicio y fin de la petición.
     *
     * @param actividadData Datos de la actividad a editar
     * @return ID de la petición actualizada
     */
    public Integer editActividad(PycEditAct actividadData);

    /**
     * Actualiza las horas reales y estado de una actividad específica.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_UPDATE_ACTIVIDAD.
     * Automáticamente inserta una observación de la actualización.
     *
     * @param actividadData Datos de la actividad a actualizar
     * @return ID de la petición actualizada
     */
    public Integer updateActividad(PycUpdAct actividadData);

    /**
     * Actualiza la encuesta de satisfacción de una petición específica.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_UPDATE_ENCUESTA.
     * Automáticamente establece el usuario que realiza la encuesta (ENCU_USER = USER)
     * y la fecha actual (ENCU_FECHA = SYSDATE).
     *
     * @param encuestaData Datos de la encuesta a actualizar
     * @return ID de la petición actualizada
     */
    public String updateEncuesta(PycUpdEnc encuestaData);
}
