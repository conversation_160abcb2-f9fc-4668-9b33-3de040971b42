package com.mapfre.tron.gt.api.dl;

import com.mapfre.tron.gt.api.model.Cobro;
import com.mapfre.tron.gt.api.model.Entidad;
import com.mapfre.tron.gt.api.model.InsertarDetalleRutaDiarioRequest;
import com.mapfre.tron.gt.api.model.InsertarDetalleRuta2Request;
import com.mapfre.tron.gt.api.model.TipoMedioPagoRequest;
import com.mapfre.tron.gt.api.model.AvisoReciboRutaRequest;
import com.mapfre.tron.gt.api.model.ReciboRutaRequest;
import com.mapfre.tron.gt.api.model.AutenticarUsuarioResponseUsuario;
import com.mapfre.tron.gt.api.model.ObtenerRolesUsuarioResponseRoles;
import com.mapfre.tron.gt.api.model.CajeroUsuarioResponseCajeros;
import com.mapfre.tron.gt.api.model.AccionesPorRolesResponseAcciones;
import com.mapfre.tron.gt.api.model.*;

import java.time.LocalDate;
import java.util.List;

import java.util.ArrayList;
import java.util.Date;

public interface IDlCobroMovil {
    /**
     * Obtiene la lista de entidades financieras según sistema y moneda
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_listarEntidades.
     *
     * @param sistema Código del sistema
     * @param moneda Código de moneda
     * @return Lista de entidades financieras
     */
    public ArrayList<Entidad> ListarEntidades(Integer sistema, String moneda);
    
    /**
     * Obtiene la lista de opciones de cobro
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_listarOpcionCobro.
     *
     * @return Lista de opciones de cobro
     */
    public ArrayList<Cobro> ListarOpcioensCobro();
    
    /**
     * Crea una ruta diaria para cobros
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_insertarRutaDiaria.
     *
     * @param idUsuarioCrea ID del usuario que crea la ruta
     * @param idUsuarioAsignado ID del usuario al que se asigna la ruta
     * @return ID de la ruta creada
     */
    public Integer CrearRutaDiaria(Integer idUsuarioCrea, Integer idUsuarioAsignado);
    
    /**
     * Crea una ruta para cobros
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_insertarRuta.
     *
     * @param idUsuarioCrea ID del usuario que crea la ruta
     * @param idUsuarioAsignado ID del usuario al que se asigna la ruta
     * @return ID de la ruta creada
     */
    public Integer CrearRuta(Integer idUsuarioCrea, Integer idUsuarioAsignado);
    
    /**
     * Modifica una ruta existente
     * Utiliza el procedimiento TRON2000_GT.gc_k_cobro_movil_mgt.sp_modificarRuta.
     *
     * @param idUsuarioCrea ID del usuario que modifica la ruta
     * @param idUsuarioAsignado ID del usuario al que se asigna la ruta
     * @param idRuta ID de la ruta a modificar
     * @return ID de la ruta modificada
     */
    public Integer ModificarRuta(Integer idUsuarioCrea, Integer idUsuarioAsignado, Integer idRuta);
    
    /**
     * Inserta detalle de ruta diaria
     * Utiliza el procedimiento TRON2000_GT.gc_k_cobro_movil_mgt.sp_insertDetalleRutaDiario.
     *
     * @param requestParams Parámetros para insertar el detalle de ruta diaria
     * @return true si la operación fue exitosa
     */
    public boolean InsertarDetalleRutaDiario(InsertarDetalleRutaDiarioRequest requestParams);
    
    /**
     * Inserta detalle de ruta
     * Utiliza el procedimiento TRON2000_GT.gc_k_cobro_movil_mgt.sp_insertDetalleRuta2.
     *
     * @param requestParams Parámetros para insertar el detalle de ruta
     * @return true si la operación fue exitosa
     */
    public boolean InsertarDetalleRuta2(InsertarDetalleRuta2Request requestParams);
    
    /**
     * Inserta tipo de medio de pago
     * Utiliza el procedimiento TRON2000_GT.gc_k_cobro_movil_mgt.sp_insertarTipoMedioPago.
     *
     * @param requestParams Parámetros para insertar el tipo de medio de pago
     * @return true si la operación fue exitosa
     */
    public boolean InsertarTipoMedioPago(TipoMedioPagoRequest requestParams);
    
    /**
     * Actualiza aviso de recibo de ruta
     * Utiliza el procedimiento TRON2000_GT.gc_k_cobro_movil_mgt.sp_updateAvisoReciboRuta.
     *
     * @param requestParams Parámetros para actualizar el aviso de recibo
     * @return true si la operación fue exitosa
     */
    public boolean UpdateAvisoReciboRuta(AvisoReciboRutaRequest requestParams);
    
    /**
     * Actualiza recibo de ruta
     * Utiliza el procedimiento TRON2000_GT.gc_k_cobro_movil_mgt.sp_updateReciboRuta.
     *
     * @param requestParams Parámetros para actualizar el recibo
     * @return Correos electrónicos asociados al recibo
     */
    public String UpdateReciboRuta(ReciboRutaRequest requestParams);
    
    /**
     * Autentica un usuario con credenciales
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_autenticarUsuario.
     *
     * @param usuario Nombre de usuario
     * @param clave Contraseña del usuario
     * @return Información del usuario autenticado
     */
    public AutenticarUsuarioResponseUsuario AutenticarUsuario(String usuario, String clave);
    
    /**
     * Autentica un usuario mediante Active Directory
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_autenticarUsuarioAD.
     *
     * @param usuario Nombre de usuario
     * @return Información del usuario autenticado
     */
    public AutenticarUsuarioResponseUsuario AutenticarUsuarioAD(String usuario);
    
    /**
     * Obtiene los roles asignados a un usuario
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_obtenerRolesUsuario.
     *
     * @param idUsuario ID del usuario
     * @return Lista de roles del usuario
     */
    public ArrayList<ObtenerRolesUsuarioResponseRoles> ObtenerRolesUsuario(Integer idUsuario);
    
    /**
     * Obtiene los cajeros asociados a un usuario
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_listarCajeroUsuario.
     *
     * @param usuario ID del usuario
     * @return Lista de cajeros asociados al usuario
     */
    public ArrayList<CajeroUsuarioResponseCajeros> ListarCajeroUsuario(Integer usuario);
    
    /**
     * Obtiene las acciones permitidas para roles específicos
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_obtenerAccionesPorRoles.
     *
     * @param roles Cadena de roles separados por coma
     * @return Lista de acciones permitidas para los roles
     */
    public ArrayList<AccionesPorRolesResponseAcciones> ObtenerAccionesPorRoles(String roles);

    /**
     * Obtiene los ids de las rutas cobradas
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_obtenerRutasUbicacion.
     *
     * @return Lista de ids rutas
     */
    public RutasCobradasResponse getRutasCobradas();

    /**
     * Obtiene información de rutas con localización
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.obtenerRutasLocalizacion.
     *
     * @param fechaInicio Fecha de inicio
     * @param fechaFin Fecha de fin
     * @param idRuta ID de la ruta
     * @return Lista de rutas con información de localización
     */
    List<RutaLocalizacionResponse> obtenerRutasLocalizacion(LocalDate fechaInicio, LocalDate fechaFin, Integer idRuta);
    
    /**
     * Obtiene información de localización de pagos
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.obtenerLocalizacionPagos.
     *
     * @param fechaInicio Fecha de inicio
     * @param fechaFin Fecha de fin
     * @param idRuta ID de la ruta
     * @return Lista de pagos con información de localización
     */
    List<LocalizacionPagosResponse> obtenerLocalizacionPagos(LocalDate fechaInicio, LocalDate fechaFin, Integer idRuta);
    
    /**
     * Obtiene información para llenar el detalle de cierre de caja
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.infoRutaCierreCajaDetalle.
     *
     * @param idUsuario ID del usuario
     * @return Lista con información de detalle de cierre de caja
     */
    List<LlenarDetalleCierreCajaResponse> llenarDetalleCierreCaja(Integer idUsuario);
    
    /**
     * Obtiene información de rutas para cierre de caja
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.infoRutaCierreCaja.
     *
     * @param idUsuario ID del usuario
     * @return Lista con información de rutas para cierre de caja
     */
    List<InfoRutaCierreCajaResponse> getInfoRutaCierreCaja(Integer idUsuario);
    
    /**
     * Obtiene información de rutas para un usuario y fecha específicos
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_Mod_Ruta.
     *
     * @param idUsuario ID del usuario
     * @param fecha Fecha en formato DD/MM/YYYY
     * @return Lista con información de rutas
     */
    List<InfoRutaResponse> getInfoRuta(Integer idUsuario, String fecha);
    
    /**
     * Obtiene el porcentaje de avance de una ruta o del total de rutas de un usuario
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fnPromedios.
     *
     * @param idUsuario ID del usuario
     * @param ruta ID de la ruta (para tipo=2)
     * @param tipo Tipo de consulta (1=rutas del usuario, 2=detalle de una ruta)
     * @param fecha Fecha en formato DD/MM/YYYY puede ser null
     * @return Porcentaje de avance
     */
    InfoPromedioResponse getPromedios(Integer idUsuario, Integer ruta, Integer tipo, String fecha);
    
    /**
     * Obtiene las pólizas para facturas de una ruta específica
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_Mod_Ruta_PolizaFacturas.
     *
     * @param idRuta ID de la ruta
     * @return Lista de pólizas para facturas de la ruta
     */
    List<ModRutaPolizaFacturasResponse> getModRutaPolizaFacturas(Integer idRuta);

    /**
     * Obtiene las pólizas de una ruta específica
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_Mod_Ruta_Poliza.
     *
     * @param idRuta ID de la ruta
     * @return Lista de pólizas de la ruta
     */
    List<ModRutaPolizaResponse> getModRutaPoliza(Integer idRuta);

    /**
     * Obtiene los recibos cobrados de una póliza en una ruta específica
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_Mod_Ruta_Poliza_Recibo_Cobrados.
     *
     * @param idRuta ID de la ruta
     * @param idePol ID de la póliza (opcional)
     * @param numCertis Número de certificado (opcional)
     * @return Lista de recibos cobrados
     */
    List<InfoRecibosPolizaRutaCobradosResponse> getInfoRecibosPolizaRutaCobrados(Integer idRuta, String idePol, String numCertis);

    /**
     * Obtiene los recibos de una póliza en una ruta específica
     * Utiliza la función TRON2000_GT.gc_k_cobro_movil_mgt.fn_Mod_Ruta_Poliza_Recibo.
     *
     * @param idRuta ID de la ruta
     * @param idePol ID de la póliza
     * @param numCertis Número de certificado
     * @return Lista de recibos de la póliza
     */
    List<InfoRecibosPolizaRutaResponse> getInfoRecibosPolizaRuta(Integer idRuta, String idePol, String numCertis);

    /**
     * Obtiene la información de firma de un recibo específico
     *
     * @param idRuta ID de la ruta
     * @param recibo Número de recibo
     * @return Lista de información de firma del recibo
     */
    List<InfoFirmaReciboResponse> getInfoFirmaRecibo(String idRuta, String recibo);

    /**
     * Obtiene la lista de cobradores
     *
     * @return Lista de cobradores
     */
    List<CobradoresResponse> getCobradores();

    /**
     * Obtiene la lista de tipos de pago
     *
     * @return Lista de tipos de pago
     */
    List<TipoPagoResponse> getTipoPago();
}
