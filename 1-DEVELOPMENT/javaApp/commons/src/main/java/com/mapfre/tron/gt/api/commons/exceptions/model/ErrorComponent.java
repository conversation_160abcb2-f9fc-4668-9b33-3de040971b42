package com.mapfre.tron.gt.api.commons.exceptions.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * ErrorComponent
 */
@SuppressWarnings("serial")
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class ErrorComponent implements Serializable {
	
	@JsonProperty("code")
	private String code = null;

	@JsonProperty("message")
	private String message = null;

	@JsonProperty("component")
	private String component = null;

	@JsonProperty("rootcase")
	private String rootcase = null;

	@JsonProperty("info")
	@Valid
	private List<ErrorInfo> info = null;

	public ErrorComponent code(String code) {
		this.code = code;
		return this;
	}

	/**
	 * Error code
	 * 
	 * @return code
	 **/
	@ApiModelProperty(example = "403", required = true, value = "Error code")
	@NotNull

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public ErrorComponent message(String message) {
		this.message = message;
		return this;
	}

	/**
	 * Error message
	 * 
	 * @return message
	 **/
	@ApiModelProperty(example = "Internal error", value = "Error message")

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public ErrorComponent component(String component) {
		this.component = component;
		return this;
	}

	/**
	 * Error component
	 * 
	 * @return component
	 **/
	@ApiModelProperty(example = "ProcessImpl", value = "Error component")

	public String getComponent() {
		return component;
	}

	public void setComponent(String component) {
		this.component = component;
	}

	public ErrorComponent rootcase(String rootcase) {
		this.rootcase = rootcase;
		return this;
	}

	/**
	 * Error cause
	 * 
	 * @return rootcase
	 **/
	@ApiModelProperty(example = "NullPointerException", value = "Error cause")

	public String getRootcase() {
		return rootcase;
	}

	public void setRootcase(String rootcase) {
		this.rootcase = rootcase;
	}

	public ErrorComponent info(List<ErrorInfo> info) {
		this.info = info;
		return this;
	}

	public ErrorComponent addInfoItem(ErrorInfo infoItem) {
		if (this.info == null) {
			this.info = new ArrayList<>();
		}
		this.info.add(infoItem);
		return this;
	}

	/**
	 * Error information
	 * 
	 * @return info
	 **/
	@ApiModelProperty(value = "Error information")

	@Valid

	public List<ErrorInfo> getInfo() {
		return info;
	}

	public void setInfo(List<ErrorInfo> info) {
		this.info = info;
	}

	@Override
	public boolean equals(java.lang.Object o) {
		if (this == o) {
			return true;
		}
		if (o == null || getClass() != o.getClass()) {
			return false;
		}
		ErrorComponent errorComponent = (ErrorComponent) o;
		return Objects.equals(this.code, errorComponent.code) && Objects.equals(this.message, errorComponent.message)
				&& Objects.equals(this.component, errorComponent.component)
				&& Objects.equals(this.rootcase, errorComponent.rootcase)
				&& Objects.equals(this.info, errorComponent.info);
	}

	@Override
	public int hashCode() {
		return Objects.hash(code, message, component, rootcase, info);
	}

	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder();
		sb.append("class ErrorComponent {\n");

		sb.append("    code: ").append(toIndentedString(code)).append("\n");
		sb.append("    message: ").append(toIndentedString(message)).append("\n");
		sb.append("    component: ").append(toIndentedString(component)).append("\n");
		sb.append("    rootcase: ").append(toIndentedString(rootcase)).append("\n");
		sb.append("    info: ").append(toIndentedString(info)).append("\n");
		sb.append("}");
		return sb.toString();
	}

	/**
	 * Convert the given object to string with each line indented by 4 spaces
	 * (except the first line).
	 */
	private String toIndentedString(java.lang.Object o) {
		if (o == null) {
			return "null";
		}
		return o.toString().replace("\n", "\n    ");
	}
}
