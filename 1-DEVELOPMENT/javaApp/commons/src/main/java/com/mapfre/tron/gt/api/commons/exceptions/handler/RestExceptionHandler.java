package com.mapfre.tron.gt.api.commons.exceptions.handler;

import java.util.Date;
import java.util.NoSuchElementException;

import javax.servlet.http.HttpServletRequest;

import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import com.mapfre.tron.gt.api.bl.Constants;
import com.mapfre.tron.gt.api.commons.exceptions.model.Error;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@ControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class RestExceptionHandler {

	@ExceptionHandler(NoSuchElementException.class)
	protected ResponseEntity<Error> handleException(final Exception ex, final HttpServletRequest request) {

		boolean isListResponse = false;
		if (request.getAttribute(Constants.LIST_RESPONSE) != null) {
			isListResponse = (Boolean) request.getAttribute(Constants.LIST_RESPONSE);
		}

		HttpStatus status;
		String msg = ex.getMessage();
		log.error(msg, request, HttpStatus.INTERNAL_SERVER_ERROR, ex);
		Error currentError = new Error(
				"4003", 
				msg, 
				"Functional Error", 
				"SR Controller", 
				"NoSuchElementException", 
				"",
				"API EDGE", 
				new Date(), 
				null);

		if (!isListResponse) {
			status = HttpStatus.NOT_FOUND;
		} else {
			return new ResponseEntity<>(HttpStatus.NO_CONTENT);
		}

		currentError.setErrors(null);
		return new ResponseEntity<>(currentError, status);
	}

}
