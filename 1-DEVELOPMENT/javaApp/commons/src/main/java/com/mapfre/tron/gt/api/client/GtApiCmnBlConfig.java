package com.mapfre.tron.gt.api.client;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.mapfre.tron.api.cmn.client.api.AccessDataApi;
import com.mapfre.tron.api.cmn.client.api.CacheApi;
import com.mapfre.tron.api.cmn.client.api.CommonApi;
import com.mapfre.tron.api.cmn.client.api.ContextApi;
import com.mapfre.tron.api.cmn.client.api.FileApi;
import com.mapfre.tron.api.cmn.client.api.MovementDefinitionApi;
import com.mapfre.tron.api.cmn.client.api.MovementRecordApi;
import com.mapfre.tron.api.cmn.client.api.NewtronOperationApi;
import com.mapfre.tron.api.cmn.client.api.ReasonEventLockingDefinitionApi;
import com.mapfre.tron.api.cmn.client.api.SelfServiceApi;
import com.mapfre.tron.api.cmn.client.api.SettingsApi;
import com.mapfre.tron.api.cmn.client.api.UserInfoApi;
import com.mapfre.tron.api.cmn.client.api.UserInformationApi;
import com.mapfre.tron.api.cmn.client.api.ValuesoptionsListApi;
import com.mapfre.tron.api.cmn.client.api.VariableDefinitionApi;
import com.mapfre.tron.api.cmn.client.invoker.ApiClient;

@Configuration
@EnableTransactionManagement
@DependsOn("jasyptPasswordManager")
public class GtApiCmnBlConfig {

	@Value("${app.env.tron.api.cmn.basePath}")
	private String basePath;

	@Value("${app.env.tron.api.cmn.userName}")
	private String userName;

	@Value("#{'${app.env.tron.api.cmn.password}' == '' ? '' : environmentUtils.resolve('${app.env.tron.api.cmn.password}')}")
	private String password;

	@Primary
	@Bean(name = "apiClientCmn")
	public ApiClient apiClient() {
		ApiClient apiClient = new ApiClient();
		apiClient.setBasePath(basePath);
		apiClient.setUsername(userName);
		apiClient.setPassword(password);
		return apiClient;
	}

	@Primary
	@Bean
	public CommonApi commonApi() {
		return new CommonApi(apiClient());
	}

	@Primary
	@Bean
	public MovementRecordApi movementRecordApi() {
		return new MovementRecordApi(apiClient());
	}

	@Primary
	@Bean(name = "cmnSettingsApi")
	public SettingsApi settingsApi() {
		return new SettingsApi(apiClient());
	}

	@Primary
	@Bean
	public VariableDefinitionApi variableDefinitionApi() {
		return new VariableDefinitionApi(apiClient());
	}

	@Primary
	@Bean
	public ValuesoptionsListApi valuesoptionsListApi() {
		return new ValuesoptionsListApi(apiClient());
	}

	@Primary
	@Bean
	public SelfServiceApi selfServiceApi() {
		return new SelfServiceApi(apiClient());
	}

	@Primary
	@Bean
	public NewtronOperationApi newtronOperationApi() {
		return new NewtronOperationApi(apiClient());
	}

	@Primary
	@Bean
	public FileApi fileApi() {
		return new FileApi(apiClient());
	}

	@Bean
	public ReasonEventLockingDefinitionApi reasonEventLockingDefinitionApi() {
		return new ReasonEventLockingDefinitionApi(apiClient());
	}

	@Bean
	public MovementDefinitionApi movementDefinitionApi() {
		return new MovementDefinitionApi(apiClient());
	}

	@Primary
	@Bean
	public ContextApi contextApi() {
		return new ContextApi(apiClient());
	}

	@Primary
	@Bean(name = "cmnUserInformationApi")
	public UserInformationApi userInformationApi() {
		return new UserInformationApi(apiClient());
	}

	@Primary
	@Bean
	public AccessDataApi accessDataApi() {
		return new AccessDataApi(apiClient());
	}

	@Primary
	@Bean(name = "cmnCacheApi")
	public CacheApi cacheApi() {
		return new CacheApi(apiClient());
	}

	@Primary
	@Bean
	public UserInfoApi userInfoApi() {
		return new UserInfoApi(apiClient());
	}

}
