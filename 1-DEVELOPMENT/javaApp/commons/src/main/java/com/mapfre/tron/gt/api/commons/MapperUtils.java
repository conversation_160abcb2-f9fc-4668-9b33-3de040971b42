package com.mapfre.tron.gt.api.commons;

import java.math.BigDecimal;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;

public final class MapperUtils {

    public static String convertBigDecimalToString(Object value) {
        String result = null;
        if (value != null && value instanceof BigDecimal) {
            result = ((BigDecimal) value).toString();
            return result;
        }
        return result;
    }

    public static Integer convertBigDecimalToInteger(Object value) {
        if (value != null && value instanceof BigDecimal) {
            return ((BigDecimal) value).intValue();
        }
        return null;
    }

    public static String convertTimestampToString(Object value) {
        if (value != null && value instanceof Timestamp) {
            SimpleDateFormat formatter = new SimpleDateFormat("ddMMyyyy");
            return formatter.format(((Timestamp) value).getTime());
        }
        return null;
    }

    public static Integer convertStringToInteger(Object value) {
        if (value != null && value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                // Log the error or handle it as needed
                return null; // Return null if the string cannot be converted to an integer
            }
        }
        return null; // Return null if the value is null
    }

    public static boolean isColumnPresent(ResultSetMetaData rsMetaData, String columnName, int columnCount) throws SQLException {
        for (int i = 1; i <= columnCount; i++) {
            if (rsMetaData.getColumnName(i).equalsIgnoreCase(columnName)) {
                return true;
            }
        }
        return false;
    }

}
