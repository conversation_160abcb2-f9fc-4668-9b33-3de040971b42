package com.mapfre.tron.gt.api.client;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.mapfre.tron.api.thp.client.api.ActivityApi;
import com.mapfre.tron.api.thp.client.api.AddressApi;
import com.mapfre.tron.api.thp.client.api.AgentApi;
import com.mapfre.tron.api.thp.client.api.BankEntityApi;
import com.mapfre.tron.api.thp.client.api.BankOfficeApi;
import com.mapfre.tron.api.thp.client.api.CacheApi;
import com.mapfre.tron.api.thp.client.api.CommonApi;
import com.mapfre.tron.api.thp.client.api.ContactApi;
import com.mapfre.tron.api.thp.client.api.CustomerSegmentationTypeApi;
import com.mapfre.tron.api.thp.client.api.CustomerSegmentationTypesApi;
import com.mapfre.tron.api.thp.client.api.DocumentTypeApi;
import com.mapfre.tron.api.thp.client.api.EmployeeAgentApi;
import com.mapfre.tron.api.thp.client.api.GenericThirdPartyApi;
import com.mapfre.tron.api.thp.client.api.InsuranceCompanyApi;
import com.mapfre.tron.api.thp.client.api.InsuredPartyApi;
import com.mapfre.tron.api.thp.client.api.PaymentCollectionMethodApi;
import com.mapfre.tron.api.thp.client.api.PersonApi;
import com.mapfre.tron.api.thp.client.api.ProfessionApi;
import com.mapfre.tron.api.thp.client.api.SettingsApi;
import com.mapfre.tron.api.thp.client.api.SubagentApi;
import com.mapfre.tron.api.thp.client.api.SupplierApi;
import com.mapfre.tron.api.thp.client.api.ThirdPartyApi;
import com.mapfre.tron.api.thp.client.api.ThirdPartyNotificationApi;
import com.mapfre.tron.api.thp.client.api.CommissionChartApi;
import com.mapfre.tron.api.thp.client.api.TimeZoneApi;
import com.mapfre.tron.api.thp.client.api.UserInformationApi;
import com.mapfre.tron.api.thp.client.invoker.ApiClient;

@Configuration
@EnableTransactionManagement
@DependsOn("jasyptPasswordManager")
public class GtApiThpBlConfig {

	@Value("${app.env.tron.api.thp.basePath}")
	private String basePath;

	@Value("${app.env.tron.api.thp.userName}")
	private String userName;

	@Value("#{'${app.env.tron.api.thp.password}' == '' ? '' : environmentUtils.resolve('${app.env.tron.api.thp.password}')}")
	private String password;

	@Bean(name = "apiClientThp")
	public ApiClient apiClient() {
		ApiClient apiClient = new ApiClient();
		apiClient.setBasePath(basePath);
		apiClient.setUsername(userName);
		apiClient.setPassword(password);
		return apiClient;
	}

	@Primary
	@Bean(name = "apiClientThpv1")
	public ApiClient apiClientv1() {
		ApiClient apiClientv1 = new ApiClient();
		apiClientv1.setBasePath(basePath.concat("/1.0"));
		apiClientv1.setUsername(userName);
		apiClientv1.setPassword(password);
		return apiClientv1;
	}

	@Primary
	@Bean
	public InsuredPartyApi insuredPartyApi() {
		return new InsuredPartyApi(apiClient());
	}

	@Primary
	@Bean
	public PaymentCollectionMethodApi paymentCollectionMethodApi() {
		return new PaymentCollectionMethodApi(apiClient());
	}

	@Primary
	@Bean
	public DocumentTypeApi documentTypeApi() {
		return new DocumentTypeApi(apiClient());
	}

	@Primary
	@Bean
	public TimeZoneApi timeZoneApi() {
		return new TimeZoneApi(apiClient());
	}

	@Primary
	@Bean
	public ThirdPartyApi thirdPartyApi() {
		return new ThirdPartyApi(apiClient());
	}

	@Primary
	@Bean(name = "thpSettingsApi")
	public SettingsApi settingsApi() {
		return new SettingsApi(apiClient());
	}

	@Primary
	@Bean
	public BankEntityApi bankEntityApi() {
		return new BankEntityApi(apiClient());
	}

	@Primary
	@Bean
	public InsuranceCompanyApi insuranceCompanyApi() {
		return new InsuranceCompanyApi(apiClient());
	}

	@Primary
	@Bean
	public ActivityApi activityApi() {
		return new ActivityApi(apiClient());
	}

	@Primary
	@Bean
	public PersonApi personApi() {
		return new PersonApi(apiClient());
	}

	@Primary
	@Bean
	public AddressApi addressApi() {
		return new AddressApi(apiClient());
	}

	@Primary
	@Bean
	public ContactApi contactApi() {
		return new ContactApi(apiClient());
	}

	@Primary
	@Bean
	public AgentApi agentApi() {
		return new AgentApi(apiClient());
	}

	@Primary
	@Bean
	public GenericThirdPartyApi genericThirdPartyApi() {
		return new GenericThirdPartyApi(apiClient());
	}

	@Primary
	@Bean
	public CommonApi commonApiThp() {
		return new CommonApi(apiClient());
	}

	@Primary
	@Bean
	public CustomerSegmentationTypeApi CustomerSegmentationTypeApiThp() {
		return new CustomerSegmentationTypeApi(apiClient());
	}

	@Primary
	@Bean
	public CustomerSegmentationTypesApi CustomerSegmentationTypesApiThp() {
		return new CustomerSegmentationTypesApi(apiClient());
	}

	@Primary
	@Bean
	public BankOfficeApi BankOfficeApi() {
		return new BankOfficeApi(apiClient());
	}

	@Primary
	@Bean
	public ThirdPartyNotificationApi ThirdPartyNotificationApi() {
		return new ThirdPartyNotificationApi(apiClient());
	}

	@Primary
	@Bean
	public com.mapfre.tron.api.thp.client.api.v1_0.InsuredPartyApi insuredPartyApiV1() {
		return new com.mapfre.tron.api.thp.client.api.v1_0.InsuredPartyApi(apiClientv1());
	}

	/**
	 * <AUTHOR>
	 *
	 */
	@Primary
	@Bean
	public SupplierApi SupplierApi() {
		return new SupplierApi(apiClient());
	}

	@Bean
	public ProfessionApi professionApi() {
		return new ProfessionApi(apiClient());
	}

	@Primary
	@Bean
	public UserInformationApi userInformationApi() {
		return new UserInformationApi(apiClient());
	}

	@Primary
	@Bean
	public SubagentApi subagentApi() {
		return new SubagentApi(apiClient());
	}

	@Primary
	@Bean
	public EmployeeAgentApi employeeAgentApi() {
		return new EmployeeAgentApi(apiClient());
	}

	@Primary
	@Bean(name = "thpCacheApi")
	public CacheApi cacheApi() {
		return new CacheApi(apiClient());
	}

	@Primary
	@Bean
	public CommissionChartApi commissionChartApi() {
		return new CommissionChartApi(apiClient());
	}

}
