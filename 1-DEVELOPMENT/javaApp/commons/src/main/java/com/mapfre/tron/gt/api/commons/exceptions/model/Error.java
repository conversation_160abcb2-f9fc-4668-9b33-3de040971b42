package com.mapfre.tron.gt.api.commons.exceptions.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * Error
 */

@SuppressWarnings("serial")
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class Error implements Serializable {
	
	@JsonProperty("code")
	private String code = null;

	@JsonProperty("message")
	private String message = null;

	@JsonProperty("type")
	private String type = null;

	@JsonProperty("context")
	private String context = null;

	@JsonProperty("exception")
	private String exception = null;

	@JsonProperty("component")
	private String component = null;

	@JsonProperty("application")
	private String application = null;

	@JsonProperty("timestamp")
	private Date timestamp = null;

	@JsonProperty("errors")
	@Valid
	private List<ErrorComponent> errors = new ArrayList<>();

	public Error code(String code) {
		this.code = code;
		return this;
	}

	/**
	 * Error code
	 * 
	 * @return code
	 **/
	@ApiModelProperty(example = "403", required = true, value = "Error code")
	@NotNull

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Error message(String message) {
		this.message = message;
		return this;
	}

	/**
	 * Error description
	 * 
	 * @return message
	 **/
	@ApiModelProperty(example = "Internal error in the service", required = true, value = "Error description")
	@NotNull

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public Error type(String type) {
		this.type = type;
		return this;
	}

	/**
	 * Error type
	 * 
	 * @return type
	 **/
	@ApiModelProperty(example = "Null pointer", required = true, value = "Error type")
	@NotNull

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Error context(String context) {
		this.context = context;
		return this;
	}

	/**
	 * Error context
	 * 
	 * @return context
	 **/
	@ApiModelProperty(example = "Process query action", value = "Error context")

	public String getContext() {
		return context;
	}

	public void setContext(String context) {
		this.context = context;
	}

	public Error exception(String exception) {
		this.exception = exception;
		return this;
	}

	/**
	 * Exception
	 * 
	 * @return exception
	 **/
	@ApiModelProperty(example = "NullPointerException", value = "Exception")

	public String getException() {
		return exception;
	}

	public void setException(String exception) {
		this.exception = exception;
	}

	public Error component(String component) {
		this.component = component;
		return this;
	}

	/**
	 * Error component
	 * 
	 * @return component
	 **/
	@ApiModelProperty(example = "ProcessImpl", required = true, value = "Error component")
	@NotNull

	public String getComponent() {
		return component;
	}

	public void setComponent(String component) {
		this.component = component;
	}

	public Error application(String application) {
		this.application = application;
		return this;
	}

	/**
	 * Error application
	 * 
	 * @return application
	 **/
	@ApiModelProperty(example = "Process_Backend", required = true, value = "Error application")
	@NotNull

	public String getApplication() {
		return application;
	}

	public void setApplication(String application) {
		this.application = application;
	}

	public Error timestamp(Date timestamp) {
		this.timestamp = timestamp;
		return this;
	}

	/**
	 * Error time
	 * 
	 * @return timestamp
	 **/
	@ApiModelProperty(example = "2019-01-13T18:27:41.511Z", required = true, value = "Error time")
	@NotNull

	@Valid

	public Date getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(Date timestamp) {
		this.timestamp = timestamp;
	}

	public Error errors(List<ErrorComponent> errors) {
		this.errors = errors;
		return this;
	}

	public Error addErrorsItem(ErrorComponent errorsItem) {
		this.errors.add(errorsItem);
		return this;
	}

	/**
	 * Error list
	 * 
	 * @return errors
	 **/
	@ApiModelProperty(required = true, value = "Error list")
	@NotNull

	@Valid

	public List<ErrorComponent> getErrors() {
		return errors;
	}

	public void setErrors(List<ErrorComponent> errors) {
		this.errors = errors;
	}

	@Override
	public boolean equals(java.lang.Object o) {
		if (this == o) {
			return true;
		}
		if (o == null || getClass() != o.getClass()) {
			return false;
		}
		Error error = (Error) o;
		return Objects.equals(this.code, error.code) && Objects.equals(this.message, error.message)
				&& Objects.equals(this.type, error.type) && Objects.equals(this.context, error.context)
				&& Objects.equals(this.exception, error.exception) && Objects.equals(this.component, error.component)
				&& Objects.equals(this.application, error.application)
				&& Objects.equals(this.timestamp, error.timestamp) && Objects.equals(this.errors, error.errors);
	}

	@Override
	public int hashCode() {
		return Objects.hash(code, message, type, context, exception, component, application, timestamp, errors);
	}

	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder();
		sb.append("class Error {\n");

		sb.append("    code: ").append(toIndentedString(code)).append("\n");
		sb.append("    message: ").append(toIndentedString(message)).append("\n");
		sb.append("    type: ").append(toIndentedString(type)).append("\n");
		sb.append("    context: ").append(toIndentedString(context)).append("\n");
		sb.append("    exception: ").append(toIndentedString(exception)).append("\n");
		sb.append("    component: ").append(toIndentedString(component)).append("\n");
		sb.append("    application: ").append(toIndentedString(application)).append("\n");
		sb.append("    timestamp: ").append(toIndentedString(timestamp)).append("\n");
		sb.append("    errors: ").append(toIndentedString(errors)).append("\n");
		sb.append("}");
		return sb.toString();
	}

	/**
	 * Convert the given object to string with each line indented by 4 spaces
	 * (except the first line).
	 */
	private String toIndentedString(java.lang.Object o) {
		if (o == null) {
			return "null";
		}
		return o.toString().replace("\n", "\n    ");
	}
}
