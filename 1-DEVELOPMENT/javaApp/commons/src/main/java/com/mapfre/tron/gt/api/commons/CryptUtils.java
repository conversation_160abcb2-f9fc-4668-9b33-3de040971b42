package com.mapfre.tron.gt.api.commons;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Base64;

public class CryptUtils {

    private static final String ALGORITHM = "DESede";
    private static final String TRANSFORMATION = "DESede/ECB/PKCS5Padding";

    public static String encrypt(String securityKey, String textToEncrypt) throws Exception {
        byte[] key = generateMD5Key(securityKey);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
        DESedeKeySpec spec = new DESedeKeySpec(key);
        SecretKey secretKey = keyFactory.generateSecret(spec);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedBytes = cipher.doFinal(textToEncrypt.getBytes("UTF-8"));
        return base64UrlEncode(encryptedBytes);
    }

    public static String decrypt(String securityKey, String textToDecrypt) throws Exception {
        byte[] key = generateMD5Key(securityKey);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
        DESedeKeySpec spec = new DESedeKeySpec(key);
        SecretKey secretKey = keyFactory.generateSecret(spec);
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        byte[] decodedBytes = base64UrlDecode(textToDecrypt);
        byte[] decryptedBytes = cipher.doFinal(decodedBytes);
        return new String(decryptedBytes, "UTF-8");
    }

    private static byte[] generateMD5Key(String key) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        return md.digest(key.getBytes());
    }

    private static String base64UrlEncode(byte[] bytes) {
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes);
    }

    private static byte[] base64UrlDecode(String s) {
        return Base64.getUrlDecoder().decode(s);
    }

    public static String createKey() throws NoSuchAlgorithmException {
        SecretKey secretKey = null;
        try {
            secretKey = SecretKeyFactory.getInstance(ALGORITHM).generateSecret(new DESedeKeySpec(new byte[24]));
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException(e);
        } catch (InvalidKeyException e) {
            throw new RuntimeException(e);
        }
        return Base64.getUrlEncoder().withoutPadding().encodeToString(secretKey.getEncoded());
    }

}
