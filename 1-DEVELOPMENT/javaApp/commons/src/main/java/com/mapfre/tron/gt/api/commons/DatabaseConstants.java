package com.mapfre.tron.gt.api.commons;

import oracle.jdbc.OracleTypes;

public final class DatabaseConstants {

	// Schema and Catalog Constants
    public static final String SCHEMA_NAME = "TRON2000_HN";
    public static final String CATALOG_OFICINA_DIGITAL_NAME = "dc_k_oficina_digital_mhn";
    public static final String CATALOG_PLANILLA_ELECTRONICA_NAME = "CO_K_PLANILLA_ELECTRONICA_MGT";

    // Stored Procedures Oficina Digital
    public static final String PROC_DATOS_BUSQ_ASEGURADOS = "pp_datos_busq_asegurados";

    // Stored Procedures Planilla Electronica
    public static final String PROC_BUSCAR_MEDIOS_PAGO_PLANILLA = "BUSCAR_MEDIOS_PAGO_PLANILLA";
    public static final String PROC_BUSCAR_DEPOSITOS_PLANILLA = "BUSCAR_DEPOSITOS_PLANILLA";
    public static final String PROC_BUSCAR_IDENTIFICADOR_MEDIO_PAGO = "BUSCAR_IDENTIFICADOR_MEDIO_PAGO";
    public static final String PROC_BUSCAR_DETALLE_PLANILLA = "BUSCAR_DETALLE_PLANILLA";
    public static final String PROC_GET_CUENTAS_BANCOS = "GET_CUENTAS_BANCOS";
    public static final String PROC_OBTENER_TOTAL_PAGOS_PLANILLA = "OBTENER_TOTAL_PAGOS_PLANILLA";
    public static final String PROC_OBTENER_MEDIO_PAGO = "OBTENER_MEDIO_PAGO";
    public static final String PROC_OBTENER_TIPO_PLANILLA = "OBTENER_TIPO_PLANILLA";
    public static final String PROC_OBTENER_MEDIOS_PLANILLA = "OBTENER_MEDIOS_PLANILLA";
    public static final String PROC_BUSCAR_DETALLE_PLANILLA_PAGOS = "BUSCAR_DETALLE_PLANILLA_PAGOS";
    public static final String PROC_OBTENER_PLANILLA = "OBTENER_PLANILLA";
    public static final String PROC_OBTENER_CONTADOR_PAGOS = "OBTENER_CONTADOR_PAGOS";
    public static final String PROC_OBTENER_REPORTE_PRIMAS_PLANILLA = "OBTENER_REPORTE_PRIMAS_PLANILLA";
    public static final String PROC_COBRAR_PLANILLA = "COBRAR_PLANILLA";
    public static final String PROC_UPDATE_REQ_COMENTARIO = "UPDATE_REQ_COMENTARIO";
    public static final String PROC_UPDATE_MEDIO_PAGO = "UPDATE_MEDIO_PAGO";
    public static final String PROC_UPDATE_REQ_COBRO = "UPDATE_REQ_COBRO";
    public static final String PROC_NOTIFICAR_RESPUESTA = "NOTIFICAR_RESPUESTA";
    public static final String PROC_ACTUALIZAR_ID_TRANSACCION = "ACTUALIZAR_ID_TRANSACCION";
    public static final String PROC_OBTENER_MEDIOS_PAGO = "OBTENER_MEDIOS_PAGO";
    public static final String PROC_OBTENER_ENTIDADES = "OBTENER_ENTIDADES";

    // Parameters cobranzas
    public static final String PARAM_COD_CIA = "p_cod_cia";

    // Parameters inspeccion
    public static final String PARAM_REG_BUSQ_ASEGURADOS = "reg_busq_asegurados";
    public static final String PARAM_NAME_AUX = "p_nameAux";
    public static final String PARAM_COD_AGENTE = "p_cod_agente";
    public static final String PARAM_POLIZA = "p_poliza";



    // SQL Types for Out Parameters
    public static final int SQL_TYPE_OUT_CURSOR = OracleTypes.CURSOR;

    public static final String LOG_LAUNCH_DESC = "Launching %s.%s.%s";
    public static final String LOG_PROC_CALL = "The %s database logic implementation called";

    public static final String MESSAGE_NO_RESULTS = "No se encontraron registros";

    public static final String COD_USER = "TRON2000";

}
