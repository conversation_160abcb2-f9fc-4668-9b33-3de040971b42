package com.mapfre.tron.gt.api.client;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.mapfre.tron.api.tsy.client.api.AccountingNotificationApi;
import com.mapfre.tron.api.tsy.client.api.ActionsApi;
import com.mapfre.tron.api.tsy.client.api.CacheApi;
import com.mapfre.tron.api.tsy.client.api.DigitalPaymentApi;
import com.mapfre.tron.api.tsy.client.api.ReceiptApi;
import com.mapfre.tron.api.tsy.client.api.ReceiptGroupApi;
import com.mapfre.tron.api.tsy.client.api.SettingsApi;
import com.mapfre.tron.api.tsy.client.api.TreasuryApi;
import com.mapfre.tron.api.tsy.client.api.TreasuryNotificationApi;
import com.mapfre.tron.api.tsy.client.invoker.ApiClient;

@Configuration
@EnableTransactionManagement
@DependsOn("jasyptPasswordManager")
public class GtApiTsyBlConfig {

	@Value("${app.env.tron.api.tsy.basePath}")
	private String basePath;

	@Value("${app.env.tron.api.tsy.userName}")
	private String userName;

	@Value("#{'${app.env.tron.api.tsy.password}' == '' ? '' : environmentUtils.resolve('${app.env.tron.api.tsy.password}')}")
	private String password;

	@Primary
	@Bean(name = "apiClientTsy")
	public ApiClient apiClient() {
		ApiClient apiClient = new ApiClient();
		apiClient.setBasePath(basePath);
		apiClient.setUsername(userName);
		apiClient.setPassword(password);
		return apiClient;
	}

	@Primary
	@Bean
	public TreasuryApi treasuryApi() {
		return new TreasuryApi(apiClient());
	}

	@Primary
	@Bean
	public ActionsApi actionsApi() {
		return new ActionsApi(apiClient());
	}

	@Primary
	@Bean(name = "tsySettingsApi")
	public SettingsApi settingsApi() {
		return new SettingsApi(apiClient());
	}

	@Primary
	@Bean
	public ReceiptApi receiptApi() {
		return new ReceiptApi(apiClient());
	}

	@Primary
	@Bean
	public TreasuryNotificationApi treasuryNotificationApi() {
		return new TreasuryNotificationApi(apiClient());
	}

	@Primary
	@Bean
	public AccountingNotificationApi accountingNotificationApi() {
		return new AccountingNotificationApi(apiClient());
	}

	@Primary
	@Bean
	public ReceiptGroupApi receiptGroupApi() {
		return new ReceiptGroupApi(apiClient());
	}

	@Primary
	@Bean
	public DigitalPaymentApi digitalPaymentApi() {
		return new DigitalPaymentApi(apiClient());
	}

	@Primary
	@Bean(name = "tsyCacheApi")
	public CacheApi cacheApi() {
		return new CacheApi(apiClient());
	}
}