package com.mapfre.tron.gt.api.aspect;

/**
 * Enum para utilizar placeholder en los mensajes asociados a los aspectos de
 * log
 * 
 * <AUTHOR>
 *
 */
public enum TronApiAspectPlaceHolder {

	ARGUMENTS("$[arguments]"), 
	METHOD_NAME("$[methodName]"), 
	TARGET_CLASS_NAME("$[targetClassName]"),
	RETURN_VALUE("$[returnValue]"), 
	EXCEPTION("$[exception]"), 
	EXECUTION_TIME("$[executionTime]");

	private String placeHolder;

	private TronApiAspectPlaceHolder(String placeHolder) {
		this.placeHolder = placeHolder;
	}

	public String getPlaceHolder() {
		return this.placeHolder;
	}

}