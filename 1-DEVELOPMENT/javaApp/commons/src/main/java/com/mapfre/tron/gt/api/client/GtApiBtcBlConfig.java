package com.mapfre.tron.gt.api.client;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.mapfre.tron.api.btc.client.api.ArchiveApi;
import com.mapfre.tron.api.btc.client.api.DocumentApi;
import com.mapfre.tron.api.btc.client.invoker.ApiClient;

@Configuration
@EnableTransactionManagement
@DependsOn("jasyptPasswordManager")
public class GtApiBtcBlConfig {

	@Value("${app.env.tron.api.btc.basePath}")
	private String basePath;

	@Value("${app.env.tron.api.btc.userName}")
	private String userName;

	@Value("#{'${app.env.tron.api.btc.password}' == '' ? '' : environmentUtils.resolve('${app.env.tron.api.btc.password}')}")
	private String password;

	@Primary
	@Bean(name = "apiClientBtc")
	public ApiClient apiClient() {
		ApiClient apiClient = new ApiClient();
		apiClient.setBasePath(basePath);
		apiClient.setUsername(userName);
		apiClient.setPassword(password);
		return apiClient;
	}

	@Primary
	@Bean
	public ArchiveApi archiveApi() {
		return new ArchiveApi(apiClient());
	}

	@Primary
	@Bean(name = "btcDocumentApi")
	public DocumentApi documentApi() {
		return new DocumentApi(apiClient());
	}

}
