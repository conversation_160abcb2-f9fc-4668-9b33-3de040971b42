package com.mapfre.tron.gt.api;

import java.util.HashMap;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.object.StoredProcedure;
import org.springframework.stereotype.Service;

@Service
public class ResetPackage extends StoredProcedure {

	/** The constant with the DBMS_SESSION.RESET_PACKAGE qualified name. */
	public static final String PROC_RESET_PACKAGE = "DBMS_SESSION.RESET_PACKAGE";

	/**
	 * Create a new instance of the class.
	 * 
	 * @param ds the datasource property
	 */
	@Autowired
	public ResetPackage(@Qualifier("dataSource") DataSource ds) {
		super(ds, PROC_RESET_PACKAGE);
		compile();
	}

	/**
	 * The execute method.
	 */
	public void executeRP() {
		execute(new HashMap<String, Object>());
	}

}