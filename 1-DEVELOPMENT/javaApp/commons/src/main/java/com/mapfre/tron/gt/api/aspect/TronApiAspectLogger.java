package com.mapfre.tron.gt.api.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mapfre.dgtp.gaia.exception.GaiaBackendException;

@Component
@Aspect
public class TronApiAspectLogger {

	protected Logger log;

	@Value("${app.env.errorLog.enterMessage:ENTER: $[targetClassName].$[methodName]($[arguments])}")
	private String enterMessage;
	@Value("${app.env.errorLog.exitMessage:EXIT: $[targetClassName].$[methodName]() := $[returnValue] - TIME: $[executionTime]ms}")
	private String exitMessage;
	private String exceptionMessage;

	ObjectMapper mapper;

	public TronApiAspectLogger() {

		this.log = LoggerFactory.getLogger(TronApiAspectLogger.class);
		mapper = new ObjectMapper();
		log.debug("TronApiAspectLogger init.");
	}

	@Around("(@within(org.springframework.stereotype.Controller)) && execution(public * com.mapfre.tron..*.*(..))")
	public Object processMethodInvocation(ProceedingJoinPoint joinPoint) throws Throwable {
		return this.invokeRealMethod(joinPoint);
	}

	/**
	 * Metodo para saber como transformar el objeto resultado a una forma String.
	 * Esto debe afinar el metodo toString() implementado por todos los objects.
	 * 
	 * @param returnObject El objeto resultado de la ejecucion del metodo.
	 * @return El String que transforma el objeto resultado a una representacion
	 *         como String
	 * @throws IllegalArgumentException En caso de uso de argumentos no validos.
	 * @throws IllegalAccessException   En caso de no tener acceso al objeto o sus
	 *                                  atributos permitido.
	 */
	protected String toStringResultObject(Object returnObject) throws IllegalArgumentException, IllegalAccessException {
		StringBuffer buffer = new StringBuffer();
		buffer.append("resultObject=[");
		try {
			if (returnObject != null) {
				if (returnObject instanceof byte[]) {
					buffer.append(returnObject.toString());
				} else {
					buffer.append(mapper.writeValueAsString(returnObject));
				}
			}
		} catch (Throwable e) {
			buffer.append("LOGGER ERROR:\n").append(new GaiaBackendException(e));
		}
		buffer.append("]");
		return buffer.toString();
	}

	/**
	 * Devuelve el mensaje de entrada usado en la ejecucion de un metodo
	 * 
	 * @return el mensaje de entrada
	 */
	public String getEnterMessage() {
		return enterMessage;
	}

	/**
	 * Define el mensaje de entrada usado en la ejecucion de un metodo
	 * 
	 * @param enterMessage Un string con los placeholder que se desean mostrar.
	 */
	public void setEnterMessage(String enterMessage) {
		this.enterMessage = enterMessage;
	}

	/**
	 * Devuelve el mensaje de salida usado en la ejecucion de un metodo
	 * 
	 * @return el mensaje de salida
	 */
	public String getExitMessage() {
		return exitMessage;
	}

	/**
	 * Define el mensaje de salida usado en la ejecucion de un metodo
	 * 
	 * @param exitMessage Un string con los placeholder que se desean mostrar.
	 */
	public void setExitMessage(String exitMessage) {
		this.exitMessage = exitMessage;
	}

	/**
	 * Devuelve el mensaje de excepcion usado en caso de excepcion del metodo
	 * 
	 * @return el mensaje de excepcion
	 */
	public String getExceptionMessage() {
		return exceptionMessage;
	}

	/**
	 * Define el mensaje de excepcion usado en caso de excepcion del metodo
	 * 
	 * @param exceptionMessage Un string con los placeholder que se desean mostrar.
	 */
	public void setExceptionMessage(String exceptionMessage) {
		this.exceptionMessage = exceptionMessage;
	}

	/**
	 * Sustituye un placeholder dentro de un String por el valor correspondiente
	 * 
	 * @param placeHolder Tipo de placeholder
	 * @param message     El mensaje con los placeholders a sustituir
	 * @param value       El valor que se aplicara al placeholder.
	 * @return El String con los placeholder ya resueltos.
	 */
	protected String replacePlaceHolder(TronApiAspectPlaceHolder placeHolder, String message, String value) {
		if (message.contains(placeHolder.getPlaceHolder()) && value != null) {
			return message.replace(placeHolder.getPlaceHolder(), value);
		}
		return message;
	}

	/**
	 * Realiza la invocacion real al metodo especificado por un pointcut, ejecuta la
	 * traza del mensaje de entrada, la traza despues de ejecutar el metodo o la del
	 * mensaje de excepcion en caso de error.
	 * 
	 * @param joinPoint El punto de interseccion con el pointcut
	 * @return El objeto invocado
	 * @throws Throwable
	 */
	protected Object invokeRealMethod(ProceedingJoinPoint joinPoint) throws Throwable {
		Object returnObject = null;

		if (log.isInfoEnabled()) {
			try {
				writeToLog(processMessage(validateEnterMessage(), joinPoint));
			} catch (Exception e) {
				log.info("Error writing log.", e);
			}
		}

		try {
			long initTime = System.currentTimeMillis();
			returnObject = joinPoint.proceed();
			long timeExecution = System.currentTimeMillis() - initTime;

			if (log.isInfoEnabled()) {
				try {
					String resultObjectToString = replacePlaceHolder(TronApiAspectPlaceHolder.RETURN_VALUE,
							validateExitMessage(), toStringResultObject(returnObject));
					writeToLog(processMessage(replacePlaceHolder(TronApiAspectPlaceHolder.EXECUTION_TIME,
							resultObjectToString, String.valueOf(timeExecution)), joinPoint));
				} catch (Exception e) {
					log.info("Error writing log.", e);
				}
			}

			return returnObject;
		} catch (Throwable e) {
			log.error(processMessage(validateExceptionMessage(), joinPoint, e), e);
			throw e;
		}

	}

	/**
	 * Escribe un String en el log a nivel INFO.
	 * 
	 * @param trace El string a escribir
	 */
	protected void writeToLog(String trace) {
		if (log.isInfoEnabled()) {
			log.info(trace);
		}
	}

	/**
	 * Procesa mensaje sustituyendo los placeholders por el valor requerido, utiliza
	 * tambien el Throwable para el placeholder $[exception]
	 * 
	 * @param message
	 * @param joinPoint
	 * @param t
	 * @return
	 */
	private String processMessage(String message, ProceedingJoinPoint joinPoint, Throwable t) {
		if (t != null) {
			message = replacePlaceHolder(TronApiAspectPlaceHolder.EXCEPTION, message, t.toString());
		}
		return processMessage(message, joinPoint);
	}

	/**
	 * Procesa mensaje sustituyendo los placeholders por el valor requerido
	 * 
	 * @param message   El string con los placeholders definidos.
	 * @param joinPoint Punto de union con el pointcut
	 * @return El mensaje con los valores ya resueltos
	 */
	private String processMessage(String message, ProceedingJoinPoint joinPoint) {

		if (message.contains(TronApiAspectPlaceHolder.METHOD_NAME.getPlaceHolder())) {
			message = replacePlaceHolder(TronApiAspectPlaceHolder.METHOD_NAME, message,
					joinPoint.getSignature().getName());
		}

		if (message.contains(TronApiAspectPlaceHolder.ARGUMENTS.getPlaceHolder())) {
			StringBuilder builder = new StringBuilder();
			for (Object obj : joinPoint.getArgs()) {
				try {
					builder.append(mapper.writeValueAsString(obj));
					builder.append(",");
				} catch (Exception e) {
					builder.append("NOT_SERIALIZED[");
					builder.append(obj.getClass().toString());
					builder.append("],");
					log.debug("Can't serialize parameter.", e);
				}
			}
			message = replacePlaceHolder(TronApiAspectPlaceHolder.ARGUMENTS, message, builder.toString());
		}

		if (message.contains(TronApiAspectPlaceHolder.TARGET_CLASS_NAME.getPlaceHolder())) {
			message = replacePlaceHolder(TronApiAspectPlaceHolder.TARGET_CLASS_NAME, message,
					getInterfaceNameTarget(joinPoint.getTarget()));
		}

		if (message.contains(TronApiAspectPlaceHolder.EXECUTION_TIME.getPlaceHolder())) {
			message = replacePlaceHolder(TronApiAspectPlaceHolder.ARGUMENTS, enterMessage,
					String.valueOf(System.currentTimeMillis()));
		}

		return message;
	}

	/**
	 * Valida el mensaje de entrada al metodo, no puede utilizar el placeholder
	 * $[returnValue],$[exception],$[executionTime]. En caso de no haberse
	 * inicializado aplica uno por defecto.
	 * 
	 * @return el mensaje validado
	 */
	private String validateEnterMessage() {
		if (enterMessage == null || enterMessage.isEmpty()) {
			setEnterMessage("Enter in " + TronApiAspectPlaceHolder.METHOD_NAME.getPlaceHolder() + "() with arguments "
					+ TronApiAspectPlaceHolder.ARGUMENTS.getPlaceHolder());
		}
		if (enterMessage.contains(TronApiAspectPlaceHolder.RETURN_VALUE.getPlaceHolder())
				|| enterMessage.contains(TronApiAspectPlaceHolder.EXCEPTION.getPlaceHolder())
				|| enterMessage.contains(TronApiAspectPlaceHolder.EXECUTION_TIME.getPlaceHolder())) {
			throw new BeanCreationException("PlaceHolder not valid in enterMessage");
		}
		return enterMessage;
	}

	/**
	 * Valida el mensaje de salida al metodo, no puede utilizar el placeholder
	 * $[exception],y debe contener $[returnValue]. En caso de no haberse
	 * inicializado aplica uno por defecto.
	 * 
	 * @return el mensaje validado
	 */
	private String validateExitMessage() {
		if (exitMessage == null || exitMessage.isEmpty()) {
			setExitMessage("Invoke to: " + TronApiAspectPlaceHolder.METHOD_NAME.getPlaceHolder() + "() result:= "
					+ TronApiAspectPlaceHolder.RETURN_VALUE.getPlaceHolder());
		}
		if (!exitMessage.contains(TronApiAspectPlaceHolder.RETURN_VALUE.getPlaceHolder())
				|| enterMessage.contains(TronApiAspectPlaceHolder.EXCEPTION.getPlaceHolder())) {
			throw new BeanCreationException("PlaceHolder not valid in exitMessage");
		}
		return exitMessage;
	}

	/**
	 * Valida el mensaje de excepcion en el metodo, no puede utilizar el placeholder
	 * $[returnValue],$[executionTime] y debe contener $[exception]. En caso de no
	 * haberse inicializado aplica uno por defecto.
	 * 
	 * @return el mensaje validado
	 */
	private String validateExceptionMessage() {
		if (exceptionMessage == null || exceptionMessage.isEmpty()) {
			setExceptionMessage(
					"Exception in Tron API service: " + TronApiAspectPlaceHolder.TARGET_CLASS_NAME.getPlaceHolder()
							+ "." + TronApiAspectPlaceHolder.METHOD_NAME.getPlaceHolder() + "("
							+ TronApiAspectPlaceHolder.ARGUMENTS.getPlaceHolder() + ") - "
							+ TronApiAspectPlaceHolder.EXCEPTION.getPlaceHolder());
		}
		if (exceptionMessage.contains(TronApiAspectPlaceHolder.RETURN_VALUE.getPlaceHolder())
				|| !exceptionMessage.contains(TronApiAspectPlaceHolder.EXCEPTION.getPlaceHolder())
				|| exceptionMessage.contains(TronApiAspectPlaceHolder.EXECUTION_TIME.getPlaceHolder())) {
			throw new BeanCreationException("PlaceHolder not valid in exceptionMessage");
		}
		return exceptionMessage;
	}

	private String getInterfaceNameTarget(Object target) {
		Class<?>[] ai = ClassUtils.getAllInterfaces(target);
		if (ai != null && ai.length > 0) {
			return ClassUtils.getAllInterfaces(target)[0].getName();
		} else {
			return "InterfaceNameTarget:NONE";
		}
	}

}
