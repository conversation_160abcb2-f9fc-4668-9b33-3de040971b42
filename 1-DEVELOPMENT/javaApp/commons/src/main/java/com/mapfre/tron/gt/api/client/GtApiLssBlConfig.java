package com.mapfre.tron.gt.api.client;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.mapfre.tron.api.lss.client.api.ActionsApi;
import com.mapfre.tron.api.lss.client.api.AttributeApi;
import com.mapfre.tron.api.lss.client.api.CacheApi;
import com.mapfre.tron.api.lss.client.api.ClaimDescriptionApi;
import com.mapfre.tron.api.lss.client.api.ExternalInterventionApi;
import com.mapfre.tron.api.lss.client.api.FileApi;
import com.mapfre.tron.api.lss.client.api.LossApi;
import com.mapfre.tron.api.lss.client.api.LossNotificationApi;
import com.mapfre.tron.api.lss.client.api.OcurrenceApi;
import com.mapfre.tron.api.lss.client.api.SettingsApi;
import com.mapfre.tron.api.lss.client.invoker.ApiClient;

@Configuration
@EnableTransactionManagement
@DependsOn("jasyptPasswordManager")
public class GtApiLssBlConfig {

	@Value("${app.env.tron.api.lss.basePath}")
	private String basePath;

	@Value("${app.env.tron.api.lss.userName}")
	private String userName;

	@Value("#{'${app.env.tron.api.lss.password}' == '' ? '' : environmentUtils.resolve('${app.env.tron.api.lss.password}')}")
	private String password;

	@Primary
	@Bean(name = "apiClientLss")
	public ApiClient apiClient() {
		ApiClient apiClient = new ApiClient();
		apiClient.setBasePath(basePath);
		apiClient.setUsername(userName);
		apiClient.setPassword(password);
		return apiClient;
	}

	@Primary
	@Bean
	public LossApi lossApi() {
		return new LossApi(apiClient());
	}

	@Primary
	@Bean
	public ClaimDescriptionApi claimDescriptionApi() {
		return new ClaimDescriptionApi(apiClient());
	}

	@Primary
	@Bean
	public AttributeApi attributeApi() {
		return new AttributeApi(apiClient());
	}

	@Primary
	@Bean
	public OcurrenceApi ocurrenceApi() {
		return new OcurrenceApi(apiClient());
	}

	@Primary
	@Bean(name = "lssSettingsApi")
	public SettingsApi settingsApi() {
		return new SettingsApi(apiClient());
	}

	@Primary
	@Bean
	public ExternalInterventionApi externalInterventionApi() {
		return new ExternalInterventionApi(apiClient());
	}

	@Primary
	@Bean(name = "lssActionsApi")
	public ActionsApi actionsApi() {
		return new ActionsApi(apiClient());
	}

	@Primary
	@Bean(name = "lssLossNotificationApi")
	public LossNotificationApi lossNotificationApi() {
		return new LossNotificationApi(apiClient());
	}

	@Bean(name = "lssFileApi")
	public FileApi fileApi() {
		return new FileApi(apiClient());
	}

	@Primary
	@Bean(name = "lssCacheApi")
	public CacheApi cacheApi() {
		return new CacheApi(apiClient());
	}

}