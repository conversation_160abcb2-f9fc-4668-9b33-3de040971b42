package com.mapfre.tron.gt.api.commons;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import org.springframework.http.HttpInputMessage;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.StringHttpMessageConverter;

public class PlainStringHttpMessageConverter extends StringHttpMessageConverter {

	public PlainStringHttpMessageConverter() {
		super(StandardCharsets.UTF_8);
	}

	@Override
	public boolean supports(Class<?> clazz) {
		return String.class.equals(clazz);
	}

	@Override
	protected String readInternal(Class<? extends String> clazz, HttpInputMessage inputMessage) throws IOException {
		return super.readInternal(clazz, inputMessage);
	}

	@Override
	protected void writeInternal(String str, HttpOutputMessage outputMessage) throws IOException {
		outputMessage.getHeaders().setContentType(MediaType.TEXT_PLAIN);
		super.writeInternal(str, outputMessage);
	}
}
