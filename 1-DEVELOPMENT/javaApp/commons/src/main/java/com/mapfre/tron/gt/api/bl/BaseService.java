package com.mapfre.tron.gt.api.bl;

import org.springframework.beans.factory.annotation.Autowired;

import com.mapfre.tron.gt.api.ResetPackage;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BaseService extends Constants {

	/** The resetPackage property. */
	@Autowired
	protected ResetPackage resetPackage;

	/** Reset the session. */
	protected void resetSession() {
		if (log.isDebugEnabled()) {
			log.debug("Reseting session...");
		}
		resetPackage.executeRP();
		if (log.isDebugEnabled()) {
			log.debug("The session has been reset.");
		}
	}

}
