package com.mapfre.tron.gt.api.client;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.mapfre.tron.api.isu.client.api.ActionsApi;
import com.mapfre.tron.api.isu.client.api.AgentInterventionApi;
import com.mapfre.tron.api.isu.client.api.AttributeDefinitionApi;
import com.mapfre.tron.api.isu.client.api.BreakDownApi;
import com.mapfre.tron.api.isu.client.api.CacheApi;
import com.mapfre.tron.api.isu.client.api.InterventionApi;
import com.mapfre.tron.api.isu.client.api.IssueApi;
import com.mapfre.tron.api.isu.client.api.IssueNotificationApi;
import com.mapfre.tron.api.isu.client.api.ModalityDefinitionApi;
import com.mapfre.tron.api.isu.client.api.PaymentScheduleDefinitionApi;
import com.mapfre.tron.api.isu.client.api.PolicyApi;
import com.mapfre.tron.api.isu.client.api.QuotationApi;
import com.mapfre.tron.api.isu.client.api.SettingsApi;
import com.mapfre.tron.api.isu.client.api.SimulationApi;
import com.mapfre.tron.api.isu.client.api.SubModelApi;
import com.mapfre.tron.api.isu.client.api.TechnicalControlApi;
import com.mapfre.tron.api.isu.client.api.VehicleUseApi;
import com.mapfre.tron.api.isu.client.invoker.ApiClient;

@Configuration
@EnableTransactionManagement
@DependsOn("jasyptPasswordManager")
public class GtApiIsuBlConfig {

	@Value("${app.env.tron.api.isu.basePath}")
	private String basePath;

	@Value("${app.env.tron.api.isu.userName}")
	private String userName;

	@Value("#{'${app.env.tron.api.isu.password}' == '' ? '' : environmentUtils.resolve('${app.env.tron.api.isu.password}')}")
	private String password;

	@Primary
	@Bean(name = "apiClientIsu")
	public ApiClient apiClient() {
		ApiClient apiClient = new ApiClient();
		apiClient.setBasePath(basePath);
		apiClient.setUsername(userName);
		apiClient.setPassword(password);
		return apiClient;
	}

	@Primary
	@Bean
	public IssueApi issueApi() {
		return new IssueApi(apiClient());
	}

	@Primary
	@Bean
	public BreakDownApi breakDownApi() {
		return new BreakDownApi(apiClient());
	}

	@Primary
	@Bean
	public PolicyApi policyApi() {
		return new PolicyApi(apiClient());
	}

	@Primary
	@Bean
	public PaymentScheduleDefinitionApi paymentScheduleDefinitionApi() {
		return new PaymentScheduleDefinitionApi(apiClient());
	}

	@Primary
	@Bean
	public TechnicalControlApi technicalControlApi() {
		return new TechnicalControlApi(apiClient());
	}

	@Primary
	@Bean
	public AgentInterventionApi agentInterventionApi() {
		return new AgentInterventionApi(apiClient());
	}

	@Primary
	@Bean
	public InterventionApi interventionApi() {
		return new InterventionApi(apiClient());
	}

	@Primary
	@Bean
	public ModalityDefinitionApi modalityDefinitionApi() {
		return new ModalityDefinitionApi(apiClient());
	}

	@Primary
	@Bean(name = "isuActionsApi")
	public ActionsApi actionsApi() {
		return new ActionsApi(apiClient());
	}

	@Primary
	@Bean
	public SimulationApi simulationApi() {
		return new SimulationApi(apiClient());
	}

	@Primary
	@Bean
	public QuotationApi quotationApi() {
		return new QuotationApi(apiClient());
	}

	@Primary
	@Bean(name = "isuSettingsApi")
	public SettingsApi settingsApi() {
		return new SettingsApi(apiClient());
	}

	@Primary
	@Bean
	public IssueNotificationApi IssueNotificationApi() {
		return new IssueNotificationApi(apiClient());
	}

	@Primary
	@Bean
	public AttributeDefinitionApi AttributeDefinitionApi() {
		return new AttributeDefinitionApi(apiClient());
	}

	@Bean
	public SubModelApi subModelApi() {
		return new SubModelApi(apiClient());
	}

	@Bean
	public VehicleUseApi vehicleUseApi() {
		return new VehicleUseApi(apiClient());
	}

	@Primary
	@Bean(name = "isuCacheApi")
	public CacheApi cacheApi() {
		return new CacheApi(apiClient());
	}

}