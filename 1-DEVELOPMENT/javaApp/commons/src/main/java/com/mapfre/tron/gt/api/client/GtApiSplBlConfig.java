package com.mapfre.tron.gt.api.client;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.mapfre.tron.api.spl.client.api.AttributeSplApi;
import com.mapfre.tron.api.spl.client.api.BillingApi;
import com.mapfre.tron.api.spl.client.api.DeductibleDefinitionApi;
import com.mapfre.tron.api.spl.client.api.FraudApi;
import com.mapfre.tron.api.spl.client.api.OcurrenceApi;
import com.mapfre.tron.api.spl.client.api.ProcessApi;
import com.mapfre.tron.api.spl.client.api.ServiceAdvancementApi;
import com.mapfre.tron.api.spl.client.api.ServiceApi;
import com.mapfre.tron.api.spl.client.api.ServicesApi;
import com.mapfre.tron.api.spl.client.api.ServicesOrderApi;
import com.mapfre.tron.api.spl.client.invoker.ApiClient;

@Configuration
@EnableTransactionManagement
@DependsOn("jasyptPasswordManager")
public class GtApiSplBlConfig {

	@Value("${app.env.tron.api.spl.basePath}")
	private String basePath;

	@Value("${app.env.tron.api.spl.userName}")
	private String userName;

	@Value("#{'${app.env.tron.api.spl.password}' == '' ? '' : environmentUtils.resolve('${app.env.tron.api.spl.password}')}")
	private String password;

	@Primary
	@Bean(name = "apiClientSpl")
	public ApiClient apiClient() {
		ApiClient apiClient = new ApiClient();
		apiClient.setBasePath(basePath);
		apiClient.setUsername(userName);
		apiClient.setPassword(password);
		return apiClient;
	}

	@Primary
	@Bean
	public ServicesApi servicesApi() {
		return new ServicesApi(apiClient());
	}

	@Primary
	@Bean
	public ServicesOrderApi servicesOrderApi() {
		return new ServicesOrderApi(apiClient());
	}

	@Primary
	@Bean
	public ServiceApi serviceApi() {
		return new ServiceApi(apiClient());
	}

	@Primary
	@Bean
	public AttributeSplApi attributeSplApi() {
		return new AttributeSplApi(apiClient());
	}

	@Bean
	public ServiceAdvancementApi serviceAdvancementApi() {
		return new ServiceAdvancementApi(apiClient());
	}

	@Bean(name = "splOcurrenceApi")
	public OcurrenceApi ocurrenceApi() {
		return new OcurrenceApi(apiClient());
	}

	@Bean
	public BillingApi billingApi() {
		return new BillingApi(apiClient());
	}

	@Bean
	public ProcessApi processApi() {
		return new ProcessApi(apiClient());
	}

	@Bean
	public DeductibleDefinitionApi deductibleDefinitionApi() {
		return new DeductibleDefinitionApi(apiClient());
	}

	@Bean
	public FraudApi fraudApi() {
		return new FraudApi(apiClient());
	}

}
