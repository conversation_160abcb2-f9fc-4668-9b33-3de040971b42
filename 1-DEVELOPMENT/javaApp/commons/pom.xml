<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.mapfre.tron.gt</groupId>
    <artifactId>nwt_api_gt_be.javaApp</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>

  <artifactId>nwt_api_gt_be-commons</artifactId>
  <packaging>jar</packaging>

  <name>${project.artifactId}:${project.version}</name>
  <description>${project.artifactId}:${project.version}</description>

  <properties>
    <gaia.artifact.folder>commons</gaia.artifact.folder>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.3.1_MAPFRE</version>
    </dependency>
    <dependency>
      <groupId>com.oracle.database.jdbc</groupId>
      <artifactId>ojdbc8</artifactId>
    </dependency>
    <!-- API Clients -->
    <dependency>
      <groupId>com.mapfre.tron</groupId>
      <artifactId>nwt_cmn_api_be-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.tron</groupId>
      <artifactId>nwt_btc_api_be-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.tron</groupId>
      <artifactId>nwt_isu_api_be-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.tron</groupId>
      <artifactId>nwt_lss_api_be-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.tron</groupId>
      <artifactId>nwt_spl_api_be-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.tron</groupId>
      <artifactId>nwt_thp_api_be-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.tron</groupId>
      <artifactId>nwt_tsy_api_be-client</artifactId>
    </dependency>
  </dependencies>

</project>