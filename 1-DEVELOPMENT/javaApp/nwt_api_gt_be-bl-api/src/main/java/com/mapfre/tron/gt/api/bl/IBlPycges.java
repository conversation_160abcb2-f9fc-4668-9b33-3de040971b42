package com.mapfre.tron.gt.api.bl;

import java.util.List;

import com.mapfre.tron.gt.api.model.*;

/**
 * Interfaz para operaciones de Pycges en la capa de negocio.
 *
 * Proporciona métodos para realizar operaciones relacionadas con Pycges.
 */
public interface IBlPycges {

    /**
     * Obtiene la lista de usuarios.
     *
     * @return Lista de objetos PycUsuario con la información de los usuarios
     */
    public List<PycUsuario> getUsuarios();

    /**
     * Obtiene la lista de aplicaciones asociadas a un proceso.
     *
     * @param idProceso ID del proceso
     * @return Lista de objetos PycAplicacion con la información de las aplicaciones
     */
    public List<PycAplicacion> getAplicaciones(Integer idProceso);

    /**
     * Obtiene la información completa de un proceso específico.
     *
     * @param idProceso ID del proceso
     * @return Lista de objetos PycInfoProceso con la información del proceso
     */
    public List<PycInfoProceso> getInfoProceso(Integer idProceso);

    /**
     * Obtiene la lista de controles asociados a un proceso específico y tipo de control.
     *
     * @param idProceso ID del proceso
     * @param tipoControl Código del tipo de control
     * @return Lista de objetos PycControlProc con la información de los controles
     */
    public List<PycControlProc> getControlesProceso(Integer idProceso, String tipoControl);

    /**
     * Obtiene la lista de estados disponibles para un perfil específico en un proceso determinado.
     *
     * @param idPerfil ID del perfil
     * @param idProceso ID del proceso
     * @return Lista de objetos PycEstadoPerfil con la información de los estados
     */
    public List<PycEstadoPerfil> getEstadoPerfil(Integer idPerfil, Integer idProceso);

    /**
     * Obtiene la lista de procesos asociados a un tipo de petición.
     *
     * @param idTipo ID del tipo de petición
     * @return Lista de objetos PycProcesoPorTipo con la información de los procesos
     */
    public List<PycProcesoPorTipo> getProcesosPorTipo(Integer idTipo);

    /**
     * Obtiene la lista de perfiles activos.
     *
     * @return Lista de objetos PycPerfil con la información de los perfiles
     */
    public List<PycPerfil> getPerfiles();

    /**
     * Obtiene la lista de usuarios asociados a un perfil.
     *
     * @param idPerfil ID del perfil (opcional, si es null devuelve todos los usuarios con sus perfiles)
     * @return Lista de objetos PycUsuarioPerfil con la información de los usuarios y sus perfiles
     */
    public List<PycUsuarioPerfil> getUsuariosPerfil(Integer idPerfil);

    /**
     * Obtiene el listado de asignaciones por perfil.
     *
     * @param idPerfil ID del perfil
     * @return Lista de objetos PycListadoAsigna con la información de los usuarios asignados al perfil
     */
    public List<PycListadoAsigna> getListadoAsigna(Integer idPerfil);

    /**
     * Obtiene la consulta SQL asociada a un dato variable.
     *
     * @param idProceso ID del proceso
     * @param idSeccion ID de la sección
     * @param idDatoVar ID del dato variable
     * @return Objeto PycGetQueryDatVar con la consulta SQL
     */
    public PycGetQueryDatVar getQueryDatVar(Integer idProceso, Integer idSeccion, Integer idDatoVar);

    /**
     * Verifica si un perfil tiene la opción de selección múltiple.
     *
     * @param idPerfil ID del perfil
     * @return Objeto PycSrcMultipleOption con el resultado de la verificación
     */
    public PycSrcMultipleOption getSrcMultipleOption(Integer idPerfil);

    /**
     * Obtiene la lista de usuarios asignados según proceso, perfil, aplicación y estado.
     *
     * @param idProceso ID del proceso
     * @param idPerfil ID del perfil
     * @param idAplicacion ID de la aplicación
     * @param idEstado ID del estado
     * @return Lista de objetos PycGetUsuariosAsig con la información de los usuarios asignados
     */
    public List<PycGetUsuariosAsig> getUsuariosAsig(Integer idProceso, Integer idPerfil, Integer idAplicacion, Integer idEstado);

    /**
     * Obtiene la lista de usuarios asociados a un perfil y proceso específicos.
     *
     * @param idPerfil ID del perfil
     * @param idProceso ID del proceso
     * @return Lista de objetos PycUsuPerfilProceso con la información de los usuarios
     */
    public List<PycUsuPerfilProceso> getUsuPerfilProceso(Integer idPerfil, Integer idProceso);

    /**
     * Obtiene la lista de procesos asociados a un usuario específico.
     *
     * @param usuario Nombre de usuario en la base de datos
     * @return Lista de objetos PycUsuProcesos con la información de los procesos del usuario
     */
    public List<PycUsuProcesos> getUsuarioProcesos(String usuario);

    /**
     * Actualiza o crea un registro de perfil para un usuario específico.
     *
     * @param perfilData Datos del perfil a actualizar o crear
     * @return ID del usuario actualizado o creado
     */
    public Integer updatePerfil(PycUpdPerfil perfilData);

    /**
     * Actualiza o crea un registro de usuario con sus datos personales y departamento.
     *
     * @param usuarioData Datos del usuario a actualizar o crear
     * @return ID del usuario actualizado o creado
     */
    public Integer updateUsuario(PycUpdUsuario usuarioData);

    /**
     * Crea una nueva solicitud en el sistema PYCGES.
     *
     * @param solicitudData Datos de la solicitud a crear
     * @return ID de la solicitud creada
     */
    public Integer creaSolicitud(PycCreaSolicitud solicitudData);
    /**
     * Inserta una nueva petición en el sistema PYCGES.
     *
     * @param peticionData Datos de la petición a insertar
     * @return ID de la petición creada
     */
    public Integer insertPeticion(PycInsertPeticion peticionData);
    /**
     * Obtiene la lista de peticiones filtradas por perfil, proceso, estado y otros criterios.
     *
     * @param idPerfil ID del perfil
     * @param idProceso ID del proceso
     * @param idEstado ID del estado
     * @param idPeticion ID de la petición (opcional)
     * @param idCanal ID del canal (opcional)
     * @param idUsuario ID del usuario (opcional)
     * @param indSubs Indicador de substitución (opcional)
     * @param idOficina ID de la oficina (opcional)
     * @param numaOUsuario Número o usuario que realiza la consulta
     * @return Lista de objetos PycPeticionPerfil con la información de las peticiones
     */
    public List<PycPeticionPerfil> getPeticionPerfil(Integer idPerfil, Integer idProceso, String idEstado,
                                                     Integer idPeticion, Integer idCanal, Integer idUsuario,
                                                     String indSubs, Integer idOficina, String numaOUsuario);

    /**
     * Obtiene la lista de peticiones que no tienen programador asignado.
     *
     * @return Lista de objetos PycPetSinProgramador con la información de las peticiones sin programador
     */
    public List<PycPetSinProgramador> getPeticionSinProgramador();
    /**
     * Obtiene la lista de áreas que tienen usuarios con peticiones asociadas.
     *
     * @return Lista de objetos PycAreaPeti con la información de las áreas con peticiones
     */
    public List<PycAreaPeti> getConArea();

    /**
     * Obtiene la lista de departamentos que tienen usuarios con peticiones, filtrados por área.
     *
     * @param idArea ID del área (opcional)
     * @return Lista de objetos PycDepartamento con la información de los departamentos
     */
    public List<PycDepartamento> getConDepartamento(Integer idArea);

    /**
     * Obtiene la lista de usuarios que han realizado peticiones, filtrados por área y/o departamento.
     *
     * @param idArea ID del área (opcional)
     * @param idDepartamento ID del departamento (opcional)
     * @return Lista de objetos PycAreaUsuario con la información de los usuarios solicitantes
     */
    public List<PycAreaUsuario> getConSolicitante(Integer idArea, Integer idDepartamento);

    /**
     * Obtiene la lista de analistas disponibles filtrados por solicitante y prioridad de petición.
     *
     * @param idSolicitante ID del usuario solicitante
     * @param prioridad Prioridad de la petición (opcional)
     * @return Lista de objetos PycAnalista con la información de los analistas
     */
    public List<PycAnalista> getConAnalista(Integer idSolicitante, String prioridad);

    /**
     * Obtiene la lista de estados de peticiones filtrados por solicitante, prioridad y analista.
     *
     * @param idSolicitante ID del usuario solicitante
     * @param prioridad Prioridad de la petición (usar 'null' para peticiones sin prioridad)
     * @param idAnalista ID del analista asignado (opcional)
     * @return Lista de objetos PycConEstado con la información de los estados
     */
    public List<PycConEstado> getConEstado(Integer idSolicitante, String prioridad, Integer idAnalista);

    /**
     * Obtiene la lista de prioridades de peticiones filtradas por solicitante, área y departamento.
     *
     * @param idSolicitante ID del usuario solicitante (opcional)
     * @param idArea ID del área (opcional)
     * @param idDepartamento ID del departamento (opcional)
     * @return Lista de objetos PycPrioridad con la información de las prioridades
     */
    public List<PycPrioridad> getConPrioridad(Integer idSolicitante, Integer idArea, Integer idDepartamento);

    /**
     * Obtiene la lista de años distintos de las peticiones filtrados por proceso.
     *
     * @param idProceso ID del proceso
     * @return Lista de objetos PycTabAnios con los años disponibles
     */
    public List<PycTabAnios> getTabAnios(Integer idProceso);

    /**
     * Obtiene la lista de áreas distintas de las peticiones filtradas por proceso y año.
     *
     * @param idProceso ID del proceso
     * @param anio Año de filtro (formato YYYY) (opcional)
     * @return Lista de objetos PycTabArea con las áreas disponibles
     */
    public List<PycTabArea> getTabArea(Integer idProceso, String anio);

    /**
     * Obtiene la lista de estados distintos de las peticiones filtradas por proceso, año y área.
     *
     * @param idProceso ID del proceso
     * @param anio Año de filtro (formato YYYY) (opcional)
     * @param idArea ID del área (opcional)
     * @return Lista de objetos PycTabEstado con los estados disponibles
     */
    public List<PycTabEstado> getTabEstado(Integer idProceso, String anio, Integer idArea);

    /**
     * Obtiene la lista de usuarios asociados a un perfil específico para una petición y aplicación determinada.
     *
     * @param idPeticion ID de la petición
     * @param idPerfil ID del perfil
     * @param idAplicacion ID de la aplicación
     * @return Lista de objetos PycUsuPerfilPeti con la información de los usuarios
     */
    public List<PycUsuPerfilPeti> getUsuariosPerfilPeticion(Integer idPeticion, Integer idPerfil, Integer idAplicacion);

    /**
     * Obtiene la lista de peticiones filtradas por múltiples criterios.
     *
     * @param idArea ID del área (opcional)
     * @param idDepartamento ID del departamento (opcional)
     * @param idPerfil ID del perfil (opcional)
     * @param idEstado ID del estado (opcional)
     * @param idUsuarioSolicitante ID del usuario solicitante (opcional)
     * @param idTipo ID del tipo de petición (opcional)
     * @param fechaInicio Fecha de inicio del filtro (opcional)
     * @param fechaFin Fecha de fin del filtro (opcional)
     * @param idProceso ID del proceso (opcional)
     * @return Lista de objetos PycPetiFiltro con la información de las peticiones filtradas
     */
    public List<PycPetiFiltro> getPeticionFiltros(Integer idArea, Integer idDepartamento, Integer idPerfil,
                                                  Integer idEstado, Integer idUsuarioSolicitante, Integer idTipo,
                                                  String fechaInicio, String fechaFin, Integer idProceso);

    /**
     * Obtiene el reporte de peticiones filtrado por múltiples criterios.
     *
     * @param idArea ID del área (opcional)
     * @param idDepartamento ID del departamento (opcional)
     * @param idSolicitante ID del usuario solicitante (opcional)
     * @param prioridad Prioridad de la petición (opcional, usar 'null' para peticiones sin prioridad)
     * @param idAnalista ID del analista asignado (opcional)
     * @param estado IDs de estados separados por coma (opcional)
     * @return Lista de objetos PycReportePeti con la información del reporte de peticiones
     */
    public List<PycReportePeti> getReportePeticion(Integer idArea, Integer idDepartamento, Integer idSolicitante,
                                                   String prioridad, Integer idAnalista, String estado);

    /**
     * Inserta o actualiza la relación entre un usuario y un proceso.
     *
     * @param userData Datos del usuario proceso a insertar/actualizar
     * @return ID del usuario procesado
     */
    public Integer insertUserProceso(PycInsertUserPro userData);

    /**
     * Inserta un nuevo usuario en el sistema PYCGES.
     *
     * @param usuarioData Datos del usuario a insertar
     * @return ID del usuario creado
     */
    public Integer insertUsuario(PycInsertUsuario usuarioData);

    /**
     * Actualiza la información de un documento asociado a una petición.
     *
     * @param documentoData Datos del documento a actualizar
     * @return ID de la petición actualizada
     */
    public Integer actDocumento(PycActDocumento documentoData);

    /**
     * Obtiene la lista de observaciones asociadas a una petición específica.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBSER_PETICION.
     * Ahora requiere el parámetro numaOUsuario para identificar al usuario específico.
     *
     * @param idPeticion ID de la petición
     * @param numaOUsuario Nombre o número de usuario (opcional)
     * @return Lista de observaciones de la petición
     */
    public List<PycObserPet> getObserPeticion(Integer idPeticion, String numaOUsuario);

    /**
     * Obtiene el resumen estadístico de actividades de una petición específica por categoría.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_RESUMEN_ACTIVIDAD.
     *
     * @param idPeticion ID de la petición
     * @param idCategoria ID de la categoría del tablero
     * @return Lista con el resumen de actividades
     */
    public List<PycResumenActi> getResumenActividad(Integer idPeticion, Integer idCategoria);

    /**
     * Obtiene el avance detallado de peticiones con información de observaciones, usuarios y estados.
     *
     * @param idProceso ID del proceso (opcional)
     * @param idArea ID del área (opcional)
     * @param estado IDs de estados separados por coma (opcional)
     * @param codcia Código de compañía (opcional)
     * @param anio Año de filtro (opcional)
     * @return Lista de objetos PycPetAvance con el avance de peticiones
     */
    public List<PycPetAvance> getPeticionAvance(Integer idProceso, Integer idArea, String estado, String codcia, Integer anio);
    /**
     * Obtiene la lista de peticiones con información de su petición siguiente asociada.
     *
     * @param idProceso ID del proceso (opcional)
     * @param idArea ID del área (opcional)
     * @param estado Lista de estados separados por comas (opcional)
     * @param codcia Código de compañía (opcional)
     * @param anio Año de filtro (opcional)
     * @return Lista de objetos PycPetAvanceSig con la información de peticiones y sus siguientes
     */
    public List<PycPetAvanceSig> getPeticionAvanceSiguiente(Integer idProceso, Integer idArea, String estado, String codcia, Integer anio);
    /**
     * Obtiene la lista de valores activos filtrados por tipo y proceso.
     *
     * @param tipo Tipo de valor a consultar
     * @param idProceso ID del proceso
     * @return Lista de objetos PycListVal con los valores y descripciones
     */
    public List<PycListVal> getListaVal(String tipo, Integer idProceso);
    /**
     * Obtiene la lista de oficinas disponibles para un usuario específico en un proceso determinado.
     * La función considera el indicador de oficina del usuario para determinar si retorna
     * todas las oficinas del proceso o solo las asignadas al usuario.
     *
     * @param idProceso ID del proceso
     * @param idUsuario ID del usuario
     * @return Lista de objetos PycOficina con las oficinas disponibles
     */
    public List<PycOficina> getOficinas(Integer idProceso, Integer idUsuario);
    /**
     * Obtiene la lista de canales disponibles para un usuario específico en un proceso y oficina determinados.
     * La función implementa lógica condicional compleja basada en los indicadores ind_canal e ind_oficina:
     * - Si ind_canal = 'T': Retorna todos los canales activos del proceso para la oficina especificada
     * - Si ind_canal != 'T' AND ind_oficina = 'T': Retorna solo canales asignados al usuario para el proceso
     * - Si ind_canal != 'T' AND ind_oficina != 'T': Retorna solo canales asignados al usuario para la oficina y proceso específicos
     *
     * @param idProceso ID del proceso
     * @param idUsuario ID del usuario
     * @param idOficina ID de la oficina
     * @return Lista de objetos PycCanal con los canales disponibles
     */
    public List<PycCanal> getCanales(Integer idProceso, Integer idUsuario, Integer idOficina);
    /**
     * Obtiene la lista de subordinados disponibles para un supervisor específico considerando canal, oficina, indicador de login y proceso.
     * La función implementa lógica condicional extremadamente compleja basada en múltiples indicadores (ind_admin, ind_oficina, ind_canal):
     * - Validaciones iniciales: Si indLogin = 'S' obtiene id_proceso del canal y ind_admin del usuario
     * - Obtiene ind_oficina e ind_canal del supervisor para el proceso
     * - ESCENARIO 1: ind_admin = 'S' OR ind_oficina = 'T' (Supervisor con privilegios administrativos)
     *   - SUB-ESCENARIO 1A: ind_canal = 'T' → Todos los subordinados de la oficina (incluye ID quemado 21)
     *   - SUB-ESCENARIO 1B: ind_canal != 'T' → Subordinados de la oficina filtrados por canal específico
     * - ESCENARIO 2: ind_admin != 'S' AND ind_oficina != 'T' (Supervisor con acceso restringido)
     *   - SUB-ESCENARIO 2A: ind_canal = 'T' → Subordinados directos del supervisor en la oficina
     *   - SUB-ESCENARIO 2B: ind_canal != 'T' → Subordinados directos filtrados por canal y oficina específicos
     * - FALLBACK: Si no se encuentran subordinados, retorna al propio supervisor como único resultado
     *
     * @param canal ID del canal
     * @param oficina ID de la oficina
     * @param idSupervisor ID del supervisor
     * @param indLogin Indicador de login (S/N)
     * @param proceso ID del proceso
     * @return Lista de objetos PycSubordinados con los subordinados disponibles
     */
    public List<PycSubordinados> getSubordinados(String canal, String oficina, String idSupervisor, String indLogin, String proceso);
    /**
     * Obtiene la información completa de un usuario específico incluyendo datos personales, área, departamento, perfil y configuraciones.
     * La función realiza múltiples joins entre tablas de usuario, área, departamento, perfil y proceso para obtener información detallada:
     * - Datos básicos del usuario: nombres, apellidos, email, género, fechas, estado
     * - Información organizacional: área y departamento del usuario
     * - Perfiles y procesos: múltiples perfiles con indicador de perfil por defecto
     * - Configuraciones: URLs de perfil e inicio
     * - Filtros: solo registros activos en todas las tablas relacionadas (excepto usuario)
     * - Búsqueda: case-insensitive por nombre de usuario en la base de datos
     * - Ordenamiento: por IND_DEFAULT DESC (perfil por defecto primero)
     *
     * @param usuario Nombre de usuario en la base de datos
     * @return Lista de objetos PycInfoUsuario con la información completa del usuario
     */
    public List<PycInfoUsuario> getInfoUsuario(String usuario);
    /**
     * Obtiene la información de un ramo específico basado en el código de ramo, modalidad y compañía.
     * La función consulta la tabla PYC_TIPO_PETICION con parámetros que tienen valores por defecto:
     * - P_COD_RAMO: Código del ramo (obligatorio)
     * - P_COD_MODALIDAD: Código de modalidad (opcional, valor por defecto '999')
     * - P_COD_CIA: Código de compañía (opcional, valor por defecto '2')
     * - Consulta directa en tabla PYC_TIPO_PETICION sin joins complejos
     * - Filtros específicos por COD_RAMO, COD_MODALIDAD y COD_CIA
     * - Retorna información del tipo de petición asociado al ramo
     *
     * @param codRamo Código del ramo (obligatorio)
     * @param codModalidad Código de modalidad (opcional, default '999')
     * @param codCia Código de compañía (opcional, default '2')
     * @return Lista de objetos PycRamo con la información del ramo
     */
    public List<PycRamo> obtenerRamo(String codRamo, Integer codModalidad, String codCia);
    /**
     * Obtiene los datos de variables equivalentes basado en el código de ramo, modalidad y compañía.
     * La función consulta la tabla PYC_DATO_VAR_EQUIV con parámetros que tienen valores por defecto y filtros avanzados:
     * - P_COD_RAMO: Código del ramo (obligatorio, se usa con LIKE para búsquedas flexibles)
     * - P_COD_MODALIDAD: Código de modalidad (opcional, valor por defecto '999')
     * - P_COD_CIA: Código de compañía (opcional, valor por defecto '2')
     * - Filtros especiales: LIKE en COD_RAMO, NVL para valores por defecto, ESTADO = 'ACT'
     * - Consulta directa en tabla PYC_DATO_VAR_EQUIV sin joins complejos
     * - Retorna mapeo entre variables PYC y REEF para formularios y secciones específicas
     *
     * @param codRamo Código del ramo (obligatorio, se usa con LIKE)
     * @param codModalidad Código de modalidad (opcional, default '999')
     * @param codCia Código de compañía (opcional, default '2')
     * @return Lista de objetos PycDatoVarEquiv con los datos de variables equivalentes
     */
    public List<PycDatoVarEquiv> obtenerDatVarEquiv(String codRamo, Integer codModalidad, String codCia);
    /**
     * Obtiene los perfiles asignados a un usuario específico para un proceso determinado.
     * La función realiza un JOIN entre las tablas PYC_USUARIO_PERFIL y PYC_PERFIL con filtros específicos:
     * - pIdUsuario: ID del usuario (obligatorio, se convierte a UPPER)
     * - pIdProceso: ID del proceso (obligatorio)
     * - JOIN: A.ID_PERFIL = B.ID_PERFIL entre PYC_USUARIO_PERFIL (A) y PYC_PERFIL (B)
     * - Filtros: Solo registros activos (ESTADO = 'ACT') en ambas tablas
     * - Filtros específicos: Por ID_PROCESO e ID_USUARIO
     * - Ordenamiento: Por A.ORDEN y B.NOMBRE_PERFIL
     * - Retorna información completa del perfil y su asignación al usuario
     *
     * @param idUsuario ID del usuario (obligatorio)
     * @param idProceso ID del proceso (obligatorio)
     * @return Lista de objetos PycPerfilUsu con los perfiles del usuario para el proceso
     */
    public List<PycPerfilUsu> perfilesUsuario(Integer idUsuario, Integer idProceso);
    /**
     * Obtiene la lista de categorías de actividades asociadas a una petición específica con estadísticas de avance.
     *
     * @param idPeticion ID de la petición (obligatorio)
     * @return Lista de objetos PycCategoriaPet con las categorías y estadísticas de la petición
     */
    public List<PycCategoriaPet> getCategoriaPeticion(Integer idPeticion);

    /**
     * Obtiene la lista completa de peticiones con información básica para mostrar en el tablero.
     *
     * @return Lista de objetos PycPeticionTab con la información básica de todas las peticiones
     */
    public List<PycPeticionTab> getPeticionesTablero();
    /**
     * Obtiene la lista jerárquica de opciones de menú disponibles para un perfil específico en un proceso determinado.
     * Ahora requiere el parámetro numaOUsuario para identificar al usuario específico.
     *
     * @param idPerfil ID del perfil (obligatorio)
     * @param idProceso ID del proceso (obligatorio)
     * @param numaOUsuario Nombre o número de usuario (obligatorio)
     * @return Lista de objetos PycOpcionUsu con las opciones de menú jerárquicas del usuario
     */
    public List<PycOpcionUsu> getOpcionUsuario(Integer idPerfil, Integer idProceso, String numaOUsuario);
    /**
     * Obtiene un reporte detallado de peticiones con información de programación filtradas por año y proceso.
     *
     * @param anio Año de filtro para las peticiones (obligatorio)
     * @param idProceso ID del proceso (obligatorio)
     * @return Lista de objetos PycReportePetProg con el reporte detallado de peticiones
     */
    public List<PycReportePetProg> getReportePeticionProgramacion(Integer anio, Integer idProceso);
    /**
     * Obtiene estadísticas agrupadas por estado de peticiones con conteos, totales y porcentajes.
     *
     * @param proceso ID del proceso (obligatorio)
     * @param area ID del área (obligatorio)
     * @param anio Año de filtro (obligatorio)
     * @param estado Lista de IDs de estados separados por coma (opcional, ej. "1,2,3")
     * @param codCia Código de compañía (opcional, puede ser null)
     * @return Lista de objetos PycEstadisticaEstado con las estadísticas por estado
     */
    public List<PycEstadisticaEstado> getEstadisticaEstados(Integer proceso, Integer area, Integer anio, String estado, String codCia);

    /**
     * Obtiene estadísticas agrupadas por estado de peticiones siguientes con conteos, totales y porcentajes.
     *
     * @param proceso ID del proceso (obligatorio)
     * @param area ID del área (obligatorio)
     * @param anio Año de filtro (obligatorio)
     * @param estado Lista de IDs de estados de petición principal separados por coma (opcional, ej. "1,2,3")
     * @param codCia Código de compañía (opcional, puede ser null)
     * @return Lista de objetos PycEstadistEstadoSig con las estadísticas por estado de peticiones siguientes
     */
    public List<PycEstadistEstadoSig> getEstadisticaEstadosSiguientes(Integer proceso, Integer area, Integer anio, String estado, String codCia);
    /**
     * Actualiza el estado de una o múltiples peticiones en el sistema PYCGES.
     * Maneja lógica compleja para actualización de estados con validaciones y transiciones.
     * Incluye soporte para usuario personalizado en operaciones de auditoría.
     *
     * @param estadoData Datos para actualizar el estado de la petición (incluye numaOUsuario opcional)
     * @return ID de la petición procesada (último ID en caso de múltiples peticiones)
     */
    public Integer updateEstadoPeticion(PycUpdEstadoPet estadoData);
    /**
     * Actualiza la URL y PATH del perfil de un usuario específico.
     *
     * @param perfilData Datos para actualizar el path y URL del perfil del usuario
     * @return Resultado de la operación (1=éxito, 0=error)
     */
    public Integer updatePathPerfil(PycUpdPathPerfil perfilData);

    /**
     * Actualiza las fechas de login/logout de un usuario específico según el estado de la sesión.
     *
     * @param sesionData Datos para actualizar la sesión del usuario
     * @return Resultado de la operación (1=éxito, 0=error)
     */
    public Integer updateSesion(PycUpdSesion sesionData);

    /**
     * Registra una acción específica en la bitácora del sistema PYCGES.
     *
     * @param bitacoraData Datos de la acción a registrar en la bitácora
     * @return true si la operación se ejecutó correctamente, false en caso de error
     */
    public boolean bitacoraAccion(PycBitacoraAccion bitacoraData);

    /**
     * Gestiona la asignación automática de usuarios para procesos, perfiles, aplicaciones y estados específicos.
     *
     * @param asignacionData Datos de la asignación automática a gestionar
     * @return true si la operación se ejecutó correctamente, false en caso de error
     */
    public boolean gestionUsrAsignacionAuto(PycGestUsrAsigAuto asignacionData);

    /**
     * Obtiene el estado de un usuario específico mediante sus credenciales de acceso.
     *
     * @param usuario Nombre de usuario
     * @param clave Clave del usuario
     * @return Lista de objetos PycEstadoUsu con la información del estado del usuario
     */
    public List<PycEstadoUsu> getEstadoUsuario(String usuario, String clave);

    /**
     * Inserta una nueva observación en una petición específica del sistema PYCGES.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_INSERT_OBSERVACION.
     * La función ahora incluye el parámetro P_NUMA_O_USUARIO para reemplazar el uso de USER.
     *
     * @param observacionData Datos de la observación a insertar (incluye numaOUsuario opcional)
     * @return ID de la observación creada
     */
    public Integer insertObservacion(PycInsertObservacion observacionData);

    /**
     * Obtiene la lista de analistas disponibles para una actividad específica de una petición.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_ANALISTA_ACTIVIDAD.
     * La función realiza una consulta UNION que incluye:
     * - Analistas asignados a la petición desde PYC_ANALISTA_PETICION
     * - Usuario solicitante de la petición desde PYC_PETICION
     * - Verificación de asignación a la actividad específica
     * - Lógica de imágenes de perfil con valores por defecto según género
     *
     * @param peticion ID de la petición
     * @param actividad ID de la actividad
     * @return Lista de objetos PycAnalistaActi con la información de los analistas
     */
    public List<PycAnalistaActi> getAnalistaActividad(Integer peticion, Integer actividad);

    /**
     * Obtiene los datos variables de un formulario específico filtrados por proceso, formulario y sección.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_DAT_VAR_FORM_SECC.
     * Retorna información completa de controles de formulario incluyendo configuración HTML,
     * validaciones, dependencias y valores por defecto o específicos de petición.
     *
     * @param idProceso ID del proceso
     * @param idFormulario ID del formulario
     * @param idSeccion ID de la sección
     * @param idPeticion ID de la petición (opcional)
     * @return Lista de objetos PycGetDatVarFormSec con la información de datos variables del formulario
     */
    public List<PycGetDatVarFormSec> getDatVarFormSecc(Integer idProceso, Integer idFormulario, Integer idSeccion, Integer idPeticion);

    /**
     * Obtiene la bitácora completa de una petición con información detallada de cambios de estado.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBTENER_BITACORA_PETICION.
     * Retorna información completa de la bitácora incluyendo cambios de estado, responsables,
     * observaciones, tiempos transcurridos y contenido HTML formateado.
     *
     * @param idPeticion ID de la petición
     * @return Lista de objetos PycGetBitacoraPet con la información completa de la bitácora
     */
    public List<PycGetBitacoraPet> getBitacoraPeticion(Integer idPeticion);

    /**
     * Obtiene la lista de tipos de petición disponibles para un proceso específico.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_TIPO_PETICION.
     * Retorna información completa de los tipos de petición incluyendo descripción,
     * estado, usuario y fecha de creación/modificación.
     *
     * @param idProceso ID del proceso
     * @return Lista de objetos PycTipoPeticion con la información de los tipos de petición
     */
    public List<PycTipoPeticion> getTipoPeticion(Integer idProceso);

    /**
     * Obtiene la lista de estados de transición disponibles para un perfil, proceso y estado específicos.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_CAMBIO_ESTADO.
     * Retorna información de los estados de transición permitidos incluyendo estado inicial,
     * estado final y nombre del estado final.
     *
     * @param idPerfil ID del perfil
     * @param idProceso ID del proceso
     * @param idEstado ID del estado inicial
     * @return Lista de objetos PycEstadoTransc con la información de los estados de transición
     */
    public List<PycEstadoTransc> getCambioEstado(Integer idPerfil, Integer idProceso, Integer idEstado);

    /**
     * Obtiene la lista de documentos asociados a una petición específica.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_DOCUMENTO_PETICION.
     * Retorna información completa de los documentos incluyendo documentos existentes
     * y documentos disponibles para agregar según el tipo de petición y perfil.
     *
     * @param idPeticion ID de la petición
     * @param perfilAct ID del perfil actual (opcional)
     * @return Lista de objetos PycDocPeticion con la información de los documentos
     */
    public List<PycDocPeticion> getDocumentoPeticion(Integer idPeticion, Integer perfilAct);

    /**
     * Obtiene la lista de documentos requeridos para un tipo de petición específico.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_DOC_X_PETICION.
     * Retorna información completa de los documentos requeridos incluyendo obligatoriedad,
     * configuración de archivos múltiples, extensiones permitidas y tamaño máximo.
     *
     * @param idTipo ID del tipo de petición
     * @return Lista de objetos PycDocXPeticion con la información de los documentos requeridos
     */
    public List<PycDocXPeticion> getDocXPeticion(Integer idTipo);

    /**
     * Obtiene los datos variables de formulario basándose en el proceso y tipo de petición.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_DAT_VAR_FORM_SECC_TIPPE.
     * Retorna información completa de los datos variables incluyendo configuración HTML,
     * validaciones, dependencias y valores por defecto, resolviendo automáticamente
     * el formulario y sección correspondientes al tipo de petición.
     *
     * @param idProceso ID del proceso
     * @param idTipo ID del tipo de petición
     * @return Lista de objetos PycDatVarForSeccTippe con la información de datos variables del formulario
     */
    public List<PycDatVarForSeccTippe> getDatVarFormSeccTippe(Integer idProceso, Integer idTipo);

    /**
     * Crea o actualiza un dato variable específico de una petición.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_NEW_DAT_VAR_PETICION.
     * Maneja operaciones UPSERT para almacenar valores de formularios dinámicos
     * asociados a peticiones específicas con validación de integridad referencial.
     *
     * @param datVarData Datos del variable de petición a crear o actualizar
     * @return ID del registro creado o actualizado
     */
    public Integer newDatVarPeticion(PycNewDatVarPet datVarData);

    /**
     * Obtiene la lista de perfiles asociados a un proceso específico.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_PERFIL.
     * Retorna información completa de perfiles incluyendo nombre del proceso y perfil,
     * excluyendo perfiles configurados en la lista de exclusiones del sistema.
     *
     * @param idProceso ID del proceso
     * @return Lista de objetos PycGetPerfiles con la información de perfiles del proceso
     */
    public List<PycGetPerfiles> getPerfilesPorProceso(Integer idProceso);

    /**
     * Obtiene la lista de estados asociados a un perfil específico dentro de un proceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_ESTADO_PERFIL.
     * Retorna información completa de estados incluyendo datos del perfil y estado,
     * excluyendo perfiles configurados en la lista de exclusiones del sistema.
     *
     * @param idProceso ID del proceso
     * @param idPerfil ID del perfil
     * @return Lista de objetos PycGetEstadoPerfilCod con la información de estados del perfil
     */
    public List<PycGetEstadoPerfilCod> getEstadoPerfilCod(Integer idProceso, Integer idPerfil);

    /**
     * Asigna una petición específica a un desarrollador/analista.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_ASIGNA_PETICION_DESA.
     * Maneja lógica compleja de asignación con operaciones UPSERT y actualización
     * de responsabilidades según la configuración del proceso.
     * Incluye soporte para el parámetro opcional p_numa_o_usuario.
     *
     * @param asignacionData Datos de la asignación de petición a desarrollador (incluye numaOUsuario opcional)
     * @return ID de la petición procesada
     */
    public Integer asignaPeticionDesa(PycAsignaPeticionDesa asignacionData);

    /**
     * Obtiene la lista de responsables asociados a una petición específica.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_RESPONSABLE_PETICION.
     * Retorna información de solicitante y usuarios asignados con sus perfiles,
     * estados de conexión e información detallada de asignaciones.
     *
     * @param idPeticion ID de la petición
     * @param numaOUsuario Número o usuario que realiza la consulta (opcional)
     * @return Lista de objetos PycRespPeti con información de responsables
     */
    public List<PycRespPeti> getResponsablePeticion(Integer idPeticion, String numaOUsuario);

    /**
     * Obtiene la lista de actividades asociadas a una petición específica.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_ACT_PETICION.
     * Retorna información detallada de actividades incluyendo datos básicos,
     * información de horas (base, real, pendientes), fechas formateadas,
     * usuario asignado con nombre completo y URL de perfil, estado y categoría del tablero.
     *
     * @param idPeticion ID de la petición
     * @param idActividad ID de la actividad (opcional)
     * @return Lista de objetos PycActPeticion con información de actividades
     */
    public List<PycActPeticion> getActPeticion(Integer idPeticion, Integer idActividad);

    /**
     * Actualiza los datos de una petición existente en el sistema PYCGES.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_UPDATE_PETICION.
     * Permite actualizar información básica, datos del cliente y parámetros adicionales.
     * Automáticamente inserta una observación de la actualización.
     * Incluye soporte para el parámetro opcional numaOUsuario para auditoría.
     *
     * @param peticionData Datos de la petición a actualizar (incluye todos los parámetros obligatorios y opcionales)
     * @return ID de la petición actualizada
     */
    public Integer updatePeticion(PycUpdPeticion peticionData);

    /**
     * Actualiza los datos de una petición existente en el sistema PYCGES (método POST).
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_UPDATE_PETICION.
     * Funcionalidad idéntica a updatePeticion pero siguiendo el patrón POST del proyecto.
     * Permite actualizar información básica, datos del cliente y parámetros adicionales.
     * Automáticamente inserta una observación de la actualización.
     * Incluye soporte para el parámetro opcional numaOUsuario para auditoría.
     *
     * @param peticionData Datos de la petición a actualizar (incluye todos los parámetros obligatorios y opcionales)
     * @return ID de la petición actualizada
     */
    public Integer actualizarPeticion(PycUpdPeticion peticionData);

    /**
     * Crea una nueva solicitud de Recursos Humanos en el sistema PYCGES.
     * Utiliza la función TRON2000_GT.GC_K_PYC_RRHH_MGT.p_crea_solicitud.
     * Procesa información completa del candidato incluyendo datos personales,
     * académicos, laborales y de ubicación.
     *
     * @param solicitudData Datos de la solicitud de RH a crear (incluye todos los parámetros obligatorios)
     * @return ID de la solicitud creada
     */
    public Integer creaSolicitudRH(PycCreaSoliRH solicitudData);

    /**
     * Obtiene la lista de documentos no obligatorios que faltan por adjuntar a una petición.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_DOC_NOOBLIGATORIOS.
     * Retorna información de documentos no obligatorios que no han sido adjuntados
     * a la petición específica, excluyendo documentos ya presentes.
     *
     * @param idPeticion ID de la petición
     * @return Lista de objetos PycDocNoObli con la información de los documentos no obligatorios faltantes
     */
    public List<PycDocNoObli> getDocNoObligatorios(Integer idPeticion);

    /**
     * Obtiene la lista de categorías activas del tablero asociadas a un proceso específico.
     *
     * @param idProceso ID del proceso
     * @return Lista de objetos PycCatAct con las categorías activas
     */
    public List<PycCatAct> getCategoriaAct(Integer idProceso);

    /**
     * Inserta una nueva actividad en una petición específica.
     * Automáticamente actualiza las fechas de inicio y fin de la petición.
     *
     * @param actividadData Datos de la actividad a insertar
     * @return ID de la actividad creada
     */
    public Integer insertActividad(PycInserAct actividadData);

    /**
     * Edita una actividad existente de una petición específica.
     * Automáticamente recalcula las fechas de inicio y fin de la petición.
     *
     * @param actividadData Datos de la actividad a editar
     * @return ID de la petición actualizada
     */
    public Integer editActividad(PycEditAct actividadData);

    /**
     * Actualiza las horas reales y estado de una actividad específica.
     * Automáticamente inserta una observación de la actualización.
     *
     * @param actividadData Datos de la actividad a actualizar
     * @return ID de la petición actualizada
     */
    public Integer updateActividad(PycUpdAct actividadData);

    /**
     * Actualiza la encuesta de satisfacción de una petición específica.
     * Automáticamente establece el usuario que realiza la encuesta y la fecha actual.
     *
     * @param encuestaData Datos de la encuesta a actualizar
     * @return ID de la petición actualizada
     */
    public String updateEncuesta(PycUpdEnc encuestaData);
}
