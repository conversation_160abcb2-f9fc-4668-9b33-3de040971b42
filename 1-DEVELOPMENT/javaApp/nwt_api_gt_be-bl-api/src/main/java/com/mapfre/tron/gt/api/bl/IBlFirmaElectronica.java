package com.mapfre.tron.gt.api.bl;

import java.util.List;

import com.mapfre.tron.gt.api.model.PLE_MedioPagoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_DepositoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_IdentificadorMedioPago;
import com.mapfre.tron.gt.api.model.PLE_DetallePlanilla;
import com.mapfre.tron.gt.api.model.PLE_CuentaBanco;
import com.mapfre.tron.gt.api.model.PLE_TotalPagoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_MedioPago;
import com.mapfre.tron.gt.api.model.PLE_TipoPlanilla;

public interface IBlFirmaElectronica {

    /**
     * Buscar medios de pago de planilla
     * @param idPlanilla ID de la planilla
     * @return Lista de medios de pago
     */
    public List<PLE_MedioPagoPlanilla> buscarMediosPagoPlanilla(Integer idPlanilla);

    /**
     * Buscar depósitos de planilla
     * @param idPlanilla ID de la planilla
     * @return Lista de depósitos
     */
    public List<PLE_DepositoPlanilla> buscarDepositosPlanilla(Integer idPlanilla);

    /**
     * Buscar identificador de medio de pago
     * @param sistema Sistema (A/T)
     * @param moneda Código de moneda
     * @param medio Medio de pago
     * @param tipo Tipo (N/I)
     * @return Lista de identificadores
     */
    public List<PLE_IdentificadorMedioPago> buscarIdentificadorMedioPago(String sistema, String moneda, String medio, String tipo);

    /**
     * Buscar detalle de planilla
     * @param idPlanilla ID de la planilla
     * @return Lista de detalles
     */
    public List<PLE_DetallePlanilla> buscarDetallePlanilla(Integer idPlanilla);

    /**
     * Obtener cuentas de bancos
     * @param moneda Código de moneda
     * @param entidad Código de entidad
     * @return Lista de cuentas bancarias
     */
    public List<PLE_CuentaBanco> getCuentasBancos(String moneda, String entidad);

    /**
     * Obtener total de pagos de planilla
     * @param idPlanilla ID de la planilla
     * @return Lista de totales de pagos
     */
    public List<PLE_TotalPagoPlanilla> obtenerTotalPagosPlanilla(Integer idPlanilla);

    /**
     * Obtener medios de pago disponibles
     * @return Lista de medios de pago
     */
    public List<PLE_MedioPago> obtenerMedioPago();

    /**
     * Obtener tipos de planilla disponibles
     * @return Lista de tipos de planilla
     */
    public List<PLE_TipoPlanilla> obtenerTipoPlanilla();

}
