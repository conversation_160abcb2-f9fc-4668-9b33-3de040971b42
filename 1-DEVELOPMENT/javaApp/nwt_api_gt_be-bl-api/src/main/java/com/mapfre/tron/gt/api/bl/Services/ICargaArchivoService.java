package com.mapfre.tron.gt.api.bl.Services;

import com.mapfre.tron.gt.api.model.CargaArchivoRequest;

/**
 * Interface para el servicio de carga de archivos via SOAP
 */
public interface ICargaArchivoService {
    
    /**
     * Carga archivos al servicio SOAP
     * @param request Datos de la solicitud de carga
     * @return JSON con la respuesta del servicio SOAP
     */
    String cargarArchivos(CargaArchivoRequest request);
}
