package com.mapfre.tron.gt.api.bl.cache.config;

import java.util.Collection;
import java.util.function.Supplier;

/**
 * Interface for cache management operations. Provides methods for clearing,
 * updating, checking, and retrieving cache values.
 */
public interface ICacheService {

	/**
	 * Evicts a specific cache value by its key.
	 *
	 * @param cacheName the name of the cache
	 * @param cacheKey  the key of the cache value to evict
	 */
	void evictSingleCacheValue(String cacheName, String cacheKey);

	/**
	 * Evicts all values in a specific cache.
	 *
	 * @param cacheName the name of the cache to clear
	 */
	void evictAllCacheValues(String cacheName);

	/**
	 * Evicts all caches managed by the cache manager.
	 */
	void evictAllCaches();

	/**
	 * Retrieves all cache names.
	 *
	 * @return a collection of all cache names
	 */
	Collection<String> getAllCacheNames();

	/**
	 * Retrieves a value from a specific cache by its key.
	 *
	 * @param cacheName the name of the cache
	 * @param cacheKey  the key of the cache value to retrieve
	 * @return the cache value, or null if the key does not exist
	 */
	Object getCacheValue(String cacheName, String cacheKey);

	/**
	 * Puts a value into a specific cache.
	 *
	 * @param cacheName the name of the cache
	 * @param cacheKey  the key for the value
	 * @param value     the value to store in the cache
	 */
	void putCacheValue(String cacheName, String cacheKey, Object value);

	/**
	 * Checks if a specific cache contains a given key.
	 *
	 * @param cacheName the name of the cache
	 * @param cacheKey  the key to check
	 * @return true if the cache contains the key, false otherwise
	 */
	boolean containsCacheValue(String cacheName, String cacheKey);

	/**
	 * Retrieves the size of a specific cache.
	 *
	 * @param cacheName the name of the cache
	 * @return the size of the cache, or -1 if the size cannot be determined
	 */
	int getCacheSize(String cacheName);

	/**
	 * Refreshes the cache with new data using a data loader function.
	 *
	 * @param cacheName  the name of the cache to refresh
	 * @param dataLoader a function that loads new data for the cache
	 */
	void refreshCache(String cacheName, Supplier<Object> dataLoader);

}