package com.mapfre.tron.gt.api.bl;

import com.mapfre.tron.gt.api.model.CobroResponse;
import com.mapfre.tron.gt.api.model.EntidadResponse;
import com.mapfre.tron.gt.api.model.InsertarDetalleRutaDiarioRequest;
import com.mapfre.tron.gt.api.model.InsertarDetalleRutaDiarioResponse;
import com.mapfre.tron.gt.api.model.InsertarDetalleRuta2Request;
import com.mapfre.tron.gt.api.model.InsertarDetalleRuta2Response;
import com.mapfre.tron.gt.api.model.TipoMedioPagoRequest;
import com.mapfre.tron.gt.api.model.TipoMedioPagoResponse;
import com.mapfre.tron.gt.api.model.AvisoReciboRutaRequest;
import com.mapfre.tron.gt.api.model.AvisoReciboRutaResponse;
import com.mapfre.tron.gt.api.model.ReciboRutaRequest;
import com.mapfre.tron.gt.api.model.ReciboRutaResponse;
import com.mapfre.tron.gt.api.model.AutenticarUsuarioResponse;
import com.mapfre.tron.gt.api.model.ObtenerRolesUsuarioResponse;
import com.mapfre.tron.gt.api.model.CajeroUsuarioResponse;
import com.mapfre.tron.gt.api.model.AccionesPorRolesResponse;
import com.mapfre.tron.gt.api.model.ModificarRutaResponse;
import com.mapfre.tron.gt.api.model.RutaDiaria;

import com.mapfre.tron.gt.api.model.*;

import java.time.LocalDate;
import java.util.List;

public interface IBlCobroMovil {
    /**
     * Obtiene la lista de entidades financieras según sistema y moneda
     *
     * @param codigoSistema Código del sistema
     * @param codigoMoneda Código de moneda
     * @return Respuesta con la lista de entidades financieras
     */
    public EntidadResponse ListaEntidad(Integer codigoSistema, String codigoMoneda);
    
    /**
     * Obtiene la lista de opciones de cobro
     *
     * @return Respuesta con la lista de opciones de cobro
     */
    public CobroResponse ListaOpcionesCobro();
    
    /**
     * Crea una ruta diaria para cobros
     *
     * @param idUsuarioCrea ID del usuario que crea la ruta
     * @param idUsuarioAsignado ID del usuario al que se asigna la ruta
     * @return Información de la ruta diaria creada
     */
    public RutaDiaria CrearRutaDiaria(Integer idUsuarioCrea, Integer idUsuarioAsignado);
    
    /**
     * Crea una ruta para cobros
     *
     * @param idUsuarioCrea ID del usuario que crea la ruta
     * @param idUsuarioAsignado ID del usuario al que se asigna la ruta
     * @return Información de la ruta creada
     */
    public RutaDiaria CrearRuta(Integer idUsuarioCrea, Integer idUsuarioAsignado);
    
    /**
     * Modifica una ruta existente
     *
     * @param idUsuarioCrea ID del usuario que modifica la ruta
     * @param idUsuarioAsignado ID del usuario al que se asigna la ruta
     * @param idRuta ID de la ruta a modificar
     * @return Respuesta con el resultado de la modificación
     */
    public ModificarRutaResponse ModificarRuta(Integer idUsuarioCrea, Integer idUsuarioAsignado, Integer idRuta);
    
    /**
     * Inserta detalle de ruta diaria
     *
     * @param request Parámetros para insertar el detalle de ruta diaria
     * @return Respuesta con el resultado de la operación
     */
    public InsertarDetalleRutaDiarioResponse InsertarDetalleRutaDiario(InsertarDetalleRutaDiarioRequest request);
    
    /**
     * Inserta detalle de ruta
     *
     * @param request Parámetros para insertar el detalle de ruta
     * @return Respuesta con el resultado de la operación
     */
    public InsertarDetalleRuta2Response InsertarDetalleRuta2(InsertarDetalleRuta2Request request);
    
    /**
     * Inserta tipo de medio de pago
     *
     * @param request Parámetros para insertar el tipo de medio de pago
     * @return Respuesta con el resultado de la operación
     */
    public TipoMedioPagoResponse InsertarTipoMedioPago(TipoMedioPagoRequest request);
    
    /**
     * Actualiza aviso de recibo de ruta
     *
     * @param request Parámetros para actualizar el aviso de recibo
     * @return Respuesta con el resultado de la operación
     */
    public AvisoReciboRutaResponse UpdateAvisoReciboRuta(AvisoReciboRutaRequest request);
    
    /**
     * Actualiza recibo de ruta
     *
     * @param request Parámetros para actualizar el recibo
     * @return Respuesta con el resultado de la operación y correos electrónicos asociados
     */
    public UpdateReciboRutaResponse UpdateReciboRuta(ReciboRutaRequest request);
    
    /**
     * Autentica un usuario con credenciales
     *
     * @param usuario Nombre de usuario
     * @param clave Contraseña del usuario
     * @return Respuesta con la información del usuario autenticado
     */
    public AutenticarUsuarioResponse AutenticarUsuario(String usuario, String clave);
    
    /**
     * Obtiene los roles asignados a un usuario
     *
     * @param idUsuario ID del usuario
     * @return Respuesta con la lista de roles del usuario
     */
    public ObtenerRolesUsuarioResponse ObtenerRolesUsuario(Integer idUsuario);
    
    /**
     * Obtiene los cajeros asociados a un usuario
     *
     * @param usuario ID del usuario
     * @return Respuesta con la lista de cajeros asociados al usuario
     */
    public CajeroUsuarioResponse ListarCajeroUsuario(Integer usuario);
    
    /**
     * Obtiene las acciones permitidas para roles específicos
     *
     * @param roles Cadena de roles separados por coma
     * @return Respuesta con la lista de acciones permitidas para los roles
     */
    public AccionesPorRolesResponse ObtenerAccionesPorRoles(String roles);

    /**
     * Obtiene los ids de las rutas cobradas
     *
     * @return Lista de ids rutas
     */
    RutasCobradasResponse getRutasCobradas();

    /**
     * Obtiene información de rutas con localización
     *
     * @param fechaInicio Fecha de inicio
     * @param fechaFin    Fecha de fin
     * @param idRuta      ID de la ruta
     * @return Lista de rutas con información de localización
     */
    List<RutaLocalizacionResponse> obtenerRutasLocalizacion(
            LocalDate fechaInicio, LocalDate fechaFin, Integer idRuta);
            
    /**
     * Obtiene información de localización de pagos
     *
     * @param fechaInicio Fecha de inicio
     * @param fechaFin    Fecha de fin
     * @param idRuta      ID de la ruta
     * @return Lista de pagos con información de localización
     */
    List<LocalizacionPagosResponse> obtenerLocalizacionPagos(
            LocalDate fechaInicio, LocalDate fechaFin, Integer idRuta);
            
    /**
     * Obtiene información para llenar el detalle de cierre de caja
     *
     * @param idUsuario ID del usuario
     * @return Lista con información de detalle de cierre de caja
     */
    List<LlenarDetalleCierreCajaResponse> llenarDetalleCierreCaja(Integer idUsuario);
    
    /**
     * Obtiene información de rutas para cierre de caja
     *
     * @param idUsuario ID del usuario
     * @return Lista con información de rutas para cierre de caja
     */
    List<InfoRutaCierreCajaResponse> getInfoRutaCierreCaja(Integer idUsuario);
    
    /**
     * Obtiene información de rutas para un usuario y fecha específicos
     *
     * @param idUsuario ID del usuario
     * @param fecha Fecha en formato YYYY-MM-DD (se convertirá a DD/MM/YYYY para la BD)
     * @return Lista con información de rutas
     */
    List<InfoRutaResponse> getInfoRuta(Integer idUsuario, String fecha);
    
    /**
     * Obtiene el porcentaje de avance de una ruta o del total de rutas de un usuario
     *
     * @param idUsuario ID del usuario
     * @param ruta ID de la ruta (para tipo=2)
     * @param tipo Tipo de consulta (1=rutas del usuario, 2=detalle de una ruta)
     * @param fecha Fecha en formato YYYY-MM-DD (se convertirá a DD/MM/YYYY para la BD) puede ser null
     * @return Porcentaje de avance
     */
    InfoPromedioResponse getPromedios(Integer idUsuario, Integer ruta, Integer tipo, String fecha);
    
    /**
     * Obtiene las pólizas para facturas de una ruta específica
     *
     * @param idRuta ID de la ruta
     * @return Lista de pólizas par facturas de la ruta
     */
    List<ModRutaPolizaFacturasResponse> getModRutaPolizaFacturas(Integer idRuta);

    /**
     * Obtiene las pólizas de una ruta específica
     *
     * @param idRuta ID de la ruta
     * @return Lista de pólizas de la ruta
     */
    List<ModRutaPolizaResponse> getModRutaPoliza(Integer idRuta);

    /**
     * Obtiene los recibos cobrados de una póliza en una ruta específica
     *
     * @param idRuta ID de la ruta
     * @param idePol ID de la póliza (opcional)
     * @param numCertis Número de certificado (opcional)
     * @return Lista de recibos cobrados
     */
    List<InfoRecibosPolizaRutaCobradosResponse> getInfoRecibosPolizaRutaCobrados(Integer idRuta, String idePol, String numCertis);

    /**
     * Obtiene los recibos de una póliza en una ruta específica
     *
     * @param idRuta ID de la ruta
     * @param idePol ID de la póliza
     * @param numCertis Número de certificado
     * @return Lista de recibos de la póliza
     */
    List<InfoRecibosPolizaRutaResponse> getInfoRecibosPolizaRuta(Integer idRuta, String idePol, String numCertis);

    /**
     * Obtiene la información de firma de un recibo específico
     *
     * @param idRuta ID de la ruta
     * @param recibo Número de recibo
     * @return Lista de información de firma del recibo
     */
    List<InfoFirmaReciboResponse> getInfoFirmaRecibo(String idRuta, String recibo);

    /**
     * Obtiene la lista de cobradores
     *
     * @return Lista de cobradores
     */
    List<CobradoresResponse> getCobradores();

    /**
     * Obtiene la lista de tipos de pago
     *
     * @return Lista de tipos de pago
     */
    List<TipoPagoResponse> getTipoPago();
}
