package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * InsertarDetalleRutaDiarioRequest
 */
@Validated

public class InsertarDetalleRutaDiarioRequest   {
  @JsonProperty("ruta")
  private Integer ruta = null;

  @JsonProperty("idpol")
  private String idpol = null;

  @JsonProperty("codpol")
  private String codpol = null;

  @JsonProperty("numpol")
  private String numpol = null;

  @JsonProperty("esAviso")
  private String esAviso = null;

  @JsonProperty("numcert")
  private String numcert = null;

  @JsonProperty("numreq")
  private String numreq = null;

  @JsonProperty("moneda")
  private String moneda = null;

  @JsonProperty("total")
  private String total = null;

  @JsonProperty("cuota")
  private String cuota = null;

  @JsonProperty("sistema")
  private String sistema = null;

  @JsonProperty("asegurado")
  private String asegurado = null;

  @JsonProperty("direccion")
  private String direccion = null;

  @JsonProperty("fechaVencimiento")
  private String fechaVencimiento = null;

  public InsertarDetalleRutaDiarioRequest ruta(Integer ruta) {
    this.ruta = ruta;
    return this;
  }

  /**
   * Número de ruta
   * @return ruta
  **/
  @ApiModelProperty(example = "26", required = true, value = "Número de ruta")
  @NotNull


  public Integer getRuta() {
    return ruta;
  }

  public void setRuta(Integer ruta) {
    this.ruta = ruta;
  }

  public InsertarDetalleRutaDiarioRequest idpol(String idpol) {
    this.idpol = idpol;
    return this;
  }

  /**
   * ID de la póliza
   * @return idpol
  **/
  @ApiModelProperty(example = "9783443", required = true, value = "ID de la póliza")
  @NotNull


  public String getIdpol() {
    return idpol;
  }

  public void setIdpol(String idpol) {
    this.idpol = idpol;
  }

  public InsertarDetalleRutaDiarioRequest codpol(String codpol) {
    this.codpol = codpol;
    return this;
  }

  /**
   * Código de la póliza
   * @return codpol
  **/
  @ApiModelProperty(example = "SCKD", required = true, value = "Código de la póliza")
  @NotNull


  public String getCodpol() {
    return codpol;
  }

  public void setCodpol(String codpol) {
    this.codpol = codpol;
  }

  public InsertarDetalleRutaDiarioRequest numpol(String numpol) {
    this.numpol = numpol;
    return this;
  }

  /**
   * Número de la póliza
   * @return numpol
  **/
  @ApiModelProperty(example = "1707", required = true, value = "Número de la póliza")
  @NotNull


  public String getNumpol() {
    return numpol;
  }

  public void setNumpol(String numpol) {
    this.numpol = numpol;
  }

  public InsertarDetalleRutaDiarioRequest esAviso(String esAviso) {
    this.esAviso = esAviso;
    return this;
  }

  /**
   * S o N para indicar si es aviso(Poliza grupo)
   * @return esAviso
  **/
  @ApiModelProperty(example = "S", required = true, value = "S o N para indicar si es aviso(Poliza grupo)")
  @NotNull


  public String getEsAviso() {
    return esAviso;
  }

  public void setEsAviso(String esAviso) {
    this.esAviso = esAviso;
  }

  public InsertarDetalleRutaDiarioRequest numcert(String numcert) {
    this.numcert = numcert;
    return this;
  }

  /**
   * Número de certificado
   * @return numcert
  **/
  @ApiModelProperty(example = "1", value = "Número de certificado")


  public String getNumcert() {
    return numcert;
  }

  public void setNumcert(String numcert) {
    this.numcert = numcert;
  }

  public InsertarDetalleRutaDiarioRequest numreq(String numreq) {
    this.numreq = numreq;
    return this;
  }

  /**
   * Número de requerimiento
   * @return numreq
  **/
  @ApiModelProperty(example = "107291860", required = true, value = "Número de requerimiento")
  @NotNull


  public String getNumreq() {
    return numreq;
  }

  public void setNumreq(String numreq) {
    this.numreq = numreq;
  }

  public InsertarDetalleRutaDiarioRequest moneda(String moneda) {
    this.moneda = moneda;
    return this;
  }

  /**
   * Código de moneda
   * @return moneda
  **/
  @ApiModelProperty(example = "Q", required = true, value = "Código de moneda")
  @NotNull


  public String getMoneda() {
    return moneda;
  }

  public void setMoneda(String moneda) {
    this.moneda = moneda;
  }

  public InsertarDetalleRutaDiarioRequest total(String total) {
    this.total = total;
    return this;
  }

  /**
   * Monto total
   * @return total
  **/
  @ApiModelProperty(example = "230", required = true, value = "Monto total")
  @NotNull


  public String getTotal() {
    return total;
  }

  public void setTotal(String total) {
    this.total = total;
  }

  public InsertarDetalleRutaDiarioRequest cuota(String cuota) {
    this.cuota = cuota;
    return this;
  }

  /**
   * Número de cuota
   * @return cuota
  **/
  @ApiModelProperty(example = "1/12", required = true, value = "Número de cuota")
  @NotNull


  public String getCuota() {
    return cuota;
  }

  public void setCuota(String cuota) {
    this.cuota = cuota;
  }

  public InsertarDetalleRutaDiarioRequest sistema(String sistema) {
    this.sistema = sistema;
    return this;
  }

  /**
   * Sistema origen
   * @return sistema
  **/
  @ApiModelProperty(example = "A", required = true, value = "Sistema origen")
  @NotNull


  public String getSistema() {
    return sistema;
  }

  public void setSistema(String sistema) {
    this.sistema = sistema;
  }

  public InsertarDetalleRutaDiarioRequest asegurado(String asegurado) {
    this.asegurado = asegurado;
    return this;
  }

  /**
   * Nombre del asegurado
   * @return asegurado
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre del asegurado")


  public String getAsegurado() {
    return asegurado;
  }

  public void setAsegurado(String asegurado) {
    this.asegurado = asegurado;
  }

  public InsertarDetalleRutaDiarioRequest direccion(String direccion) {
    this.direccion = direccion;
    return this;
  }

  /**
   * Dirección del asegurado
   * @return direccion
  **/
  @ApiModelProperty(example = "Zona 10, Ciudad de Guatemala", value = "Dirección del asegurado")


  public String getDireccion() {
    return direccion;
  }

  public void setDireccion(String direccion) {
    this.direccion = direccion;
  }

  public InsertarDetalleRutaDiarioRequest fechaVencimiento(String fechaVencimiento) {
    this.fechaVencimiento = fechaVencimiento;
    return this;
  }

  /**
   * Fecha de vencimiento (formato DD-MM-YYYY)
   * @return fechaVencimiento
  **/
  @ApiModelProperty(example = "01/02/2025", required = true, value = "Fecha de vencimiento (formato DD-MM-YYYY)")
  @NotNull


  public String getFechaVencimiento() {
    return fechaVencimiento;
  }

  public void setFechaVencimiento(String fechaVencimiento) {
    this.fechaVencimiento = fechaVencimiento;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InsertarDetalleRutaDiarioRequest insertarDetalleRutaDiarioRequest = (InsertarDetalleRutaDiarioRequest) o;
    return Objects.equals(this.ruta, insertarDetalleRutaDiarioRequest.ruta) &&
        Objects.equals(this.idpol, insertarDetalleRutaDiarioRequest.idpol) &&
        Objects.equals(this.codpol, insertarDetalleRutaDiarioRequest.codpol) &&
        Objects.equals(this.numpol, insertarDetalleRutaDiarioRequest.numpol) &&
        Objects.equals(this.esAviso, insertarDetalleRutaDiarioRequest.esAviso) &&
        Objects.equals(this.numcert, insertarDetalleRutaDiarioRequest.numcert) &&
        Objects.equals(this.numreq, insertarDetalleRutaDiarioRequest.numreq) &&
        Objects.equals(this.moneda, insertarDetalleRutaDiarioRequest.moneda) &&
        Objects.equals(this.total, insertarDetalleRutaDiarioRequest.total) &&
        Objects.equals(this.cuota, insertarDetalleRutaDiarioRequest.cuota) &&
        Objects.equals(this.sistema, insertarDetalleRutaDiarioRequest.sistema) &&
        Objects.equals(this.asegurado, insertarDetalleRutaDiarioRequest.asegurado) &&
        Objects.equals(this.direccion, insertarDetalleRutaDiarioRequest.direccion) &&
        Objects.equals(this.fechaVencimiento, insertarDetalleRutaDiarioRequest.fechaVencimiento);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ruta, idpol, codpol, numpol, esAviso, numcert, numreq, moneda, total, cuota, sistema, asegurado, direccion, fechaVencimiento);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InsertarDetalleRutaDiarioRequest {\n");
    
    sb.append("    ruta: ").append(toIndentedString(ruta)).append("\n");
    sb.append("    idpol: ").append(toIndentedString(idpol)).append("\n");
    sb.append("    codpol: ").append(toIndentedString(codpol)).append("\n");
    sb.append("    numpol: ").append(toIndentedString(numpol)).append("\n");
    sb.append("    esAviso: ").append(toIndentedString(esAviso)).append("\n");
    sb.append("    numcert: ").append(toIndentedString(numcert)).append("\n");
    sb.append("    numreq: ").append(toIndentedString(numreq)).append("\n");
    sb.append("    moneda: ").append(toIndentedString(moneda)).append("\n");
    sb.append("    total: ").append(toIndentedString(total)).append("\n");
    sb.append("    cuota: ").append(toIndentedString(cuota)).append("\n");
    sb.append("    sistema: ").append(toIndentedString(sistema)).append("\n");
    sb.append("    asegurado: ").append(toIndentedString(asegurado)).append("\n");
    sb.append("    direccion: ").append(toIndentedString(direccion)).append("\n");
    sb.append("    fechaVencimiento: ").append(toIndentedString(fechaVencimiento)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

