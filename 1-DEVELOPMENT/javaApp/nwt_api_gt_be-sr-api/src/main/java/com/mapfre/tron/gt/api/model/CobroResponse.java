package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.mapfre.tron.gt.api.model.Cobro;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CobroResponse
 */
@Validated

public class CobroResponse   {
  @JsonProperty("listaOpcionCobro")
  @Valid
  private List<Cobro> listaOpcionCobro = null;

  @JsonProperty("codigo")
  private String codigo = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  public CobroResponse listaOpcionCobro(List<Cobro> listaOpcionCobro) {
    this.listaOpcionCobro = listaOpcionCobro;
    return this;
  }

  public CobroResponse addListaOpcionCobroItem(Cobro listaOpcionCobroItem) {
    if (this.listaOpcionCobro == null) {
      this.listaOpcionCobro = new ArrayList<>();
    }
    this.listaOpcionCobro.add(listaOpcionCobroItem);
    return this;
  }

  /**
   * Lista de opciones de cobro
   * @return listaOpcionCobro
  **/
  @ApiModelProperty(value = "Lista de opciones de cobro")

  @Valid

  public List<Cobro> getListaOpcionCobro() {
    return listaOpcionCobro;
  }

  public void setListaOpcionCobro(List<Cobro> listaOpcionCobro) {
    this.listaOpcionCobro = listaOpcionCobro;
  }

  public CobroResponse codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Código de resultado de la operación
   * @return codigo
  **/
  @ApiModelProperty(example = "200", value = "Código de resultado de la operación")


  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public CobroResponse mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje descriptivo del resultado de la operación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Operación exitosa", value = "Mensaje descriptivo del resultado de la operación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CobroResponse cobroResponse = (CobroResponse) o;
    return Objects.equals(this.listaOpcionCobro, cobroResponse.listaOpcionCobro) &&
        Objects.equals(this.codigo, cobroResponse.codigo) &&
        Objects.equals(this.mensaje, cobroResponse.mensaje);
  }

  @Override
  public int hashCode() {
    return Objects.hash(listaOpcionCobro, codigo, mensaje);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CobroResponse {\n");
    
    sb.append("    listaOpcionCobro: ").append(toIndentedString(listaOpcionCobro)).append("\n");
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

