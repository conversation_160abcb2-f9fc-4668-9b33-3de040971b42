package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycDocPeticion
 */
@Validated

public class PycDocPeticion   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("idDocumento")
  private Integer idDocumento = null;

  @JsonProperty("nombreDocumento")
  private String nombreDocumento = null;

  @JsonProperty("descripcionDocumento")
  private String descripcionDocumento = null;

  @JsonProperty("localizacion")
  private String localizacion = null;

  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("usuarioBaseDatos")
  private String usuarioBaseDatos = null;

  @JsonProperty("fechaRecepcion")
  private String fechaRecepcion = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("ftpWeb")
  private String ftpWeb = null;

  @JsonProperty("fecha")
  private String fecha = null;

  @JsonProperty("version")
  private String version = null;

  public PycDocPeticion idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador único de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador único de la petición")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycDocPeticion idDocumento(Integer idDocumento) {
    this.idDocumento = idDocumento;
    return this;
  }

  /**
   * Identificador único del documento
   * @return idDocumento
  **/
  @ApiModelProperty(example = "5", value = "Identificador único del documento")


  public Integer getIdDocumento() {
    return idDocumento;
  }

  public void setIdDocumento(Integer idDocumento) {
    this.idDocumento = idDocumento;
  }

  public PycDocPeticion nombreDocumento(String nombreDocumento) {
    this.nombreDocumento = nombreDocumento;
    return this;
  }

  /**
   * Nombre del documento con secuencia si aplica
   * @return nombreDocumento
  **/
  @ApiModelProperty(example = "Cédula de Identidad-2", value = "Nombre del documento con secuencia si aplica")


  public String getNombreDocumento() {
    return nombreDocumento;
  }

  public void setNombreDocumento(String nombreDocumento) {
    this.nombreDocumento = nombreDocumento;
  }

  public PycDocPeticion descripcionDocumento(String descripcionDocumento) {
    this.descripcionDocumento = descripcionDocumento;
    return this;
  }

  /**
   * Descripción del documento
   * @return descripcionDocumento
  **/
  @ApiModelProperty(example = "Documento de identificación personal", value = "Descripción del documento")


  public String getDescripcionDocumento() {
    return descripcionDocumento;
  }

  public void setDescripcionDocumento(String descripcionDocumento) {
    this.descripcionDocumento = descripcionDocumento;
  }

  public PycDocPeticion localizacion(String localizacion) {
    this.localizacion = localizacion;
    return this;
  }

  /**
   * Ubicación o ruta del archivo del documento
   * @return localizacion
  **/
  @ApiModelProperty(example = "/documents/cedula_123456.pdf", value = "Ubicación o ruta del archivo del documento")


  public String getLocalizacion() {
    return localizacion;
  }

  public void setLocalizacion(String localizacion) {
    this.localizacion = localizacion;
  }

  public PycDocPeticion idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Identificador del usuario que subió el documento
   * @return idUsuario
  **/
  @ApiModelProperty(example = "25", value = "Identificador del usuario que subió el documento")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycDocPeticion usuarioBaseDatos(String usuarioBaseDatos) {
    this.usuarioBaseDatos = usuarioBaseDatos;
    return this;
  }

  /**
   * Usuario de base de datos que realizó la operación
   * @return usuarioBaseDatos
  **/
  @ApiModelProperty(example = "user_admin", value = "Usuario de base de datos que realizó la operación")


  public String getUsuarioBaseDatos() {
    return usuarioBaseDatos;
  }

  public void setUsuarioBaseDatos(String usuarioBaseDatos) {
    this.usuarioBaseDatos = usuarioBaseDatos;
  }

  public PycDocPeticion fechaRecepcion(String fechaRecepcion) {
    this.fechaRecepcion = fechaRecepcion;
    return this;
  }

  /**
   * Fecha y hora de recepción del documento (formato dd/MM/yyyy HH24:MI)
   * @return fechaRecepcion
  **/
  @ApiModelProperty(example = "15/01/2024 14:30", value = "Fecha y hora de recepción del documento (formato dd/MM/yyyy HH24:MI)")


  public String getFechaRecepcion() {
    return fechaRecepcion;
  }

  public void setFechaRecepcion(String fechaRecepcion) {
    this.fechaRecepcion = fechaRecepcion;
  }

  public PycDocPeticion usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Nombre completo del usuario que subió el documento
   * @return usuario
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre completo del usuario que subió el documento")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public PycDocPeticion ftpWeb(String ftpWeb) {
    this.ftpWeb = ftpWeb;
    return this;
  }

  /**
   * Indicador de ubicación FTP o Web del documento
   * @return ftpWeb
  **/
  @ApiModelProperty(example = "WEB", value = "Indicador de ubicación FTP o Web del documento")


  public String getFtpWeb() {
    return ftpWeb;
  }

  public void setFtpWeb(String ftpWeb) {
    this.ftpWeb = ftpWeb;
  }

  public PycDocPeticion fecha(String fecha) {
    this.fecha = fecha;
    return this;
  }

  /**
   * Fecha de recepción en formato timestamp
   * @return fecha
  **/
  @ApiModelProperty(example = "2025-05-28 01:37:45", value = "Fecha de recepción en formato timestamp")


  public String getFecha() {
    return fecha;
  }

  public void setFecha(String fecha) {
    this.fecha = fecha;
  }

  public PycDocPeticion version(String version) {
    this.version = version;
    return this;
  }

  /**
   * Versión del documento
   * @return version
  **/
  @ApiModelProperty(example = "1.0", value = "Versión del documento")


  public String getVersion() {
    return version;
  }

  public void setVersion(String version) {
    this.version = version;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycDocPeticion pycDocPeticion = (PycDocPeticion) o;
    return Objects.equals(this.idPeticion, pycDocPeticion.idPeticion) &&
        Objects.equals(this.idDocumento, pycDocPeticion.idDocumento) &&
        Objects.equals(this.nombreDocumento, pycDocPeticion.nombreDocumento) &&
        Objects.equals(this.descripcionDocumento, pycDocPeticion.descripcionDocumento) &&
        Objects.equals(this.localizacion, pycDocPeticion.localizacion) &&
        Objects.equals(this.idUsuario, pycDocPeticion.idUsuario) &&
        Objects.equals(this.usuarioBaseDatos, pycDocPeticion.usuarioBaseDatos) &&
        Objects.equals(this.fechaRecepcion, pycDocPeticion.fechaRecepcion) &&
        Objects.equals(this.usuario, pycDocPeticion.usuario) &&
        Objects.equals(this.ftpWeb, pycDocPeticion.ftpWeb) &&
        Objects.equals(this.fecha, pycDocPeticion.fecha) &&
        Objects.equals(this.version, pycDocPeticion.version);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, idDocumento, nombreDocumento, descripcionDocumento, localizacion, idUsuario, usuarioBaseDatos, fechaRecepcion, usuario, ftpWeb, fecha, version);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycDocPeticion {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    idDocumento: ").append(toIndentedString(idDocumento)).append("\n");
    sb.append("    nombreDocumento: ").append(toIndentedString(nombreDocumento)).append("\n");
    sb.append("    descripcionDocumento: ").append(toIndentedString(descripcionDocumento)).append("\n");
    sb.append("    localizacion: ").append(toIndentedString(localizacion)).append("\n");
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    usuarioBaseDatos: ").append(toIndentedString(usuarioBaseDatos)).append("\n");
    sb.append("    fechaRecepcion: ").append(toIndentedString(fechaRecepcion)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    ftpWeb: ").append(toIndentedString(ftpWeb)).append("\n");
    sb.append("    fecha: ").append(toIndentedString(fecha)).append("\n");
    sb.append("    version: ").append(toIndentedString(version)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

