package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycPetSinProgramador
 */
@Validated

public class PycPetSinProgramador   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("tipoPeticion")
  private String tipoPeticion = null;

  @JsonProperty("nombrePeticion")
  private String nombrePeticion = null;

  @JsonProperty("descripcionPeticion")
  private String descripcionPeticion = null;

  @JsonProperty("fechaCreacion")
  private String fechaCreacion = null;

  @JsonProperty("idTipo")
  private Integer idTipo = null;

  @JsonProperty("idUsuarioSolicitante")
  private Integer idUsuarioSolicitante = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("fechaInicio")
  private String fechaInicio = null;

  @JsonProperty("fechaFin")
  private String fechaFin = null;

  @JsonProperty("totalHoras")
  private Double totalHoras = null;

  @JsonProperty("porcentajeBase")
  private Double porcentajeBase = null;

  @JsonProperty("porcentajeReal")
  private Double porcentajeReal = null;

  @JsonProperty("idEstado")
  private Integer idEstado = null;

  @JsonProperty("nombreEstado")
  private String nombreEstado = null;

  @JsonProperty("colorEstado")
  private String colorEstado = null;

  @JsonProperty("idPerfil")
  private Integer idPerfil = null;

  @JsonProperty("nombrePerfil")
  private String nombrePerfil = null;

  @JsonProperty("usuarioGraba")
  private String usuarioGraba = null;

  @JsonProperty("usuarioSoli")
  private String usuarioSoli = null;

  @JsonProperty("origen")
  private String origen = null;

  @JsonProperty("codClarity")
  private String codClarity = null;

  @JsonProperty("codcia")
  private String codcia = null;

  @JsonProperty("prioridad")
  private String prioridad = null;

  @JsonProperty("prioriDesc")
  private String prioriDesc = null;

  @JsonProperty("idPeticionSiguiente")
  private Integer idPeticionSiguiente = null;

  public PycPetSinProgramador idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador de la petición")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycPetSinProgramador tipoPeticion(String tipoPeticion) {
    this.tipoPeticion = tipoPeticion;
    return this;
  }

  /**
   * Tipo de petición
   * @return tipoPeticion
  **/
  @ApiModelProperty(example = "Solicitud", value = "Tipo de petición")


  public String getTipoPeticion() {
    return tipoPeticion;
  }

  public void setTipoPeticion(String tipoPeticion) {
    this.tipoPeticion = tipoPeticion;
  }

  public PycPetSinProgramador nombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
    return this;
  }

  /**
   * Nombre de la petición
   * @return nombrePeticion
  **/
  @ApiModelProperty(example = "Solicitud de cambio de datos", value = "Nombre de la petición")


  public String getNombrePeticion() {
    return nombrePeticion;
  }

  public void setNombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
  }

  public PycPetSinProgramador descripcionPeticion(String descripcionPeticion) {
    this.descripcionPeticion = descripcionPeticion;
    return this;
  }

  /**
   * Descripción detallada de la petición
   * @return descripcionPeticion
  **/
  @ApiModelProperty(example = "Solicitud para cambiar datos personales en la póliza", value = "Descripción detallada de la petición")


  public String getDescripcionPeticion() {
    return descripcionPeticion;
  }

  public void setDescripcionPeticion(String descripcionPeticion) {
    this.descripcionPeticion = descripcionPeticion;
  }

  public PycPetSinProgramador fechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
    return this;
  }

  /**
   * Fecha de creación de la petición (formato DD/MM/YYYY)
   * @return fechaCreacion
  **/
  @ApiModelProperty(example = "21/05/2025", value = "Fecha de creación de la petición (formato DD/MM/YYYY)")


  public String getFechaCreacion() {
    return fechaCreacion;
  }

  public void setFechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
  }

  public PycPetSinProgramador idTipo(Integer idTipo) {
    this.idTipo = idTipo;
    return this;
  }

  /**
   * Identificador del tipo
   * @return idTipo
  **/
  @ApiModelProperty(example = "1", value = "Identificador del tipo")


  public Integer getIdTipo() {
    return idTipo;
  }

  public void setIdTipo(Integer idTipo) {
    this.idTipo = idTipo;
  }

  public PycPetSinProgramador idUsuarioSolicitante(Integer idUsuarioSolicitante) {
    this.idUsuarioSolicitante = idUsuarioSolicitante;
    return this;
  }

  /**
   * Identificador del usuario solicitante
   * @return idUsuarioSolicitante
  **/
  @ApiModelProperty(example = "1234", value = "Identificador del usuario solicitante")


  public Integer getIdUsuarioSolicitante() {
    return idUsuarioSolicitante;
  }

  public void setIdUsuarioSolicitante(Integer idUsuarioSolicitante) {
    this.idUsuarioSolicitante = idUsuarioSolicitante;
  }

  public PycPetSinProgramador usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Nombre del usuario
   * @return usuario
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre del usuario")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public PycPetSinProgramador fechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
    return this;
  }

  /**
   * Fecha de inicio
   * @return fechaInicio
  **/
  @ApiModelProperty(example = "2025-05-21T14:20:00Z", value = "Fecha de inicio")


  public String getFechaInicio() {
    return fechaInicio;
  }

  public void setFechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
  }

  public PycPetSinProgramador fechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
    return this;
  }

  /**
   * Fecha de fin
   * @return fechaFin
  **/
  @ApiModelProperty(example = "2025-05-28T14:20:00Z", value = "Fecha de fin")


  public String getFechaFin() {
    return fechaFin;
  }

  public void setFechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
  }

  public PycPetSinProgramador totalHoras(Double totalHoras) {
    this.totalHoras = totalHoras;
    return this;
  }

  /**
   * Total de horas estimadas
   * @return totalHoras
  **/
  @ApiModelProperty(example = "40.5", value = "Total de horas estimadas")


  public Double getTotalHoras() {
    return totalHoras;
  }

  public void setTotalHoras(Double totalHoras) {
    this.totalHoras = totalHoras;
  }

  public PycPetSinProgramador porcentajeBase(Double porcentajeBase) {
    this.porcentajeBase = porcentajeBase;
    return this;
  }

  /**
   * Porcentaje base
   * @return porcentajeBase
  **/
  @ApiModelProperty(example = "75.0", value = "Porcentaje base")


  public Double getPorcentajeBase() {
    return porcentajeBase;
  }

  public void setPorcentajeBase(Double porcentajeBase) {
    this.porcentajeBase = porcentajeBase;
  }

  public PycPetSinProgramador porcentajeReal(Double porcentajeReal) {
    this.porcentajeReal = porcentajeReal;
    return this;
  }

  /**
   * Porcentaje real
   * @return porcentajeReal
  **/
  @ApiModelProperty(example = "80.0", value = "Porcentaje real")


  public Double getPorcentajeReal() {
    return porcentajeReal;
  }

  public void setPorcentajeReal(Double porcentajeReal) {
    this.porcentajeReal = porcentajeReal;
  }

  public PycPetSinProgramador idEstado(Integer idEstado) {
    this.idEstado = idEstado;
    return this;
  }

  /**
   * Identificador del estado
   * @return idEstado
  **/
  @ApiModelProperty(example = "1", value = "Identificador del estado")


  public Integer getIdEstado() {
    return idEstado;
  }

  public void setIdEstado(Integer idEstado) {
    this.idEstado = idEstado;
  }

  public PycPetSinProgramador nombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
    return this;
  }

  /**
   * Nombre del estado
   * @return nombreEstado
  **/
  @ApiModelProperty(example = "Pendiente", value = "Nombre del estado")


  public String getNombreEstado() {
    return nombreEstado;
  }

  public void setNombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
  }

  public PycPetSinProgramador colorEstado(String colorEstado) {
    this.colorEstado = colorEstado;
    return this;
  }

  /**
   * Color asociado al estado
   * @return colorEstado
  **/
  @ApiModelProperty(example = "#FF5733", value = "Color asociado al estado")


  public String getColorEstado() {
    return colorEstado;
  }

  public void setColorEstado(String colorEstado) {
    this.colorEstado = colorEstado;
  }

  public PycPetSinProgramador idPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
    return this;
  }

  /**
   * Identificador del perfil
   * @return idPerfil
  **/
  @ApiModelProperty(example = "1", value = "Identificador del perfil")


  public Integer getIdPerfil() {
    return idPerfil;
  }

  public void setIdPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
  }

  public PycPetSinProgramador nombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
    return this;
  }

  /**
   * Nombre del perfil
   * @return nombrePerfil
  **/
  @ApiModelProperty(example = "Administrador", value = "Nombre del perfil")


  public String getNombrePerfil() {
    return nombrePerfil;
  }

  public void setNombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
  }

  public PycPetSinProgramador usuarioGraba(String usuarioGraba) {
    this.usuarioGraba = usuarioGraba;
    return this;
  }

  /**
   * Usuario que grabó la petición
   * @return usuarioGraba
  **/
  @ApiModelProperty(example = "admin", value = "Usuario que grabó la petición")


  public String getUsuarioGraba() {
    return usuarioGraba;
  }

  public void setUsuarioGraba(String usuarioGraba) {
    this.usuarioGraba = usuarioGraba;
  }

  public PycPetSinProgramador usuarioSoli(String usuarioSoli) {
    this.usuarioSoli = usuarioSoli;
    return this;
  }

  /**
   * Usuario solicitante
   * @return usuarioSoli
  **/
  @ApiModelProperty(example = "jperez", value = "Usuario solicitante")


  public String getUsuarioSoli() {
    return usuarioSoli;
  }

  public void setUsuarioSoli(String usuarioSoli) {
    this.usuarioSoli = usuarioSoli;
  }

  public PycPetSinProgramador origen(String origen) {
    this.origen = origen;
    return this;
  }

  /**
   * Origen de la petición
   * @return origen
  **/
  @ApiModelProperty(example = "CO257", value = "Origen de la petición")


  public String getOrigen() {
    return origen;
  }

  public void setOrigen(String origen) {
    this.origen = origen;
  }

  public PycPetSinProgramador codClarity(String codClarity) {
    this.codClarity = codClarity;
    return this;
  }

  /**
   * Código Clarity
   * @return codClarity
  **/
  @ApiModelProperty(example = "MU-2019-038493", value = "Código Clarity")


  public String getCodClarity() {
    return codClarity;
  }

  public void setCodClarity(String codClarity) {
    this.codClarity = codClarity;
  }

  public PycPetSinProgramador codcia(String codcia) {
    this.codcia = codcia;
    return this;
  }

  /**
   * Código de compañía
   * @return codcia
  **/
  @ApiModelProperty(example = "MGT", value = "Código de compañía")


  public String getCodcia() {
    return codcia;
  }

  public void setCodcia(String codcia) {
    this.codcia = codcia;
  }

  public PycPetSinProgramador prioridad(String prioridad) {
    this.prioridad = prioridad;
    return this;
  }

  /**
   * Prioridad de la petición
   * @return prioridad
  **/
  @ApiModelProperty(example = "1", value = "Prioridad de la petición")


  public String getPrioridad() {
    return prioridad;
  }

  public void setPrioridad(String prioridad) {
    this.prioridad = prioridad;
  }

  public PycPetSinProgramador prioriDesc(String prioriDesc) {
    this.prioriDesc = prioriDesc;
    return this;
  }

  /**
   * Descripción de la prioridad
   * @return prioriDesc
  **/
  @ApiModelProperty(example = "Alta", value = "Descripción de la prioridad")


  public String getPrioriDesc() {
    return prioriDesc;
  }

  public void setPrioriDesc(String prioriDesc) {
    this.prioriDesc = prioriDesc;
  }

  public PycPetSinProgramador idPeticionSiguiente(Integer idPeticionSiguiente) {
    this.idPeticionSiguiente = idPeticionSiguiente;
    return this;
  }

  /**
   * Identificador de la petición siguiente
   * @return idPeticionSiguiente
  **/
  @ApiModelProperty(example = "1002", value = "Identificador de la petición siguiente")


  public Integer getIdPeticionSiguiente() {
    return idPeticionSiguiente;
  }

  public void setIdPeticionSiguiente(Integer idPeticionSiguiente) {
    this.idPeticionSiguiente = idPeticionSiguiente;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycPetSinProgramador pycPetSinProgramador = (PycPetSinProgramador) o;
    return Objects.equals(this.idPeticion, pycPetSinProgramador.idPeticion) &&
        Objects.equals(this.tipoPeticion, pycPetSinProgramador.tipoPeticion) &&
        Objects.equals(this.nombrePeticion, pycPetSinProgramador.nombrePeticion) &&
        Objects.equals(this.descripcionPeticion, pycPetSinProgramador.descripcionPeticion) &&
        Objects.equals(this.fechaCreacion, pycPetSinProgramador.fechaCreacion) &&
        Objects.equals(this.idTipo, pycPetSinProgramador.idTipo) &&
        Objects.equals(this.idUsuarioSolicitante, pycPetSinProgramador.idUsuarioSolicitante) &&
        Objects.equals(this.usuario, pycPetSinProgramador.usuario) &&
        Objects.equals(this.fechaInicio, pycPetSinProgramador.fechaInicio) &&
        Objects.equals(this.fechaFin, pycPetSinProgramador.fechaFin) &&
        Objects.equals(this.totalHoras, pycPetSinProgramador.totalHoras) &&
        Objects.equals(this.porcentajeBase, pycPetSinProgramador.porcentajeBase) &&
        Objects.equals(this.porcentajeReal, pycPetSinProgramador.porcentajeReal) &&
        Objects.equals(this.idEstado, pycPetSinProgramador.idEstado) &&
        Objects.equals(this.nombreEstado, pycPetSinProgramador.nombreEstado) &&
        Objects.equals(this.colorEstado, pycPetSinProgramador.colorEstado) &&
        Objects.equals(this.idPerfil, pycPetSinProgramador.idPerfil) &&
        Objects.equals(this.nombrePerfil, pycPetSinProgramador.nombrePerfil) &&
        Objects.equals(this.usuarioGraba, pycPetSinProgramador.usuarioGraba) &&
        Objects.equals(this.usuarioSoli, pycPetSinProgramador.usuarioSoli) &&
        Objects.equals(this.origen, pycPetSinProgramador.origen) &&
        Objects.equals(this.codClarity, pycPetSinProgramador.codClarity) &&
        Objects.equals(this.codcia, pycPetSinProgramador.codcia) &&
        Objects.equals(this.prioridad, pycPetSinProgramador.prioridad) &&
        Objects.equals(this.prioriDesc, pycPetSinProgramador.prioriDesc) &&
        Objects.equals(this.idPeticionSiguiente, pycPetSinProgramador.idPeticionSiguiente);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, tipoPeticion, nombrePeticion, descripcionPeticion, fechaCreacion, idTipo, idUsuarioSolicitante, usuario, fechaInicio, fechaFin, totalHoras, porcentajeBase, porcentajeReal, idEstado, nombreEstado, colorEstado, idPerfil, nombrePerfil, usuarioGraba, usuarioSoli, origen, codClarity, codcia, prioridad, prioriDesc, idPeticionSiguiente);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycPetSinProgramador {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    tipoPeticion: ").append(toIndentedString(tipoPeticion)).append("\n");
    sb.append("    nombrePeticion: ").append(toIndentedString(nombrePeticion)).append("\n");
    sb.append("    descripcionPeticion: ").append(toIndentedString(descripcionPeticion)).append("\n");
    sb.append("    fechaCreacion: ").append(toIndentedString(fechaCreacion)).append("\n");
    sb.append("    idTipo: ").append(toIndentedString(idTipo)).append("\n");
    sb.append("    idUsuarioSolicitante: ").append(toIndentedString(idUsuarioSolicitante)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    fechaInicio: ").append(toIndentedString(fechaInicio)).append("\n");
    sb.append("    fechaFin: ").append(toIndentedString(fechaFin)).append("\n");
    sb.append("    totalHoras: ").append(toIndentedString(totalHoras)).append("\n");
    sb.append("    porcentajeBase: ").append(toIndentedString(porcentajeBase)).append("\n");
    sb.append("    porcentajeReal: ").append(toIndentedString(porcentajeReal)).append("\n");
    sb.append("    idEstado: ").append(toIndentedString(idEstado)).append("\n");
    sb.append("    nombreEstado: ").append(toIndentedString(nombreEstado)).append("\n");
    sb.append("    colorEstado: ").append(toIndentedString(colorEstado)).append("\n");
    sb.append("    idPerfil: ").append(toIndentedString(idPerfil)).append("\n");
    sb.append("    nombrePerfil: ").append(toIndentedString(nombrePerfil)).append("\n");
    sb.append("    usuarioGraba: ").append(toIndentedString(usuarioGraba)).append("\n");
    sb.append("    usuarioSoli: ").append(toIndentedString(usuarioSoli)).append("\n");
    sb.append("    origen: ").append(toIndentedString(origen)).append("\n");
    sb.append("    codClarity: ").append(toIndentedString(codClarity)).append("\n");
    sb.append("    codcia: ").append(toIndentedString(codcia)).append("\n");
    sb.append("    prioridad: ").append(toIndentedString(prioridad)).append("\n");
    sb.append("    prioriDesc: ").append(toIndentedString(prioriDesc)).append("\n");
    sb.append("    idPeticionSiguiente: ").append(toIndentedString(idPeticionSiguiente)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

