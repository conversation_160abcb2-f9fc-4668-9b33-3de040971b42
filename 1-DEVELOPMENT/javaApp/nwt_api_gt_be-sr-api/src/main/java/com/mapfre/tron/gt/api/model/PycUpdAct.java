package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUpdAct
 */
@Validated

public class PycUpdAct   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("idActividad")
  private Integer idActividad = null;

  @JsonProperty("hrsReal")
  private String hrsReal = null;

  @JsonProperty("observacion")
  private String observacion = null;

  /**
   * Indicador si la actividad se marca como terminada ('S') o solo se actualizan horas ('N')
   */
  public enum IndEnum {
    S("S"),
    
    N("N");

    private String value;

    IndEnum(String value) {
      this.value = value;
    }

    @Override
    @JsonValue
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static IndEnum fromValue(String text) {
      for (IndEnum b : IndEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      return null;
    }
  }

  @JsonProperty("ind")
  private IndEnum ind = null;

  @JsonProperty("numaOUsuario")
  private String numaOUsuario = null;

  public PycUpdAct idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", required = true, value = "Identificador de la petición")
  @NotNull


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycUpdAct idActividad(Integer idActividad) {
    this.idActividad = idActividad;
    return this;
  }

  /**
   * Identificador de la actividad a actualizar
   * @return idActividad
  **/
  @ApiModelProperty(example = "5001", required = true, value = "Identificador de la actividad a actualizar")
  @NotNull


  public Integer getIdActividad() {
    return idActividad;
  }

  public void setIdActividad(Integer idActividad) {
    this.idActividad = idActividad;
  }

  public PycUpdAct hrsReal(String hrsReal) {
    this.hrsReal = hrsReal;
    return this;
  }

  /**
   * Horas reales trabajadas en la actividad
   * @return hrsReal
  **/
  @ApiModelProperty(example = "8", required = true, value = "Horas reales trabajadas en la actividad")
  @NotNull


  public String getHrsReal() {
    return hrsReal;
  }

  public void setHrsReal(String hrsReal) {
    this.hrsReal = hrsReal;
  }

  public PycUpdAct observacion(String observacion) {
    this.observacion = observacion;
    return this;
  }

  /**
   * Observación sobre el trabajo realizado
   * @return observacion
  **/
  @ApiModelProperty(example = "Completado análisis de requerimientos funcionales", required = true, value = "Observación sobre el trabajo realizado")
  @NotNull


  public String getObservacion() {
    return observacion;
  }

  public void setObservacion(String observacion) {
    this.observacion = observacion;
  }

  public PycUpdAct ind(IndEnum ind) {
    this.ind = ind;
    return this;
  }

  /**
   * Indicador si la actividad se marca como terminada ('S') o solo se actualizan horas ('N')
   * @return ind
  **/
  @ApiModelProperty(example = "S", required = true, value = "Indicador si la actividad se marca como terminada ('S') o solo se actualizan horas ('N')")
  @NotNull


  public IndEnum getInd() {
    return ind;
  }

  public void setInd(IndEnum ind) {
    this.ind = ind;
  }

  public PycUpdAct numaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
    return this;
  }

  /**
   * Número de usuario para auditoría (opcional, usa valor por defecto si es null)
   * @return numaOUsuario
  **/
  @ApiModelProperty(example = "jperez", value = "Número de usuario para auditoría (opcional, usa valor por defecto si es null)")


  public String getNumaOUsuario() {
    return numaOUsuario;
  }

  public void setNumaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUpdAct pycUpdAct = (PycUpdAct) o;
    return Objects.equals(this.idPeticion, pycUpdAct.idPeticion) &&
        Objects.equals(this.idActividad, pycUpdAct.idActividad) &&
        Objects.equals(this.hrsReal, pycUpdAct.hrsReal) &&
        Objects.equals(this.observacion, pycUpdAct.observacion) &&
        Objects.equals(this.ind, pycUpdAct.ind) &&
        Objects.equals(this.numaOUsuario, pycUpdAct.numaOUsuario);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, idActividad, hrsReal, observacion, ind, numaOUsuario);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUpdAct {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    idActividad: ").append(toIndentedString(idActividad)).append("\n");
    sb.append("    hrsReal: ").append(toIndentedString(hrsReal)).append("\n");
    sb.append("    observacion: ").append(toIndentedString(observacion)).append("\n");
    sb.append("    ind: ").append(toIndentedString(ind)).append("\n");
    sb.append("    numaOUsuario: ").append(toIndentedString(numaOUsuario)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

