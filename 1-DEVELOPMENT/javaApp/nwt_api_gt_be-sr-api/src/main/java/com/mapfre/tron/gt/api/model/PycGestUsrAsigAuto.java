package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycGestUsrAsigAuto
 */
@Validated

public class PycGestUsrAsigAuto   {
  @JsonProperty("idProceso")
  private Integer idProceso = null;

  @JsonProperty("idPerfil")
  private Integer idPerfil = null;

  @JsonProperty("idAplicacion")
  private Integer idAplicacion = null;

  @JsonProperty("idEstado")
  private Integer idEstado = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("codinter")
  private String codinter = null;

  @JsonProperty("numaOUsuario")
  private String numaOUsuario = null;

  public PycGestUsrAsigAuto idProceso(Integer idProceso) {
    this.idProceso = idProceso;
    return this;
  }

  /**
   * ID del proceso asociado a la asignación automática
   * @return idProceso
  **/
  @ApiModelProperty(example = "7", required = true, value = "ID del proceso asociado a la asignación automática")
  @NotNull


  public Integer getIdProceso() {
    return idProceso;
  }

  public void setIdProceso(Integer idProceso) {
    this.idProceso = idProceso;
  }

  public PycGestUsrAsigAuto idPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
    return this;
  }

  /**
   * ID del perfil para la asignación automática
   * @return idPerfil
  **/
  @ApiModelProperty(example = "3", required = true, value = "ID del perfil para la asignación automática")
  @NotNull


  public Integer getIdPerfil() {
    return idPerfil;
  }

  public void setIdPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
  }

  public PycGestUsrAsigAuto idAplicacion(Integer idAplicacion) {
    this.idAplicacion = idAplicacion;
    return this;
  }

  /**
   * ID de la aplicación para la asignación automática
   * @return idAplicacion
  **/
  @ApiModelProperty(example = "1", required = true, value = "ID de la aplicación para la asignación automática")
  @NotNull


  public Integer getIdAplicacion() {
    return idAplicacion;
  }

  public void setIdAplicacion(Integer idAplicacion) {
    this.idAplicacion = idAplicacion;
  }

  public PycGestUsrAsigAuto idEstado(Integer idEstado) {
    this.idEstado = idEstado;
    return this;
  }

  /**
   * ID del estado para la asignación automática
   * @return idEstado
  **/
  @ApiModelProperty(example = "5", required = true, value = "ID del estado para la asignación automática")
  @NotNull


  public Integer getIdEstado() {
    return idEstado;
  }

  public void setIdEstado(Integer idEstado) {
    this.idEstado = idEstado;
  }

  public PycGestUsrAsigAuto estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado de la asignación automática (ACT/INA)
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", required = true, value = "Estado de la asignación automática (ACT/INA)")
  @NotNull


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycGestUsrAsigAuto idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * ID del usuario para la asignación automática
   * @return idUsuario
  **/
  @ApiModelProperty(example = "123", required = true, value = "ID del usuario para la asignación automática")
  @NotNull


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycGestUsrAsigAuto codinter(String codinter) {
    this.codinter = codinter;
    return this;
  }

  /**
   * Código de intermediario (opcional)
   * @return codinter
  **/
  @ApiModelProperty(example = "900000", value = "Código de intermediario (opcional)")


  public String getCodinter() {
    return codinter;
  }

  public void setCodinter(String codinter) {
    this.codinter = codinter;
  }

  public PycGestUsrAsigAuto numaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
    return this;
  }

  /**
   * Número de usuario para auditoría (opcional)
   * @return numaOUsuario
  **/
  @ApiModelProperty(example = "TRON2000", value = "Número de usuario para auditoría (opcional)")


  public String getNumaOUsuario() {
    return numaOUsuario;
  }

  public void setNumaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycGestUsrAsigAuto pycGestUsrAsigAuto = (PycGestUsrAsigAuto) o;
    return Objects.equals(this.idProceso, pycGestUsrAsigAuto.idProceso) &&
        Objects.equals(this.idPerfil, pycGestUsrAsigAuto.idPerfil) &&
        Objects.equals(this.idAplicacion, pycGestUsrAsigAuto.idAplicacion) &&
        Objects.equals(this.idEstado, pycGestUsrAsigAuto.idEstado) &&
        Objects.equals(this.estado, pycGestUsrAsigAuto.estado) &&
        Objects.equals(this.idUsuario, pycGestUsrAsigAuto.idUsuario) &&
        Objects.equals(this.codinter, pycGestUsrAsigAuto.codinter) &&
        Objects.equals(this.numaOUsuario, pycGestUsrAsigAuto.numaOUsuario);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idProceso, idPerfil, idAplicacion, idEstado, estado, idUsuario, codinter, numaOUsuario);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycGestUsrAsigAuto {\n");
    
    sb.append("    idProceso: ").append(toIndentedString(idProceso)).append("\n");
    sb.append("    idPerfil: ").append(toIndentedString(idPerfil)).append("\n");
    sb.append("    idAplicacion: ").append(toIndentedString(idAplicacion)).append("\n");
    sb.append("    idEstado: ").append(toIndentedString(idEstado)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    codinter: ").append(toIndentedString(codinter)).append("\n");
    sb.append("    numaOUsuario: ").append(toIndentedString(numaOUsuario)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

