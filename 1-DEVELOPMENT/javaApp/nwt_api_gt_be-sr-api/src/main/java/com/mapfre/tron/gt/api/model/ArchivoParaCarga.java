package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ArchivoParaCarga
 */
@Validated

public class ArchivoParaCarga   {
  @JsonProperty("item1")
  private String item1 = null;

  @JsonProperty("item2")
  private String item2 = null;

  @JsonProperty("item3")
  private String item3 = null;

  public ArchivoParaCarga item1(String item1) {
    this.item1 = item1;
    return this;
  }

  /**
   * Primer elemento del tuple (m_Item1) - Identificador o número
   * @return item1
  **/
  @ApiModelProperty(example = "999999", required = true, value = "Primer elemento del tuple (m_Item1) - Identificador o número")
  @NotNull


  public String getItem1() {
    return item1;
  }

  public void setItem1(String item1) {
    this.item1 = item1;
  }

  public ArchivoParaCarga item2(String item2) {
    this.item2 = item2;
    return this;
  }

  /**
   * Segundo elemento del tuple (m_Item2) - Nombre del archivo
   * @return item2
  **/
  @ApiModelProperty(example = "prueba.pdf", required = true, value = "Segundo elemento del tuple (m_Item2) - Nombre del archivo")
  @NotNull


  public String getItem2() {
    return item2;
  }

  public void setItem2(String item2) {
    this.item2 = item2;
  }

  public ArchivoParaCarga item3(String item3) {
    this.item3 = item3;
    return this;
  }

  /**
   * Tercer elemento del tuple (m_Item3) - Contenido del archivo en Base64
   * @return item3
  **/
  @ApiModelProperty(example = "JVBERi0xLjQKJeLjz9MKNCAwIG9iago8PAovY2EgMQovQk0gL05vcm1hbAo+PgplbmRvYmoKNyAwIG9iago8PAovTiAzCi9GaWx0ZXIgL0ZsYXRlRGVjb2RlCi9MZW5ndGggMjkzCj4+CnN0cmVhbQp4nH2Q", required = true, value = "Tercer elemento del tuple (m_Item3) - Contenido del archivo en Base64")
  @NotNull


  public String getItem3() {
    return item3;
  }

  public void setItem3(String item3) {
    this.item3 = item3;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ArchivoParaCarga archivoParaCarga = (ArchivoParaCarga) o;
    return Objects.equals(this.item1, archivoParaCarga.item1) &&
        Objects.equals(this.item2, archivoParaCarga.item2) &&
        Objects.equals(this.item3, archivoParaCarga.item3);
  }

  @Override
  public int hashCode() {
    return Objects.hash(item1, item2, item3);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ArchivoParaCarga {\n");
    
    sb.append("    item1: ").append(toIndentedString(item1)).append("\n");
    sb.append("    item2: ").append(toIndentedString(item2)).append("\n");
    sb.append("    item3: ").append(toIndentedString(item3)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

