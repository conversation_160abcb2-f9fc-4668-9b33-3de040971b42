package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * Entidad
 */
@Validated

public class Entidad   {
  @JsonProperty("codigo")
  private String codigo = null;

  @JsonProperty("nombre")
  private String nombre = null;

  public Entidad codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Codigo de la entidad bancaria
   * @return codigo
  **/
  @ApiModelProperty(example = "000004", value = "Codigo de la entidad bancaria")


  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public Entidad nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre de la entidad bancaria
   * @return nombre
  **/
  @ApiModelProperty(example = "000004 - NOMBRE DEL BANCO", value = "Nombre de la entidad bancaria")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Entidad entidad = (Entidad) o;
    return Objects.equals(this.codigo, entidad.codigo) &&
        Objects.equals(this.nombre, entidad.nombre);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codigo, nombre);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Entidad {\n");
    
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

