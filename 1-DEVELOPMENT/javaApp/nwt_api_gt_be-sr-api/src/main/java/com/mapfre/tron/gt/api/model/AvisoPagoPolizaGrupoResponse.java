package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * AvisoPagoPolizaGrupoResponse
 */
@Validated

public class AvisoPagoPolizaGrupoResponse   {
  @JsonProperty("codActTercero")
  private Integer codActTercero = null;

  @JsonProperty("codAgt")
  private String codAgt = null;

  @JsonProperty("codCia")
  private Integer codCia = null;

  @JsonProperty("codDocum")
  private String codDocum = null;

  @JsonProperty("codDocumPago")
  private String codDocumPago = null;

  @JsonProperty("codGestor")
  private Integer codGestor = null;

  @JsonProperty("codMon")
  private Integer codMon = null;

  @JsonProperty("fecMvto")
  private String fecMvto = null;

  @JsonProperty("fecVcto")
  private String fecVcto = null;

  @JsonProperty("impDocum")
  private Double impDocum = null;

  @JsonProperty("numContrato")
  private Integer numContrato = null;

  @JsonProperty("numMvto")
  private Integer numMvto = null;

  @JsonProperty("numPoliza")
  private String numPoliza = null;

  @JsonProperty("numPolizaCliente")
  private String numPolizaCliente = null;

  @JsonProperty("numPolizaGrupo")
  private String numPolizaGrupo = null;

  @JsonProperty("tipDocum")
  private String tipDocum = null;

  @JsonProperty("tipDocumPago")
  private String tipDocumPago = null;

  @JsonProperty("tipEstado")
  private String tipEstado = null;

  @JsonProperty("tipGestor")
  private String tipGestor = null;

  @JsonProperty("valCambio")
  private Double valCambio = null;

  public AvisoPagoPolizaGrupoResponse codActTercero(Integer codActTercero) {
    this.codActTercero = codActTercero;
    return this;
  }

  /**
   * Código de actividad del tercero
   * @return codActTercero
  **/
  @ApiModelProperty(example = "integer", value = "Código de actividad del tercero")


  public Integer getCodActTercero() {
    return codActTercero;
  }

  public void setCodActTercero(Integer codActTercero) {
    this.codActTercero = codActTercero;
  }

  public AvisoPagoPolizaGrupoResponse codAgt(String codAgt) {
    this.codAgt = codAgt;
    return this;
  }

  /**
   * Código de agente
   * @return codAgt
  **/
  @ApiModelProperty(example = "string", value = "Código de agente")


  public String getCodAgt() {
    return codAgt;
  }

  public void setCodAgt(String codAgt) {
    this.codAgt = codAgt;
  }

  public AvisoPagoPolizaGrupoResponse codCia(Integer codCia) {
    this.codCia = codCia;
    return this;
  }

  /**
   * Código de compañía
   * @return codCia
  **/
  @ApiModelProperty(example = "integer", value = "Código de compañía")


  public Integer getCodCia() {
    return codCia;
  }

  public void setCodCia(Integer codCia) {
    this.codCia = codCia;
  }

  public AvisoPagoPolizaGrupoResponse codDocum(String codDocum) {
    this.codDocum = codDocum;
    return this;
  }

  /**
   * Código de documento
   * @return codDocum
  **/
  @ApiModelProperty(example = "string", value = "Código de documento")


  public String getCodDocum() {
    return codDocum;
  }

  public void setCodDocum(String codDocum) {
    this.codDocum = codDocum;
  }

  public AvisoPagoPolizaGrupoResponse codDocumPago(String codDocumPago) {
    this.codDocumPago = codDocumPago;
    return this;
  }

  /**
   * Código de documento de pago
   * @return codDocumPago
  **/
  @ApiModelProperty(example = "string", value = "Código de documento de pago")


  public String getCodDocumPago() {
    return codDocumPago;
  }

  public void setCodDocumPago(String codDocumPago) {
    this.codDocumPago = codDocumPago;
  }

  public AvisoPagoPolizaGrupoResponse codGestor(Integer codGestor) {
    this.codGestor = codGestor;
    return this;
  }

  /**
   * Código de gestor
   * @return codGestor
  **/
  @ApiModelProperty(example = "integer", value = "Código de gestor")


  public Integer getCodGestor() {
    return codGestor;
  }

  public void setCodGestor(Integer codGestor) {
    this.codGestor = codGestor;
  }

  public AvisoPagoPolizaGrupoResponse codMon(Integer codMon) {
    this.codMon = codMon;
    return this;
  }

  /**
   * Código de moneda
   * @return codMon
  **/
  @ApiModelProperty(example = "integer", value = "Código de moneda")


  public Integer getCodMon() {
    return codMon;
  }

  public void setCodMon(Integer codMon) {
    this.codMon = codMon;
  }

  public AvisoPagoPolizaGrupoResponse fecMvto(String fecMvto) {
    this.fecMvto = fecMvto;
    return this;
  }

  /**
   * Fecha de movimiento
   * @return fecMvto
  **/
  @ApiModelProperty(example = "string", value = "Fecha de movimiento")


  public String getFecMvto() {
    return fecMvto;
  }

  public void setFecMvto(String fecMvto) {
    this.fecMvto = fecMvto;
  }

  public AvisoPagoPolizaGrupoResponse fecVcto(String fecVcto) {
    this.fecVcto = fecVcto;
    return this;
  }

  /**
   * Fecha de vencimiento
   * @return fecVcto
  **/
  @ApiModelProperty(example = "string", value = "Fecha de vencimiento")


  public String getFecVcto() {
    return fecVcto;
  }

  public void setFecVcto(String fecVcto) {
    this.fecVcto = fecVcto;
  }

  public AvisoPagoPolizaGrupoResponse impDocum(Double impDocum) {
    this.impDocum = impDocum;
    return this;
  }

  /**
   * Importe del documento
   * @return impDocum
  **/
  @ApiModelProperty(example = "number", value = "Importe del documento")


  public Double getImpDocum() {
    return impDocum;
  }

  public void setImpDocum(Double impDocum) {
    this.impDocum = impDocum;
  }

  public AvisoPagoPolizaGrupoResponse numContrato(Integer numContrato) {
    this.numContrato = numContrato;
    return this;
  }

  /**
   * Número de contrato
   * @return numContrato
  **/
  @ApiModelProperty(example = "integer", value = "Número de contrato")


  public Integer getNumContrato() {
    return numContrato;
  }

  public void setNumContrato(Integer numContrato) {
    this.numContrato = numContrato;
  }

  public AvisoPagoPolizaGrupoResponse numMvto(Integer numMvto) {
    this.numMvto = numMvto;
    return this;
  }

  /**
   * Número de movimiento
   * @return numMvto
  **/
  @ApiModelProperty(example = "integer", value = "Número de movimiento")


  public Integer getNumMvto() {
    return numMvto;
  }

  public void setNumMvto(Integer numMvto) {
    this.numMvto = numMvto;
  }

  public AvisoPagoPolizaGrupoResponse numPoliza(String numPoliza) {
    this.numPoliza = numPoliza;
    return this;
  }

  /**
   * Número de póliza
   * @return numPoliza
  **/
  @ApiModelProperty(example = "string", value = "Número de póliza")


  public String getNumPoliza() {
    return numPoliza;
  }

  public void setNumPoliza(String numPoliza) {
    this.numPoliza = numPoliza;
  }

  public AvisoPagoPolizaGrupoResponse numPolizaCliente(String numPolizaCliente) {
    this.numPolizaCliente = numPolizaCliente;
    return this;
  }

  /**
   * Número de póliza del cliente
   * @return numPolizaCliente
  **/
  @ApiModelProperty(example = "string", value = "Número de póliza del cliente")


  public String getNumPolizaCliente() {
    return numPolizaCliente;
  }

  public void setNumPolizaCliente(String numPolizaCliente) {
    this.numPolizaCliente = numPolizaCliente;
  }

  public AvisoPagoPolizaGrupoResponse numPolizaGrupo(String numPolizaGrupo) {
    this.numPolizaGrupo = numPolizaGrupo;
    return this;
  }

  /**
   * Número de póliza grupo
   * @return numPolizaGrupo
  **/
  @ApiModelProperty(example = "string", value = "Número de póliza grupo")


  public String getNumPolizaGrupo() {
    return numPolizaGrupo;
  }

  public void setNumPolizaGrupo(String numPolizaGrupo) {
    this.numPolizaGrupo = numPolizaGrupo;
  }

  public AvisoPagoPolizaGrupoResponse tipDocum(String tipDocum) {
    this.tipDocum = tipDocum;
    return this;
  }

  /**
   * Tipo de documento
   * @return tipDocum
  **/
  @ApiModelProperty(example = "string", value = "Tipo de documento")


  public String getTipDocum() {
    return tipDocum;
  }

  public void setTipDocum(String tipDocum) {
    this.tipDocum = tipDocum;
  }

  public AvisoPagoPolizaGrupoResponse tipDocumPago(String tipDocumPago) {
    this.tipDocumPago = tipDocumPago;
    return this;
  }

  /**
   * Tipo de documento de pago
   * @return tipDocumPago
  **/
  @ApiModelProperty(example = "string", value = "Tipo de documento de pago")


  public String getTipDocumPago() {
    return tipDocumPago;
  }

  public void setTipDocumPago(String tipDocumPago) {
    this.tipDocumPago = tipDocumPago;
  }

  public AvisoPagoPolizaGrupoResponse tipEstado(String tipEstado) {
    this.tipEstado = tipEstado;
    return this;
  }

  /**
   * Tipo de estado
   * @return tipEstado
  **/
  @ApiModelProperty(example = "string", value = "Tipo de estado")


  public String getTipEstado() {
    return tipEstado;
  }

  public void setTipEstado(String tipEstado) {
    this.tipEstado = tipEstado;
  }

  public AvisoPagoPolizaGrupoResponse tipGestor(String tipGestor) {
    this.tipGestor = tipGestor;
    return this;
  }

  /**
   * Tipo de gestor
   * @return tipGestor
  **/
  @ApiModelProperty(example = "string", value = "Tipo de gestor")


  public String getTipGestor() {
    return tipGestor;
  }

  public void setTipGestor(String tipGestor) {
    this.tipGestor = tipGestor;
  }

  public AvisoPagoPolizaGrupoResponse valCambio(Double valCambio) {
    this.valCambio = valCambio;
    return this;
  }

  /**
   * Valor de cambio
   * @return valCambio
  **/
  @ApiModelProperty(example = "number", value = "Valor de cambio")


  public Double getValCambio() {
    return valCambio;
  }

  public void setValCambio(Double valCambio) {
    this.valCambio = valCambio;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AvisoPagoPolizaGrupoResponse avisoPagoPolizaGrupoResponse = (AvisoPagoPolizaGrupoResponse) o;
    return Objects.equals(this.codActTercero, avisoPagoPolizaGrupoResponse.codActTercero) &&
        Objects.equals(this.codAgt, avisoPagoPolizaGrupoResponse.codAgt) &&
        Objects.equals(this.codCia, avisoPagoPolizaGrupoResponse.codCia) &&
        Objects.equals(this.codDocum, avisoPagoPolizaGrupoResponse.codDocum) &&
        Objects.equals(this.codDocumPago, avisoPagoPolizaGrupoResponse.codDocumPago) &&
        Objects.equals(this.codGestor, avisoPagoPolizaGrupoResponse.codGestor) &&
        Objects.equals(this.codMon, avisoPagoPolizaGrupoResponse.codMon) &&
        Objects.equals(this.fecMvto, avisoPagoPolizaGrupoResponse.fecMvto) &&
        Objects.equals(this.fecVcto, avisoPagoPolizaGrupoResponse.fecVcto) &&
        Objects.equals(this.impDocum, avisoPagoPolizaGrupoResponse.impDocum) &&
        Objects.equals(this.numContrato, avisoPagoPolizaGrupoResponse.numContrato) &&
        Objects.equals(this.numMvto, avisoPagoPolizaGrupoResponse.numMvto) &&
        Objects.equals(this.numPoliza, avisoPagoPolizaGrupoResponse.numPoliza) &&
        Objects.equals(this.numPolizaCliente, avisoPagoPolizaGrupoResponse.numPolizaCliente) &&
        Objects.equals(this.numPolizaGrupo, avisoPagoPolizaGrupoResponse.numPolizaGrupo) &&
        Objects.equals(this.tipDocum, avisoPagoPolizaGrupoResponse.tipDocum) &&
        Objects.equals(this.tipDocumPago, avisoPagoPolizaGrupoResponse.tipDocumPago) &&
        Objects.equals(this.tipEstado, avisoPagoPolizaGrupoResponse.tipEstado) &&
        Objects.equals(this.tipGestor, avisoPagoPolizaGrupoResponse.tipGestor) &&
        Objects.equals(this.valCambio, avisoPagoPolizaGrupoResponse.valCambio);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codActTercero, codAgt, codCia, codDocum, codDocumPago, codGestor, codMon, fecMvto, fecVcto, impDocum, numContrato, numMvto, numPoliza, numPolizaCliente, numPolizaGrupo, tipDocum, tipDocumPago, tipEstado, tipGestor, valCambio);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AvisoPagoPolizaGrupoResponse {\n");
    
    sb.append("    codActTercero: ").append(toIndentedString(codActTercero)).append("\n");
    sb.append("    codAgt: ").append(toIndentedString(codAgt)).append("\n");
    sb.append("    codCia: ").append(toIndentedString(codCia)).append("\n");
    sb.append("    codDocum: ").append(toIndentedString(codDocum)).append("\n");
    sb.append("    codDocumPago: ").append(toIndentedString(codDocumPago)).append("\n");
    sb.append("    codGestor: ").append(toIndentedString(codGestor)).append("\n");
    sb.append("    codMon: ").append(toIndentedString(codMon)).append("\n");
    sb.append("    fecMvto: ").append(toIndentedString(fecMvto)).append("\n");
    sb.append("    fecVcto: ").append(toIndentedString(fecVcto)).append("\n");
    sb.append("    impDocum: ").append(toIndentedString(impDocum)).append("\n");
    sb.append("    numContrato: ").append(toIndentedString(numContrato)).append("\n");
    sb.append("    numMvto: ").append(toIndentedString(numMvto)).append("\n");
    sb.append("    numPoliza: ").append(toIndentedString(numPoliza)).append("\n");
    sb.append("    numPolizaCliente: ").append(toIndentedString(numPolizaCliente)).append("\n");
    sb.append("    numPolizaGrupo: ").append(toIndentedString(numPolizaGrupo)).append("\n");
    sb.append("    tipDocum: ").append(toIndentedString(tipDocum)).append("\n");
    sb.append("    tipDocumPago: ").append(toIndentedString(tipDocumPago)).append("\n");
    sb.append("    tipEstado: ").append(toIndentedString(tipEstado)).append("\n");
    sb.append("    tipGestor: ").append(toIndentedString(tipGestor)).append("\n");
    sb.append("    valCambio: ").append(toIndentedString(valCambio)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

