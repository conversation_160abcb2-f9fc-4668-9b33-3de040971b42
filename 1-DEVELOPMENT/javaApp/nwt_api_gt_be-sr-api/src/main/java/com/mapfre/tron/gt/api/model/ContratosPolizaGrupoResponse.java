package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ContratosPolizaGrupoResponse
 */
@Validated

public class ContratosPolizaGrupoResponse   {
  @JsonProperty("codCia")
  private Integer codCia = null;

  @JsonProperty("numPoliza")
  private String numPoliza = null;

  @JsonProperty("numContrato")
  private Integer numContrato = null;

  @JsonProperty("tipPoliza")
  private String tipPoliza = null;

  @JsonProperty("mcaRiesgos")
  private String mcaRiesgos = null;

  @JsonProperty("fecVctoPoliza")
  private String fecVctoPoliza = null;

  @JsonProperty("cantAviso")
  private Integer cantAviso = null;

  @JsonProperty("cantPoliza")
  private Integer cantPoliza = null;

  public ContratosPolizaGrupoResponse codCia(Integer codCia) {
    this.codCia = codCia;
    return this;
  }

  /**
   * Código de compañía
   * @return codCia
  **/
  @ApiModelProperty(example = "integer", value = "Código de compañía")


  public Integer getCodCia() {
    return codCia;
  }

  public void setCodCia(Integer codCia) {
    this.codCia = codCia;
  }

  public ContratosPolizaGrupoResponse numPoliza(String numPoliza) {
    this.numPoliza = numPoliza;
    return this;
  }

  /**
   * Número de póliza
   * @return numPoliza
  **/
  @ApiModelProperty(example = "string", value = "Número de póliza")


  public String getNumPoliza() {
    return numPoliza;
  }

  public void setNumPoliza(String numPoliza) {
    this.numPoliza = numPoliza;
  }

  public ContratosPolizaGrupoResponse numContrato(Integer numContrato) {
    this.numContrato = numContrato;
    return this;
  }

  /**
   * Número de contrato
   * @return numContrato
  **/
  @ApiModelProperty(example = "integer", value = "Número de contrato")


  public Integer getNumContrato() {
    return numContrato;
  }

  public void setNumContrato(Integer numContrato) {
    this.numContrato = numContrato;
  }

  public ContratosPolizaGrupoResponse tipPoliza(String tipPoliza) {
    this.tipPoliza = tipPoliza;
    return this;
  }

  /**
   * Tipo de póliza
   * @return tipPoliza
  **/
  @ApiModelProperty(example = "string", value = "Tipo de póliza")


  public String getTipPoliza() {
    return tipPoliza;
  }

  public void setTipPoliza(String tipPoliza) {
    this.tipPoliza = tipPoliza;
  }

  public ContratosPolizaGrupoResponse mcaRiesgos(String mcaRiesgos) {
    this.mcaRiesgos = mcaRiesgos;
    return this;
  }

  /**
   * Marca de riesgos
   * @return mcaRiesgos
  **/
  @ApiModelProperty(example = "string", value = "Marca de riesgos")


  public String getMcaRiesgos() {
    return mcaRiesgos;
  }

  public void setMcaRiesgos(String mcaRiesgos) {
    this.mcaRiesgos = mcaRiesgos;
  }

  public ContratosPolizaGrupoResponse fecVctoPoliza(String fecVctoPoliza) {
    this.fecVctoPoliza = fecVctoPoliza;
    return this;
  }

  /**
   * Fecha de vencimiento de la póliza
   * @return fecVctoPoliza
  **/
  @ApiModelProperty(example = "string", value = "Fecha de vencimiento de la póliza")


  public String getFecVctoPoliza() {
    return fecVctoPoliza;
  }

  public void setFecVctoPoliza(String fecVctoPoliza) {
    this.fecVctoPoliza = fecVctoPoliza;
  }

  public ContratosPolizaGrupoResponse cantAviso(Integer cantAviso) {
    this.cantAviso = cantAviso;
    return this;
  }

  /**
   * Cantidad de avisos
   * @return cantAviso
  **/
  @ApiModelProperty(example = "integer", value = "Cantidad de avisos")


  public Integer getCantAviso() {
    return cantAviso;
  }

  public void setCantAviso(Integer cantAviso) {
    this.cantAviso = cantAviso;
  }

  public ContratosPolizaGrupoResponse cantPoliza(Integer cantPoliza) {
    this.cantPoliza = cantPoliza;
    return this;
  }

  /**
   * Cantidad de pólizas
   * @return cantPoliza
  **/
  @ApiModelProperty(example = "integer", value = "Cantidad de pólizas")


  public Integer getCantPoliza() {
    return cantPoliza;
  }

  public void setCantPoliza(Integer cantPoliza) {
    this.cantPoliza = cantPoliza;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ContratosPolizaGrupoResponse contratosPolizaGrupoResponse = (ContratosPolizaGrupoResponse) o;
    return Objects.equals(this.codCia, contratosPolizaGrupoResponse.codCia) &&
        Objects.equals(this.numPoliza, contratosPolizaGrupoResponse.numPoliza) &&
        Objects.equals(this.numContrato, contratosPolizaGrupoResponse.numContrato) &&
        Objects.equals(this.tipPoliza, contratosPolizaGrupoResponse.tipPoliza) &&
        Objects.equals(this.mcaRiesgos, contratosPolizaGrupoResponse.mcaRiesgos) &&
        Objects.equals(this.fecVctoPoliza, contratosPolizaGrupoResponse.fecVctoPoliza) &&
        Objects.equals(this.cantAviso, contratosPolizaGrupoResponse.cantAviso) &&
        Objects.equals(this.cantPoliza, contratosPolizaGrupoResponse.cantPoliza);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codCia, numPoliza, numContrato, tipPoliza, mcaRiesgos, fecVctoPoliza, cantAviso, cantPoliza);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ContratosPolizaGrupoResponse {\n");
    
    sb.append("    codCia: ").append(toIndentedString(codCia)).append("\n");
    sb.append("    numPoliza: ").append(toIndentedString(numPoliza)).append("\n");
    sb.append("    numContrato: ").append(toIndentedString(numContrato)).append("\n");
    sb.append("    tipPoliza: ").append(toIndentedString(tipPoliza)).append("\n");
    sb.append("    mcaRiesgos: ").append(toIndentedString(mcaRiesgos)).append("\n");
    sb.append("    fecVctoPoliza: ").append(toIndentedString(fecVctoPoliza)).append("\n");
    sb.append("    cantAviso: ").append(toIndentedString(cantAviso)).append("\n");
    sb.append("    cantPoliza: ").append(toIndentedString(cantPoliza)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

