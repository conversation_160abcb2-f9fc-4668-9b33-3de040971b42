package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycOficina
 */
@Validated

public class PycOficina   {
  @JsonProperty("idOficina")
  private Integer idOficina = null;

  @JsonProperty("nombre")
  private String nombre = null;

  public PycOficina idOficina(Integer idOficina) {
    this.idOficina = idOficina;
    return this;
  }

  /**
   * Identificador de la oficina
   * @return idOficina
  **/
  @ApiModelProperty(example = "1", value = "Identificador de la oficina")


  public Integer getIdOficina() {
    return idOficina;
  }

  public void setIdOficina(Integer idOficina) {
    this.idOficina = idOficina;
  }

  public PycOficina nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre de la oficina
   * @return nombre
  **/
  @ApiModelProperty(example = "Oficina Central", value = "Nombre de la oficina")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycOficina pycOficina = (PycOficina) o;
    return Objects.equals(this.idOficina, pycOficina.idOficina) &&
        Objects.equals(this.nombre, pycOficina.nombre);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idOficina, nombre);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycOficina {\n");
    
    sb.append("    idOficina: ").append(toIndentedString(idOficina)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

