package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycInserAct
 */
@Validated

public class PycInserAct   {
  @JsonProperty("idPeti")
  private Integer idPeti = null;

  @JsonProperty("nombAct")
  private String nombAct = null;

  @JsonProperty("descAct")
  private String descAct = null;

  @JsonProperty("hrsBase")
  private String hrsBase = null;

  @JsonProperty("fecIni")
  private String fecIni = null;

  @JsonProperty("fecFin")
  private String fecFin = null;

  @JsonProperty("userPet")
  private Integer userPet = null;

  @JsonProperty("cate")
  private String cate = null;

  @JsonProperty("asigna")
  private String asigna = null;

  public PycInserAct idPeti(Integer idPeti) {
    this.idPeti = idPeti;
    return this;
  }

  /**
   * Identificador de la petición
   * @return idPeti
  **/
  @ApiModelProperty(example = "1001", required = true, value = "Identificador de la petición")
  @NotNull


  public Integer getIdPeti() {
    return idPeti;
  }

  public void setIdPeti(Integer idPeti) {
    this.idPeti = idPeti;
  }

  public PycInserAct nombAct(String nombAct) {
    this.nombAct = nombAct;
    return this;
  }

  /**
   * Nombre de la actividad
   * @return nombAct
  **/
  @ApiModelProperty(example = "Análisis de requerimientos", required = true, value = "Nombre de la actividad")
  @NotNull


  public String getNombAct() {
    return nombAct;
  }

  public void setNombAct(String nombAct) {
    this.nombAct = nombAct;
  }

  public PycInserAct descAct(String descAct) {
    this.descAct = descAct;
    return this;
  }

  /**
   * Descripción de la actividad
   * @return descAct
  **/
  @ApiModelProperty(example = "Análisis detallado de los requerimientos del cliente", required = true, value = "Descripción de la actividad")
  @NotNull


  public String getDescAct() {
    return descAct;
  }

  public void setDescAct(String descAct) {
    this.descAct = descAct;
  }

  public PycInserAct hrsBase(String hrsBase) {
    this.hrsBase = hrsBase;
    return this;
  }

  /**
   * Horas base estimadas para la actividad
   * @return hrsBase
  **/
  @ApiModelProperty(example = "40", required = true, value = "Horas base estimadas para la actividad")
  @NotNull


  public String getHrsBase() {
    return hrsBase;
  }

  public void setHrsBase(String hrsBase) {
    this.hrsBase = hrsBase;
  }

  public PycInserAct fecIni(String fecIni) {
    this.fecIni = fecIni;
    return this;
  }

  /**
   * Fecha de inicio de la actividad (formato DD/MM/YYYY)
   * @return fecIni
  **/
  @ApiModelProperty(example = "15/01/2024", required = true, value = "Fecha de inicio de la actividad (formato DD/MM/YYYY)")
  @NotNull


  public String getFecIni() {
    return fecIni;
  }

  public void setFecIni(String fecIni) {
    this.fecIni = fecIni;
  }

  public PycInserAct fecFin(String fecFin) {
    this.fecFin = fecFin;
    return this;
  }

  /**
   * Fecha de fin de la actividad (formato DD/MM/YYYY)
   * @return fecFin
  **/
  @ApiModelProperty(example = "20/01/2024", required = true, value = "Fecha de fin de la actividad (formato DD/MM/YYYY)")
  @NotNull


  public String getFecFin() {
    return fecFin;
  }

  public void setFecFin(String fecFin) {
    this.fecFin = fecFin;
  }

  public PycInserAct userPet(Integer userPet) {
    this.userPet = userPet;
    return this;
  }

  /**
   * Identificador del usuario responsable de la actividad
   * @return userPet
  **/
  @ApiModelProperty(example = "1234", required = true, value = "Identificador del usuario responsable de la actividad")
  @NotNull


  public Integer getUserPet() {
    return userPet;
  }

  public void setUserPet(Integer userPet) {
    this.userPet = userPet;
  }

  public PycInserAct cate(String cate) {
    this.cate = cate;
    return this;
  }

  /**
   * Identificador de la categoría del tablero
   * @return cate
  **/
  @ApiModelProperty(example = "1", required = true, value = "Identificador de la categoría del tablero")
  @NotNull


  public String getCate() {
    return cate;
  }

  public void setCate(String cate) {
    this.cate = cate;
  }

  public PycInserAct asigna(String asigna) {
    this.asigna = asigna;
    return this;
  }

  /**
   * Usuario asignado a la actividad (opcional)
   * @return asigna
  **/
  @ApiModelProperty(example = "jperez", value = "Usuario asignado a la actividad (opcional)")


  public String getAsigna() {
    return asigna;
  }

  public void setAsigna(String asigna) {
    this.asigna = asigna;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycInserAct pycInserAct = (PycInserAct) o;
    return Objects.equals(this.idPeti, pycInserAct.idPeti) &&
        Objects.equals(this.nombAct, pycInserAct.nombAct) &&
        Objects.equals(this.descAct, pycInserAct.descAct) &&
        Objects.equals(this.hrsBase, pycInserAct.hrsBase) &&
        Objects.equals(this.fecIni, pycInserAct.fecIni) &&
        Objects.equals(this.fecFin, pycInserAct.fecFin) &&
        Objects.equals(this.userPet, pycInserAct.userPet) &&
        Objects.equals(this.cate, pycInserAct.cate) &&
        Objects.equals(this.asigna, pycInserAct.asigna);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeti, nombAct, descAct, hrsBase, fecIni, fecFin, userPet, cate, asigna);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycInserAct {\n");
    
    sb.append("    idPeti: ").append(toIndentedString(idPeti)).append("\n");
    sb.append("    nombAct: ").append(toIndentedString(nombAct)).append("\n");
    sb.append("    descAct: ").append(toIndentedString(descAct)).append("\n");
    sb.append("    hrsBase: ").append(toIndentedString(hrsBase)).append("\n");
    sb.append("    fecIni: ").append(toIndentedString(fecIni)).append("\n");
    sb.append("    fecFin: ").append(toIndentedString(fecFin)).append("\n");
    sb.append("    userPet: ").append(toIndentedString(userPet)).append("\n");
    sb.append("    cate: ").append(toIndentedString(cate)).append("\n");
    sb.append("    asigna: ").append(toIndentedString(asigna)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

