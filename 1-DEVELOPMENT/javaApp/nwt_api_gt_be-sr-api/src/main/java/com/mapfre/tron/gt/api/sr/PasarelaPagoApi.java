/**
 * NOTE: This class is auto generated by the swagger code generator program (2.4.14).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.mapfre.tron.gt.api.sr;

import com.mapfre.tron.gt.api.model.Error;
import com.mapfre.tron.gt.api.model.TipoDocumentoPagoResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Api(value = "pasarela_pago", description = "the pasarela_pago API")
@RequestMapping(value = "/newtron/api")
public interface PasarelaPagoApi {

    Logger log = LoggerFactory.getLogger(PasarelaPagoApi.class);

    default Optional<ObjectMapper> getObjectMapper() {
        return Optional.empty();
    }

    default Optional<HttpServletRequest> getRequest() {
        return Optional.empty();
    }

    default Optional<String> getAcceptHeader() {
        return getRequest().map(r -> r.getHeader("Accept"));
    }

    @ApiOperation(value = "Validar tipos de documentos de pago(Recibos, Avisos)", nickname = "validaTipoDocumento", notes = "Obtiene los tipos de documentos de pago válidos según el requerimiento", response = TipoDocumentoPagoResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "PasarelaPago", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = TipoDocumentoPagoResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pasarela_pago/valida_tipo_documento",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<TipoDocumentoPagoResponse> validaTipoDocumento(@NotNull @ApiParam(value = "Código de documento a filtrar", required = true) @Valid @RequestParam(value = "codDocto", required = true) String codDocto) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"string\",  \"tiposDocumento\" : [ {    \"monto\" : \"number\",    \"noDocto\" : \"string\",    \"mcaAviso\" : \"string\"  }, {    \"monto\" : \"number\",    \"noDocto\" : \"string\",    \"mcaAviso\" : \"string\"  } ],  \"mensaje\" : \"string\"}", TipoDocumentoPagoResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PasarelaPagoApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

}
