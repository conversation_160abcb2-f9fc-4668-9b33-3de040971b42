package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycAsignaPeticionDesa
 */
@Validated

public class PycAsignaPeticionDesa   {
  @JsonProperty("idAplicacion")
  private Integer idAplicacion = null;

  @JsonProperty("idDesarrollador")
  private Integer idDesarrollador = null;

  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("idPerfil")
  private Integer idPerfil = null;

  @JsonProperty("numaOUsuario")
  private String numaOUsuario = null;

  public PycAsignaPeticionDesa idAplicacion(Integer idAplicacion) {
    this.idAplicacion = idAplicacion;
    return this;
  }

  /**
   * Identificador de la aplicación
   * @return idAplicacion
  **/
  @ApiModelProperty(example = "1", required = true, value = "Identificador de la aplicación")
  @NotNull


  public Integer getIdAplicacion() {
    return idAplicacion;
  }

  public void setIdAplicacion(Integer idAplicacion) {
    this.idAplicacion = idAplicacion;
  }

  public PycAsignaPeticionDesa idDesarrollador(Integer idDesarrollador) {
    this.idDesarrollador = idDesarrollador;
    return this;
  }

  /**
   * Identificador del desarrollador/analista asignado
   * @return idDesarrollador
  **/
  @ApiModelProperty(example = "25", required = true, value = "Identificador del desarrollador/analista asignado")
  @NotNull


  public Integer getIdDesarrollador() {
    return idDesarrollador;
  }

  public void setIdDesarrollador(Integer idDesarrollador) {
    this.idDesarrollador = idDesarrollador;
  }

  public PycAsignaPeticionDesa idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición a asignar
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", required = true, value = "Identificador de la petición a asignar")
  @NotNull


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycAsignaPeticionDesa idPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
    return this;
  }

  /**
   * Identificador del perfil del desarrollador
   * @return idPerfil
  **/
  @ApiModelProperty(example = "5", required = true, value = "Identificador del perfil del desarrollador")
  @NotNull


  public Integer getIdPerfil() {
    return idPerfil;
  }

  public void setIdPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
  }

  public PycAsignaPeticionDesa numaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
    return this;
  }

  /**
   * Número de usuario para identificación en operaciones de base de datos (opcional)
   * @return numaOUsuario
  **/
  @ApiModelProperty(example = "USR001", value = "Número de usuario para identificación en operaciones de base de datos (opcional)")


  public String getNumaOUsuario() {
    return numaOUsuario;
  }

  public void setNumaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycAsignaPeticionDesa pycAsignaPeticionDesa = (PycAsignaPeticionDesa) o;
    return Objects.equals(this.idAplicacion, pycAsignaPeticionDesa.idAplicacion) &&
        Objects.equals(this.idDesarrollador, pycAsignaPeticionDesa.idDesarrollador) &&
        Objects.equals(this.idPeticion, pycAsignaPeticionDesa.idPeticion) &&
        Objects.equals(this.idPerfil, pycAsignaPeticionDesa.idPerfil) &&
        Objects.equals(this.numaOUsuario, pycAsignaPeticionDesa.numaOUsuario);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idAplicacion, idDesarrollador, idPeticion, idPerfil, numaOUsuario);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycAsignaPeticionDesa {\n");
    
    sb.append("    idAplicacion: ").append(toIndentedString(idAplicacion)).append("\n");
    sb.append("    idDesarrollador: ").append(toIndentedString(idDesarrollador)).append("\n");
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    idPerfil: ").append(toIndentedString(idPerfil)).append("\n");
    sb.append("    numaOUsuario: ").append(toIndentedString(numaOUsuario)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

