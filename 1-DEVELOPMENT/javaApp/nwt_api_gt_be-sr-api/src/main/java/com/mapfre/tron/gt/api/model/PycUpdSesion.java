package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUpdSesion
 */
@Validated

public class PycUpdSesion   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("estado")
  private Integer estado = null;

  public PycUpdSesion idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * ID del usuario al que se le actualizará la sesión
   * @return idUsuario
  **/
  @ApiModelProperty(example = "123", required = true, value = "ID del usuario al que se le actualizará la sesión")
  @NotNull


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycUpdSesion estado(Integer estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado de la sesión (1=login/entrada, 0=logout/salida)
   * @return estado
  **/
  @ApiModelProperty(example = "1", required = true, value = "Estado de la sesión (1=login/entrada, 0=logout/salida)")
  @NotNull


  public Integer getEstado() {
    return estado;
  }

  public void setEstado(Integer estado) {
    this.estado = estado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUpdSesion pycUpdSesion = (PycUpdSesion) o;
    return Objects.equals(this.idUsuario, pycUpdSesion.idUsuario) &&
        Objects.equals(this.estado, pycUpdSesion.estado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, estado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUpdSesion {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

