package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycAnalistaActi
 */
@Validated

public class PycAnalistaActi   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("nombre")
  private String nombre = null;

  @JsonProperty("urlPerfil")
  private String urlPerfil = null;

  @JsonProperty("estadoUsuario")
  private String estadoUsuario = null;

  @JsonProperty("asigna")
  private String asigna = null;

  public PycAnalistaActi idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * ID único del usuario analista
   * @return idUsuario
  **/
  @ApiModelProperty(example = "123", value = "ID único del usuario analista")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycAnalistaActi nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre completo del analista (primer nombre + primer apellido)
   * @return nombre
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre completo del analista (primer nombre + primer apellido)")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public PycAnalistaActi urlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
    return this;
  }

  /**
   * URL de la imagen de perfil del analista o imagen por defecto según género
   * @return urlPerfil
  **/
  @ApiModelProperty(example = "/images/profiles/user123.jpg", value = "URL de la imagen de perfil del analista o imagen por defecto según género")


  public String getUrlPerfil() {
    return urlPerfil;
  }

  public void setUrlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
  }

  public PycAnalistaActi estadoUsuario(String estadoUsuario) {
    this.estadoUsuario = estadoUsuario;
    return this;
  }

  /**
   * Estado del usuario analista
   * @return estadoUsuario
  **/
  @ApiModelProperty(example = "ACT", value = "Estado del usuario analista")


  public String getEstadoUsuario() {
    return estadoUsuario;
  }

  public void setEstadoUsuario(String estadoUsuario) {
    this.estadoUsuario = estadoUsuario;
  }

  public PycAnalistaActi asigna(String asigna) {
    this.asigna = asigna;
    return this;
  }

  /**
   * Indicador si el analista está asignado a la actividad (0=No asignado, 1=Asignado)
   * @return asigna
  **/
  @ApiModelProperty(example = "1", value = "Indicador si el analista está asignado a la actividad (0=No asignado, 1=Asignado)")


  public String getAsigna() {
    return asigna;
  }

  public void setAsigna(String asigna) {
    this.asigna = asigna;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycAnalistaActi pycAnalistaActi = (PycAnalistaActi) o;
    return Objects.equals(this.idUsuario, pycAnalistaActi.idUsuario) &&
        Objects.equals(this.nombre, pycAnalistaActi.nombre) &&
        Objects.equals(this.urlPerfil, pycAnalistaActi.urlPerfil) &&
        Objects.equals(this.estadoUsuario, pycAnalistaActi.estadoUsuario) &&
        Objects.equals(this.asigna, pycAnalistaActi.asigna);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, nombre, urlPerfil, estadoUsuario, asigna);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycAnalistaActi {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    urlPerfil: ").append(toIndentedString(urlPerfil)).append("\n");
    sb.append("    estadoUsuario: ").append(toIndentedString(estadoUsuario)).append("\n");
    sb.append("    asigna: ").append(toIndentedString(asigna)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

