package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycCreaSoliRHResponse
 */
@Validated

public class PycCreaSoliRHResponse   {
  @JsonProperty("idSolicitud")
  private Integer idSolicitud = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  @JsonProperty("ejecutado")
  private Boolean ejecutado = null;

  public PycCreaSoliRHResponse idSolicitud(Integer idSolicitud) {
    this.idSolicitud = idSolicitud;
    return this;
  }

  /**
   * ID de la solicitud de RH creada
   * @return idSolicitud
  **/
  @ApiModelProperty(example = "456", value = "ID de la solicitud de RH creada")


  public Integer getIdSolicitud() {
    return idSolicitud;
  }

  public void setIdSolicitud(Integer idSolicitud) {
    this.idSolicitud = idSolicitud;
  }

  public PycCreaSoliRHResponse mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje de confirmación de la operación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Solicitud de Recursos Humanos creada exitosamente", value = "Mensaje de confirmación de la operación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }

  public PycCreaSoliRHResponse ejecutado(Boolean ejecutado) {
    this.ejecutado = ejecutado;
    return this;
  }

  /**
   * Indica si la operación se ejecutó correctamente
   * @return ejecutado
  **/
  @ApiModelProperty(example = "true", value = "Indica si la operación se ejecutó correctamente")


  public Boolean isEjecutado() {
    return ejecutado;
  }

  public void setEjecutado(Boolean ejecutado) {
    this.ejecutado = ejecutado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycCreaSoliRHResponse pycCreaSoliRHResponse = (PycCreaSoliRHResponse) o;
    return Objects.equals(this.idSolicitud, pycCreaSoliRHResponse.idSolicitud) &&
        Objects.equals(this.mensaje, pycCreaSoliRHResponse.mensaje) &&
        Objects.equals(this.ejecutado, pycCreaSoliRHResponse.ejecutado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idSolicitud, mensaje, ejecutado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycCreaSoliRHResponse {\n");
    
    sb.append("    idSolicitud: ").append(toIndentedString(idSolicitud)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("    ejecutado: ").append(toIndentedString(ejecutado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

