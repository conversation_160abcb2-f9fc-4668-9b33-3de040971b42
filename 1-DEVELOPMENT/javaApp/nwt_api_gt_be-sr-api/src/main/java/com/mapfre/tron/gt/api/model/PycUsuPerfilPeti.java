package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUsuPerfilPeti
 */
@Validated

public class PycUsuPerfilPeti   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("nombre")
  private String nombre = null;

  @JsonProperty("genero")
  private String genero = null;

  @JsonProperty("urlPerfil")
  private String urlPerfil = null;

  @JsonProperty("idPerfil")
  private Integer idPerfil = null;

  @JsonProperty("nombrePerfil")
  private String nombrePerfil = null;

  @JsonProperty("asigna")
  private String asigna = null;

  public PycUsuPerfilPeti idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Identificador del usuario
   * @return idUsuario
  **/
  @ApiModelProperty(example = "1234", value = "Identificador del usuario")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycUsuPerfilPeti nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre completo del usuario (primer nombre, segundo nombre y primer apellido)
   * @return nombre
  **/
  @ApiModelProperty(example = "Juan Carlos Pérez", value = "Nombre completo del usuario (primer nombre, segundo nombre y primer apellido)")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public PycUsuPerfilPeti genero(String genero) {
    this.genero = genero;
    return this;
  }

  /**
   * Género del usuario
   * @return genero
  **/
  @ApiModelProperty(example = "M", value = "Género del usuario")


  public String getGenero() {
    return genero;
  }

  public void setGenero(String genero) {
    this.genero = genero;
  }

  public PycUsuPerfilPeti urlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
    return this;
  }

  /**
   * URL de la imagen de perfil del usuario
   * @return urlPerfil
  **/
  @ApiModelProperty(example = "/images/user_male.png", value = "URL de la imagen de perfil del usuario")


  public String getUrlPerfil() {
    return urlPerfil;
  }

  public void setUrlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
  }

  public PycUsuPerfilPeti idPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
    return this;
  }

  /**
   * Identificador del perfil
   * @return idPerfil
  **/
  @ApiModelProperty(example = "5", value = "Identificador del perfil")


  public Integer getIdPerfil() {
    return idPerfil;
  }

  public void setIdPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
  }

  public PycUsuPerfilPeti nombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
    return this;
  }

  /**
   * Nombre del perfil
   * @return nombrePerfil
  **/
  @ApiModelProperty(example = "Analista Senior", value = "Nombre del perfil")


  public String getNombrePerfil() {
    return nombrePerfil;
  }

  public void setNombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
  }

  public PycUsuPerfilPeti asigna(String asigna) {
    this.asigna = asigna;
    return this;
  }

  /**
   * Indicador si el usuario está asignado (1) o no (0)
   * @return asigna
  **/
  @ApiModelProperty(example = "1", value = "Indicador si el usuario está asignado (1) o no (0)")


  public String getAsigna() {
    return asigna;
  }

  public void setAsigna(String asigna) {
    this.asigna = asigna;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUsuPerfilPeti pycUsuPerfilPeti = (PycUsuPerfilPeti) o;
    return Objects.equals(this.idUsuario, pycUsuPerfilPeti.idUsuario) &&
        Objects.equals(this.nombre, pycUsuPerfilPeti.nombre) &&
        Objects.equals(this.genero, pycUsuPerfilPeti.genero) &&
        Objects.equals(this.urlPerfil, pycUsuPerfilPeti.urlPerfil) &&
        Objects.equals(this.idPerfil, pycUsuPerfilPeti.idPerfil) &&
        Objects.equals(this.nombrePerfil, pycUsuPerfilPeti.nombrePerfil) &&
        Objects.equals(this.asigna, pycUsuPerfilPeti.asigna);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, nombre, genero, urlPerfil, idPerfil, nombrePerfil, asigna);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUsuPerfilPeti {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    genero: ").append(toIndentedString(genero)).append("\n");
    sb.append("    urlPerfil: ").append(toIndentedString(urlPerfil)).append("\n");
    sb.append("    idPerfil: ").append(toIndentedString(idPerfil)).append("\n");
    sb.append("    nombrePerfil: ").append(toIndentedString(nombrePerfil)).append("\n");
    sb.append("    asigna: ").append(toIndentedString(asigna)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

