package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycCategoriaPet
 */
@Validated

public class PycCategoriaPet   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("idCategoriaTablero")
  private Integer idCategoriaTablero = null;

  @JsonProperty("nombre")
  private String nombre = null;

  @JsonProperty("total")
  private Integer total = null;

  @JsonProperty("terminadas")
  private Integer terminadas = null;

  @JsonProperty("pendientes")
  private Integer pendientes = null;

  @JsonProperty("horasBase")
  private Double horasBase = null;

  @JsonProperty("horasReal")
  private Double horasReal = null;

  @JsonProperty("porcentaje")
  private Double porcentaje = null;

  @JsonProperty("fechaInicio")
  private String fechaInicio = null;

  @JsonProperty("fechaFin")
  private String fechaFin = null;

  @JsonProperty("fechaRealTerminado")
  private String fechaRealTerminado = null;

  @JsonProperty("fechaRealPendiente")
  private String fechaRealPendiente = null;

  public PycCategoriaPet idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador de la petición")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycCategoriaPet idCategoriaTablero(Integer idCategoriaTablero) {
    this.idCategoriaTablero = idCategoriaTablero;
    return this;
  }

  /**
   * Identificador de la categoría del tablero
   * @return idCategoriaTablero
  **/
  @ApiModelProperty(example = "5", value = "Identificador de la categoría del tablero")


  public Integer getIdCategoriaTablero() {
    return idCategoriaTablero;
  }

  public void setIdCategoriaTablero(Integer idCategoriaTablero) {
    this.idCategoriaTablero = idCategoriaTablero;
  }

  public PycCategoriaPet nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre de la categoría
   * @return nombre
  **/
  @ApiModelProperty(example = "Desarrollo", value = "Nombre de la categoría")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public PycCategoriaPet total(Integer total) {
    this.total = total;
    return this;
  }

  /**
   * Número total de actividades en la categoría
   * @return total
  **/
  @ApiModelProperty(example = "15", value = "Número total de actividades en la categoría")


  public Integer getTotal() {
    return total;
  }

  public void setTotal(Integer total) {
    this.total = total;
  }

  public PycCategoriaPet terminadas(Integer terminadas) {
    this.terminadas = terminadas;
    return this;
  }

  /**
   * Número de actividades terminadas
   * @return terminadas
  **/
  @ApiModelProperty(example = "8", value = "Número de actividades terminadas")


  public Integer getTerminadas() {
    return terminadas;
  }

  public void setTerminadas(Integer terminadas) {
    this.terminadas = terminadas;
  }

  public PycCategoriaPet pendientes(Integer pendientes) {
    this.pendientes = pendientes;
    return this;
  }

  /**
   * Número de actividades pendientes
   * @return pendientes
  **/
  @ApiModelProperty(example = "7", value = "Número de actividades pendientes")


  public Integer getPendientes() {
    return pendientes;
  }

  public void setPendientes(Integer pendientes) {
    this.pendientes = pendientes;
  }

  public PycCategoriaPet horasBase(Double horasBase) {
    this.horasBase = horasBase;
    return this;
  }

  /**
   * Total de horas base planificadas
   * @return horasBase
  **/
  @ApiModelProperty(example = "120.5", value = "Total de horas base planificadas")


  public Double getHorasBase() {
    return horasBase;
  }

  public void setHorasBase(Double horasBase) {
    this.horasBase = horasBase;
  }

  public PycCategoriaPet horasReal(Double horasReal) {
    this.horasReal = horasReal;
    return this;
  }

  /**
   * Total de horas reales ejecutadas
   * @return horasReal
  **/
  @ApiModelProperty(example = "135.75", value = "Total de horas reales ejecutadas")


  public Double getHorasReal() {
    return horasReal;
  }

  public void setHorasReal(Double horasReal) {
    this.horasReal = horasReal;
  }

  public PycCategoriaPet porcentaje(Double porcentaje) {
    this.porcentaje = porcentaje;
    return this;
  }

  /**
   * Porcentaje de avance calculado
   * @return porcentaje
  **/
  @ApiModelProperty(example = "85.25", value = "Porcentaje de avance calculado")


  public Double getPorcentaje() {
    return porcentaje;
  }

  public void setPorcentaje(Double porcentaje) {
    this.porcentaje = porcentaje;
  }

  public PycCategoriaPet fechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
    return this;
  }

  /**
   * Fecha de inicio más temprana de las actividades
   * @return fechaInicio
  **/
  @ApiModelProperty(example = "2024-01-15", value = "Fecha de inicio más temprana de las actividades")


  public String getFechaInicio() {
    return fechaInicio;
  }

  public void setFechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
  }

  public PycCategoriaPet fechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
    return this;
  }

  /**
   * Fecha de fin más tardía de las actividades
   * @return fechaFin
  **/
  @ApiModelProperty(example = "2024-02-20", value = "Fecha de fin más tardía de las actividades")


  public String getFechaFin() {
    return fechaFin;
  }

  public void setFechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
  }

  public PycCategoriaPet fechaRealTerminado(String fechaRealTerminado) {
    this.fechaRealTerminado = fechaRealTerminado;
    return this;
  }

  /**
   * Fecha real de terminación más tardía de actividades terminadas
   * @return fechaRealTerminado
  **/
  @ApiModelProperty(example = "2024-02-18", value = "Fecha real de terminación más tardía de actividades terminadas")


  public String getFechaRealTerminado() {
    return fechaRealTerminado;
  }

  public void setFechaRealTerminado(String fechaRealTerminado) {
    this.fechaRealTerminado = fechaRealTerminado;
  }

  public PycCategoriaPet fechaRealPendiente(String fechaRealPendiente) {
    this.fechaRealPendiente = fechaRealPendiente;
    return this;
  }

  /**
   * Fecha de fin más tardía de actividades pendientes
   * @return fechaRealPendiente
  **/
  @ApiModelProperty(example = "2024-02-20", value = "Fecha de fin más tardía de actividades pendientes")


  public String getFechaRealPendiente() {
    return fechaRealPendiente;
  }

  public void setFechaRealPendiente(String fechaRealPendiente) {
    this.fechaRealPendiente = fechaRealPendiente;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycCategoriaPet pycCategoriaPet = (PycCategoriaPet) o;
    return Objects.equals(this.idPeticion, pycCategoriaPet.idPeticion) &&
        Objects.equals(this.idCategoriaTablero, pycCategoriaPet.idCategoriaTablero) &&
        Objects.equals(this.nombre, pycCategoriaPet.nombre) &&
        Objects.equals(this.total, pycCategoriaPet.total) &&
        Objects.equals(this.terminadas, pycCategoriaPet.terminadas) &&
        Objects.equals(this.pendientes, pycCategoriaPet.pendientes) &&
        Objects.equals(this.horasBase, pycCategoriaPet.horasBase) &&
        Objects.equals(this.horasReal, pycCategoriaPet.horasReal) &&
        Objects.equals(this.porcentaje, pycCategoriaPet.porcentaje) &&
        Objects.equals(this.fechaInicio, pycCategoriaPet.fechaInicio) &&
        Objects.equals(this.fechaFin, pycCategoriaPet.fechaFin) &&
        Objects.equals(this.fechaRealTerminado, pycCategoriaPet.fechaRealTerminado) &&
        Objects.equals(this.fechaRealPendiente, pycCategoriaPet.fechaRealPendiente);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, idCategoriaTablero, nombre, total, terminadas, pendientes, horasBase, horasReal, porcentaje, fechaInicio, fechaFin, fechaRealTerminado, fechaRealPendiente);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycCategoriaPet {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    idCategoriaTablero: ").append(toIndentedString(idCategoriaTablero)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    total: ").append(toIndentedString(total)).append("\n");
    sb.append("    terminadas: ").append(toIndentedString(terminadas)).append("\n");
    sb.append("    pendientes: ").append(toIndentedString(pendientes)).append("\n");
    sb.append("    horasBase: ").append(toIndentedString(horasBase)).append("\n");
    sb.append("    horasReal: ").append(toIndentedString(horasReal)).append("\n");
    sb.append("    porcentaje: ").append(toIndentedString(porcentaje)).append("\n");
    sb.append("    fechaInicio: ").append(toIndentedString(fechaInicio)).append("\n");
    sb.append("    fechaFin: ").append(toIndentedString(fechaFin)).append("\n");
    sb.append("    fechaRealTerminado: ").append(toIndentedString(fechaRealTerminado)).append("\n");
    sb.append("    fechaRealPendiente: ").append(toIndentedString(fechaRealPendiente)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

