package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycInsertObservacion
 */
@Validated

public class PycInsertObservacion   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("observacion")
  private String observacion = null;

  @JsonProperty("usuarioPet")
  private Integer usuarioPet = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("publico")
  private String publico = null;

  @JsonProperty("numaOUsuario")
  private String numaOUsuario = null;

  public PycInsertObservacion idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * ID de la petición a la que se asociará la observación
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", required = true, value = "ID de la petición a la que se asociará la observación")
  @NotNull


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycInsertObservacion observacion(String observacion) {
    this.observacion = observacion;
    return this;
  }

  /**
   * Texto de la observación a insertar
   * @return observacion
  **/
  @ApiModelProperty(example = "Se requiere información adicional del cliente", required = true, value = "Texto de la observación a insertar")
  @NotNull


  public String getObservacion() {
    return observacion;
  }

  public void setObservacion(String observacion) {
    this.observacion = observacion;
  }

  public PycInsertObservacion usuarioPet(Integer usuarioPet) {
    this.usuarioPet = usuarioPet;
    return this;
  }

  /**
   * ID del usuario que realiza la petición
   * @return usuarioPet
  **/
  @ApiModelProperty(example = "123", required = true, value = "ID del usuario que realiza la petición")
  @NotNull


  public Integer getUsuarioPet() {
    return usuarioPet;
  }

  public void setUsuarioPet(Integer usuarioPet) {
    this.usuarioPet = usuarioPet;
  }

  public PycInsertObservacion estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado de la observación
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", required = true, value = "Estado de la observación")
  @NotNull


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycInsertObservacion publico(String publico) {
    this.publico = publico;
    return this;
  }

  /**
   * Indicador si la observación es pública (S/N)
   * @return publico
  **/
  @ApiModelProperty(example = "S", value = "Indicador si la observación es pública (S/N)")


  public String getPublico() {
    return publico;
  }

  public void setPublico(String publico) {
    this.publico = publico;
  }

  public PycInsertObservacion numaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
    return this;
  }

  /**
   * Número o usuario que realiza la operación
   * @return numaOUsuario
  **/
  @ApiModelProperty(example = "ADMIN_USER", value = "Número o usuario que realiza la operación")


  public String getNumaOUsuario() {
    return numaOUsuario;
  }

  public void setNumaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycInsertObservacion pycInsertObservacion = (PycInsertObservacion) o;
    return Objects.equals(this.idPeticion, pycInsertObservacion.idPeticion) &&
        Objects.equals(this.observacion, pycInsertObservacion.observacion) &&
        Objects.equals(this.usuarioPet, pycInsertObservacion.usuarioPet) &&
        Objects.equals(this.estado, pycInsertObservacion.estado) &&
        Objects.equals(this.publico, pycInsertObservacion.publico) &&
        Objects.equals(this.numaOUsuario, pycInsertObservacion.numaOUsuario);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, observacion, usuarioPet, estado, publico, numaOUsuario);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycInsertObservacion {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    observacion: ").append(toIndentedString(observacion)).append("\n");
    sb.append("    usuarioPet: ").append(toIndentedString(usuarioPet)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    publico: ").append(toIndentedString(publico)).append("\n");
    sb.append("    numaOUsuario: ").append(toIndentedString(numaOUsuario)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

