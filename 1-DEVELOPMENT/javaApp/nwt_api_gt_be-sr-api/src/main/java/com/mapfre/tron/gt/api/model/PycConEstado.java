package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycConEstado
 */
@Validated

public class PycConEstado   {
  @JsonProperty("idEstado")
  private Integer idEstado = null;

  @JsonProperty("nombreEstado")
  private String nombreEstado = null;

  public PycConEstado idEstado(Integer idEstado) {
    this.idEstado = idEstado;
    return this;
  }

  /**
   * Identificador del estado
   * @return idEstado
  **/
  @ApiModelProperty(example = "1", value = "Identificador del estado")


  public Integer getIdEstado() {
    return idEstado;
  }

  public void setIdEstado(Integer idEstado) {
    this.idEstado = idEstado;
  }

  public PycConEstado nombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
    return this;
  }

  /**
   * Nombre del estado
   * @return nombreEstado
  **/
  @ApiModelProperty(example = "Pendiente", value = "Nombre del estado")


  public String getNombreEstado() {
    return nombreEstado;
  }

  public void setNombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycConEstado pycConEstado = (PycConEstado) o;
    return Objects.equals(this.idEstado, pycConEstado.idEstado) &&
        Objects.equals(this.nombreEstado, pycConEstado.nombreEstado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idEstado, nombreEstado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycConEstado {\n");
    
    sb.append("    idEstado: ").append(toIndentedString(idEstado)).append("\n");
    sb.append("    nombreEstado: ").append(toIndentedString(nombreEstado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

