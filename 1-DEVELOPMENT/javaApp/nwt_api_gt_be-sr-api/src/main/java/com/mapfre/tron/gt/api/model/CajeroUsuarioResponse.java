package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.mapfre.tron.gt.api.model.CajeroUsuarioResponseCajeros;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CajeroUsuarioResponse
 */
@Validated

public class CajeroUsuarioResponse   {
  @JsonProperty("codigo")
  private String codigo = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  @JsonProperty("cajeros")
  @Valid
  private List<CajeroUsuarioResponseCajeros> cajeros = null;

  public CajeroUsuarioResponse codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Código de resultado de la operación
   * @return codigo
  **/
  @ApiModelProperty(example = "200", value = "Código de resultado de la operación")


  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public CajeroUsuarioResponse mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje descriptivo del resultado de la operación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Cajeros obtenidos exitosamente", value = "Mensaje descriptivo del resultado de la operación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }

  public CajeroUsuarioResponse cajeros(List<CajeroUsuarioResponseCajeros> cajeros) {
    this.cajeros = cajeros;
    return this;
  }

  public CajeroUsuarioResponse addCajerosItem(CajeroUsuarioResponseCajeros cajerosItem) {
    if (this.cajeros == null) {
      this.cajeros = new ArrayList<>();
    }
    this.cajeros.add(cajerosItem);
    return this;
  }

  /**
   * Lista de cajeros del usuario
   * @return cajeros
  **/
  @ApiModelProperty(value = "Lista de cajeros del usuario")

  @Valid

  public List<CajeroUsuarioResponseCajeros> getCajeros() {
    return cajeros;
  }

  public void setCajeros(List<CajeroUsuarioResponseCajeros> cajeros) {
    this.cajeros = cajeros;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CajeroUsuarioResponse cajeroUsuarioResponse = (CajeroUsuarioResponse) o;
    return Objects.equals(this.codigo, cajeroUsuarioResponse.codigo) &&
        Objects.equals(this.mensaje, cajeroUsuarioResponse.mensaje) &&
        Objects.equals(this.cajeros, cajeroUsuarioResponse.cajeros);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codigo, mensaje, cajeros);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CajeroUsuarioResponse {\n");
    
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("    cajeros: ").append(toIndentedString(cajeros)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

