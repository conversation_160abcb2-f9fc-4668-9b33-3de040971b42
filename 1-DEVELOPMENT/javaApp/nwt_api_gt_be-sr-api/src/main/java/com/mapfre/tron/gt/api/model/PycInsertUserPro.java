package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycInsertUserPro
 */
@Validated

public class PycInsertUserPro   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("idProceso")
  private Integer idProceso = null;

  @JsonProperty("estado")
  private String estado = null;

  public PycInsertUserPro idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Identificador del usuario
   * @return idUsuario
  **/
  @ApiModelProperty(example = "123", required = true, value = "Identificador del usuario")
  @NotNull


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycInsertUserPro idProceso(Integer idProceso) {
    this.idProceso = idProceso;
    return this;
  }

  /**
   * Identificador del proceso
   * @return idProceso
  **/
  @ApiModelProperty(example = "456", required = true, value = "Identificador del proceso")
  @NotNull


  public Integer getIdProceso() {
    return idProceso;
  }

  public void setIdProceso(Integer idProceso) {
    this.idProceso = idProceso;
  }

  public PycInsertUserPro estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado de la relación usuario-proceso (ACT/INA)
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", required = true, value = "Estado de la relación usuario-proceso (ACT/INA)")
  @NotNull


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycInsertUserPro pycInsertUserPro = (PycInsertUserPro) o;
    return Objects.equals(this.idUsuario, pycInsertUserPro.idUsuario) &&
        Objects.equals(this.idProceso, pycInsertUserPro.idProceso) &&
        Objects.equals(this.estado, pycInsertUserPro.estado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, idProceso, estado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycInsertUserPro {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    idProceso: ").append(toIndentedString(idProceso)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

