package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycDocXPeticion
 */
@Validated

public class PycDocXPeticion   {
  @JsonProperty("obligatorio")
  private String obligatorio = null;

  @JsonProperty("idDocumento")
  private Integer idDocumento = null;

  @JsonProperty("nombreDocumento")
  private String nombreDocumento = null;

  @JsonProperty("descripcionDocumento")
  private String descripcionDocumento = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("fechaHora")
  private String fechaHora = null;

  @JsonProperty("multiArchivos")
  private String multiArchivos = null;

  @JsonProperty("extension")
  private String extension = null;

  @JsonProperty("tamanoMaximo")
  private Integer tamanoMaximo = null;

  public PycDocXPeticion obligatorio(String obligatorio) {
    this.obligatorio = obligatorio;
    return this;
  }

  /**
   * Indicador si el documento es obligatorio (S/N)
   * @return obligatorio
  **/
  @ApiModelProperty(example = "S", value = "Indicador si el documento es obligatorio (S/N)")


  public String getObligatorio() {
    return obligatorio;
  }

  public void setObligatorio(String obligatorio) {
    this.obligatorio = obligatorio;
  }

  public PycDocXPeticion idDocumento(Integer idDocumento) {
    this.idDocumento = idDocumento;
    return this;
  }

  /**
   * Identificador único del documento
   * @return idDocumento
  **/
  @ApiModelProperty(example = "5", value = "Identificador único del documento")


  public Integer getIdDocumento() {
    return idDocumento;
  }

  public void setIdDocumento(Integer idDocumento) {
    this.idDocumento = idDocumento;
  }

  public PycDocXPeticion nombreDocumento(String nombreDocumento) {
    this.nombreDocumento = nombreDocumento;
    return this;
  }

  /**
   * Nombre del documento
   * @return nombreDocumento
  **/
  @ApiModelProperty(example = "Cédula de Identidad", value = "Nombre del documento")


  public String getNombreDocumento() {
    return nombreDocumento;
  }

  public void setNombreDocumento(String nombreDocumento) {
    this.nombreDocumento = nombreDocumento;
  }

  public PycDocXPeticion descripcionDocumento(String descripcionDocumento) {
    this.descripcionDocumento = descripcionDocumento;
    return this;
  }

  /**
   * Descripción del documento
   * @return descripcionDocumento
  **/
  @ApiModelProperty(example = "Documento de identificación personal", value = "Descripción del documento")


  public String getDescripcionDocumento() {
    return descripcionDocumento;
  }

  public void setDescripcionDocumento(String descripcionDocumento) {
    this.descripcionDocumento = descripcionDocumento;
  }

  public PycDocXPeticion estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado del documento
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", value = "Estado del documento")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycDocXPeticion usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Usuario que creó o modificó el documento
   * @return usuario
  **/
  @ApiModelProperty(example = "admin", value = "Usuario que creó o modificó el documento")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public PycDocXPeticion fechaHora(String fechaHora) {
    this.fechaHora = fechaHora;
    return this;
  }

  /**
   * Fecha y hora de creación o modificación
   * @return fechaHora
  **/
  @ApiModelProperty(example = "2024-01-15 10:30:00", value = "Fecha y hora de creación o modificación")


  public String getFechaHora() {
    return fechaHora;
  }

  public void setFechaHora(String fechaHora) {
    this.fechaHora = fechaHora;
  }

  public PycDocXPeticion multiArchivos(String multiArchivos) {
    this.multiArchivos = multiArchivos;
    return this;
  }

  /**
   * Indicador si permite múltiples archivos (S/N)
   * @return multiArchivos
  **/
  @ApiModelProperty(example = "S", value = "Indicador si permite múltiples archivos (S/N)")


  public String getMultiArchivos() {
    return multiArchivos;
  }

  public void setMultiArchivos(String multiArchivos) {
    this.multiArchivos = multiArchivos;
  }

  public PycDocXPeticion extension(String extension) {
    this.extension = extension;
    return this;
  }

  /**
   * Extensiones de archivo permitidas
   * @return extension
  **/
  @ApiModelProperty(example = "PDF,JPG,PNG", value = "Extensiones de archivo permitidas")


  public String getExtension() {
    return extension;
  }

  public void setExtension(String extension) {
    this.extension = extension;
  }

  public PycDocXPeticion tamanoMaximo(Integer tamanoMaximo) {
    this.tamanoMaximo = tamanoMaximo;
    return this;
  }

  /**
   * Tamaño máximo del archivo en KB
   * @return tamanoMaximo
  **/
  @ApiModelProperty(example = "5120", value = "Tamaño máximo del archivo en KB")


  public Integer getTamanoMaximo() {
    return tamanoMaximo;
  }

  public void setTamanoMaximo(Integer tamanoMaximo) {
    this.tamanoMaximo = tamanoMaximo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycDocXPeticion pycDocXPeticion = (PycDocXPeticion) o;
    return Objects.equals(this.obligatorio, pycDocXPeticion.obligatorio) &&
        Objects.equals(this.idDocumento, pycDocXPeticion.idDocumento) &&
        Objects.equals(this.nombreDocumento, pycDocXPeticion.nombreDocumento) &&
        Objects.equals(this.descripcionDocumento, pycDocXPeticion.descripcionDocumento) &&
        Objects.equals(this.estado, pycDocXPeticion.estado) &&
        Objects.equals(this.usuario, pycDocXPeticion.usuario) &&
        Objects.equals(this.fechaHora, pycDocXPeticion.fechaHora) &&
        Objects.equals(this.multiArchivos, pycDocXPeticion.multiArchivos) &&
        Objects.equals(this.extension, pycDocXPeticion.extension) &&
        Objects.equals(this.tamanoMaximo, pycDocXPeticion.tamanoMaximo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(obligatorio, idDocumento, nombreDocumento, descripcionDocumento, estado, usuario, fechaHora, multiArchivos, extension, tamanoMaximo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycDocXPeticion {\n");
    
    sb.append("    obligatorio: ").append(toIndentedString(obligatorio)).append("\n");
    sb.append("    idDocumento: ").append(toIndentedString(idDocumento)).append("\n");
    sb.append("    nombreDocumento: ").append(toIndentedString(nombreDocumento)).append("\n");
    sb.append("    descripcionDocumento: ").append(toIndentedString(descripcionDocumento)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    fechaHora: ").append(toIndentedString(fechaHora)).append("\n");
    sb.append("    multiArchivos: ").append(toIndentedString(multiArchivos)).append("\n");
    sb.append("    extension: ").append(toIndentedString(extension)).append("\n");
    sb.append("    tamanoMaximo: ").append(toIndentedString(tamanoMaximo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

