package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycPerfilUsu
 */
@Validated

public class PycPerfilUsu   {
  @JsonProperty("idPerfil")
  private Integer idPerfil = null;

  @JsonProperty("nombrePerfil")
  private String nombrePerfil = null;

  @JsonProperty("descripcionPerfil")
  private String descripcionPerfil = null;

  @JsonProperty("indDefault")
  private String indDefault = null;

  @JsonProperty("multiperfil")
  private String multiperfil = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("indConsulta")
  private String indConsulta = null;

  public PycPerfilUsu idPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
    return this;
  }

  /**
   * Identificador del perfil
   * @return idPerfil
  **/
  @ApiModelProperty(example = "1", value = "Identificador del perfil")


  public Integer getIdPerfil() {
    return idPerfil;
  }

  public void setIdPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
  }

  public PycPerfilUsu nombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
    return this;
  }

  /**
   * Nombre del perfil
   * @return nombrePerfil
  **/
  @ApiModelProperty(example = "Administrador", value = "Nombre del perfil")


  public String getNombrePerfil() {
    return nombrePerfil;
  }

  public void setNombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
  }

  public PycPerfilUsu descripcionPerfil(String descripcionPerfil) {
    this.descripcionPerfil = descripcionPerfil;
    return this;
  }

  /**
   * Descripción del perfil
   * @return descripcionPerfil
  **/
  @ApiModelProperty(example = "Perfil con acceso completo al sistema", value = "Descripción del perfil")


  public String getDescripcionPerfil() {
    return descripcionPerfil;
  }

  public void setDescripcionPerfil(String descripcionPerfil) {
    this.descripcionPerfil = descripcionPerfil;
  }

  public PycPerfilUsu indDefault(String indDefault) {
    this.indDefault = indDefault;
    return this;
  }

  /**
   * Indicador si es el perfil por defecto del usuario
   * @return indDefault
  **/
  @ApiModelProperty(example = "S", value = "Indicador si es el perfil por defecto del usuario")


  public String getIndDefault() {
    return indDefault;
  }

  public void setIndDefault(String indDefault) {
    this.indDefault = indDefault;
  }

  public PycPerfilUsu multiperfil(String multiperfil) {
    this.multiperfil = multiperfil;
    return this;
  }

  /**
   * Indicador si el usuario tiene múltiples perfiles
   * @return multiperfil
  **/
  @ApiModelProperty(example = "S", value = "Indicador si el usuario tiene múltiples perfiles")


  public String getMultiperfil() {
    return multiperfil;
  }

  public void setMultiperfil(String multiperfil) {
    this.multiperfil = multiperfil;
  }

  public PycPerfilUsu estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado del perfil del usuario
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", value = "Estado del perfil del usuario")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycPerfilUsu indConsulta(String indConsulta) {
    this.indConsulta = indConsulta;
    return this;
  }

  /**
   * Indicador de consulta del perfil
   * @return indConsulta
  **/
  @ApiModelProperty(example = "S", value = "Indicador de consulta del perfil")


  public String getIndConsulta() {
    return indConsulta;
  }

  public void setIndConsulta(String indConsulta) {
    this.indConsulta = indConsulta;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycPerfilUsu pycPerfilUsu = (PycPerfilUsu) o;
    return Objects.equals(this.idPerfil, pycPerfilUsu.idPerfil) &&
        Objects.equals(this.nombrePerfil, pycPerfilUsu.nombrePerfil) &&
        Objects.equals(this.descripcionPerfil, pycPerfilUsu.descripcionPerfil) &&
        Objects.equals(this.indDefault, pycPerfilUsu.indDefault) &&
        Objects.equals(this.multiperfil, pycPerfilUsu.multiperfil) &&
        Objects.equals(this.estado, pycPerfilUsu.estado) &&
        Objects.equals(this.indConsulta, pycPerfilUsu.indConsulta);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPerfil, nombrePerfil, descripcionPerfil, indDefault, multiperfil, estado, indConsulta);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycPerfilUsu {\n");
    
    sb.append("    idPerfil: ").append(toIndentedString(idPerfil)).append("\n");
    sb.append("    nombrePerfil: ").append(toIndentedString(nombrePerfil)).append("\n");
    sb.append("    descripcionPerfil: ").append(toIndentedString(descripcionPerfil)).append("\n");
    sb.append("    indDefault: ").append(toIndentedString(indDefault)).append("\n");
    sb.append("    multiperfil: ").append(toIndentedString(multiperfil)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    indConsulta: ").append(toIndentedString(indConsulta)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

