package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ValidaIngresoEncuestaResponse
 */
@Validated

public class ValidaIngresoEncuestaResponse   {
  @JsonProperty("resultado")
  private String resultado = null;

  public ValidaIngresoEncuestaResponse resultado(String resultado) {
    this.resultado = resultado;
    return this;
  }

  /**
   * Resultado de la validación
   * @return resultado
  **/
  @ApiModelProperty(example = "OK", value = "Resultado de la validación")


  public String getResultado() {
    return resultado;
  }

  public void setResultado(String resultado) {
    this.resultado = resultado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ValidaIngresoEncuestaResponse validaIngresoEncuestaResponse = (ValidaIngresoEncuestaResponse) o;
    return Objects.equals(this.resultado, validaIngresoEncuestaResponse.resultado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(resultado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ValidaIngresoEncuestaResponse {\n");
    
    sb.append("    resultado: ").append(toIndentedString(resultado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

