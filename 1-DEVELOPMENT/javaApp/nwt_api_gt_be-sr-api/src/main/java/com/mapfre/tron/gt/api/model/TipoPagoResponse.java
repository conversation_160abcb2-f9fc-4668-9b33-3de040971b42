package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * TipoPagoResponse
 */
@Validated

public class TipoPagoResponse   {
  @JsonProperty("codigo")
  private String codigo = null;

  @JsonProperty("nombre")
  private String nombre = null;

  public TipoPagoResponse codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Codigo del tipo de pago
   * @return codigo
  **/
  @ApiModelProperty(example = "DEP", value = "Codigo del tipo de pago")


  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public TipoPagoResponse nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre del tipo de pago
   * @return nombre
  **/
  @ApiModelProperty(example = "DEPOSITO", value = "Nombre del tipo de pago")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TipoPagoResponse tipoPagoResponse = (TipoPagoResponse) o;
    return Objects.equals(this.codigo, tipoPagoResponse.codigo) &&
        Objects.equals(this.nombre, tipoPagoResponse.nombre);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codigo, nombre);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TipoPagoResponse {\n");
    
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

