package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycResumenActi
 */
@Validated

public class PycResumenActi   {
  @JsonProperty("totales")
  private Integer totales = null;

  @JsonProperty("terminadas")
  private Integer terminadas = null;

  @JsonProperty("pendientes")
  private Integer pendientes = null;

  @JsonProperty("base")
  private Double base = null;

  @JsonProperty("rea")
  private Double rea = null;

  @JsonProperty("porcentaje")
  private Integer porcentaje = null;

  public PycResumenActi totales(Integer totales) {
    this.totales = totales;
    return this;
  }

  /**
   * Número total de actividades
   * @return totales
  **/
  @ApiModelProperty(example = "15", value = "Número total de actividades")


  public Integer getTotales() {
    return totales;
  }

  public void setTotales(Integer totales) {
    this.totales = totales;
  }

  public PycResumenActi terminadas(Integer terminadas) {
    this.terminadas = terminadas;
    return this;
  }

  /**
   * Número de actividades terminadas (estado 'TER')
   * @return terminadas
  **/
  @ApiModelProperty(example = "8", value = "Número de actividades terminadas (estado 'TER')")


  public Integer getTerminadas() {
    return terminadas;
  }

  public void setTerminadas(Integer terminadas) {
    this.terminadas = terminadas;
  }

  public PycResumenActi pendientes(Integer pendientes) {
    this.pendientes = pendientes;
    return this;
  }

  /**
   * Número de actividades pendientes (total - terminadas)
   * @return pendientes
  **/
  @ApiModelProperty(example = "7", value = "Número de actividades pendientes (total - terminadas)")


  public Integer getPendientes() {
    return pendientes;
  }

  public void setPendientes(Integer pendientes) {
    this.pendientes = pendientes;
  }

  public PycResumenActi base(Double base) {
    this.base = base;
    return this;
  }

  /**
   * Total de horas base planificadas
   * @return base
  **/
  @ApiModelProperty(example = "120.5", value = "Total de horas base planificadas")


  public Double getBase() {
    return base;
  }

  public void setBase(Double base) {
    this.base = base;
  }

  public PycResumenActi rea(Double rea) {
    this.rea = rea;
    return this;
  }

  /**
   * Total de horas reales ejecutadas
   * @return rea
  **/
  @ApiModelProperty(example = "135.75", value = "Total de horas reales ejecutadas")


  public Double getRea() {
    return rea;
  }

  public void setRea(Double rea) {
    this.rea = rea;
  }

  public PycResumenActi porcentaje(Integer porcentaje) {
    this.porcentaje = porcentaje;
    return this;
  }

  /**
   * Porcentaje de avance (horas reales / horas base * 100)
   * @return porcentaje
  **/
  @ApiModelProperty(example = "113", value = "Porcentaje de avance (horas reales / horas base * 100)")


  public Integer getPorcentaje() {
    return porcentaje;
  }

  public void setPorcentaje(Integer porcentaje) {
    this.porcentaje = porcentaje;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycResumenActi pycResumenActi = (PycResumenActi) o;
    return Objects.equals(this.totales, pycResumenActi.totales) &&
        Objects.equals(this.terminadas, pycResumenActi.terminadas) &&
        Objects.equals(this.pendientes, pycResumenActi.pendientes) &&
        Objects.equals(this.base, pycResumenActi.base) &&
        Objects.equals(this.rea, pycResumenActi.rea) &&
        Objects.equals(this.porcentaje, pycResumenActi.porcentaje);
  }

  @Override
  public int hashCode() {
    return Objects.hash(totales, terminadas, pendientes, base, rea, porcentaje);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycResumenActi {\n");
    
    sb.append("    totales: ").append(toIndentedString(totales)).append("\n");
    sb.append("    terminadas: ").append(toIndentedString(terminadas)).append("\n");
    sb.append("    pendientes: ").append(toIndentedString(pendientes)).append("\n");
    sb.append("    base: ").append(toIndentedString(base)).append("\n");
    sb.append("    rea: ").append(toIndentedString(rea)).append("\n");
    sb.append("    porcentaje: ").append(toIndentedString(porcentaje)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

