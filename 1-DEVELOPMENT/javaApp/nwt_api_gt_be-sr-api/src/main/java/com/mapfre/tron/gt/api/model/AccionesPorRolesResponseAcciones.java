package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * AccionesPorRolesResponseAcciones
 */
@Validated

public class AccionesPorRolesResponseAcciones   {
  @JsonProperty("idAccion")
  private Integer idAccion = null;

  @JsonProperty("nombre")
  private String nombre = null;

  public AccionesPorRolesResponseAcciones idAccion(Integer idAccion) {
    this.idAccion = idAccion;
    return this;
  }

  /**
   * ID de la acción
   * @return idAccion
  **/
  @ApiModelProperty(example = "1", value = "ID de la acción")


  public Integer getIdAccion() {
    return idAccion;
  }

  public void setIdAccion(Integer idAccion) {
    this.idAccion = idAccion;
  }

  public AccionesPorRolesResponseAcciones nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre de la acción
   * @return nombre
  **/
  @ApiModelProperty(example = "CREAR_RUTA", value = "Nombre de la acción")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AccionesPorRolesResponseAcciones accionesPorRolesResponseAcciones = (AccionesPorRolesResponseAcciones) o;
    return Objects.equals(this.idAccion, accionesPorRolesResponseAcciones.idAccion) &&
        Objects.equals(this.nombre, accionesPorRolesResponseAcciones.nombre);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idAccion, nombre);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AccionesPorRolesResponseAcciones {\n");
    
    sb.append("    idAccion: ").append(toIndentedString(idAccion)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

