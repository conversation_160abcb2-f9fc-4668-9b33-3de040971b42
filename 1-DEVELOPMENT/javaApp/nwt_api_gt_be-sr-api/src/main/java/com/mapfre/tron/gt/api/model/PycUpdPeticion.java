package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUpdPeticion
 */
@Validated

public class PycUpdPeticion   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("idSolUser")
  private Integer idSolUser = null;

  @JsonProperty("nomPeticion")
  private String nomPeticion = null;

  @JsonProperty("descPeticion")
  private String descPeticion = null;

  @JsonProperty("idTipo")
  private Integer idTipo = null;

  @JsonProperty("observacion")
  private String observacion = null;

  @JsonProperty("codCia")
  private String codCia = null;

  @JsonProperty("nomClie")
  private String nomClie = null;

  @JsonProperty("telClie")
  private String telClie = null;

  @JsonProperty("emailClie")
  private String emailClie = null;

  @JsonProperty("origen")
  private String origen = null;

  @JsonProperty("clarity")
  private String clarity = null;

  @JsonProperty("prioridad")
  private String prioridad = null;

  @JsonProperty("tipoCliente")
  private String tipoCliente = null;

  @JsonProperty("codCliente")
  private String codCliente = null;

  @JsonProperty("noPoliza")
  private String noPoliza = null;

  @JsonProperty("tipServicio")
  private String tipServicio = null;

  @JsonProperty("causa")
  private String causa = null;

  @JsonProperty("gravedad")
  private String gravedad = null;

  @JsonProperty("numaOUsuario")
  private String numaOUsuario = null;

  public PycUpdPeticion idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición a actualizar
   * @return idPeticion
  **/
  @ApiModelProperty(example = "123", required = true, value = "Identificador de la petición a actualizar")
  @NotNull


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycUpdPeticion idSolUser(Integer idSolUser) {
    this.idSolUser = idSolUser;
    return this;
  }

  /**
   * Identificador del usuario solicitante
   * @return idSolUser
  **/
  @ApiModelProperty(example = "456", required = true, value = "Identificador del usuario solicitante")
  @NotNull


  public Integer getIdSolUser() {
    return idSolUser;
  }

  public void setIdSolUser(Integer idSolUser) {
    this.idSolUser = idSolUser;
  }

  public PycUpdPeticion nomPeticion(String nomPeticion) {
    this.nomPeticion = nomPeticion;
    return this;
  }

  /**
   * Nombre de la petición
   * @return nomPeticion
  **/
  @ApiModelProperty(example = "Actualización del sistema de facturación", required = true, value = "Nombre de la petición")
  @NotNull


  public String getNomPeticion() {
    return nomPeticion;
  }

  public void setNomPeticion(String nomPeticion) {
    this.nomPeticion = nomPeticion;
  }

  public PycUpdPeticion descPeticion(String descPeticion) {
    this.descPeticion = descPeticion;
    return this;
  }

  /**
   * Descripción detallada de la petición
   * @return descPeticion
  **/
  @ApiModelProperty(example = "Actualización completa del módulo de facturación para incluir nuevas funcionalidades", required = true, value = "Descripción detallada de la petición")
  @NotNull


  public String getDescPeticion() {
    return descPeticion;
  }

  public void setDescPeticion(String descPeticion) {
    this.descPeticion = descPeticion;
  }

  public PycUpdPeticion idTipo(Integer idTipo) {
    this.idTipo = idTipo;
    return this;
  }

  /**
   * Identificador del tipo de petición
   * @return idTipo
  **/
  @ApiModelProperty(example = "2", required = true, value = "Identificador del tipo de petición")
  @NotNull


  public Integer getIdTipo() {
    return idTipo;
  }

  public void setIdTipo(Integer idTipo) {
    this.idTipo = idTipo;
  }

  public PycUpdPeticion observacion(String observacion) {
    this.observacion = observacion;
    return this;
  }

  /**
   * Observación de la actualización
   * @return observacion
  **/
  @ApiModelProperty(example = "Actualización solicitada por el área de contabilidad", required = true, value = "Observación de la actualización")
  @NotNull


  public String getObservacion() {
    return observacion;
  }

  public void setObservacion(String observacion) {
    this.observacion = observacion;
  }

  public PycUpdPeticion codCia(String codCia) {
    this.codCia = codCia;
    return this;
  }

  /**
   * Código de la compañía
   * @return codCia
  **/
  @ApiModelProperty(example = "001", required = true, value = "Código de la compañía")
  @NotNull


  public String getCodCia() {
    return codCia;
  }

  public void setCodCia(String codCia) {
    this.codCia = codCia;
  }

  public PycUpdPeticion nomClie(String nomClie) {
    this.nomClie = nomClie;
    return this;
  }

  /**
   * Nombre del cliente (opcional)
   * @return nomClie
  **/
  @ApiModelProperty(example = "Empresa ABC S.A.", value = "Nombre del cliente (opcional)")


  public String getNomClie() {
    return nomClie;
  }

  public void setNomClie(String nomClie) {
    this.nomClie = nomClie;
  }

  public PycUpdPeticion telClie(String telClie) {
    this.telClie = telClie;
    return this;
  }

  /**
   * Teléfono del cliente (opcional)
   * @return telClie
  **/
  @ApiModelProperty(example = "2345-6789", value = "Teléfono del cliente (opcional)")


  public String getTelClie() {
    return telClie;
  }

  public void setTelClie(String telClie) {
    this.telClie = telClie;
  }

  public PycUpdPeticion emailClie(String emailClie) {
    this.emailClie = emailClie;
    return this;
  }

  /**
   * Correo electrónico del cliente (opcional)
   * @return emailClie
  **/
  @ApiModelProperty(example = "<EMAIL>", value = "Correo electrónico del cliente (opcional)")


  public String getEmailClie() {
    return emailClie;
  }

  public void setEmailClie(String emailClie) {
    this.emailClie = emailClie;
  }

  public PycUpdPeticion origen(String origen) {
    this.origen = origen;
    return this;
  }

  /**
   * Origen de la petición (opcional)
   * @return origen
  **/
  @ApiModelProperty(example = "WEBRH", value = "Origen de la petición (opcional)")


  public String getOrigen() {
    return origen;
  }

  public void setOrigen(String origen) {
    this.origen = origen;
  }

  public PycUpdPeticion clarity(String clarity) {
    this.clarity = clarity;
    return this;
  }

  /**
   * Código Clarity asociado (opcional)
   * @return clarity
  **/
  @ApiModelProperty(example = "CLR-2025-001", value = "Código Clarity asociado (opcional)")


  public String getClarity() {
    return clarity;
  }

  public void setClarity(String clarity) {
    this.clarity = clarity;
  }

  public PycUpdPeticion prioridad(String prioridad) {
    this.prioridad = prioridad;
    return this;
  }

  /**
   * Prioridad de la petición (opcional)
   * @return prioridad
  **/
  @ApiModelProperty(example = "ALTA", value = "Prioridad de la petición (opcional)")


  public String getPrioridad() {
    return prioridad;
  }

  public void setPrioridad(String prioridad) {
    this.prioridad = prioridad;
  }

  public PycUpdPeticion tipoCliente(String tipoCliente) {
    this.tipoCliente = tipoCliente;
    return this;
  }

  /**
   * Tipo de cliente (opcional)
   * @return tipoCliente
  **/
  @ApiModelProperty(example = "CORPORATIVO", value = "Tipo de cliente (opcional)")


  public String getTipoCliente() {
    return tipoCliente;
  }

  public void setTipoCliente(String tipoCliente) {
    this.tipoCliente = tipoCliente;
  }

  public PycUpdPeticion codCliente(String codCliente) {
    this.codCliente = codCliente;
    return this;
  }

  /**
   * Código del cliente (opcional)
   * @return codCliente
  **/
  @ApiModelProperty(example = "CLI-001", value = "Código del cliente (opcional)")


  public String getCodCliente() {
    return codCliente;
  }

  public void setCodCliente(String codCliente) {
    this.codCliente = codCliente;
  }

  public PycUpdPeticion noPoliza(String noPoliza) {
    this.noPoliza = noPoliza;
    return this;
  }

  /**
   * Número de póliza (opcional)
   * @return noPoliza
  **/
  @ApiModelProperty(example = "POL-2025-001", value = "Número de póliza (opcional)")


  public String getNoPoliza() {
    return noPoliza;
  }

  public void setNoPoliza(String noPoliza) {
    this.noPoliza = noPoliza;
  }

  public PycUpdPeticion tipServicio(String tipServicio) {
    this.tipServicio = tipServicio;
    return this;
  }

  /**
   * Tipo de servicio (opcional)
   * @return tipServicio
  **/
  @ApiModelProperty(example = "DESARROLLO", value = "Tipo de servicio (opcional)")


  public String getTipServicio() {
    return tipServicio;
  }

  public void setTipServicio(String tipServicio) {
    this.tipServicio = tipServicio;
  }

  public PycUpdPeticion causa(String causa) {
    this.causa = causa;
    return this;
  }

  /**
   * Causa de la petición (opcional)
   * @return causa
  **/
  @ApiModelProperty(example = "MEJORA_PROCESO", value = "Causa de la petición (opcional)")


  public String getCausa() {
    return causa;
  }

  public void setCausa(String causa) {
    this.causa = causa;
  }

  public PycUpdPeticion gravedad(String gravedad) {
    this.gravedad = gravedad;
    return this;
  }

  /**
   * Gravedad de la petición (opcional)
   * @return gravedad
  **/
  @ApiModelProperty(example = "MEDIA", value = "Gravedad de la petición (opcional)")


  public String getGravedad() {
    return gravedad;
  }

  public void setGravedad(String gravedad) {
    this.gravedad = gravedad;
  }

  public PycUpdPeticion numaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
    return this;
  }

  /**
   * Número de usuario para auditoría (opcional)
   * @return numaOUsuario
  **/
  @ApiModelProperty(example = "USR001", value = "Número de usuario para auditoría (opcional)")


  public String getNumaOUsuario() {
    return numaOUsuario;
  }

  public void setNumaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUpdPeticion pycUpdPeticion = (PycUpdPeticion) o;
    return Objects.equals(this.idPeticion, pycUpdPeticion.idPeticion) &&
        Objects.equals(this.idSolUser, pycUpdPeticion.idSolUser) &&
        Objects.equals(this.nomPeticion, pycUpdPeticion.nomPeticion) &&
        Objects.equals(this.descPeticion, pycUpdPeticion.descPeticion) &&
        Objects.equals(this.idTipo, pycUpdPeticion.idTipo) &&
        Objects.equals(this.observacion, pycUpdPeticion.observacion) &&
        Objects.equals(this.codCia, pycUpdPeticion.codCia) &&
        Objects.equals(this.nomClie, pycUpdPeticion.nomClie) &&
        Objects.equals(this.telClie, pycUpdPeticion.telClie) &&
        Objects.equals(this.emailClie, pycUpdPeticion.emailClie) &&
        Objects.equals(this.origen, pycUpdPeticion.origen) &&
        Objects.equals(this.clarity, pycUpdPeticion.clarity) &&
        Objects.equals(this.prioridad, pycUpdPeticion.prioridad) &&
        Objects.equals(this.tipoCliente, pycUpdPeticion.tipoCliente) &&
        Objects.equals(this.codCliente, pycUpdPeticion.codCliente) &&
        Objects.equals(this.noPoliza, pycUpdPeticion.noPoliza) &&
        Objects.equals(this.tipServicio, pycUpdPeticion.tipServicio) &&
        Objects.equals(this.causa, pycUpdPeticion.causa) &&
        Objects.equals(this.gravedad, pycUpdPeticion.gravedad) &&
        Objects.equals(this.numaOUsuario, pycUpdPeticion.numaOUsuario);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, idSolUser, nomPeticion, descPeticion, idTipo, observacion, codCia, nomClie, telClie, emailClie, origen, clarity, prioridad, tipoCliente, codCliente, noPoliza, tipServicio, causa, gravedad, numaOUsuario);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUpdPeticion {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    idSolUser: ").append(toIndentedString(idSolUser)).append("\n");
    sb.append("    nomPeticion: ").append(toIndentedString(nomPeticion)).append("\n");
    sb.append("    descPeticion: ").append(toIndentedString(descPeticion)).append("\n");
    sb.append("    idTipo: ").append(toIndentedString(idTipo)).append("\n");
    sb.append("    observacion: ").append(toIndentedString(observacion)).append("\n");
    sb.append("    codCia: ").append(toIndentedString(codCia)).append("\n");
    sb.append("    nomClie: ").append(toIndentedString(nomClie)).append("\n");
    sb.append("    telClie: ").append(toIndentedString(telClie)).append("\n");
    sb.append("    emailClie: ").append(toIndentedString(emailClie)).append("\n");
    sb.append("    origen: ").append(toIndentedString(origen)).append("\n");
    sb.append("    clarity: ").append(toIndentedString(clarity)).append("\n");
    sb.append("    prioridad: ").append(toIndentedString(prioridad)).append("\n");
    sb.append("    tipoCliente: ").append(toIndentedString(tipoCliente)).append("\n");
    sb.append("    codCliente: ").append(toIndentedString(codCliente)).append("\n");
    sb.append("    noPoliza: ").append(toIndentedString(noPoliza)).append("\n");
    sb.append("    tipServicio: ").append(toIndentedString(tipServicio)).append("\n");
    sb.append("    causa: ").append(toIndentedString(causa)).append("\n");
    sb.append("    gravedad: ").append(toIndentedString(gravedad)).append("\n");
    sb.append("    numaOUsuario: ").append(toIndentedString(numaOUsuario)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

