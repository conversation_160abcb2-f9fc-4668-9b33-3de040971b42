package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.mapfre.tron.gt.api.model.ArchivoCargado;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CargaArchivoResponse
 */
@Validated

public class CargaArchivoResponse   {
  @JsonProperty("codigo")
  private String codigo = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  @JsonProperty("archivos")
  @Valid
  private List<ArchivoCargado> archivos = null;

  public CargaArchivoResponse codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Código de respuesta de la operación
   * @return codigo
  **/
  @ApiModelProperty(example = "200", value = "Código de respuesta de la operación")


  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public CargaArchivoResponse mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje descriptivo de la operación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Archivos cargados exitosamente", value = "Mensaje descriptivo de la operación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }

  public CargaArchivoResponse archivos(List<ArchivoCargado> archivos) {
    this.archivos = archivos;
    return this;
  }

  public CargaArchivoResponse addArchivosItem(ArchivoCargado archivosItem) {
    if (this.archivos == null) {
      this.archivos = new ArrayList<>();
    }
    this.archivos.add(archivosItem);
    return this;
  }

  /**
   * Lista de archivos cargados
   * @return archivos
  **/
  @ApiModelProperty(value = "Lista de archivos cargados")

  @Valid

  public List<ArchivoCargado> getArchivos() {
    return archivos;
  }

  public void setArchivos(List<ArchivoCargado> archivos) {
    this.archivos = archivos;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CargaArchivoResponse cargaArchivoResponse = (CargaArchivoResponse) o;
    return Objects.equals(this.codigo, cargaArchivoResponse.codigo) &&
        Objects.equals(this.mensaje, cargaArchivoResponse.mensaje) &&
        Objects.equals(this.archivos, cargaArchivoResponse.archivos);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codigo, mensaje, archivos);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CargaArchivoResponse {\n");
    
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("    archivos: ").append(toIndentedString(archivos)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

