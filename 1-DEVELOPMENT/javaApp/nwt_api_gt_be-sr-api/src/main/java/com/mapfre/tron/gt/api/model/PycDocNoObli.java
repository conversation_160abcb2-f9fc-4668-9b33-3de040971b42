package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycDocNoObli
 */
@Validated

public class PycDocNoObli   {
  @JsonProperty("idTipoPeticion")
  private Integer idTipoPeticion = null;

  @JsonProperty("idDocumento")
  private Integer idDocumento = null;

  @JsonProperty("nombreDocumento")
  private String nombreDocumento = null;

  @JsonProperty("descripcionDocumento")
  private String descripcionDocumento = null;

  public PycDocNoObli idTipoPeticion(Integer idTipoPeticion) {
    this.idTipoPeticion = idTipoPeticion;
    return this;
  }

  /**
   * Identificador del tipo de petición
   * @return idTipoPeticion
  **/
  @ApiModelProperty(example = "5", value = "Identificador del tipo de petición")


  public Integer getIdTipoPeticion() {
    return idTipoPeticion;
  }

  public void setIdTipoPeticion(Integer idTipoPeticion) {
    this.idTipoPeticion = idTipoPeticion;
  }

  public PycDocNoObli idDocumento(Integer idDocumento) {
    this.idDocumento = idDocumento;
    return this;
  }

  /**
   * Identificador del documento
   * @return idDocumento
  **/
  @ApiModelProperty(example = "12", value = "Identificador del documento")


  public Integer getIdDocumento() {
    return idDocumento;
  }

  public void setIdDocumento(Integer idDocumento) {
    this.idDocumento = idDocumento;
  }

  public PycDocNoObli nombreDocumento(String nombreDocumento) {
    this.nombreDocumento = nombreDocumento;
    return this;
  }

  /**
   * Nombre del documento
   * @return nombreDocumento
  **/
  @ApiModelProperty(example = "Carta de autorización", value = "Nombre del documento")


  public String getNombreDocumento() {
    return nombreDocumento;
  }

  public void setNombreDocumento(String nombreDocumento) {
    this.nombreDocumento = nombreDocumento;
  }

  public PycDocNoObli descripcionDocumento(String descripcionDocumento) {
    this.descripcionDocumento = descripcionDocumento;
    return this;
  }

  /**
   * Descripción detallada del documento
   * @return descripcionDocumento
  **/
  @ApiModelProperty(example = "Carta de autorización firmada por el solicitante para proceder con la gestión", value = "Descripción detallada del documento")


  public String getDescripcionDocumento() {
    return descripcionDocumento;
  }

  public void setDescripcionDocumento(String descripcionDocumento) {
    this.descripcionDocumento = descripcionDocumento;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycDocNoObli pycDocNoObli = (PycDocNoObli) o;
    return Objects.equals(this.idTipoPeticion, pycDocNoObli.idTipoPeticion) &&
        Objects.equals(this.idDocumento, pycDocNoObli.idDocumento) &&
        Objects.equals(this.nombreDocumento, pycDocNoObli.nombreDocumento) &&
        Objects.equals(this.descripcionDocumento, pycDocNoObli.descripcionDocumento);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idTipoPeticion, idDocumento, nombreDocumento, descripcionDocumento);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycDocNoObli {\n");
    
    sb.append("    idTipoPeticion: ").append(toIndentedString(idTipoPeticion)).append("\n");
    sb.append("    idDocumento: ").append(toIndentedString(idDocumento)).append("\n");
    sb.append("    nombreDocumento: ").append(toIndentedString(nombreDocumento)).append("\n");
    sb.append("    descripcionDocumento: ").append(toIndentedString(descripcionDocumento)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

