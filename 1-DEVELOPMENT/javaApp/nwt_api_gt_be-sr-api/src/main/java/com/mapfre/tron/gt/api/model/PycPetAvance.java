package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycPetAvance
 */
@Validated

public class PycPetAvance   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("nombrePeticion")
  private String nombrePeticion = null;

  @JsonProperty("fechaInicio")
  private String fechaInicio = null;

  @JsonProperty("fechaFin")
  private String fechaFin = null;

  @JsonProperty("nombreEstado")
  private String nombreEstado = null;

  @JsonProperty("idPeticionSiguiente")
  private Integer idPeticionSiguiente = null;

  @JsonProperty("nombrePeticionSiguiente")
  private String nombrePeticionSiguiente = null;

  @JsonProperty("fechaInicioSiguiente")
  private String fechaInicioSiguiente = null;

  @JsonProperty("fechaFinSiguiente")
  private String fechaFinSiguiente = null;

  @JsonProperty("idObservacion")
  private Integer idObservacion = null;

  @JsonProperty("observacion")
  private String observacion = null;

  @JsonProperty("fechaHoraObservacion")
  private String fechaHoraObservacion = null;

  @JsonProperty("nombreUsuario")
  private String nombreUsuario = null;

  @JsonProperty("generoUsuario")
  private String generoUsuario = null;

  @JsonProperty("indicadorUsuario")
  private String indicadorUsuario = null;

  @JsonProperty("urlPerfil")
  private String urlPerfil = null;

  @JsonProperty("tipoPeticion")
  private String tipoPeticion = null;

  @JsonProperty("prioridad")
  private String prioridad = null;

  @JsonProperty("solicitante")
  private String solicitante = null;

  public PycPetAvance idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador de la petición")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycPetAvance nombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
    return this;
  }

  /**
   * Nombre de la petición
   * @return nombrePeticion
  **/
  @ApiModelProperty(example = "Desarrollo de nueva funcionalidad", value = "Nombre de la petición")


  public String getNombrePeticion() {
    return nombrePeticion;
  }

  public void setNombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
  }

  public PycPetAvance fechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
    return this;
  }

  /**
   * Fecha de inicio de la petición
   * @return fechaInicio
  **/
  @ApiModelProperty(example = "15/01/2024", value = "Fecha de inicio de la petición")


  public String getFechaInicio() {
    return fechaInicio;
  }

  public void setFechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
  }

  public PycPetAvance fechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
    return this;
  }

  /**
   * Fecha de fin de la petición
   * @return fechaFin
  **/
  @ApiModelProperty(example = "20/02/2024", value = "Fecha de fin de la petición")


  public String getFechaFin() {
    return fechaFin;
  }

  public void setFechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
  }

  public PycPetAvance nombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
    return this;
  }

  /**
   * Nombre del estado actual
   * @return nombreEstado
  **/
  @ApiModelProperty(example = "En Progreso", value = "Nombre del estado actual")


  public String getNombreEstado() {
    return nombreEstado;
  }

  public void setNombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
  }

  public PycPetAvance idPeticionSiguiente(Integer idPeticionSiguiente) {
    this.idPeticionSiguiente = idPeticionSiguiente;
    return this;
  }

  /**
   * Identificador de la petición siguiente
   * @return idPeticionSiguiente
  **/
  @ApiModelProperty(example = "1002", value = "Identificador de la petición siguiente")


  public Integer getIdPeticionSiguiente() {
    return idPeticionSiguiente;
  }

  public void setIdPeticionSiguiente(Integer idPeticionSiguiente) {
    this.idPeticionSiguiente = idPeticionSiguiente;
  }

  public PycPetAvance nombrePeticionSiguiente(String nombrePeticionSiguiente) {
    this.nombrePeticionSiguiente = nombrePeticionSiguiente;
    return this;
  }

  /**
   * Nombre de la petición siguiente
   * @return nombrePeticionSiguiente
  **/
  @ApiModelProperty(example = "Pruebas de funcionalidad", value = "Nombre de la petición siguiente")


  public String getNombrePeticionSiguiente() {
    return nombrePeticionSiguiente;
  }

  public void setNombrePeticionSiguiente(String nombrePeticionSiguiente) {
    this.nombrePeticionSiguiente = nombrePeticionSiguiente;
  }

  public PycPetAvance fechaInicioSiguiente(String fechaInicioSiguiente) {
    this.fechaInicioSiguiente = fechaInicioSiguiente;
    return this;
  }

  /**
   * Fecha de inicio de la petición siguiente
   * @return fechaInicioSiguiente
  **/
  @ApiModelProperty(example = "21/02/2024", value = "Fecha de inicio de la petición siguiente")


  public String getFechaInicioSiguiente() {
    return fechaInicioSiguiente;
  }

  public void setFechaInicioSiguiente(String fechaInicioSiguiente) {
    this.fechaInicioSiguiente = fechaInicioSiguiente;
  }

  public PycPetAvance fechaFinSiguiente(String fechaFinSiguiente) {
    this.fechaFinSiguiente = fechaFinSiguiente;
    return this;
  }

  /**
   * Fecha de fin de la petición siguiente
   * @return fechaFinSiguiente
  **/
  @ApiModelProperty(example = "28/02/2024", value = "Fecha de fin de la petición siguiente")


  public String getFechaFinSiguiente() {
    return fechaFinSiguiente;
  }

  public void setFechaFinSiguiente(String fechaFinSiguiente) {
    this.fechaFinSiguiente = fechaFinSiguiente;
  }

  public PycPetAvance idObservacion(Integer idObservacion) {
    this.idObservacion = idObservacion;
    return this;
  }

  /**
   * Identificador de la observación
   * @return idObservacion
  **/
  @ApiModelProperty(example = "123", value = "Identificador de la observación")


  public Integer getIdObservacion() {
    return idObservacion;
  }

  public void setIdObservacion(Integer idObservacion) {
    this.idObservacion = idObservacion;
  }

  public PycPetAvance observacion(String observacion) {
    this.observacion = observacion;
    return this;
  }

  /**
   * Texto de la observación
   * @return observacion
  **/
  @ApiModelProperty(example = "Avance del 75% completado", value = "Texto de la observación")


  public String getObservacion() {
    return observacion;
  }

  public void setObservacion(String observacion) {
    this.observacion = observacion;
  }

  public PycPetAvance fechaHoraObservacion(String fechaHoraObservacion) {
    this.fechaHoraObservacion = fechaHoraObservacion;
    return this;
  }

  /**
   * Fecha y hora de la observación (formato dd/MM/yyyy HH24:MI)
   * @return fechaHoraObservacion
  **/
  @ApiModelProperty(example = "15/01/2024 14:30", value = "Fecha y hora de la observación (formato dd/MM/yyyy HH24:MI)")


  public String getFechaHoraObservacion() {
    return fechaHoraObservacion;
  }

  public void setFechaHoraObservacion(String fechaHoraObservacion) {
    this.fechaHoraObservacion = fechaHoraObservacion;
  }

  public PycPetAvance nombreUsuario(String nombreUsuario) {
    this.nombreUsuario = nombreUsuario;
    return this;
  }

  /**
   * Nombre completo del usuario que hizo la observación
   * @return nombreUsuario
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre completo del usuario que hizo la observación")


  public String getNombreUsuario() {
    return nombreUsuario;
  }

  public void setNombreUsuario(String nombreUsuario) {
    this.nombreUsuario = nombreUsuario;
  }

  public PycPetAvance generoUsuario(String generoUsuario) {
    this.generoUsuario = generoUsuario;
    return this;
  }

  /**
   * Género del usuario (M/F)
   * @return generoUsuario
  **/
  @ApiModelProperty(example = "M", value = "Género del usuario (M/F)")


  public String getGeneroUsuario() {
    return generoUsuario;
  }

  public void setGeneroUsuario(String generoUsuario) {
    this.generoUsuario = generoUsuario;
  }

  public PycPetAvance indicadorUsuario(String indicadorUsuario) {
    this.indicadorUsuario = indicadorUsuario;
    return this;
  }

  /**
   * Indicador de posición del usuario ('right'/'left')
   * @return indicadorUsuario
  **/
  @ApiModelProperty(example = "left", value = "Indicador de posición del usuario ('right'/'left')")


  public String getIndicadorUsuario() {
    return indicadorUsuario;
  }

  public void setIndicadorUsuario(String indicadorUsuario) {
    this.indicadorUsuario = indicadorUsuario;
  }

  public PycPetAvance urlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
    return this;
  }

  /**
   * URL del perfil del usuario o imagen por defecto
   * @return urlPerfil
  **/
  @ApiModelProperty(example = "/images/profile/user_male.png", value = "URL del perfil del usuario o imagen por defecto")


  public String getUrlPerfil() {
    return urlPerfil;
  }

  public void setUrlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
  }

  public PycPetAvance tipoPeticion(String tipoPeticion) {
    this.tipoPeticion = tipoPeticion;
    return this;
  }

  /**
   * Tipo de petición
   * @return tipoPeticion
  **/
  @ApiModelProperty(example = "Desarrollo", value = "Tipo de petición")


  public String getTipoPeticion() {
    return tipoPeticion;
  }

  public void setTipoPeticion(String tipoPeticion) {
    this.tipoPeticion = tipoPeticion;
  }

  public PycPetAvance prioridad(String prioridad) {
    this.prioridad = prioridad;
    return this;
  }

  /**
   * Descripción de la prioridad
   * @return prioridad
  **/
  @ApiModelProperty(example = "Alta", value = "Descripción de la prioridad")


  public String getPrioridad() {
    return prioridad;
  }

  public void setPrioridad(String prioridad) {
    this.prioridad = prioridad;
  }

  public PycPetAvance solicitante(String solicitante) {
    this.solicitante = solicitante;
    return this;
  }

  /**
   * Nombre completo del usuario solicitante
   * @return solicitante
  **/
  @ApiModelProperty(example = "María García", value = "Nombre completo del usuario solicitante")


  public String getSolicitante() {
    return solicitante;
  }

  public void setSolicitante(String solicitante) {
    this.solicitante = solicitante;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycPetAvance pycPetAvance = (PycPetAvance) o;
    return Objects.equals(this.idPeticion, pycPetAvance.idPeticion) &&
        Objects.equals(this.nombrePeticion, pycPetAvance.nombrePeticion) &&
        Objects.equals(this.fechaInicio, pycPetAvance.fechaInicio) &&
        Objects.equals(this.fechaFin, pycPetAvance.fechaFin) &&
        Objects.equals(this.nombreEstado, pycPetAvance.nombreEstado) &&
        Objects.equals(this.idPeticionSiguiente, pycPetAvance.idPeticionSiguiente) &&
        Objects.equals(this.nombrePeticionSiguiente, pycPetAvance.nombrePeticionSiguiente) &&
        Objects.equals(this.fechaInicioSiguiente, pycPetAvance.fechaInicioSiguiente) &&
        Objects.equals(this.fechaFinSiguiente, pycPetAvance.fechaFinSiguiente) &&
        Objects.equals(this.idObservacion, pycPetAvance.idObservacion) &&
        Objects.equals(this.observacion, pycPetAvance.observacion) &&
        Objects.equals(this.fechaHoraObservacion, pycPetAvance.fechaHoraObservacion) &&
        Objects.equals(this.nombreUsuario, pycPetAvance.nombreUsuario) &&
        Objects.equals(this.generoUsuario, pycPetAvance.generoUsuario) &&
        Objects.equals(this.indicadorUsuario, pycPetAvance.indicadorUsuario) &&
        Objects.equals(this.urlPerfil, pycPetAvance.urlPerfil) &&
        Objects.equals(this.tipoPeticion, pycPetAvance.tipoPeticion) &&
        Objects.equals(this.prioridad, pycPetAvance.prioridad) &&
        Objects.equals(this.solicitante, pycPetAvance.solicitante);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, nombrePeticion, fechaInicio, fechaFin, nombreEstado, idPeticionSiguiente, nombrePeticionSiguiente, fechaInicioSiguiente, fechaFinSiguiente, idObservacion, observacion, fechaHoraObservacion, nombreUsuario, generoUsuario, indicadorUsuario, urlPerfil, tipoPeticion, prioridad, solicitante);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycPetAvance {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    nombrePeticion: ").append(toIndentedString(nombrePeticion)).append("\n");
    sb.append("    fechaInicio: ").append(toIndentedString(fechaInicio)).append("\n");
    sb.append("    fechaFin: ").append(toIndentedString(fechaFin)).append("\n");
    sb.append("    nombreEstado: ").append(toIndentedString(nombreEstado)).append("\n");
    sb.append("    idPeticionSiguiente: ").append(toIndentedString(idPeticionSiguiente)).append("\n");
    sb.append("    nombrePeticionSiguiente: ").append(toIndentedString(nombrePeticionSiguiente)).append("\n");
    sb.append("    fechaInicioSiguiente: ").append(toIndentedString(fechaInicioSiguiente)).append("\n");
    sb.append("    fechaFinSiguiente: ").append(toIndentedString(fechaFinSiguiente)).append("\n");
    sb.append("    idObservacion: ").append(toIndentedString(idObservacion)).append("\n");
    sb.append("    observacion: ").append(toIndentedString(observacion)).append("\n");
    sb.append("    fechaHoraObservacion: ").append(toIndentedString(fechaHoraObservacion)).append("\n");
    sb.append("    nombreUsuario: ").append(toIndentedString(nombreUsuario)).append("\n");
    sb.append("    generoUsuario: ").append(toIndentedString(generoUsuario)).append("\n");
    sb.append("    indicadorUsuario: ").append(toIndentedString(indicadorUsuario)).append("\n");
    sb.append("    urlPerfil: ").append(toIndentedString(urlPerfil)).append("\n");
    sb.append("    tipoPeticion: ").append(toIndentedString(tipoPeticion)).append("\n");
    sb.append("    prioridad: ").append(toIndentedString(prioridad)).append("\n");
    sb.append("    solicitante: ").append(toIndentedString(solicitante)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

