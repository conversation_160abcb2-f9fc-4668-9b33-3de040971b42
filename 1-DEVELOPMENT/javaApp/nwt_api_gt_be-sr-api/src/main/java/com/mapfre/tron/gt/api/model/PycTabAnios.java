package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycTabAnios
 */
@Validated

public class PycTabAnios   {
  @JsonProperty("anios")
  private String anios = null;

  public PycTabAnios anios(String anios) {
    this.anios = anios;
    return this;
  }

  /**
   * Año de las peticiones en formato YYYY
   * @return anios
  **/
  @ApiModelProperty(example = "2024", value = "Año de las peticiones en formato YYYY")


  public String getAnios() {
    return anios;
  }

  public void setAnios(String anios) {
    this.anios = anios;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycTabAnios pycTabAnios = (PycTabAnios) o;
    return Objects.equals(this.anios, pycTabAnios.anios);
  }

  @Override
  public int hashCode() {
    return Objects.hash(anios);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycTabAnios {\n");
    
    sb.append("    anios: ").append(toIndentedString(anios)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

