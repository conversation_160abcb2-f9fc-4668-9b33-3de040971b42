package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycPetAvanceSig
 */
@Validated

public class PycPetAvanceSig   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("nombrePeticion")
  private String nombrePeticion = null;

  @JsonProperty("fechaInicio")
  private String fechaInicio = null;

  @JsonProperty("fechaFin")
  private String fechaFin = null;

  @JsonProperty("nombreEstado")
  private String nombreEstado = null;

  @JsonProperty("idPeticionSiguiente")
  private Integer idPeticionSiguiente = null;

  @JsonProperty("nombrePeticionSiguiente")
  private String nombrePeticionSiguiente = null;

  @JsonProperty("fechaInicioSiguiente")
  private String fechaInicioSiguiente = null;

  @JsonProperty("fechaFinSiguiente")
  private String fechaFinSiguiente = null;

  @JsonProperty("nombreEstadoSiguiente")
  private String nombreEstadoSiguiente = null;

  public PycPetAvanceSig idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición principal
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador de la petición principal")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycPetAvanceSig nombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
    return this;
  }

  /**
   * Nombre de la petición principal
   * @return nombrePeticion
  **/
  @ApiModelProperty(example = "Solicitud de cambio de datos", value = "Nombre de la petición principal")


  public String getNombrePeticion() {
    return nombrePeticion;
  }

  public void setNombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
  }

  public PycPetAvanceSig fechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
    return this;
  }

  /**
   * Fecha de inicio de la petición principal
   * @return fechaInicio
  **/
  @ApiModelProperty(example = "2025-01-15", value = "Fecha de inicio de la petición principal")


  public String getFechaInicio() {
    return fechaInicio;
  }

  public void setFechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
  }

  public PycPetAvanceSig fechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
    return this;
  }

  /**
   * Fecha de fin de la petición principal
   * @return fechaFin
  **/
  @ApiModelProperty(example = "2025-01-20", value = "Fecha de fin de la petición principal")


  public String getFechaFin() {
    return fechaFin;
  }

  public void setFechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
  }

  public PycPetAvanceSig nombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
    return this;
  }

  /**
   * Nombre del estado de la petición principal
   * @return nombreEstado
  **/
  @ApiModelProperty(example = "En Proceso", value = "Nombre del estado de la petición principal")


  public String getNombreEstado() {
    return nombreEstado;
  }

  public void setNombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
  }

  public PycPetAvanceSig idPeticionSiguiente(Integer idPeticionSiguiente) {
    this.idPeticionSiguiente = idPeticionSiguiente;
    return this;
  }

  /**
   * Identificador de la petición siguiente
   * @return idPeticionSiguiente
  **/
  @ApiModelProperty(example = "1002", value = "Identificador de la petición siguiente")


  public Integer getIdPeticionSiguiente() {
    return idPeticionSiguiente;
  }

  public void setIdPeticionSiguiente(Integer idPeticionSiguiente) {
    this.idPeticionSiguiente = idPeticionSiguiente;
  }

  public PycPetAvanceSig nombrePeticionSiguiente(String nombrePeticionSiguiente) {
    this.nombrePeticionSiguiente = nombrePeticionSiguiente;
    return this;
  }

  /**
   * Nombre de la petición siguiente
   * @return nombrePeticionSiguiente
  **/
  @ApiModelProperty(example = "Validación de cambio de datos", value = "Nombre de la petición siguiente")


  public String getNombrePeticionSiguiente() {
    return nombrePeticionSiguiente;
  }

  public void setNombrePeticionSiguiente(String nombrePeticionSiguiente) {
    this.nombrePeticionSiguiente = nombrePeticionSiguiente;
  }

  public PycPetAvanceSig fechaInicioSiguiente(String fechaInicioSiguiente) {
    this.fechaInicioSiguiente = fechaInicioSiguiente;
    return this;
  }

  /**
   * Fecha de inicio de la petición siguiente
   * @return fechaInicioSiguiente
  **/
  @ApiModelProperty(example = "2025-01-21", value = "Fecha de inicio de la petición siguiente")


  public String getFechaInicioSiguiente() {
    return fechaInicioSiguiente;
  }

  public void setFechaInicioSiguiente(String fechaInicioSiguiente) {
    this.fechaInicioSiguiente = fechaInicioSiguiente;
  }

  public PycPetAvanceSig fechaFinSiguiente(String fechaFinSiguiente) {
    this.fechaFinSiguiente = fechaFinSiguiente;
    return this;
  }

  /**
   * Fecha de fin de la petición siguiente
   * @return fechaFinSiguiente
  **/
  @ApiModelProperty(example = "2025-01-25", value = "Fecha de fin de la petición siguiente")


  public String getFechaFinSiguiente() {
    return fechaFinSiguiente;
  }

  public void setFechaFinSiguiente(String fechaFinSiguiente) {
    this.fechaFinSiguiente = fechaFinSiguiente;
  }

  public PycPetAvanceSig nombreEstadoSiguiente(String nombreEstadoSiguiente) {
    this.nombreEstadoSiguiente = nombreEstadoSiguiente;
    return this;
  }

  /**
   * Nombre del estado de la petición siguiente
   * @return nombreEstadoSiguiente
  **/
  @ApiModelProperty(example = "Pendiente", value = "Nombre del estado de la petición siguiente")


  public String getNombreEstadoSiguiente() {
    return nombreEstadoSiguiente;
  }

  public void setNombreEstadoSiguiente(String nombreEstadoSiguiente) {
    this.nombreEstadoSiguiente = nombreEstadoSiguiente;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycPetAvanceSig pycPetAvanceSig = (PycPetAvanceSig) o;
    return Objects.equals(this.idPeticion, pycPetAvanceSig.idPeticion) &&
        Objects.equals(this.nombrePeticion, pycPetAvanceSig.nombrePeticion) &&
        Objects.equals(this.fechaInicio, pycPetAvanceSig.fechaInicio) &&
        Objects.equals(this.fechaFin, pycPetAvanceSig.fechaFin) &&
        Objects.equals(this.nombreEstado, pycPetAvanceSig.nombreEstado) &&
        Objects.equals(this.idPeticionSiguiente, pycPetAvanceSig.idPeticionSiguiente) &&
        Objects.equals(this.nombrePeticionSiguiente, pycPetAvanceSig.nombrePeticionSiguiente) &&
        Objects.equals(this.fechaInicioSiguiente, pycPetAvanceSig.fechaInicioSiguiente) &&
        Objects.equals(this.fechaFinSiguiente, pycPetAvanceSig.fechaFinSiguiente) &&
        Objects.equals(this.nombreEstadoSiguiente, pycPetAvanceSig.nombreEstadoSiguiente);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, nombrePeticion, fechaInicio, fechaFin, nombreEstado, idPeticionSiguiente, nombrePeticionSiguiente, fechaInicioSiguiente, fechaFinSiguiente, nombreEstadoSiguiente);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycPetAvanceSig {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    nombrePeticion: ").append(toIndentedString(nombrePeticion)).append("\n");
    sb.append("    fechaInicio: ").append(toIndentedString(fechaInicio)).append("\n");
    sb.append("    fechaFin: ").append(toIndentedString(fechaFin)).append("\n");
    sb.append("    nombreEstado: ").append(toIndentedString(nombreEstado)).append("\n");
    sb.append("    idPeticionSiguiente: ").append(toIndentedString(idPeticionSiguiente)).append("\n");
    sb.append("    nombrePeticionSiguiente: ").append(toIndentedString(nombrePeticionSiguiente)).append("\n");
    sb.append("    fechaInicioSiguiente: ").append(toIndentedString(fechaInicioSiguiente)).append("\n");
    sb.append("    fechaFinSiguiente: ").append(toIndentedString(fechaFinSiguiente)).append("\n");
    sb.append("    nombreEstadoSiguiente: ").append(toIndentedString(nombreEstadoSiguiente)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

