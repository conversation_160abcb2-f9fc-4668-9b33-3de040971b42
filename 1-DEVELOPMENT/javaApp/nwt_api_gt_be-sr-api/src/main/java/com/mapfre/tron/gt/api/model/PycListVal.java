package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycListVal
 */
@Validated

public class PycListVal   {
  @JsonProperty("valor")
  private String valor = null;

  @JsonProperty("descripcion")
  private String descripcion = null;

  public PycListVal valor(String valor) {
    this.valor = valor;
    return this;
  }

  /**
   * Valor del elemento de la lista
   * @return valor
  **/
  @ApiModelProperty(example = "001", value = "Valor del elemento de la lista")


  public String getValor() {
    return valor;
  }

  public void setValor(String valor) {
    this.valor = valor;
  }

  public PycListVal descripcion(String descripcion) {
    this.descripcion = descripcion;
    return this;
  }

  /**
   * Descripción del valor
   * @return descripcion
  **/
  @ApiModelProperty(example = "Opción número uno", value = "Descripción del valor")


  public String getDescripcion() {
    return descripcion;
  }

  public void setDescripcion(String descripcion) {
    this.descripcion = descripcion;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycListVal pycListVal = (PycListVal) o;
    return Objects.equals(this.valor, pycListVal.valor) &&
        Objects.equals(this.descripcion, pycListVal.descripcion);
  }

  @Override
  public int hashCode() {
    return Objects.hash(valor, descripcion);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycListVal {\n");
    
    sb.append("    valor: ").append(toIndentedString(valor)).append("\n");
    sb.append("    descripcion: ").append(toIndentedString(descripcion)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

