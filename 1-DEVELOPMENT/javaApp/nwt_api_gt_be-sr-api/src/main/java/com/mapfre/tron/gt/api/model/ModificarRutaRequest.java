package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ModificarRutaRequest
 */
@Validated

public class ModificarRutaRequest   {
  @JsonProperty("idUsuarioCrea")
  private Integer idUsuarioCrea = null;

  @JsonProperty("idUsuarioAsignado")
  private Integer idUsuarioAsignado = null;

  @JsonProperty("idRuta")
  private Integer idRuta = null;

  public ModificarRutaRequest idUsuarioCrea(Integer idUsuarioCrea) {
    this.idUsuarioCrea = idUsuarioCrea;
    return this;
  }

  /**
   * id de usuario que crea la ruta
   * @return idUsuarioCrea
  **/
  @ApiModelProperty(example = "1", required = true, value = "id de usuario que crea la ruta")
  @NotNull


  public Integer getIdUsuarioCrea() {
    return idUsuarioCrea;
  }

  public void setIdUsuarioCrea(Integer idUsuarioCrea) {
    this.idUsuarioCrea = idUsuarioCrea;
  }

  public ModificarRutaRequest idUsuarioAsignado(Integer idUsuarioAsignado) {
    this.idUsuarioAsignado = idUsuarioAsignado;
    return this;
  }

  /**
   * id del usuario asigado a la ruta
   * @return idUsuarioAsignado
  **/
  @ApiModelProperty(example = "1", required = true, value = "id del usuario asigado a la ruta")
  @NotNull


  public Integer getIdUsuarioAsignado() {
    return idUsuarioAsignado;
  }

  public void setIdUsuarioAsignado(Integer idUsuarioAsignado) {
    this.idUsuarioAsignado = idUsuarioAsignado;
  }

  public ModificarRutaRequest idRuta(Integer idRuta) {
    this.idRuta = idRuta;
    return this;
  }

  /**
   * id de ruta que se desea modificar
   * @return idRuta
  **/
  @ApiModelProperty(example = "1", required = true, value = "id de ruta que se desea modificar")
  @NotNull


  public Integer getIdRuta() {
    return idRuta;
  }

  public void setIdRuta(Integer idRuta) {
    this.idRuta = idRuta;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ModificarRutaRequest modificarRutaRequest = (ModificarRutaRequest) o;
    return Objects.equals(this.idUsuarioCrea, modificarRutaRequest.idUsuarioCrea) &&
        Objects.equals(this.idUsuarioAsignado, modificarRutaRequest.idUsuarioAsignado) &&
        Objects.equals(this.idRuta, modificarRutaRequest.idRuta);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuarioCrea, idUsuarioAsignado, idRuta);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ModificarRutaRequest {\n");
    
    sb.append("    idUsuarioCrea: ").append(toIndentedString(idUsuarioCrea)).append("\n");
    sb.append("    idUsuarioAsignado: ").append(toIndentedString(idUsuarioAsignado)).append("\n");
    sb.append("    idRuta: ").append(toIndentedString(idRuta)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

