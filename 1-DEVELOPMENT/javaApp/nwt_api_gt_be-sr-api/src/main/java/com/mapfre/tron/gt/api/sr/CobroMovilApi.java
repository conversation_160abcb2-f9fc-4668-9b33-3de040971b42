/**
 * NOTE: This class is auto generated by the swagger code generator program (2.4.14).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.mapfre.tron.gt.api.sr;

import com.mapfre.tron.gt.api.model.AccionesPorRolesResponse;
import com.mapfre.tron.gt.api.model.AutenticarUsuarioResponse;
import com.mapfre.tron.gt.api.model.AvisoReciboRutaRequest;
import com.mapfre.tron.gt.api.model.AvisoReciboRutaResponse;
import com.mapfre.tron.gt.api.model.CajeroUsuarioResponse;
import com.mapfre.tron.gt.api.model.CobradoresResponse;
import com.mapfre.tron.gt.api.model.CobroResponse;
import com.mapfre.tron.gt.api.model.EntidadResponse;
import com.mapfre.tron.gt.api.model.Error;
import com.mapfre.tron.gt.api.model.InfoFirmaReciboResponse;
import com.mapfre.tron.gt.api.model.InfoPromedioResponse;
import com.mapfre.tron.gt.api.model.InfoRecibosPolizaRutaCobradosResponse;
import com.mapfre.tron.gt.api.model.InfoRecibosPolizaRutaResponse;
import com.mapfre.tron.gt.api.model.InfoRutaCierreCajaResponse;
import com.mapfre.tron.gt.api.model.InfoRutaResponse;
import com.mapfre.tron.gt.api.model.InsertarDetalleRuta2Request;
import com.mapfre.tron.gt.api.model.InsertarDetalleRuta2Response;
import com.mapfre.tron.gt.api.model.InsertarDetalleRutaDiarioRequest;
import com.mapfre.tron.gt.api.model.InsertarDetalleRutaDiarioResponse;
import com.mapfre.tron.gt.api.model.LlenarDetalleCierreCajaResponse;
import com.mapfre.tron.gt.api.model.LocalizacionPagosResponse;
import com.mapfre.tron.gt.api.model.ModRutaPolizaFacturasResponse;
import com.mapfre.tron.gt.api.model.ModRutaPolizaResponse;
import com.mapfre.tron.gt.api.model.ModificarRutaRequest;
import com.mapfre.tron.gt.api.model.ModificarRutaResponse;
import com.mapfre.tron.gt.api.model.ObtenerRolesUsuarioResponse;
import com.mapfre.tron.gt.api.model.ReciboRutaRequest;
import com.mapfre.tron.gt.api.model.RutaDiaria;
import com.mapfre.tron.gt.api.model.RutaDiariaRequest;
import com.mapfre.tron.gt.api.model.RutaLocalizacionResponse;
import com.mapfre.tron.gt.api.model.RutasCobradasResponse;
import com.mapfre.tron.gt.api.model.TipoMedioPagoRequest;
import com.mapfre.tron.gt.api.model.TipoMedioPagoResponse;
import com.mapfre.tron.gt.api.model.TipoPagoResponse;
import com.mapfre.tron.gt.api.model.UpdateReciboRutaResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Api(value = "cobroMovil", description = "the cobroMovil API")
@RequestMapping(value = "/newtron/api")
public interface CobroMovilApi {

    Logger log = LoggerFactory.getLogger(CobroMovilApi.class);

    default Optional<ObjectMapper> getObjectMapper() {
        return Optional.empty();
    }

    default Optional<HttpServletRequest> getRequest() {
        return Optional.empty();
    }

    default Optional<String> getAcceptHeader() {
        return getRequest().map(r -> r.getHeader("Accept"));
    }

    @ApiOperation(value = "Autenticar usuario", nickname = "autenticarUsuario", notes = "Autentica un usuario mediante la función Autenticar_Usuario", response = AutenticarUsuarioResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "CobroMovil", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Autenticación exitosa", response = AutenticarUsuarioResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/autenticar-usuario",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<AutenticarUsuarioResponse> autenticarUsuario(@NotNull @ApiParam(value = "Nombre de usuario en doble base64", required = true) @Valid @RequestParam(value = "usuario", required = true) String usuario,@ApiParam(value = "Clave del usuario en doble base64") @Valid @RequestParam(value = "clave", required = false) String clave) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"200\",  \"usuario\" : {    \"apellidos\" : \"Pérez García\",    \"estado\" : \"A\",    \"tipo\" : \"ADMINISTRADOR\",    \"codigo\" : \"ADM\",    \"imgPerfil\" : \"/images/perfil/admin.jpg\",    \"cobrador\" : \"S\",    \"idUsuario\" : 123,    \"nombreUnicoUsuario\" : \"admin\",    \"idTipo\" : 1,    \"esInterno\" : 1,    \"nombres\" : \"Juan Carlos\"  },  \"mensaje\" : \"Usuario autenticado exitosamente\"}", AutenticarUsuarioResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Crea nueva ruta de cobro", nickname = "crearNuevaRuta", notes = "Crea nueva ruta de cobro", response = RutaDiaria.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "CobroMovil", })
    @ApiResponses(value = { 
        @ApiResponse(code = 201, message = "Operación exitosa", response = RutaDiaria.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/insertar-ruta",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<RutaDiaria> crearNuevaRuta(@ApiParam(value = "Datos de usurios para crear nueva ruta." ,required=true )  @Valid @RequestBody RutaDiariaRequest body) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"201\",  \"mensaje\" : \"Operación exitosa\",  \"idRutaCreada\" : 1}", RutaDiaria.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Crea nueva ruta de cobro para el dia en curso", nickname = "crearNuevaRutaDiaria", notes = "Crea nueva ruta de cobro unica para el dia en curso", response = RutaDiaria.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "CobroMovil", })
    @ApiResponses(value = { 
        @ApiResponse(code = 201, message = "Operación exitosa", response = RutaDiaria.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/insertar-ruta-diaria",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<RutaDiaria> crearNuevaRutaDiaria(@ApiParam(value = "Datos de usurios para crear nueva ruta diaria" ,required=true )  @Valid @RequestBody RutaDiariaRequest body) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"201\",  \"mensaje\" : \"Operación exitosa\",  \"idRutaCreada\" : 1}", RutaDiaria.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "datos de cobradores", nickname = "getCobradores", notes = "Obtiene lista de cobradores", response = CobradoresResponse.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={  })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = CobradoresResponse.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/getCobradores",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<CobradoresResponse>> getCobradores() {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idUsuario\" : 4,  \"nombre\" : \"JUAN PEREZ\"}, {  \"idUsuario\" : 4,  \"nombre\" : \"JUAN PEREZ\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "datos de firma de recibo", nickname = "getInfoFirmaRecibo", notes = "Obtiene información de firma de recibo", response = InfoFirmaReciboResponse.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={  })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = InfoFirmaReciboResponse.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/getInfoFirmaRecibo",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<InfoFirmaReciboResponse>> getInfoFirmaRecibo(@NotNull @ApiParam(value = "Id de ruta", required = true) @Valid @RequestParam(value = "idRuta", required = true) String idRuta,@NotNull @ApiParam(value = "Numero de recibo", required = true) @Valid @RequestParam(value = "recibo", required = true) String recibo) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"cuota\" : \"0/1\",  \"idDetalle\" : 4,  \"sistema\" : \"A\",  \"fechaReciCobro\" : \"2020-11-30 00:00:00\",  \"asegurado\" : \"Grupo H\",  \"fechaReciVencimiento\" : \"2020-11-30 00:00:00\",  \"idUsuarioCobra\" : 1,  \"codPol\" : \"SC\",  \"imgFirma\" : \"iVBORw0KGgoAAAANSUhEUgAAAA...\",  \"total\" : \"4337.09\",  \"fechaModi\" : \"2020-11-30 00:00:00\",  \"correo\" : \"<EMAIL>\",  \"moneda\" : \"Q\",  \"orden\" : 1,  \"idePol\" : \"AUOL\",  \"dirCobLatitud\" : \"99993\",  \"dirCobLongitud\" : \"12\",  \"recibo\" : \"443500\",  \"direccion\" : \"ciudad de Guatemala\",  \"comentario\" : \"comentario\",  \"fechaRecordatorio\" : \"2020-11-30 00:00:00\",  \"idEstado\" : 4,  \"fechaCrea\" : \"2020-12-22 15:42:45\",  \"idRuta\" : 0,  \"numCert\" : \"1\",  \"numPol\" : \"79628\"}, {  \"cuota\" : \"0/1\",  \"idDetalle\" : 4,  \"sistema\" : \"A\",  \"fechaReciCobro\" : \"2020-11-30 00:00:00\",  \"asegurado\" : \"Grupo H\",  \"fechaReciVencimiento\" : \"2020-11-30 00:00:00\",  \"idUsuarioCobra\" : 1,  \"codPol\" : \"SC\",  \"imgFirma\" : \"iVBORw0KGgoAAAANSUhEUgAAAA...\",  \"total\" : \"4337.09\",  \"fechaModi\" : \"2020-11-30 00:00:00\",  \"correo\" : \"<EMAIL>\",  \"moneda\" : \"Q\",  \"orden\" : 1,  \"idePol\" : \"AUOL\",  \"dirCobLatitud\" : \"99993\",  \"dirCobLongitud\" : \"12\",  \"recibo\" : \"443500\",  \"direccion\" : \"ciudad de Guatemala\",  \"comentario\" : \"comentario\",  \"fechaRecordatorio\" : \"2020-11-30 00:00:00\",  \"idEstado\" : 4,  \"fechaCrea\" : \"2020-12-22 15:42:45\",  \"idRuta\" : 0,  \"numCert\" : \"1\",  \"numPol\" : \"79628\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "datos de recibos de rutas", nickname = "getInfoRecibosPolizaRuta", notes = "Obtiene información de polizas de la ruta", response = InfoRecibosPolizaRutaResponse.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={  })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = InfoRecibosPolizaRutaResponse.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/getInfoRecibosPolizaRuta",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<InfoRecibosPolizaRutaResponse>> getInfoRecibosPolizaRuta(@NotNull @ApiParam(value = "ID de la ruta", required = true) @Valid @RequestParam(value = "idRuta", required = true) Integer idRuta,@NotNull @ApiParam(value = "Id de poliza", required = true) @Valid @RequestParam(value = "idePol", required = true) String idePol,@NotNull @ApiParam(value = "Numero de certificacion", required = true) @Valid @RequestParam(value = "numCertis", required = true) String numCertis) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"nombrePagador\" : \"S. A.\",  \"estado\" : \"COB\",  \"idPol\" : \"1594513\",  \"sistema\" : \"T\",  \"numeroCuota\" : \"5\",  \"nomEstado\" : \"COBRADO\",  \"direccion\" : \"Ciudad de Guatemala\",  \"totalRequerimiento\" : \"579.76\",  \"certificado\" : \"1\",  \"vencimientoRequerimiento\" : \"25/12/2020\",  \"codPol\" : \"AUOL\",  \"numeroRequerimiento\" : \"94644\",  \"moneda\" : \"Q\",  \"idRuta\" : 4,  \"numPol\" : \"17083\",  \"esAviso\" : \"S\"}, {  \"nombrePagador\" : \"S. A.\",  \"estado\" : \"COB\",  \"idPol\" : \"1594513\",  \"sistema\" : \"T\",  \"numeroCuota\" : \"5\",  \"nomEstado\" : \"COBRADO\",  \"direccion\" : \"Ciudad de Guatemala\",  \"totalRequerimiento\" : \"579.76\",  \"certificado\" : \"1\",  \"vencimientoRequerimiento\" : \"25/12/2020\",  \"codPol\" : \"AUOL\",  \"numeroRequerimiento\" : \"94644\",  \"moneda\" : \"Q\",  \"idRuta\" : 4,  \"numPol\" : \"17083\",  \"esAviso\" : \"S\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "datos de recibos de rutas cobradas", nickname = "getInfoRecibosPolizaRutaCobrados", notes = "Obtiene información de polizas y rutas cobradas", response = InfoRecibosPolizaRutaCobradosResponse.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={  })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = InfoRecibosPolizaRutaCobradosResponse.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/getInfoRecibosPolizaRutaCobrados",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<InfoRecibosPolizaRutaCobradosResponse>> getInfoRecibosPolizaRutaCobrados(@NotNull @ApiParam(value = "ID de la ruta", required = true) @Valid @RequestParam(value = "idRuta", required = true) Integer idRuta,@ApiParam(value = "Id de poliza") @Valid @RequestParam(value = "idePol", required = false) String idePol,@ApiParam(value = "Numero de certificacion") @Valid @RequestParam(value = "numCertis", required = false) String numCertis) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"nombrePagador\" : \"S. A.\",  \"estado\" : \"COB\",  \"idPol\" : \"1594513\",  \"sistema\" : \"T\",  \"numeroCuota\" : \"5\",  \"nomEstado\" : \"COBRADO\",  \"totalRequerimiento\" : \"579.76\",  \"certificado\" : \"1\",  \"vencimientoRequerimiento\" : \"25/12/2020\",  \"codPol\" : \"AUOL\",  \"numeroRequerimiento\" : \"94644\",  \"moneda\" : \"Q\",  \"idRuta\" : 4,  \"numPol\" : \"17083\",  \"esAviso\" : \"N\"}, {  \"nombrePagador\" : \"S. A.\",  \"estado\" : \"COB\",  \"idPol\" : \"1594513\",  \"sistema\" : \"T\",  \"numeroCuota\" : \"5\",  \"nomEstado\" : \"COBRADO\",  \"totalRequerimiento\" : \"579.76\",  \"certificado\" : \"1\",  \"vencimientoRequerimiento\" : \"25/12/2020\",  \"codPol\" : \"AUOL\",  \"numeroRequerimiento\" : \"94644\",  \"moneda\" : \"Q\",  \"idRuta\" : 4,  \"numPol\" : \"17083\",  \"esAviso\" : \"N\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "datos de rutas", nickname = "getInfoRuta", notes = "Obtiene información de rutas", response = InfoRutaResponse.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={  })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = InfoRutaResponse.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/getInfoRuta",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<InfoRutaResponse>> getInfoRuta(@NotNull @ApiParam(value = "ID del usuario", required = true) @Valid @RequestParam(value = "idUsuario", required = true) Integer idUsuario,@NotNull @ApiParam(value = "Fecha  (formato YYYY-MM-DD)", required = true) @Valid @RequestParam(value = "fecha", required = true) String fecha) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"fecha\" : \"16-12-2020 03:52 PM\",  \"cobradorImg\" : \"JUAN.png\",  \"estado\" : \"PENDIENTE\",  \"total\" : \"45960.33\",  \"cobrador\" : \"JUAN\",  \"estadoCod\" : \"PEN\",  \"idRuta\" : 4}, {  \"fecha\" : \"16-12-2020 03:52 PM\",  \"cobradorImg\" : \"JUAN.png\",  \"estado\" : \"PENDIENTE\",  \"total\" : \"45960.33\",  \"cobrador\" : \"JUAN\",  \"estadoCod\" : \"PEN\",  \"idRuta\" : 4} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "datos para cierre caja", nickname = "getInfoRutaCierreCaja", notes = "Obtiene información para ruta cierre caja", response = InfoRutaCierreCajaResponse.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={  })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = InfoRutaCierreCajaResponse.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/getInfoRutaCierreCaja",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<InfoRutaCierreCajaResponse>> getInfoRutaCierreCaja(@NotNull @ApiParam(value = "ID del usuario", required = true) @Valid @RequestParam(value = "idUsuario", required = true) Integer idUsuario) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"cantidadQ\" : 45960.33,  \"fecha\" : \"16-12-2020 03:52 PM\",  \"ruta\" : 4,  \"cantidad\" : 45960.33,  \"cantidadD\" : 45960.33}, {  \"cantidadQ\" : 45960.33,  \"fecha\" : \"16-12-2020 03:52 PM\",  \"ruta\" : 4,  \"cantidad\" : 45960.33,  \"cantidadD\" : 45960.33} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "datos de rutas poliza", nickname = "getModRutaPoliza", notes = "Obtiene información de rutas poliza", response = ModRutaPolizaResponse.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={  })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = ModRutaPolizaResponse.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/getModRutaPoliza",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<ModRutaPolizaResponse>> getModRutaPoliza(@NotNull @ApiParam(value = "ID de la ruta", required = true) @Valid @RequestParam(value = "idRuta", required = true) Integer idRuta) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"estado\" : \"AVISADO\",  \"ruta\" : 4,  \"polizaCodigo\" : \"AUOL\",  \"estadoCod\" : \"AVI\",  \"polizaNumero\" : \"30020111007991\",  \"asegurado\" : \"Sociedad Anonima\",  \"direccion\" : \"Ciudad de Guatemala\",  \"certificado\" : \"1\",  \"idePol\" : \"3002000007993\"}, {  \"estado\" : \"AVISADO\",  \"ruta\" : 4,  \"polizaCodigo\" : \"AUOL\",  \"estadoCod\" : \"AVI\",  \"polizaNumero\" : \"30020111007991\",  \"asegurado\" : \"Sociedad Anonima\",  \"direccion\" : \"Ciudad de Guatemala\",  \"certificado\" : \"1\",  \"idePol\" : \"3002000007993\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "datos de rutas poliza facturas", nickname = "getModRutaPolizaFacturas", notes = "Obtiene información de rutas poliza para facturas", response = ModRutaPolizaFacturasResponse.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={  })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = ModRutaPolizaFacturasResponse.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/getModRutaPolizaFacturas",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<ModRutaPolizaFacturasResponse>> getModRutaPolizaFacturas(@NotNull @ApiParam(value = "ID de la ruta", required = true) @Valid @RequestParam(value = "idRuta", required = true) Integer idRuta) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"estado\" : \"AVISADO\",  \"ruta\" : 4,  \"polizaCodigo\" : \"AUOL\",  \"estadoCod\" : \"AVI\",  \"polizaNumero\" : \"30020111007991\",  \"asegurado\" : \"Sociedad Anonima\",  \"direccion\" : \"Ciudad de Guatemala\",  \"certificado\" : \"1\",  \"idePol\" : \"3002000007993\"}, {  \"estado\" : \"AVISADO\",  \"ruta\" : 4,  \"polizaCodigo\" : \"AUOL\",  \"estadoCod\" : \"AVI\",  \"polizaNumero\" : \"30020111007991\",  \"asegurado\" : \"Sociedad Anonima\",  \"direccion\" : \"Ciudad de Guatemala\",  \"certificado\" : \"1\",  \"idePol\" : \"3002000007993\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "dato de promedio", nickname = "getPromedios", notes = "Obtiene informacion de promedio", response = InfoPromedioResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={  })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = InfoPromedioResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/getPromedios",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<InfoPromedioResponse> getPromedios(@NotNull @ApiParam(value = "ID del usuario asignado", required = true) @Valid @RequestParam(value = "idUsuario", required = true) Integer idUsuario,@NotNull @ApiParam(value = "ID de la ruta", required = true) @Valid @RequestParam(value = "ruta", required = true) Integer ruta,@NotNull @ApiParam(value = "tipo", required = true) @Valid @RequestParam(value = "tipo", required = true) Integer tipo,@ApiParam(value = "Fecha  (formato YYYY-MM-DD)") @Valid @RequestParam(value = "fecha", required = false) String fecha) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"promedio\" : \"45\"}", InfoPromedioResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Endpoint para obtener la rutas cobradas", nickname = "getRutasCobradas", notes = "", response = RutasCobradasResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={  })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Lista de rutas cobradas cargadas correctamente", response = RutasCobradasResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/getRutasCobradas",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<RutasCobradasResponse> getRutasCobradas() {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"idRutas\" : [ \"218\", \"217\" ]}", RutasCobradasResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "datos de tipos de pago", nickname = "getTipoPago", notes = "Obtiene lista de tipos de pago", response = TipoPagoResponse.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={  })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = TipoPagoResponse.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/getTipoPago",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<TipoPagoResponse>> getTipoPago() {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"codigo\" : \"DEP\",  \"nombre\" : \"DEPOSITO\"}, {  \"codigo\" : \"DEP\",  \"nombre\" : \"DEPOSITO\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Insertar detalle de ruta con correo", nickname = "insertarDetalleRuta2", notes = "Inserta el detalle de una ruta mediante stored procedure sp_insert_detalle_ruta2", response = InsertarDetalleRuta2Response.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "CobroMovil", })
    @ApiResponses(value = { 
        @ApiResponse(code = 201, message = "Operación exitosa sin contenido", response = InsertarDetalleRuta2Response.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/insertar-detalle-ruta2",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<InsertarDetalleRuta2Response> insertarDetalleRuta2(@ApiParam(value = "Datos para insertar el detalle de la ruta con correo" ,required=true )  @Valid @RequestBody InsertarDetalleRuta2Request body) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"201\",  \"mensaje\" : \"Detalle de ruta insertado exitosamente\"}", InsertarDetalleRuta2Response.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Insertar detalle de ruta diario", nickname = "insertarDetalleRutaDiario", notes = "Inserta el detalle de una ruta diaria mediante stored procedure", response = InsertarDetalleRutaDiarioResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "CobroMovil", })
    @ApiResponses(value = { 
        @ApiResponse(code = 201, message = "Operación exitosa sin contenido", response = InsertarDetalleRutaDiarioResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/insertar-detalle-ruta-diario",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<InsertarDetalleRutaDiarioResponse> insertarDetalleRutaDiario(@ApiParam(value = "Datos para insertar el detalle de la ruta diaria" ,required=true )  @Valid @RequestBody InsertarDetalleRutaDiarioRequest body) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"201\",  \"mensaje\" : \"Ruta modificada exitosamente\"}", InsertarDetalleRutaDiarioResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Insertar tipo de medio de pago", nickname = "insertarTipoMedioPago", notes = "Inserta un tipo de medio de pago mediante stored procedure sp_insertar_tipo_medio_pago", response = TipoMedioPagoResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "CobroMovil", })
    @ApiResponses(value = { 
        @ApiResponse(code = 201, message = "Operación exitosa sin contenido", response = TipoMedioPagoResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/insertar-tipo-medio-pago",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<TipoMedioPagoResponse> insertarTipoMedioPago(@ApiParam(value = "Datos para insertar el tipo de medio de pago" ,required=true )  @Valid @RequestBody TipoMedioPagoRequest body) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"201\",  \"mensaje\" : \"Tipo de medio de pago insertado exitosamente\"}", TipoMedioPagoResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Listar cajeros de usuario", nickname = "listarCajeroUsuario", notes = "Obtiene la lista de cajeros asignados a un usuario mediante la función fn_listarCajeroUsuario", response = CajeroUsuarioResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "CobroMovil", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Cajeros obtenidos exitosamente", response = CajeroUsuarioResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/listar-cajero-usuario",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<CajeroUsuarioResponse> listarCajeroUsuario(@NotNull @ApiParam(value = "ID del usuario", required = true) @Valid @RequestParam(value = "idUsuario", required = true) Integer idUsuario) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"200\",  \"cajeros\" : [ {    \"codigo\" : \"CAJ001\",    \"sistema\" : \"ACSEL\",    \"usuario\" : \"cajero01\"  }, {    \"codigo\" : \"CAJ001\",    \"sistema\" : \"ACSEL\",    \"usuario\" : \"cajero01\"  } ],  \"mensaje\" : \"Cajeros obtenidos exitosamente\"}", CajeroUsuarioResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "datos para llenado de detalle", nickname = "llenarDetalleCierreCaja", notes = "Obtiene información para llnado de detalle de caja", response = LlenarDetalleCierreCajaResponse.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={  })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = LlenarDetalleCierreCajaResponse.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/llenarDetalleCierreCaja",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<LlenarDetalleCierreCajaResponse>> llenarDetalleCierreCaja(@NotNull @ApiParam(value = "ID del usuario", required = true) @Valid @RequestParam(value = "idUsuario", required = true) Integer idUsuario) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"ruta\" : 4,  \"moneda\" : \"Q\",  \"tipoPago\" : \"EFECTIVO\",  \"cantidad\" : 45960.33}, {  \"ruta\" : 4,  \"moneda\" : \"Q\",  \"tipoPago\" : \"EFECTIVO\",  \"cantidad\" : 45960.33} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Modificar ruta de cobro existente", nickname = "modificarRutaCobro", notes = "Modifica una ruta de cobro existente", response = ModificarRutaResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "CobroMovil", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = ModificarRutaResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/modificar-ruta",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<ModificarRutaResponse> modificarRutaCobro(@ApiParam(value = "Datos para modificar una ruta." ,required=true )  @Valid @RequestBody ModificarRutaRequest body) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"200\",  \"mensaje\" : \"Ruta modificada exitosamente\",  \"idRuta\" : 1}", ModificarRutaResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener acciones por roles", nickname = "obtenerAccionesPorRoles", notes = "Obtiene las acciones disponibles para los roles especificados mediante la función Obtener_Acciones_por_Roles", response = AccionesPorRolesResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "CobroMovil", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Acciones obtenidas exitosamente", response = AccionesPorRolesResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/obtener-acciones-por-roles",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<AccionesPorRolesResponse> obtenerAccionesPorRoles(@NotNull @ApiParam(value = "Roles separados por coma (ej. \"1,2,3\")", required = true) @Valid @RequestParam(value = "roles", required = true) String roles) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"200\",  \"acciones\" : [ {    \"idAccion\" : 1,    \"nombre\" : \"CREAR_RUTA\"  }, {    \"idAccion\" : 1,    \"nombre\" : \"CREAR_RUTA\"  } ],  \"mensaje\" : \"Acciones obtenidas exitosamente\"}", AccionesPorRolesResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener lista de entidades bancarias", nickname = "obtenerEntidadesBancarias", notes = "Obtiene lista de entidades bancarias por sistema y moneda", response = EntidadResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "CobroMovil", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = EntidadResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/listar-entidades",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<EntidadResponse> obtenerEntidadesBancarias(@NotNull @ApiParam(value = "Id del sistema (1 ACSEL, 2 TRON)", required = true) @Valid @RequestParam(value = "sistema", required = true) Integer sistema,@NotNull @ApiParam(value = "Codigo de modena(Q,$)", required = true) @Valid @RequestParam(value = "moneda", required = true) String moneda) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"200\",  \"listaEntidades\" : [ {    \"codigo\" : \"000004\",    \"nombre\" : \"000004 - NOMBRE DEL BANCO\"  }, {    \"codigo\" : \"000004\",    \"nombre\" : \"000004 - NOMBRE DEL BANCO\"  } ],  \"mensaje\" : \"Operación exitosa\"}", EntidadResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener localización pagos", nickname = "obtenerLocalizacionPagos", notes = "Obtiene información de localización de pagos según los parámetros proporcionados", response = LocalizacionPagosResponse.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={  })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = LocalizacionPagosResponse.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/obtenerLocalizacionPagos",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<LocalizacionPagosResponse>> obtenerLocalizacionPagos(@NotNull @ApiParam(value = "Fecha de inicio (formato YYYY-MM-DD)", required = true) @Valid @RequestParam(value = "fechaInicio", required = true) String fechaInicio,@NotNull @ApiParam(value = "Fecha de fin (formato YYYY-MM-DD)", required = true) @Valid @RequestParam(value = "fechaFin", required = true) String fechaFin,@NotNull @ApiParam(value = "ID de la ruta", required = true) @Valid @RequestParam(value = "idRuta", required = true) Integer idRuta) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"recibo\" : \"44268442\",  \"latitud\" : \"14.5880933\",  \"cuota\" : \"4/6\",  \"idDetalle\" : 4,  \"asegurado\" : \"Sociedad anonima\",  \"nombre\" : \"Juan Fernandez\",  \"certificado\" : \"1\",  \"total\" : \"90.1\",  \"longitud\" : \"-90.5925073\",  \"moneda\" : \"Q\",  \"idRuta\" : 4,  \"idePol\" : \"12234567\",  \"fechaCobro\" : \"16-12-2020 03:52 PM\"}, {  \"recibo\" : \"44268442\",  \"latitud\" : \"14.5880933\",  \"cuota\" : \"4/6\",  \"idDetalle\" : 4,  \"asegurado\" : \"Sociedad anonima\",  \"nombre\" : \"Juan Fernandez\",  \"certificado\" : \"1\",  \"total\" : \"90.1\",  \"longitud\" : \"-90.5925073\",  \"moneda\" : \"Q\",  \"idRuta\" : 4,  \"idePol\" : \"12234567\",  \"fechaCobro\" : \"16-12-2020 03:52 PM\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener lista de ocpines de cobro", nickname = "obtenerOpcionesDeCobro", notes = "Obtiene lista de opciones de cobro", response = CobroResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "CobroMovil", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = CobroResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/listar-opcion-cobro",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<CobroResponse> obtenerOpcionesDeCobro() {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"200\",  \"listaOpcionCobro\" : [ {    \"descripcion\" : \"NOMBRE DEL BANCO\",    \"codigo\" : \"006\"  }, {    \"descripcion\" : \"NOMBRE DEL BANCO\",    \"codigo\" : \"006\"  } ],  \"mensaje\" : \"Operación exitosa\"}", CobroResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener roles de usuario", nickname = "obtenerRolesUsuario", notes = "Obtiene los roles asignados a un usuario mediante la función Obtener_Roles_Usuario", response = ObtenerRolesUsuarioResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "CobroMovil", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Roles obtenidos exitosamente", response = ObtenerRolesUsuarioResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/obtener-roles-usuario",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<ObtenerRolesUsuarioResponse> obtenerRolesUsuario(@NotNull @ApiParam(value = "ID del usuario", required = true) @Valid @RequestParam(value = "idUsuario", required = true) Integer idUsuario) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"200\",  \"roles\" : [ {    \"idRol\" : 1,    \"nombre\" : \"ADMINISTRADOR\"  }, {    \"idRol\" : 1,    \"nombre\" : \"ADMINISTRADOR\"  } ],  \"mensaje\" : \"Roles obtenidos exitosamente\"}", ObtenerRolesUsuarioResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener rutas con localización", nickname = "obtenerRutasLocalizacion", notes = "Obtiene información de rutas con localización según los parámetros proporcionados", response = RutaLocalizacionResponse.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={  })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = RutaLocalizacionResponse.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/obtenerRutasLocalizacion",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<RutaLocalizacionResponse>> obtenerRutasLocalizacion(@NotNull @ApiParam(value = "Fecha de inicio (formato YYYY-MM-DD)", required = true) @Valid @RequestParam(value = "fechaInicio", required = true) String fechaInicio,@NotNull @ApiParam(value = "Fecha de fin (formato YYYY-MM-DD)", required = true) @Valid @RequestParam(value = "fechaFin", required = true) String fechaFin,@NotNull @ApiParam(value = "ID de la ruta", required = true) @Valid @RequestParam(value = "idRuta", required = true) Integer idRuta) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"cantidadQ\" : 45960.33,  \"fecha\" : \"16-12-2020 03:52 PM\",  \"ruta\" : 4,  \"cantidadCobros\" : 9,  \"cantidad\" : 45960.33,  \"cantidadD\" : 45960.33}, {  \"cantidadQ\" : 45960.33,  \"fecha\" : \"16-12-2020 03:52 PM\",  \"ruta\" : 4,  \"cantidadCobros\" : 9,  \"cantidad\" : 45960.33,  \"cantidadD\" : 45960.33} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Actualizar aviso de recibo de ruta", nickname = "updateAvisoReciboRuta", notes = "Actualiza el aviso de recibo de ruta mediante stored procedure sp_update_aviso_recibo_ruta", response = AvisoReciboRutaResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "CobroMovil", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa sin contenido", response = AvisoReciboRutaResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/update-aviso-recibo-ruta",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<AvisoReciboRutaResponse> updateAvisoReciboRuta(@ApiParam(value = "Datos para actualizar el aviso de recibo de ruta" ,required=true )  @Valid @RequestBody AvisoReciboRutaRequest body) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"200\",  \"mensaje\" : \"Aviso de recibo de ruta actualizado exitosamente\"}", AvisoReciboRutaResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Actualizar recibo de ruta", nickname = "updateReciboRuta", notes = "Actualiza el recibo de ruta mediante stored procedure sp_update_recibo_ruta", response = UpdateReciboRutaResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "CobroMovil", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa sin contenido", response = UpdateReciboRutaResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/cobroMovil/update-recibo-ruta",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<UpdateReciboRutaResponse> updateReciboRuta(@ApiParam(value = "Datos para actualizar el recibo de ruta" ,required=true )  @Valid @RequestBody ReciboRutaRequest body) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"200\",  \"correos\" : \"<EMAIL>;<EMAIL>\",  \"mensaje\" : \"Aviso de recibo de ruta actualizado exitosamente\"}", UpdateReciboRutaResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CobroMovilApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

}
