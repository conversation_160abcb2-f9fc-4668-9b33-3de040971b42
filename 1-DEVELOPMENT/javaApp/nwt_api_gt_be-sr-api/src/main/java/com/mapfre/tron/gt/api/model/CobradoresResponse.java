package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CobradoresResponse
 */
@Validated

public class CobradoresResponse   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("nombre")
  private String nombre = null;

  public CobradoresResponse idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Id  del usuario
   * @return idUsuario
  **/
  @ApiModelProperty(example = "4", value = "Id  del usuario")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public CobradoresResponse nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre del cobrador
   * @return nombre
  **/
  @ApiModelProperty(example = "JUAN PEREZ", value = "Nombre del cobrador")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CobradoresResponse cobradoresResponse = (CobradoresResponse) o;
    return Objects.equals(this.idUsuario, cobradoresResponse.idUsuario) &&
        Objects.equals(this.nombre, cobradoresResponse.nombre);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, nombre);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CobradoresResponse {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

