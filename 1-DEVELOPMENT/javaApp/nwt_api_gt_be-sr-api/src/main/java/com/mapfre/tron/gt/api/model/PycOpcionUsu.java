package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycOpcionUsu
 */
@Validated

public class PycOpcionUsu   {
  @JsonProperty("codigoMenu")
  private Integer codigoMenu = null;

  @JsonProperty("codigoMenuPadre")
  private Integer codigoMenuPadre = null;

  @JsonProperty("titulo")
  private String titulo = null;

  @JsonProperty("logo")
  private String logo = null;

  @JsonProperty("ruta")
  private String ruta = null;

  @JsonProperty("funciones")
  private String funciones = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("level")
  private Integer level = null;

  @JsonProperty("orden")
  private Integer orden = null;

  public PycOpcionUsu codigoMenu(Integer codigoMenu) {
    this.codigoMenu = codigoMenu;
    return this;
  }

  /**
   * Identificador único de la opción de menú
   * @return codigoMenu
  **/
  @ApiModelProperty(example = "101", value = "Identificador único de la opción de menú")


  public Integer getCodigoMenu() {
    return codigoMenu;
  }

  public void setCodigoMenu(Integer codigoMenu) {
    this.codigoMenu = codigoMenu;
  }

  public PycOpcionUsu codigoMenuPadre(Integer codigoMenuPadre) {
    this.codigoMenuPadre = codigoMenuPadre;
    return this;
  }

  /**
   * Identificador del menú padre (0 para menús raíz)
   * @return codigoMenuPadre
  **/
  @ApiModelProperty(example = "0", value = "Identificador del menú padre (0 para menús raíz)")


  public Integer getCodigoMenuPadre() {
    return codigoMenuPadre;
  }

  public void setCodigoMenuPadre(Integer codigoMenuPadre) {
    this.codigoMenuPadre = codigoMenuPadre;
  }

  public PycOpcionUsu titulo(String titulo) {
    this.titulo = titulo;
    return this;
  }

  /**
   * Título de la opción de menú (puede ser personalizado por proceso)
   * @return titulo
  **/
  @ApiModelProperty(example = "Gestión de Peticiones", value = "Título de la opción de menú (puede ser personalizado por proceso)")


  public String getTitulo() {
    return titulo;
  }

  public void setTitulo(String titulo) {
    this.titulo = titulo;
  }

  public PycOpcionUsu logo(String logo) {
    this.logo = logo;
    return this;
  }

  /**
   * Icono o logo asociado a la opción de menú
   * @return logo
  **/
  @ApiModelProperty(example = "fa-tasks", value = "Icono o logo asociado a la opción de menú")


  public String getLogo() {
    return logo;
  }

  public void setLogo(String logo) {
    this.logo = logo;
  }

  public PycOpcionUsu ruta(String ruta) {
    this.ruta = ruta;
    return this;
  }

  /**
   * Ruta URL de la opción de menú
   * @return ruta
  **/
  @ApiModelProperty(example = "/peticiones/lista", value = "Ruta URL de la opción de menú")


  public String getRuta() {
    return ruta;
  }

  public void setRuta(String ruta) {
    this.ruta = ruta;
  }

  public PycOpcionUsu funciones(String funciones) {
    this.funciones = funciones;
    return this;
  }

  /**
   * Funciones o permisos asociados a la opción
   * @return funciones
  **/
  @ApiModelProperty(example = "CREATE,READ,UPDATE,DELETE", value = "Funciones o permisos asociados a la opción")


  public String getFunciones() {
    return funciones;
  }

  public void setFunciones(String funciones) {
    this.funciones = funciones;
  }

  public PycOpcionUsu estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado de la opción de menú
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", value = "Estado de la opción de menú")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycOpcionUsu level(Integer level) {
    this.level = level;
    return this;
  }

  /**
   * Nivel jerárquico en la estructura de menú
   * @return level
  **/
  @ApiModelProperty(example = "1", value = "Nivel jerárquico en la estructura de menú")


  public Integer getLevel() {
    return level;
  }

  public void setLevel(Integer level) {
    this.level = level;
  }

  public PycOpcionUsu orden(Integer orden) {
    this.orden = orden;
    return this;
  }

  /**
   * Orden de visualización de la opción en el menú
   * @return orden
  **/
  @ApiModelProperty(example = "10", value = "Orden de visualización de la opción en el menú")


  public Integer getOrden() {
    return orden;
  }

  public void setOrden(Integer orden) {
    this.orden = orden;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycOpcionUsu pycOpcionUsu = (PycOpcionUsu) o;
    return Objects.equals(this.codigoMenu, pycOpcionUsu.codigoMenu) &&
        Objects.equals(this.codigoMenuPadre, pycOpcionUsu.codigoMenuPadre) &&
        Objects.equals(this.titulo, pycOpcionUsu.titulo) &&
        Objects.equals(this.logo, pycOpcionUsu.logo) &&
        Objects.equals(this.ruta, pycOpcionUsu.ruta) &&
        Objects.equals(this.funciones, pycOpcionUsu.funciones) &&
        Objects.equals(this.estado, pycOpcionUsu.estado) &&
        Objects.equals(this.level, pycOpcionUsu.level) &&
        Objects.equals(this.orden, pycOpcionUsu.orden);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codigoMenu, codigoMenuPadre, titulo, logo, ruta, funciones, estado, level, orden);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycOpcionUsu {\n");
    
    sb.append("    codigoMenu: ").append(toIndentedString(codigoMenu)).append("\n");
    sb.append("    codigoMenuPadre: ").append(toIndentedString(codigoMenuPadre)).append("\n");
    sb.append("    titulo: ").append(toIndentedString(titulo)).append("\n");
    sb.append("    logo: ").append(toIndentedString(logo)).append("\n");
    sb.append("    ruta: ").append(toIndentedString(ruta)).append("\n");
    sb.append("    funciones: ").append(toIndentedString(funciones)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    level: ").append(toIndentedString(level)).append("\n");
    sb.append("    orden: ").append(toIndentedString(orden)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

