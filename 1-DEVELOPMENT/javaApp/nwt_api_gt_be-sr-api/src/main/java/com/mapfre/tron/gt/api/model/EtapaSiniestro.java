package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * EtapaSiniestro
 */
@Validated

public class EtapaSiniestro   {
  @JsonProperty("numOrden")
  private Integer numOrden = null;

  @JsonProperty("codigoFase")
  private String codigoFase = null;

  @JsonProperty("codCia")
  private Integer codCia = null;

  @JsonProperty("codRamo")
  private Integer codRamo = null;

  @JsonProperty("numPoliza")
  private String numPoliza = null;

  @JsonProperty("numSini")
  private String numSini = null;

  @JsonProperty("deducible")
  private Double deducible = null;

  @JsonProperty("etapa")
  private String etapa = null;

  @JsonProperty("cuenta")
  private String cuenta = null;

  @JsonProperty("cheque")
  private String cheque = null;

  @JsonProperty("monto")
  private Double monto = null;

  @JsonProperty("fecha")
  private String fecha = null;

  @JsonProperty("perito")
  private String perito = null;

  @JsonProperty("taller")
  private String taller = null;

  public EtapaSiniestro numOrden(Integer numOrden) {
    this.numOrden = numOrden;
    return this;
  }

  /**
   * Número de orden
   * @return numOrden
  **/
  @ApiModelProperty(example = "1", value = "Número de orden")


  public Integer getNumOrden() {
    return numOrden;
  }

  public void setNumOrden(Integer numOrden) {
    this.numOrden = numOrden;
  }

  public EtapaSiniestro codigoFase(String codigoFase) {
    this.codigoFase = codigoFase;
    return this;
  }

  /**
   * Código de fase del siniestro
   * @return codigoFase
  **/
  @ApiModelProperty(example = "GS", value = "Código de fase del siniestro")


  public String getCodigoFase() {
    return codigoFase;
  }

  public void setCodigoFase(String codigoFase) {
    this.codigoFase = codigoFase;
  }

  public EtapaSiniestro codCia(Integer codCia) {
    this.codCia = codCia;
    return this;
  }

  /**
   * Código de compañía
   * @return codCia
  **/
  @ApiModelProperty(example = "2", value = "Código de compañía")


  public Integer getCodCia() {
    return codCia;
  }

  public void setCodCia(Integer codCia) {
    this.codCia = codCia;
  }

  public EtapaSiniestro codRamo(Integer codRamo) {
    this.codRamo = codRamo;
    return this;
  }

  /**
   * Código de ramo
   * @return codRamo
  **/
  @ApiModelProperty(example = "300", value = "Código de ramo")


  public Integer getCodRamo() {
    return codRamo;
  }

  public void setCodRamo(Integer codRamo) {
    this.codRamo = codRamo;
  }

  public EtapaSiniestro numPoliza(String numPoliza) {
    this.numPoliza = numPoliza;
    return this;
  }

  /**
   * Número de póliza
   * @return numPoliza
  **/
  @ApiModelProperty(example = "0230025018359", value = "Número de póliza")


  public String getNumPoliza() {
    return numPoliza;
  }

  public void setNumPoliza(String numPoliza) {
    this.numPoliza = numPoliza;
  }

  public EtapaSiniestro numSini(String numSini) {
    this.numSini = numSini;
    return this;
  }

  /**
   * Número de siniestro
   * @return numSini
  **/
  @ApiModelProperty(example = "300252001000007", value = "Número de siniestro")


  public String getNumSini() {
    return numSini;
  }

  public void setNumSini(String numSini) {
    this.numSini = numSini;
  }

  public EtapaSiniestro deducible(Double deducible) {
    this.deducible = deducible;
    return this;
  }

  /**
   * Monto del deducible
   * @return deducible
  **/
  @ApiModelProperty(example = "0.0", value = "Monto del deducible")


  public Double getDeducible() {
    return deducible;
  }

  public void setDeducible(Double deducible) {
    this.deducible = deducible;
  }

  public EtapaSiniestro etapa(String etapa) {
    this.etapa = etapa;
    return this;
  }

  /**
   * Etapa del siniestro
   * @return etapa
  **/
  @ApiModelProperty(example = "GS", value = "Etapa del siniestro")


  public String getEtapa() {
    return etapa;
  }

  public void setEtapa(String etapa) {
    this.etapa = etapa;
  }

  public EtapaSiniestro cuenta(String cuenta) {
    this.cuenta = cuenta;
    return this;
  }

  /**
   * Número de cuenta
   * @return cuenta
  **/
  @ApiModelProperty(value = "Número de cuenta")


  public String getCuenta() {
    return cuenta;
  }

  public void setCuenta(String cuenta) {
    this.cuenta = cuenta;
  }

  public EtapaSiniestro cheque(String cheque) {
    this.cheque = cheque;
    return this;
  }

  /**
   * Número de cheque
   * @return cheque
  **/
  @ApiModelProperty(value = "Número de cheque")


  public String getCheque() {
    return cheque;
  }

  public void setCheque(String cheque) {
    this.cheque = cheque;
  }

  public EtapaSiniestro monto(Double monto) {
    this.monto = monto;
    return this;
  }

  /**
   * Monto asociado
   * @return monto
  **/
  @ApiModelProperty(value = "Monto asociado")


  public Double getMonto() {
    return monto;
  }

  public void setMonto(Double monto) {
    this.monto = monto;
  }

  public EtapaSiniestro fecha(String fecha) {
    this.fecha = fecha;
    return this;
  }

  /**
   * Fecha de la etapa
   * @return fecha
  **/
  @ApiModelProperty(example = "27/04/2025 17:26:26", value = "Fecha de la etapa")


  public String getFecha() {
    return fecha;
  }

  public void setFecha(String fecha) {
    this.fecha = fecha;
  }

  public EtapaSiniestro perito(String perito) {
    this.perito = perito;
    return this;
  }

  /**
   * Nombre del perito
   * @return perito
  **/
  @ApiModelProperty(value = "Nombre del perito")


  public String getPerito() {
    return perito;
  }

  public void setPerito(String perito) {
    this.perito = perito;
  }

  public EtapaSiniestro taller(String taller) {
    this.taller = taller;
    return this;
  }

  /**
   * Nombre del taller
   * @return taller
  **/
  @ApiModelProperty(value = "Nombre del taller")


  public String getTaller() {
    return taller;
  }

  public void setTaller(String taller) {
    this.taller = taller;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EtapaSiniestro etapaSiniestro = (EtapaSiniestro) o;
    return Objects.equals(this.numOrden, etapaSiniestro.numOrden) &&
        Objects.equals(this.codigoFase, etapaSiniestro.codigoFase) &&
        Objects.equals(this.codCia, etapaSiniestro.codCia) &&
        Objects.equals(this.codRamo, etapaSiniestro.codRamo) &&
        Objects.equals(this.numPoliza, etapaSiniestro.numPoliza) &&
        Objects.equals(this.numSini, etapaSiniestro.numSini) &&
        Objects.equals(this.deducible, etapaSiniestro.deducible) &&
        Objects.equals(this.etapa, etapaSiniestro.etapa) &&
        Objects.equals(this.cuenta, etapaSiniestro.cuenta) &&
        Objects.equals(this.cheque, etapaSiniestro.cheque) &&
        Objects.equals(this.monto, etapaSiniestro.monto) &&
        Objects.equals(this.fecha, etapaSiniestro.fecha) &&
        Objects.equals(this.perito, etapaSiniestro.perito) &&
        Objects.equals(this.taller, etapaSiniestro.taller);
  }

  @Override
  public int hashCode() {
    return Objects.hash(numOrden, codigoFase, codCia, codRamo, numPoliza, numSini, deducible, etapa, cuenta, cheque, monto, fecha, perito, taller);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EtapaSiniestro {\n");
    
    sb.append("    numOrden: ").append(toIndentedString(numOrden)).append("\n");
    sb.append("    codigoFase: ").append(toIndentedString(codigoFase)).append("\n");
    sb.append("    codCia: ").append(toIndentedString(codCia)).append("\n");
    sb.append("    codRamo: ").append(toIndentedString(codRamo)).append("\n");
    sb.append("    numPoliza: ").append(toIndentedString(numPoliza)).append("\n");
    sb.append("    numSini: ").append(toIndentedString(numSini)).append("\n");
    sb.append("    deducible: ").append(toIndentedString(deducible)).append("\n");
    sb.append("    etapa: ").append(toIndentedString(etapa)).append("\n");
    sb.append("    cuenta: ").append(toIndentedString(cuenta)).append("\n");
    sb.append("    cheque: ").append(toIndentedString(cheque)).append("\n");
    sb.append("    monto: ").append(toIndentedString(monto)).append("\n");
    sb.append("    fecha: ").append(toIndentedString(fecha)).append("\n");
    sb.append("    perito: ").append(toIndentedString(perito)).append("\n");
    sb.append("    taller: ").append(toIndentedString(taller)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

