package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycInserActResponse
 */
@Validated

public class PycInserActResponse   {
  @JsonProperty("idActividad")
  private Integer idActividad = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  @JsonProperty("ejecutado")
  private Boolean ejecutado = null;

  public PycInserActResponse idActividad(Integer idActividad) {
    this.idActividad = idActividad;
    return this;
  }

  /**
   * Identificador de la actividad creada
   * @return idActividad
  **/
  @ApiModelProperty(example = "5001", value = "Identificador de la actividad creada")


  public Integer getIdActividad() {
    return idActividad;
  }

  public void setIdActividad(Integer idActividad) {
    this.idActividad = idActividad;
  }

  public PycInserActResponse mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje de confirmación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Actividad creada exitosamente", value = "Mensaje de confirmación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }

  public PycInserActResponse ejecutado(Boolean ejecutado) {
    this.ejecutado = ejecutado;
    return this;
  }

  /**
   * Indicador de ejecución exitosa
   * @return ejecutado
  **/
  @ApiModelProperty(example = "true", value = "Indicador de ejecución exitosa")


  public Boolean isEjecutado() {
    return ejecutado;
  }

  public void setEjecutado(Boolean ejecutado) {
    this.ejecutado = ejecutado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycInserActResponse pycInserActResponse = (PycInserActResponse) o;
    return Objects.equals(this.idActividad, pycInserActResponse.idActividad) &&
        Objects.equals(this.mensaje, pycInserActResponse.mensaje) &&
        Objects.equals(this.ejecutado, pycInserActResponse.ejecutado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idActividad, mensaje, ejecutado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycInserActResponse {\n");
    
    sb.append("    idActividad: ").append(toIndentedString(idActividad)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("    ejecutado: ").append(toIndentedString(ejecutado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

