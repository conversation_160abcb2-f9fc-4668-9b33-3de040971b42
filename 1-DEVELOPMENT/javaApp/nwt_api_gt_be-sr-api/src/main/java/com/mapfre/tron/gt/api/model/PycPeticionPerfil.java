package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycPeticionPerfil
 */
@Validated

public class PycPeticionPerfil   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("tipoPeticion")
  private String tipoPeticion = null;

  @JsonProperty("nombrePeticion")
  private String nombrePeticion = null;

  @JsonProperty("descripcionPeticion")
  private String descripcionPeticion = null;

  @JsonProperty("fechaCreacion")
  private String fechaCreacion = null;

  @JsonProperty("idTipo")
  private Integer idTipo = null;

  @JsonProperty("idUsuarioSolicitante")
  private Integer idUsuarioSolicitante = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("fechaInicio")
  private String fechaInicio = null;

  @JsonProperty("fechaFin")
  private String fechaFin = null;

  @JsonProperty("totalHoras")
  private Double totalHoras = null;

  @JsonProperty("porcentajeBase")
  private Double porcentajeBase = null;

  @JsonProperty("porcentajeReal")
  private Double porcentajeReal = null;

  @JsonProperty("idEstado")
  private String idEstado = null;

  @JsonProperty("nombreEstado")
  private String nombreEstado = null;

  @JsonProperty("colorEstado")
  private String colorEstado = null;

  @JsonProperty("idPerfil")
  private Integer idPerfil = null;

  @JsonProperty("nombrePerfil")
  private String nombrePerfil = null;

  @JsonProperty("usuarioGraba")
  private String usuarioGraba = null;

  @JsonProperty("usuarioSoli")
  private String usuarioSoli = null;

  @JsonProperty("origen")
  private String origen = null;

  @JsonProperty("codClarity")
  private String codClarity = null;

  @JsonProperty("codcia")
  private String codcia = null;

  @JsonProperty("prioridad")
  private String prioridad = null;

  @JsonProperty("prioriDesc")
  private String prioriDesc = null;

  @JsonProperty("idPeticionSiguiente")
  private Integer idPeticionSiguiente = null;

  @JsonProperty("tituloSiguiente")
  private String tituloSiguiente = null;

  @JsonProperty("nombreCliente")
  private String nombreCliente = null;

  @JsonProperty("telefonoCliente")
  private String telefonoCliente = null;

  @JsonProperty("correoCliente")
  private String correoCliente = null;

  @JsonProperty("origenDescripcion")
  private String origenDescripcion = null;

  @JsonProperty("codciaDescripcion")
  private String codciaDescripcion = null;

  @JsonProperty("tipoCliente")
  private String tipoCliente = null;

  @JsonProperty("tipoClienteDescripcion")
  private String tipoClienteDescripcion = null;

  @JsonProperty("codCliente")
  private String codCliente = null;

  @JsonProperty("noPoliza")
  private String noPoliza = null;

  @JsonProperty("tipoServicio")
  private String tipoServicio = null;

  @JsonProperty("tipoServicioDescripcion")
  private String tipoServicioDescripcion = null;

  @JsonProperty("causa")
  private String causa = null;

  @JsonProperty("causaDescripcion")
  private String causaDescripcion = null;

  @JsonProperty("gravedad")
  private String gravedad = null;

  @JsonProperty("gravedadDescripcion")
  private String gravedadDescripcion = null;

  @JsonProperty("encuCalif")
  private String encuCalif = null;

  @JsonProperty("encuComent")
  private String encuComent = null;

  @JsonProperty("encuComentAdi")
  private String encuComentAdi = null;

  @JsonProperty("encuUser")
  private String encuUser = null;

  @JsonProperty("encuUsuario")
  private String encuUsuario = null;

  @JsonProperty("encuFecha")
  private String encuFecha = null;

  @JsonProperty("observaciones")
  private String observaciones = null;

  @JsonProperty("idArea")
  private Integer idArea = null;

  @JsonProperty("nombreArea")
  private String nombreArea = null;

  @JsonProperty("idDepartamento")
  private Integer idDepartamento = null;

  @JsonProperty("nombreDepartamento")
  private String nombreDepartamento = null;

  @JsonProperty("idReferencia")
  private String idReferencia = null;

  @JsonProperty("perfilSolicitante")
  private String perfilSolicitante = null;

  @JsonProperty("urlRedirect")
  private String urlRedirect = null;

  @JsonProperty("idResponsable")
  private String idResponsable = null;

  @JsonProperty("responsable")
  private String responsable = null;

  @JsonProperty("perfilResponsable")
  private String perfilResponsable = null;

  @JsonProperty("areaResponsable")
  private String areaResponsable = null;

  @JsonProperty("departamentoResponsable")
  private String departamentoResponsable = null;

  @JsonProperty("mcaRefId")
  private String mcaRefId = null;

  @JsonProperty("codinter")
  private String codinter = null;

  @JsonProperty("ftrDocs")
  private String ftrDocs = null;

  @JsonProperty("iconoEstado")
  private String iconoEstado = null;

  @JsonProperty("idFormulario")
  private String idFormulario = null;

  @JsonProperty("idSeccion")
  private String idSeccion = null;

  @JsonProperty("numPoliza")
  private String numPoliza = null;

  @JsonProperty("numReferencia")
  private String numReferencia = null;

  @JsonProperty("campania")
  private String campania = null;

  @JsonProperty("nextStep")
  private String nextStep = null;

  @JsonProperty("idProceso")
  private Integer idProceso = null;

  @JsonProperty("nombreProceso")
  private String nombreProceso = null;

  @JsonProperty("nombreUsuarioSolicitante")
  private String nombreUsuarioSolicitante = null;

  @JsonProperty("idCanal")
  private Integer idCanal = null;

  @JsonProperty("nombreCanal")
  private String nombreCanal = null;

  @JsonProperty("idOficina")
  private Integer idOficina = null;

  @JsonProperty("nombreOficina")
  private String nombreOficina = null;

  @JsonProperty("candidato")
  private String candidato = null;

  @JsonProperty("fechaNacimiento")
  private String fechaNacimiento = null;

  @JsonProperty("edad")
  private Integer edad = null;

  @JsonProperty("nombrePlaza")
  private String nombrePlaza = null;

  @JsonProperty("dpi")
  private String dpi = null;

  @JsonProperty("nit")
  private String nit = null;

  @JsonProperty("escolaridad")
  private String escolaridad = null;

  @JsonProperty("tituloAcademico")
  private String tituloAcademico = null;

  @JsonProperty("direccion")
  private String direccion = null;

  @JsonProperty("departamentoDir")
  private String departamentoDir = null;

  @JsonProperty("trabajaActualmente")
  private String trabajaActualmente = null;

  @JsonProperty("experienciaSeguro")
  private String experienciaSeguro = null;

  @JsonProperty("pretensionSalarial")
  private String pretensionSalarial = null;

  @JsonProperty("areaRrhh")
  private String areaRrhh = null;

  @JsonProperty("noSolicitud")
  private Integer noSolicitud = null;

  public PycPeticionPerfil idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador de la petición")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycPeticionPerfil tipoPeticion(String tipoPeticion) {
    this.tipoPeticion = tipoPeticion;
    return this;
  }

  /**
   * Tipo de petición
   * @return tipoPeticion
  **/
  @ApiModelProperty(example = "NO RESPONDE CORREO", value = "Tipo de petición")


  public String getTipoPeticion() {
    return tipoPeticion;
  }

  public void setTipoPeticion(String tipoPeticion) {
    this.tipoPeticion = tipoPeticion;
  }

  public PycPeticionPerfil nombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
    return this;
  }

  /**
   * Nombre de la petición
   * @return nombrePeticion
  **/
  @ApiModelProperty(example = "Solicitud de cambio de datos", value = "Nombre de la petición")


  public String getNombrePeticion() {
    return nombrePeticion;
  }

  public void setNombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
  }

  public PycPeticionPerfil descripcionPeticion(String descripcionPeticion) {
    this.descripcionPeticion = descripcionPeticion;
    return this;
  }

  /**
   * Descripción detallada de la petición
   * @return descripcionPeticion
  **/
  @ApiModelProperty(example = "Solicitud para cambiar datos personales en la póliza", value = "Descripción detallada de la petición")


  public String getDescripcionPeticion() {
    return descripcionPeticion;
  }

  public void setDescripcionPeticion(String descripcionPeticion) {
    this.descripcionPeticion = descripcionPeticion;
  }

  public PycPeticionPerfil fechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
    return this;
  }

  /**
   * Fecha de creación de la petición (formato DD/MM/YYYY)
   * @return fechaCreacion
  **/
  @ApiModelProperty(example = "21/05/2025", value = "Fecha de creación de la petición (formato DD/MM/YYYY)")


  public String getFechaCreacion() {
    return fechaCreacion;
  }

  public void setFechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
  }

  public PycPeticionPerfil idTipo(Integer idTipo) {
    this.idTipo = idTipo;
    return this;
  }

  /**
   * Identificador del tipo
   * @return idTipo
  **/
  @ApiModelProperty(example = "5", value = "Identificador del tipo")


  public Integer getIdTipo() {
    return idTipo;
  }

  public void setIdTipo(Integer idTipo) {
    this.idTipo = idTipo;
  }

  public PycPeticionPerfil idUsuarioSolicitante(Integer idUsuarioSolicitante) {
    this.idUsuarioSolicitante = idUsuarioSolicitante;
    return this;
  }

  /**
   * Identificador del usuario solicitante
   * @return idUsuarioSolicitante
  **/
  @ApiModelProperty(example = "1234", value = "Identificador del usuario solicitante")


  public Integer getIdUsuarioSolicitante() {
    return idUsuarioSolicitante;
  }

  public void setIdUsuarioSolicitante(Integer idUsuarioSolicitante) {
    this.idUsuarioSolicitante = idUsuarioSolicitante;
  }

  public PycPeticionPerfil usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Nombre del usuario
   * @return usuario
  **/
  @ApiModelProperty(example = "CARLOS ANTONIO MARROQUIN DIAZ", value = "Nombre del usuario")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public PycPeticionPerfil fechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
    return this;
  }

  /**
   * Fecha de inicio (formato DD/MM/YYYY)
   * @return fechaInicio
  **/
  @ApiModelProperty(example = "03/06/2020", value = "Fecha de inicio (formato DD/MM/YYYY)")


  public String getFechaInicio() {
    return fechaInicio;
  }

  public void setFechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
  }

  public PycPeticionPerfil fechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
    return this;
  }

  /**
   * Fecha de fin (formato DD/MM/YYYY)
   * @return fechaFin
  **/
  @ApiModelProperty(example = "03/06/2020", value = "Fecha de fin (formato DD/MM/YYYY)")


  public String getFechaFin() {
    return fechaFin;
  }

  public void setFechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
  }

  public PycPeticionPerfil totalHoras(Double totalHoras) {
    this.totalHoras = totalHoras;
    return this;
  }

  /**
   * Total de horas estimadas
   * @return totalHoras
  **/
  @ApiModelProperty(example = "0.0", value = "Total de horas estimadas")


  public Double getTotalHoras() {
    return totalHoras;
  }

  public void setTotalHoras(Double totalHoras) {
    this.totalHoras = totalHoras;
  }

  public PycPeticionPerfil porcentajeBase(Double porcentajeBase) {
    this.porcentajeBase = porcentajeBase;
    return this;
  }

  /**
   * Porcentaje base
   * @return porcentajeBase
  **/
  @ApiModelProperty(example = "16.0", value = "Porcentaje base")


  public Double getPorcentajeBase() {
    return porcentajeBase;
  }

  public void setPorcentajeBase(Double porcentajeBase) {
    this.porcentajeBase = porcentajeBase;
  }

  public PycPeticionPerfil porcentajeReal(Double porcentajeReal) {
    this.porcentajeReal = porcentajeReal;
    return this;
  }

  /**
   * Porcentaje real
   * @return porcentajeReal
  **/
  @ApiModelProperty(example = "0.0", value = "Porcentaje real")


  public Double getPorcentajeReal() {
    return porcentajeReal;
  }

  public void setPorcentajeReal(Double porcentajeReal) {
    this.porcentajeReal = porcentajeReal;
  }

  public PycPeticionPerfil idEstado(String idEstado) {
    this.idEstado = idEstado;
    return this;
  }

  /**
   * Identificador del estado
   * @return idEstado
  **/
  @ApiModelProperty(example = "16", value = "Identificador del estado")


  public String getIdEstado() {
    return idEstado;
  }

  public void setIdEstado(String idEstado) {
    this.idEstado = idEstado;
  }

  public PycPeticionPerfil nombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
    return this;
  }

  /**
   * Nombre del estado
   * @return nombreEstado
  **/
  @ApiModelProperty(example = "CERRADA", value = "Nombre del estado")


  public String getNombreEstado() {
    return nombreEstado;
  }

  public void setNombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
  }

  public PycPeticionPerfil colorEstado(String colorEstado) {
    this.colorEstado = colorEstado;
    return this;
  }

  /**
   * Color asociado al estado
   * @return colorEstado
  **/
  @ApiModelProperty(example = "success", value = "Color asociado al estado")


  public String getColorEstado() {
    return colorEstado;
  }

  public void setColorEstado(String colorEstado) {
    this.colorEstado = colorEstado;
  }

  public PycPeticionPerfil idPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
    return this;
  }

  /**
   * Identificador del perfil
   * @return idPerfil
  **/
  @ApiModelProperty(example = "1", value = "Identificador del perfil")


  public Integer getIdPerfil() {
    return idPerfil;
  }

  public void setIdPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
  }

  public PycPeticionPerfil nombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
    return this;
  }

  /**
   * Nombre del perfil
   * @return nombrePerfil
  **/
  @ApiModelProperty(example = "SOLICITANTE", value = "Nombre del perfil")


  public String getNombrePerfil() {
    return nombrePerfil;
  }

  public void setNombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
  }

  public PycPeticionPerfil usuarioGraba(String usuarioGraba) {
    this.usuarioGraba = usuarioGraba;
    return this;
  }

  /**
   * Usuario que grabó la petición
   * @return usuarioGraba
  **/
  @ApiModelProperty(example = "CARMAR", value = "Usuario que grabó la petición")


  public String getUsuarioGraba() {
    return usuarioGraba;
  }

  public void setUsuarioGraba(String usuarioGraba) {
    this.usuarioGraba = usuarioGraba;
  }

  public PycPeticionPerfil usuarioSoli(String usuarioSoli) {
    this.usuarioSoli = usuarioSoli;
    return this;
  }

  /**
   * Usuario solicitante
   * @return usuarioSoli
  **/
  @ApiModelProperty(example = "CARMAR", value = "Usuario solicitante")


  public String getUsuarioSoli() {
    return usuarioSoli;
  }

  public void setUsuarioSoli(String usuarioSoli) {
    this.usuarioSoli = usuarioSoli;
  }

  public PycPeticionPerfil origen(String origen) {
    this.origen = origen;
    return this;
  }

  /**
   * Origen de la petición
   * @return origen
  **/
  @ApiModelProperty(example = "MGT", value = "Origen de la petición")


  public String getOrigen() {
    return origen;
  }

  public void setOrigen(String origen) {
    this.origen = origen;
  }

  public PycPeticionPerfil codClarity(String codClarity) {
    this.codClarity = codClarity;
    return this;
  }

  /**
   * Código Clarity
   * @return codClarity
  **/
  @ApiModelProperty(example = "0", value = "Código Clarity")


  public String getCodClarity() {
    return codClarity;
  }

  public void setCodClarity(String codClarity) {
    this.codClarity = codClarity;
  }

  public PycPeticionPerfil codcia(String codcia) {
    this.codcia = codcia;
    return this;
  }

  /**
   * Código de compañía
   * @return codcia
  **/
  @ApiModelProperty(example = "ALTA", value = "Código de compañía")


  public String getCodcia() {
    return codcia;
  }

  public void setCodcia(String codcia) {
    this.codcia = codcia;
  }

  public PycPeticionPerfil prioridad(String prioridad) {
    this.prioridad = prioridad;
    return this;
  }

  /**
   * Prioridad de la petición
   * @return prioridad
  **/
  @ApiModelProperty(example = "1", value = "Prioridad de la petición")


  public String getPrioridad() {
    return prioridad;
  }

  public void setPrioridad(String prioridad) {
    this.prioridad = prioridad;
  }

  public PycPeticionPerfil prioriDesc(String prioriDesc) {
    this.prioriDesc = prioriDesc;
    return this;
  }

  /**
   * Descripción de la prioridad
   * @return prioriDesc
  **/
  @ApiModelProperty(example = "MEDIA", value = "Descripción de la prioridad")


  public String getPrioriDesc() {
    return prioriDesc;
  }

  public void setPrioriDesc(String prioriDesc) {
    this.prioriDesc = prioriDesc;
  }

  public PycPeticionPerfil idPeticionSiguiente(Integer idPeticionSiguiente) {
    this.idPeticionSiguiente = idPeticionSiguiente;
    return this;
  }

  /**
   * Identificador de la petición siguiente
   * @return idPeticionSiguiente
  **/
  @ApiModelProperty(example = "1002", value = "Identificador de la petición siguiente")


  public Integer getIdPeticionSiguiente() {
    return idPeticionSiguiente;
  }

  public void setIdPeticionSiguiente(Integer idPeticionSiguiente) {
    this.idPeticionSiguiente = idPeticionSiguiente;
  }

  public PycPeticionPerfil tituloSiguiente(String tituloSiguiente) {
    this.tituloSiguiente = tituloSiguiente;
    return this;
  }

  /**
   * Título de la petición siguiente
   * @return tituloSiguiente
  **/
  @ApiModelProperty(example = "hilda vasquez", value = "Título de la petición siguiente")


  public String getTituloSiguiente() {
    return tituloSiguiente;
  }

  public void setTituloSiguiente(String tituloSiguiente) {
    this.tituloSiguiente = tituloSiguiente;
  }

  public PycPeticionPerfil nombreCliente(String nombreCliente) {
    this.nombreCliente = nombreCliente;
    return this;
  }

  /**
   * Nombre del cliente
   * @return nombreCliente
  **/
  @ApiModelProperty(example = "Juan Carlos Pérez García", value = "Nombre del cliente")


  public String getNombreCliente() {
    return nombreCliente;
  }

  public void setNombreCliente(String nombreCliente) {
    this.nombreCliente = nombreCliente;
  }

  public PycPeticionPerfil telefonoCliente(String telefonoCliente) {
    this.telefonoCliente = telefonoCliente;
    return this;
  }

  /**
   * Teléfono del cliente
   * @return telefonoCliente
  **/
  @ApiModelProperty(example = "22085997", value = "Teléfono del cliente")


  public String getTelefonoCliente() {
    return telefonoCliente;
  }

  public void setTelefonoCliente(String telefonoCliente) {
    this.telefonoCliente = telefonoCliente;
  }

  public PycPeticionPerfil correoCliente(String correoCliente) {
    this.correoCliente = correoCliente;
    return this;
  }

  /**
   * Correo electrónico del cliente
   * @return correoCliente
  **/
  @ApiModelProperty(example = "<EMAIL>", value = "Correo electrónico del cliente")


  public String getCorreoCliente() {
    return correoCliente;
  }

  public void setCorreoCliente(String correoCliente) {
    this.correoCliente = correoCliente;
  }

  public PycPeticionPerfil origenDescripcion(String origenDescripcion) {
    this.origenDescripcion = origenDescripcion;
    return this;
  }

  /**
   * Descripción del origen
   * @return origenDescripcion
  **/
  @ApiModelProperty(example = "MAPFRE SEGUROS GUATEMALA", value = "Descripción del origen")


  public String getOrigenDescripcion() {
    return origenDescripcion;
  }

  public void setOrigenDescripcion(String origenDescripcion) {
    this.origenDescripcion = origenDescripcion;
  }

  public PycPeticionPerfil codciaDescripcion(String codciaDescripcion) {
    this.codciaDescripcion = codciaDescripcion;
    return this;
  }

  /**
   * Descripción del código de compañía
   * @return codciaDescripcion
  **/
  @ApiModelProperty(example = "MAPFRE SEGUROS GUATEMALA", value = "Descripción del código de compañía")


  public String getCodciaDescripcion() {
    return codciaDescripcion;
  }

  public void setCodciaDescripcion(String codciaDescripcion) {
    this.codciaDescripcion = codciaDescripcion;
  }

  public PycPeticionPerfil tipoCliente(String tipoCliente) {
    this.tipoCliente = tipoCliente;
    return this;
  }

  /**
   * Tipo de cliente
   * @return tipoCliente
  **/
  @ApiModelProperty(example = "TIPO_CLIENTE", value = "Tipo de cliente")


  public String getTipoCliente() {
    return tipoCliente;
  }

  public void setTipoCliente(String tipoCliente) {
    this.tipoCliente = tipoCliente;
  }

  public PycPeticionPerfil tipoClienteDescripcion(String tipoClienteDescripcion) {
    this.tipoClienteDescripcion = tipoClienteDescripcion;
    return this;
  }

  /**
   * Descripción del tipo de cliente
   * @return tipoClienteDescripcion
  **/
  @ApiModelProperty(example = "Descripción del tipo de cliente", value = "Descripción del tipo de cliente")


  public String getTipoClienteDescripcion() {
    return tipoClienteDescripcion;
  }

  public void setTipoClienteDescripcion(String tipoClienteDescripcion) {
    this.tipoClienteDescripcion = tipoClienteDescripcion;
  }

  public PycPeticionPerfil codCliente(String codCliente) {
    this.codCliente = codCliente;
    return this;
  }

  /**
   * Código del cliente
   * @return codCliente
  **/
  @ApiModelProperty(example = "COD_CLIENTE", value = "Código del cliente")


  public String getCodCliente() {
    return codCliente;
  }

  public void setCodCliente(String codCliente) {
    this.codCliente = codCliente;
  }

  public PycPeticionPerfil noPoliza(String noPoliza) {
    this.noPoliza = noPoliza;
    return this;
  }

  /**
   * Número de póliza
   * @return noPoliza
  **/
  @ApiModelProperty(example = "NO_POLIZA", value = "Número de póliza")


  public String getNoPoliza() {
    return noPoliza;
  }

  public void setNoPoliza(String noPoliza) {
    this.noPoliza = noPoliza;
  }

  public PycPeticionPerfil tipoServicio(String tipoServicio) {
    this.tipoServicio = tipoServicio;
    return this;
  }

  /**
   * Tipo de servicio
   * @return tipoServicio
  **/
  @ApiModelProperty(example = "TIPO_SERVICIO", value = "Tipo de servicio")


  public String getTipoServicio() {
    return tipoServicio;
  }

  public void setTipoServicio(String tipoServicio) {
    this.tipoServicio = tipoServicio;
  }

  public PycPeticionPerfil tipoServicioDescripcion(String tipoServicioDescripcion) {
    this.tipoServicioDescripcion = tipoServicioDescripcion;
    return this;
  }

  /**
   * Descripción del tipo de servicio
   * @return tipoServicioDescripcion
  **/
  @ApiModelProperty(example = "TIPO_SERVICIO_DESCRIPCION", value = "Descripción del tipo de servicio")


  public String getTipoServicioDescripcion() {
    return tipoServicioDescripcion;
  }

  public void setTipoServicioDescripcion(String tipoServicioDescripcion) {
    this.tipoServicioDescripcion = tipoServicioDescripcion;
  }

  public PycPeticionPerfil causa(String causa) {
    this.causa = causa;
    return this;
  }

  /**
   * Causa
   * @return causa
  **/
  @ApiModelProperty(example = "CAUSA", value = "Causa")


  public String getCausa() {
    return causa;
  }

  public void setCausa(String causa) {
    this.causa = causa;
  }

  public PycPeticionPerfil causaDescripcion(String causaDescripcion) {
    this.causaDescripcion = causaDescripcion;
    return this;
  }

  /**
   * Descripción de la causa
   * @return causaDescripcion
  **/
  @ApiModelProperty(example = "CAUSA_DESCRIPCION", value = "Descripción de la causa")


  public String getCausaDescripcion() {
    return causaDescripcion;
  }

  public void setCausaDescripcion(String causaDescripcion) {
    this.causaDescripcion = causaDescripcion;
  }

  public PycPeticionPerfil gravedad(String gravedad) {
    this.gravedad = gravedad;
    return this;
  }

  /**
   * Gravedad
   * @return gravedad
  **/
  @ApiModelProperty(example = "GRAVEDAD", value = "Gravedad")


  public String getGravedad() {
    return gravedad;
  }

  public void setGravedad(String gravedad) {
    this.gravedad = gravedad;
  }

  public PycPeticionPerfil gravedadDescripcion(String gravedadDescripcion) {
    this.gravedadDescripcion = gravedadDescripcion;
    return this;
  }

  /**
   * Descripción de la gravedad
   * @return gravedadDescripcion
  **/
  @ApiModelProperty(example = "GRAVEDAD_DESCRIPCION", value = "Descripción de la gravedad")


  public String getGravedadDescripcion() {
    return gravedadDescripcion;
  }

  public void setGravedadDescripcion(String gravedadDescripcion) {
    this.gravedadDescripcion = gravedadDescripcion;
  }

  public PycPeticionPerfil encuCalif(String encuCalif) {
    this.encuCalif = encuCalif;
    return this;
  }

  /**
   * Calificación de encuesta
   * @return encuCalif
  **/
  @ApiModelProperty(example = "ENCU_CALIF", value = "Calificación de encuesta")


  public String getEncuCalif() {
    return encuCalif;
  }

  public void setEncuCalif(String encuCalif) {
    this.encuCalif = encuCalif;
  }

  public PycPeticionPerfil encuComent(String encuComent) {
    this.encuComent = encuComent;
    return this;
  }

  /**
   * Comentario de encuesta
   * @return encuComent
  **/
  @ApiModelProperty(example = "ENCU_COMENT", value = "Comentario de encuesta")


  public String getEncuComent() {
    return encuComent;
  }

  public void setEncuComent(String encuComent) {
    this.encuComent = encuComent;
  }

  public PycPeticionPerfil encuComentAdi(String encuComentAdi) {
    this.encuComentAdi = encuComentAdi;
    return this;
  }

  /**
   * Comentario adicional de encuesta
   * @return encuComentAdi
  **/
  @ApiModelProperty(example = "ENCU_COMENT_ADI", value = "Comentario adicional de encuesta")


  public String getEncuComentAdi() {
    return encuComentAdi;
  }

  public void setEncuComentAdi(String encuComentAdi) {
    this.encuComentAdi = encuComentAdi;
  }

  public PycPeticionPerfil encuUser(String encuUser) {
    this.encuUser = encuUser;
    return this;
  }

  /**
   * Usuario de encuesta
   * @return encuUser
  **/
  @ApiModelProperty(example = "ENCU_USER", value = "Usuario de encuesta")


  public String getEncuUser() {
    return encuUser;
  }

  public void setEncuUser(String encuUser) {
    this.encuUser = encuUser;
  }

  public PycPeticionPerfil encuUsuario(String encuUsuario) {
    this.encuUsuario = encuUsuario;
    return this;
  }

  /**
   * Usuario que realizó la encuesta
   * @return encuUsuario
  **/
  @ApiModelProperty(example = "ENCU_USUARIO", value = "Usuario que realizó la encuesta")


  public String getEncuUsuario() {
    return encuUsuario;
  }

  public void setEncuUsuario(String encuUsuario) {
    this.encuUsuario = encuUsuario;
  }

  public PycPeticionPerfil encuFecha(String encuFecha) {
    this.encuFecha = encuFecha;
    return this;
  }

  /**
   * Fecha de encuesta
   * @return encuFecha
  **/
  @ApiModelProperty(example = "ENCU_FECHA", value = "Fecha de encuesta")


  public String getEncuFecha() {
    return encuFecha;
  }

  public void setEncuFecha(String encuFecha) {
    this.encuFecha = encuFecha;
  }

  public PycPeticionPerfil observaciones(String observaciones) {
    this.observaciones = observaciones;
    return this;
  }

  /**
   * Observaciones
   * @return observaciones
  **/
  @ApiModelProperty(example = "SE CONFIRMA CON EL CLIENTE", value = "Observaciones")


  public String getObservaciones() {
    return observaciones;
  }

  public void setObservaciones(String observaciones) {
    this.observaciones = observaciones;
  }

  public PycPeticionPerfil idArea(Integer idArea) {
    this.idArea = idArea;
    return this;
  }

  /**
   * Identificador del área
   * @return idArea
  **/
  @ApiModelProperty(example = "10", value = "Identificador del área")


  public Integer getIdArea() {
    return idArea;
  }

  public void setIdArea(Integer idArea) {
    this.idArea = idArea;
  }

  public PycPeticionPerfil nombreArea(String nombreArea) {
    this.nombreArea = nombreArea;
    return this;
  }

  /**
   * Nombre del área
   * @return nombreArea
  **/
  @ApiModelProperty(example = "OPERACIONES", value = "Nombre del área")


  public String getNombreArea() {
    return nombreArea;
  }

  public void setNombreArea(String nombreArea) {
    this.nombreArea = nombreArea;
  }

  public PycPeticionPerfil idDepartamento(Integer idDepartamento) {
    this.idDepartamento = idDepartamento;
    return this;
  }

  /**
   * Identificador del departamento
   * @return idDepartamento
  **/
  @ApiModelProperty(example = "10", value = "Identificador del departamento")


  public Integer getIdDepartamento() {
    return idDepartamento;
  }

  public void setIdDepartamento(Integer idDepartamento) {
    this.idDepartamento = idDepartamento;
  }

  public PycPeticionPerfil nombreDepartamento(String nombreDepartamento) {
    this.nombreDepartamento = nombreDepartamento;
    return this;
  }

  /**
   * Nombre del departamento
   * @return nombreDepartamento
  **/
  @ApiModelProperty(example = "SERVICIO AL CLIENTE", value = "Nombre del departamento")


  public String getNombreDepartamento() {
    return nombreDepartamento;
  }

  public void setNombreDepartamento(String nombreDepartamento) {
    this.nombreDepartamento = nombreDepartamento;
  }

  public PycPeticionPerfil idReferencia(String idReferencia) {
    this.idReferencia = idReferencia;
    return this;
  }

  /**
   * Identificador de referencia
   * @return idReferencia
  **/
  @ApiModelProperty(example = "ID_REFERENCIA", value = "Identificador de referencia")


  public String getIdReferencia() {
    return idReferencia;
  }

  public void setIdReferencia(String idReferencia) {
    this.idReferencia = idReferencia;
  }

  public PycPeticionPerfil perfilSolicitante(String perfilSolicitante) {
    this.perfilSolicitante = perfilSolicitante;
    return this;
  }

  /**
   * Perfil del solicitante
   * @return perfilSolicitante
  **/
  @ApiModelProperty(example = "PERFIL_SOLICITANTE", value = "Perfil del solicitante")


  public String getPerfilSolicitante() {
    return perfilSolicitante;
  }

  public void setPerfilSolicitante(String perfilSolicitante) {
    this.perfilSolicitante = perfilSolicitante;
  }

  public PycPeticionPerfil urlRedirect(String urlRedirect) {
    this.urlRedirect = urlRedirect;
    return this;
  }

  /**
   * URL de redirección
   * @return urlRedirect
  **/
  @ApiModelProperty(example = "https://app2.mapfre.com", value = "URL de redirección")


  public String getUrlRedirect() {
    return urlRedirect;
  }

  public void setUrlRedirect(String urlRedirect) {
    this.urlRedirect = urlRedirect;
  }

  public PycPeticionPerfil idResponsable(String idResponsable) {
    this.idResponsable = idResponsable;
    return this;
  }

  /**
   * Identificador del responsable
   * @return idResponsable
  **/
  @ApiModelProperty(example = "ID_RESPONSABLE", value = "Identificador del responsable")


  public String getIdResponsable() {
    return idResponsable;
  }

  public void setIdResponsable(String idResponsable) {
    this.idResponsable = idResponsable;
  }

  public PycPeticionPerfil responsable(String responsable) {
    this.responsable = responsable;
    return this;
  }

  /**
   * Responsable
   * @return responsable
  **/
  @ApiModelProperty(example = "RESPONSABLE", value = "Responsable")


  public String getResponsable() {
    return responsable;
  }

  public void setResponsable(String responsable) {
    this.responsable = responsable;
  }

  public PycPeticionPerfil perfilResponsable(String perfilResponsable) {
    this.perfilResponsable = perfilResponsable;
    return this;
  }

  /**
   * Perfil del responsable
   * @return perfilResponsable
  **/
  @ApiModelProperty(example = "PERFIL_RESPONSABLE", value = "Perfil del responsable")


  public String getPerfilResponsable() {
    return perfilResponsable;
  }

  public void setPerfilResponsable(String perfilResponsable) {
    this.perfilResponsable = perfilResponsable;
  }

  public PycPeticionPerfil areaResponsable(String areaResponsable) {
    this.areaResponsable = areaResponsable;
    return this;
  }

  /**
   * Área del responsable
   * @return areaResponsable
  **/
  @ApiModelProperty(example = "AREA_RESPONSABLE", value = "Área del responsable")


  public String getAreaResponsable() {
    return areaResponsable;
  }

  public void setAreaResponsable(String areaResponsable) {
    this.areaResponsable = areaResponsable;
  }

  public PycPeticionPerfil departamentoResponsable(String departamentoResponsable) {
    this.departamentoResponsable = departamentoResponsable;
    return this;
  }

  /**
   * Departamento del responsable
   * @return departamentoResponsable
  **/
  @ApiModelProperty(example = "DEPARTAMENTO_RESPONSABLE", value = "Departamento del responsable")


  public String getDepartamentoResponsable() {
    return departamentoResponsable;
  }

  public void setDepartamentoResponsable(String departamentoResponsable) {
    this.departamentoResponsable = departamentoResponsable;
  }

  public PycPeticionPerfil mcaRefId(String mcaRefId) {
    this.mcaRefId = mcaRefId;
    return this;
  }

  /**
   * Marca de referencia ID
   * @return mcaRefId
  **/
  @ApiModelProperty(example = "MCA_REF_ID", value = "Marca de referencia ID")


  public String getMcaRefId() {
    return mcaRefId;
  }

  public void setMcaRefId(String mcaRefId) {
    this.mcaRefId = mcaRefId;
  }

  public PycPeticionPerfil codinter(String codinter) {
    this.codinter = codinter;
    return this;
  }

  /**
   * Código interno
   * @return codinter
  **/
  @ApiModelProperty(example = "CODINTER", value = "Código interno")


  public String getCodinter() {
    return codinter;
  }

  public void setCodinter(String codinter) {
    this.codinter = codinter;
  }

  public PycPeticionPerfil ftrDocs(String ftrDocs) {
    this.ftrDocs = ftrDocs;
    return this;
  }

  /**
   * Documentos FTR
   * @return ftrDocs
  **/
  @ApiModelProperty(example = "FTR_DOCS", value = "Documentos FTR")


  public String getFtrDocs() {
    return ftrDocs;
  }

  public void setFtrDocs(String ftrDocs) {
    this.ftrDocs = ftrDocs;
  }

  public PycPeticionPerfil iconoEstado(String iconoEstado) {
    this.iconoEstado = iconoEstado;
    return this;
  }

  /**
   * Icono del estado
   * @return iconoEstado
  **/
  @ApiModelProperty(example = "N", value = "Icono del estado")


  public String getIconoEstado() {
    return iconoEstado;
  }

  public void setIconoEstado(String iconoEstado) {
    this.iconoEstado = iconoEstado;
  }

  public PycPeticionPerfil idFormulario(String idFormulario) {
    this.idFormulario = idFormulario;
    return this;
  }

  /**
   * Identificador del formulario
   * @return idFormulario
  **/
  @ApiModelProperty(example = "fa fa-user", value = "Identificador del formulario")


  public String getIdFormulario() {
    return idFormulario;
  }

  public void setIdFormulario(String idFormulario) {
    this.idFormulario = idFormulario;
  }

  public PycPeticionPerfil idSeccion(String idSeccion) {
    this.idSeccion = idSeccion;
    return this;
  }

  /**
   * Identificador de la sección
   * @return idSeccion
  **/
  @ApiModelProperty(example = "9", value = "Identificador de la sección")


  public String getIdSeccion() {
    return idSeccion;
  }

  public void setIdSeccion(String idSeccion) {
    this.idSeccion = idSeccion;
  }

  public PycPeticionPerfil numPoliza(String numPoliza) {
    this.numPoliza = numPoliza;
    return this;
  }

  /**
   * Número de póliza (campo adicional)
   * @return numPoliza
  **/
  @ApiModelProperty(example = "166295", value = "Número de póliza (campo adicional)")


  public String getNumPoliza() {
    return numPoliza;
  }

  public void setNumPoliza(String numPoliza) {
    this.numPoliza = numPoliza;
  }

  public PycPeticionPerfil numReferencia(String numReferencia) {
    this.numReferencia = numReferencia;
    return this;
  }

  /**
   * Número de referencia
   * @return numReferencia
  **/
  @ApiModelProperty(example = "166296", value = "Número de referencia")


  public String getNumReferencia() {
    return numReferencia;
  }

  public void setNumReferencia(String numReferencia) {
    this.numReferencia = numReferencia;
  }

  public PycPeticionPerfil campania(String campania) {
    this.campania = campania;
    return this;
  }

  /**
   * Campaña asociada
   * @return campania
  **/
  @ApiModelProperty(example = "COTIZADOR PUBLICO", value = "Campaña asociada")


  public String getCampania() {
    return campania;
  }

  public void setCampania(String campania) {
    this.campania = campania;
  }

  public PycPeticionPerfil nextStep(String nextStep) {
    this.nextStep = nextStep;
    return this;
  }

  /**
   * Siguiente paso
   * @return nextStep
  **/
  @ApiModelProperty(example = "warning", value = "Siguiente paso")


  public String getNextStep() {
    return nextStep;
  }

  public void setNextStep(String nextStep) {
    this.nextStep = nextStep;
  }

  public PycPeticionPerfil idProceso(Integer idProceso) {
    this.idProceso = idProceso;
    return this;
  }

  /**
   * Identificador del proceso
   * @return idProceso
  **/
  @ApiModelProperty(example = "3", value = "Identificador del proceso")


  public Integer getIdProceso() {
    return idProceso;
  }

  public void setIdProceso(Integer idProceso) {
    this.idProceso = idProceso;
  }

  public PycPeticionPerfil nombreProceso(String nombreProceso) {
    this.nombreProceso = nombreProceso;
    return this;
  }

  /**
   * Nombre del proceso
   * @return nombreProceso
  **/
  @ApiModelProperty(example = "Gestión de Peticiones", value = "Nombre del proceso")


  public String getNombreProceso() {
    return nombreProceso;
  }

  public void setNombreProceso(String nombreProceso) {
    this.nombreProceso = nombreProceso;
  }

  public PycPeticionPerfil nombreUsuarioSolicitante(String nombreUsuarioSolicitante) {
    this.nombreUsuarioSolicitante = nombreUsuarioSolicitante;
    return this;
  }

  /**
   * Nombre del usuario solicitante (alias de usuario)
   * @return nombreUsuarioSolicitante
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre del usuario solicitante (alias de usuario)")


  public String getNombreUsuarioSolicitante() {
    return nombreUsuarioSolicitante;
  }

  public void setNombreUsuarioSolicitante(String nombreUsuarioSolicitante) {
    this.nombreUsuarioSolicitante = nombreUsuarioSolicitante;
  }

  public PycPeticionPerfil idCanal(Integer idCanal) {
    this.idCanal = idCanal;
    return this;
  }

  /**
   * Identificador del canal
   * @return idCanal
  **/
  @ApiModelProperty(example = "1", value = "Identificador del canal")


  public Integer getIdCanal() {
    return idCanal;
  }

  public void setIdCanal(Integer idCanal) {
    this.idCanal = idCanal;
  }

  public PycPeticionPerfil nombreCanal(String nombreCanal) {
    this.nombreCanal = nombreCanal;
    return this;
  }

  /**
   * Nombre del canal (alias de origenDescripcion)
   * @return nombreCanal
  **/
  @ApiModelProperty(example = "Web", value = "Nombre del canal (alias de origenDescripcion)")


  public String getNombreCanal() {
    return nombreCanal;
  }

  public void setNombreCanal(String nombreCanal) {
    this.nombreCanal = nombreCanal;
  }

  public PycPeticionPerfil idOficina(Integer idOficina) {
    this.idOficina = idOficina;
    return this;
  }

  /**
   * Identificador de la oficina
   * @return idOficina
  **/
  @ApiModelProperty(example = "1", value = "Identificador de la oficina")


  public Integer getIdOficina() {
    return idOficina;
  }

  public void setIdOficina(Integer idOficina) {
    this.idOficina = idOficina;
  }

  public PycPeticionPerfil nombreOficina(String nombreOficina) {
    this.nombreOficina = nombreOficina;
    return this;
  }

  /**
   * Nombre de la oficina (alias de codciaDescripcion)
   * @return nombreOficina
  **/
  @ApiModelProperty(example = "Oficina Central", value = "Nombre de la oficina (alias de codciaDescripcion)")


  public String getNombreOficina() {
    return nombreOficina;
  }

  public void setNombreOficina(String nombreOficina) {
    this.nombreOficina = nombreOficina;
  }

  public PycPeticionPerfil candidato(String candidato) {
    this.candidato = candidato;
    return this;
  }

  /**
   * Nombre del candidato
   * @return candidato
  **/
  @ApiModelProperty(example = "Carlos Ricardo Davila Mancilla", value = "Nombre del candidato")


  public String getCandidato() {
    return candidato;
  }

  public void setCandidato(String candidato) {
    this.candidato = candidato;
  }

  public PycPeticionPerfil fechaNacimiento(String fechaNacimiento) {
    this.fechaNacimiento = fechaNacimiento;
    return this;
  }

  /**
   * Fecha de nacimiento (formato DD/MM/YYYY)
   * @return fechaNacimiento
  **/
  @ApiModelProperty(example = "11/02/1993", value = "Fecha de nacimiento (formato DD/MM/YYYY)")


  public String getFechaNacimiento() {
    return fechaNacimiento;
  }

  public void setFechaNacimiento(String fechaNacimiento) {
    this.fechaNacimiento = fechaNacimiento;
  }

  public PycPeticionPerfil edad(Integer edad) {
    this.edad = edad;
    return this;
  }

  /**
   * Edad del candidato
   * @return edad
  **/
  @ApiModelProperty(example = "32", value = "Edad del candidato")


  public Integer getEdad() {
    return edad;
  }

  public void setEdad(Integer edad) {
    this.edad = edad;
  }

  public PycPeticionPerfil nombrePlaza(String nombrePlaza) {
    this.nombrePlaza = nombrePlaza;
    return this;
  }

  /**
   * Nombre de la plaza
   * @return nombrePlaza
  **/
  @ApiModelProperty(example = "SERVICIO AL CLIENTE", value = "Nombre de la plaza")


  public String getNombrePlaza() {
    return nombrePlaza;
  }

  public void setNombrePlaza(String nombrePlaza) {
    this.nombrePlaza = nombrePlaza;
  }

  public PycPeticionPerfil dpi(String dpi) {
    this.dpi = dpi;
    return this;
  }

  /**
   * Documento de identificación personal
   * @return dpi
  **/
  @ApiModelProperty(example = "2344319670701", value = "Documento de identificación personal")


  public String getDpi() {
    return dpi;
  }

  public void setDpi(String dpi) {
    this.dpi = dpi;
  }

  public PycPeticionPerfil nit(String nit) {
    this.nit = nit;
    return this;
  }

  /**
   * Número de identificación tributaria
   * @return nit
  **/
  @ApiModelProperty(example = "8944309091", value = "Número de identificación tributaria")


  public String getNit() {
    return nit;
  }

  public void setNit(String nit) {
    this.nit = nit;
  }

  public PycPeticionPerfil escolaridad(String escolaridad) {
    this.escolaridad = escolaridad;
    return this;
  }

  /**
   * Nivel de escolaridad
   * @return escolaridad
  **/
  @ApiModelProperty(example = "UNIVERSIDAD COMPLETA", value = "Nivel de escolaridad")


  public String getEscolaridad() {
    return escolaridad;
  }

  public void setEscolaridad(String escolaridad) {
    this.escolaridad = escolaridad;
  }

  public PycPeticionPerfil tituloAcademico(String tituloAcademico) {
    this.tituloAcademico = tituloAcademico;
    return this;
  }

  /**
   * Título académico
   * @return tituloAcademico
  **/
  @ApiModelProperty(example = "Cierre de persona en psicología", value = "Título académico")


  public String getTituloAcademico() {
    return tituloAcademico;
  }

  public void setTituloAcademico(String tituloAcademico) {
    this.tituloAcademico = tituloAcademico;
  }

  public PycPeticionPerfil direccion(String direccion) {
    this.direccion = direccion;
    return this;
  }

  /**
   * Dirección del candidato
   * @return direccion
  **/
  @ApiModelProperty(example = "33 avenida 22-30 zona5", value = "Dirección del candidato")


  public String getDireccion() {
    return direccion;
  }

  public void setDireccion(String direccion) {
    this.direccion = direccion;
  }

  public PycPeticionPerfil departamentoDir(String departamentoDir) {
    this.departamentoDir = departamentoDir;
    return this;
  }

  /**
   * Departamento de dirección
   * @return departamentoDir
  **/
  @ApiModelProperty(example = "GUATEMALA", value = "Departamento de dirección")


  public String getDepartamentoDir() {
    return departamentoDir;
  }

  public void setDepartamentoDir(String departamentoDir) {
    this.departamentoDir = departamentoDir;
  }

  public PycPeticionPerfil trabajaActualmente(String trabajaActualmente) {
    this.trabajaActualmente = trabajaActualmente;
    return this;
  }

  /**
   * Indicador si trabaja actualmente
   * @return trabajaActualmente
  **/
  @ApiModelProperty(example = "N", value = "Indicador si trabaja actualmente")


  public String getTrabajaActualmente() {
    return trabajaActualmente;
  }

  public void setTrabajaActualmente(String trabajaActualmente) {
    this.trabajaActualmente = trabajaActualmente;
  }

  public PycPeticionPerfil experienciaSeguro(String experienciaSeguro) {
    this.experienciaSeguro = experienciaSeguro;
    return this;
  }

  /**
   * Experiencia en seguros
   * @return experienciaSeguro
  **/
  @ApiModelProperty(example = "N", value = "Experiencia en seguros")


  public String getExperienciaSeguro() {
    return experienciaSeguro;
  }

  public void setExperienciaSeguro(String experienciaSeguro) {
    this.experienciaSeguro = experienciaSeguro;
  }

  public PycPeticionPerfil pretensionSalarial(String pretensionSalarial) {
    this.pretensionSalarial = pretensionSalarial;
    return this;
  }

  /**
   * Pretensión salarial
   * @return pretensionSalarial
  **/
  @ApiModelProperty(example = "4000.00", value = "Pretensión salarial")


  public String getPretensionSalarial() {
    return pretensionSalarial;
  }

  public void setPretensionSalarial(String pretensionSalarial) {
    this.pretensionSalarial = pretensionSalarial;
  }

  public PycPeticionPerfil areaRrhh(String areaRrhh) {
    this.areaRrhh = areaRrhh;
    return this;
  }

  /**
   * Área de RRHH
   * @return areaRrhh
  **/
  @ApiModelProperty(example = "OPERACIONES", value = "Área de RRHH")


  public String getAreaRrhh() {
    return areaRrhh;
  }

  public void setAreaRrhh(String areaRrhh) {
    this.areaRrhh = areaRrhh;
  }

  public PycPeticionPerfil noSolicitud(Integer noSolicitud) {
    this.noSolicitud = noSolicitud;
    return this;
  }

  /**
   * Número de solicitud
   * @return noSolicitud
  **/
  @ApiModelProperty(example = "1", value = "Número de solicitud")


  public Integer getNoSolicitud() {
    return noSolicitud;
  }

  public void setNoSolicitud(Integer noSolicitud) {
    this.noSolicitud = noSolicitud;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycPeticionPerfil pycPeticionPerfil = (PycPeticionPerfil) o;
    return Objects.equals(this.idPeticion, pycPeticionPerfil.idPeticion) &&
        Objects.equals(this.tipoPeticion, pycPeticionPerfil.tipoPeticion) &&
        Objects.equals(this.nombrePeticion, pycPeticionPerfil.nombrePeticion) &&
        Objects.equals(this.descripcionPeticion, pycPeticionPerfil.descripcionPeticion) &&
        Objects.equals(this.fechaCreacion, pycPeticionPerfil.fechaCreacion) &&
        Objects.equals(this.idTipo, pycPeticionPerfil.idTipo) &&
        Objects.equals(this.idUsuarioSolicitante, pycPeticionPerfil.idUsuarioSolicitante) &&
        Objects.equals(this.usuario, pycPeticionPerfil.usuario) &&
        Objects.equals(this.fechaInicio, pycPeticionPerfil.fechaInicio) &&
        Objects.equals(this.fechaFin, pycPeticionPerfil.fechaFin) &&
        Objects.equals(this.totalHoras, pycPeticionPerfil.totalHoras) &&
        Objects.equals(this.porcentajeBase, pycPeticionPerfil.porcentajeBase) &&
        Objects.equals(this.porcentajeReal, pycPeticionPerfil.porcentajeReal) &&
        Objects.equals(this.idEstado, pycPeticionPerfil.idEstado) &&
        Objects.equals(this.nombreEstado, pycPeticionPerfil.nombreEstado) &&
        Objects.equals(this.colorEstado, pycPeticionPerfil.colorEstado) &&
        Objects.equals(this.idPerfil, pycPeticionPerfil.idPerfil) &&
        Objects.equals(this.nombrePerfil, pycPeticionPerfil.nombrePerfil) &&
        Objects.equals(this.usuarioGraba, pycPeticionPerfil.usuarioGraba) &&
        Objects.equals(this.usuarioSoli, pycPeticionPerfil.usuarioSoli) &&
        Objects.equals(this.origen, pycPeticionPerfil.origen) &&
        Objects.equals(this.codClarity, pycPeticionPerfil.codClarity) &&
        Objects.equals(this.codcia, pycPeticionPerfil.codcia) &&
        Objects.equals(this.prioridad, pycPeticionPerfil.prioridad) &&
        Objects.equals(this.prioriDesc, pycPeticionPerfil.prioriDesc) &&
        Objects.equals(this.idPeticionSiguiente, pycPeticionPerfil.idPeticionSiguiente) &&
        Objects.equals(this.tituloSiguiente, pycPeticionPerfil.tituloSiguiente) &&
        Objects.equals(this.nombreCliente, pycPeticionPerfil.nombreCliente) &&
        Objects.equals(this.telefonoCliente, pycPeticionPerfil.telefonoCliente) &&
        Objects.equals(this.correoCliente, pycPeticionPerfil.correoCliente) &&
        Objects.equals(this.origenDescripcion, pycPeticionPerfil.origenDescripcion) &&
        Objects.equals(this.codciaDescripcion, pycPeticionPerfil.codciaDescripcion) &&
        Objects.equals(this.tipoCliente, pycPeticionPerfil.tipoCliente) &&
        Objects.equals(this.tipoClienteDescripcion, pycPeticionPerfil.tipoClienteDescripcion) &&
        Objects.equals(this.codCliente, pycPeticionPerfil.codCliente) &&
        Objects.equals(this.noPoliza, pycPeticionPerfil.noPoliza) &&
        Objects.equals(this.tipoServicio, pycPeticionPerfil.tipoServicio) &&
        Objects.equals(this.tipoServicioDescripcion, pycPeticionPerfil.tipoServicioDescripcion) &&
        Objects.equals(this.causa, pycPeticionPerfil.causa) &&
        Objects.equals(this.causaDescripcion, pycPeticionPerfil.causaDescripcion) &&
        Objects.equals(this.gravedad, pycPeticionPerfil.gravedad) &&
        Objects.equals(this.gravedadDescripcion, pycPeticionPerfil.gravedadDescripcion) &&
        Objects.equals(this.encuCalif, pycPeticionPerfil.encuCalif) &&
        Objects.equals(this.encuComent, pycPeticionPerfil.encuComent) &&
        Objects.equals(this.encuComentAdi, pycPeticionPerfil.encuComentAdi) &&
        Objects.equals(this.encuUser, pycPeticionPerfil.encuUser) &&
        Objects.equals(this.encuUsuario, pycPeticionPerfil.encuUsuario) &&
        Objects.equals(this.encuFecha, pycPeticionPerfil.encuFecha) &&
        Objects.equals(this.observaciones, pycPeticionPerfil.observaciones) &&
        Objects.equals(this.idArea, pycPeticionPerfil.idArea) &&
        Objects.equals(this.nombreArea, pycPeticionPerfil.nombreArea) &&
        Objects.equals(this.idDepartamento, pycPeticionPerfil.idDepartamento) &&
        Objects.equals(this.nombreDepartamento, pycPeticionPerfil.nombreDepartamento) &&
        Objects.equals(this.idReferencia, pycPeticionPerfil.idReferencia) &&
        Objects.equals(this.perfilSolicitante, pycPeticionPerfil.perfilSolicitante) &&
        Objects.equals(this.urlRedirect, pycPeticionPerfil.urlRedirect) &&
        Objects.equals(this.idResponsable, pycPeticionPerfil.idResponsable) &&
        Objects.equals(this.responsable, pycPeticionPerfil.responsable) &&
        Objects.equals(this.perfilResponsable, pycPeticionPerfil.perfilResponsable) &&
        Objects.equals(this.areaResponsable, pycPeticionPerfil.areaResponsable) &&
        Objects.equals(this.departamentoResponsable, pycPeticionPerfil.departamentoResponsable) &&
        Objects.equals(this.mcaRefId, pycPeticionPerfil.mcaRefId) &&
        Objects.equals(this.codinter, pycPeticionPerfil.codinter) &&
        Objects.equals(this.ftrDocs, pycPeticionPerfil.ftrDocs) &&
        Objects.equals(this.iconoEstado, pycPeticionPerfil.iconoEstado) &&
        Objects.equals(this.idFormulario, pycPeticionPerfil.idFormulario) &&
        Objects.equals(this.idSeccion, pycPeticionPerfil.idSeccion) &&
        Objects.equals(this.numPoliza, pycPeticionPerfil.numPoliza) &&
        Objects.equals(this.numReferencia, pycPeticionPerfil.numReferencia) &&
        Objects.equals(this.campania, pycPeticionPerfil.campania) &&
        Objects.equals(this.nextStep, pycPeticionPerfil.nextStep) &&
        Objects.equals(this.idProceso, pycPeticionPerfil.idProceso) &&
        Objects.equals(this.nombreProceso, pycPeticionPerfil.nombreProceso) &&
        Objects.equals(this.nombreUsuarioSolicitante, pycPeticionPerfil.nombreUsuarioSolicitante) &&
        Objects.equals(this.idCanal, pycPeticionPerfil.idCanal) &&
        Objects.equals(this.nombreCanal, pycPeticionPerfil.nombreCanal) &&
        Objects.equals(this.idOficina, pycPeticionPerfil.idOficina) &&
        Objects.equals(this.nombreOficina, pycPeticionPerfil.nombreOficina) &&
        Objects.equals(this.candidato, pycPeticionPerfil.candidato) &&
        Objects.equals(this.fechaNacimiento, pycPeticionPerfil.fechaNacimiento) &&
        Objects.equals(this.edad, pycPeticionPerfil.edad) &&
        Objects.equals(this.nombrePlaza, pycPeticionPerfil.nombrePlaza) &&
        Objects.equals(this.dpi, pycPeticionPerfil.dpi) &&
        Objects.equals(this.nit, pycPeticionPerfil.nit) &&
        Objects.equals(this.escolaridad, pycPeticionPerfil.escolaridad) &&
        Objects.equals(this.tituloAcademico, pycPeticionPerfil.tituloAcademico) &&
        Objects.equals(this.direccion, pycPeticionPerfil.direccion) &&
        Objects.equals(this.departamentoDir, pycPeticionPerfil.departamentoDir) &&
        Objects.equals(this.trabajaActualmente, pycPeticionPerfil.trabajaActualmente) &&
        Objects.equals(this.experienciaSeguro, pycPeticionPerfil.experienciaSeguro) &&
        Objects.equals(this.pretensionSalarial, pycPeticionPerfil.pretensionSalarial) &&
        Objects.equals(this.areaRrhh, pycPeticionPerfil.areaRrhh) &&
        Objects.equals(this.noSolicitud, pycPeticionPerfil.noSolicitud);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, tipoPeticion, nombrePeticion, descripcionPeticion, fechaCreacion, idTipo, idUsuarioSolicitante, usuario, fechaInicio, fechaFin, totalHoras, porcentajeBase, porcentajeReal, idEstado, nombreEstado, colorEstado, idPerfil, nombrePerfil, usuarioGraba, usuarioSoli, origen, codClarity, codcia, prioridad, prioriDesc, idPeticionSiguiente, tituloSiguiente, nombreCliente, telefonoCliente, correoCliente, origenDescripcion, codciaDescripcion, tipoCliente, tipoClienteDescripcion, codCliente, noPoliza, tipoServicio, tipoServicioDescripcion, causa, causaDescripcion, gravedad, gravedadDescripcion, encuCalif, encuComent, encuComentAdi, encuUser, encuUsuario, encuFecha, observaciones, idArea, nombreArea, idDepartamento, nombreDepartamento, idReferencia, perfilSolicitante, urlRedirect, idResponsable, responsable, perfilResponsable, areaResponsable, departamentoResponsable, mcaRefId, codinter, ftrDocs, iconoEstado, idFormulario, idSeccion, numPoliza, numReferencia, campania, nextStep, idProceso, nombreProceso, nombreUsuarioSolicitante, idCanal, nombreCanal, idOficina, nombreOficina, candidato, fechaNacimiento, edad, nombrePlaza, dpi, nit, escolaridad, tituloAcademico, direccion, departamentoDir, trabajaActualmente, experienciaSeguro, pretensionSalarial, areaRrhh, noSolicitud);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycPeticionPerfil {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    tipoPeticion: ").append(toIndentedString(tipoPeticion)).append("\n");
    sb.append("    nombrePeticion: ").append(toIndentedString(nombrePeticion)).append("\n");
    sb.append("    descripcionPeticion: ").append(toIndentedString(descripcionPeticion)).append("\n");
    sb.append("    fechaCreacion: ").append(toIndentedString(fechaCreacion)).append("\n");
    sb.append("    idTipo: ").append(toIndentedString(idTipo)).append("\n");
    sb.append("    idUsuarioSolicitante: ").append(toIndentedString(idUsuarioSolicitante)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    fechaInicio: ").append(toIndentedString(fechaInicio)).append("\n");
    sb.append("    fechaFin: ").append(toIndentedString(fechaFin)).append("\n");
    sb.append("    totalHoras: ").append(toIndentedString(totalHoras)).append("\n");
    sb.append("    porcentajeBase: ").append(toIndentedString(porcentajeBase)).append("\n");
    sb.append("    porcentajeReal: ").append(toIndentedString(porcentajeReal)).append("\n");
    sb.append("    idEstado: ").append(toIndentedString(idEstado)).append("\n");
    sb.append("    nombreEstado: ").append(toIndentedString(nombreEstado)).append("\n");
    sb.append("    colorEstado: ").append(toIndentedString(colorEstado)).append("\n");
    sb.append("    idPerfil: ").append(toIndentedString(idPerfil)).append("\n");
    sb.append("    nombrePerfil: ").append(toIndentedString(nombrePerfil)).append("\n");
    sb.append("    usuarioGraba: ").append(toIndentedString(usuarioGraba)).append("\n");
    sb.append("    usuarioSoli: ").append(toIndentedString(usuarioSoli)).append("\n");
    sb.append("    origen: ").append(toIndentedString(origen)).append("\n");
    sb.append("    codClarity: ").append(toIndentedString(codClarity)).append("\n");
    sb.append("    codcia: ").append(toIndentedString(codcia)).append("\n");
    sb.append("    prioridad: ").append(toIndentedString(prioridad)).append("\n");
    sb.append("    prioriDesc: ").append(toIndentedString(prioriDesc)).append("\n");
    sb.append("    idPeticionSiguiente: ").append(toIndentedString(idPeticionSiguiente)).append("\n");
    sb.append("    tituloSiguiente: ").append(toIndentedString(tituloSiguiente)).append("\n");
    sb.append("    nombreCliente: ").append(toIndentedString(nombreCliente)).append("\n");
    sb.append("    telefonoCliente: ").append(toIndentedString(telefonoCliente)).append("\n");
    sb.append("    correoCliente: ").append(toIndentedString(correoCliente)).append("\n");
    sb.append("    origenDescripcion: ").append(toIndentedString(origenDescripcion)).append("\n");
    sb.append("    codciaDescripcion: ").append(toIndentedString(codciaDescripcion)).append("\n");
    sb.append("    tipoCliente: ").append(toIndentedString(tipoCliente)).append("\n");
    sb.append("    tipoClienteDescripcion: ").append(toIndentedString(tipoClienteDescripcion)).append("\n");
    sb.append("    codCliente: ").append(toIndentedString(codCliente)).append("\n");
    sb.append("    noPoliza: ").append(toIndentedString(noPoliza)).append("\n");
    sb.append("    tipoServicio: ").append(toIndentedString(tipoServicio)).append("\n");
    sb.append("    tipoServicioDescripcion: ").append(toIndentedString(tipoServicioDescripcion)).append("\n");
    sb.append("    causa: ").append(toIndentedString(causa)).append("\n");
    sb.append("    causaDescripcion: ").append(toIndentedString(causaDescripcion)).append("\n");
    sb.append("    gravedad: ").append(toIndentedString(gravedad)).append("\n");
    sb.append("    gravedadDescripcion: ").append(toIndentedString(gravedadDescripcion)).append("\n");
    sb.append("    encuCalif: ").append(toIndentedString(encuCalif)).append("\n");
    sb.append("    encuComent: ").append(toIndentedString(encuComent)).append("\n");
    sb.append("    encuComentAdi: ").append(toIndentedString(encuComentAdi)).append("\n");
    sb.append("    encuUser: ").append(toIndentedString(encuUser)).append("\n");
    sb.append("    encuUsuario: ").append(toIndentedString(encuUsuario)).append("\n");
    sb.append("    encuFecha: ").append(toIndentedString(encuFecha)).append("\n");
    sb.append("    observaciones: ").append(toIndentedString(observaciones)).append("\n");
    sb.append("    idArea: ").append(toIndentedString(idArea)).append("\n");
    sb.append("    nombreArea: ").append(toIndentedString(nombreArea)).append("\n");
    sb.append("    idDepartamento: ").append(toIndentedString(idDepartamento)).append("\n");
    sb.append("    nombreDepartamento: ").append(toIndentedString(nombreDepartamento)).append("\n");
    sb.append("    idReferencia: ").append(toIndentedString(idReferencia)).append("\n");
    sb.append("    perfilSolicitante: ").append(toIndentedString(perfilSolicitante)).append("\n");
    sb.append("    urlRedirect: ").append(toIndentedString(urlRedirect)).append("\n");
    sb.append("    idResponsable: ").append(toIndentedString(idResponsable)).append("\n");
    sb.append("    responsable: ").append(toIndentedString(responsable)).append("\n");
    sb.append("    perfilResponsable: ").append(toIndentedString(perfilResponsable)).append("\n");
    sb.append("    areaResponsable: ").append(toIndentedString(areaResponsable)).append("\n");
    sb.append("    departamentoResponsable: ").append(toIndentedString(departamentoResponsable)).append("\n");
    sb.append("    mcaRefId: ").append(toIndentedString(mcaRefId)).append("\n");
    sb.append("    codinter: ").append(toIndentedString(codinter)).append("\n");
    sb.append("    ftrDocs: ").append(toIndentedString(ftrDocs)).append("\n");
    sb.append("    iconoEstado: ").append(toIndentedString(iconoEstado)).append("\n");
    sb.append("    idFormulario: ").append(toIndentedString(idFormulario)).append("\n");
    sb.append("    idSeccion: ").append(toIndentedString(idSeccion)).append("\n");
    sb.append("    numPoliza: ").append(toIndentedString(numPoliza)).append("\n");
    sb.append("    numReferencia: ").append(toIndentedString(numReferencia)).append("\n");
    sb.append("    campania: ").append(toIndentedString(campania)).append("\n");
    sb.append("    nextStep: ").append(toIndentedString(nextStep)).append("\n");
    sb.append("    idProceso: ").append(toIndentedString(idProceso)).append("\n");
    sb.append("    nombreProceso: ").append(toIndentedString(nombreProceso)).append("\n");
    sb.append("    nombreUsuarioSolicitante: ").append(toIndentedString(nombreUsuarioSolicitante)).append("\n");
    sb.append("    idCanal: ").append(toIndentedString(idCanal)).append("\n");
    sb.append("    nombreCanal: ").append(toIndentedString(nombreCanal)).append("\n");
    sb.append("    idOficina: ").append(toIndentedString(idOficina)).append("\n");
    sb.append("    nombreOficina: ").append(toIndentedString(nombreOficina)).append("\n");
    sb.append("    candidato: ").append(toIndentedString(candidato)).append("\n");
    sb.append("    fechaNacimiento: ").append(toIndentedString(fechaNacimiento)).append("\n");
    sb.append("    edad: ").append(toIndentedString(edad)).append("\n");
    sb.append("    nombrePlaza: ").append(toIndentedString(nombrePlaza)).append("\n");
    sb.append("    dpi: ").append(toIndentedString(dpi)).append("\n");
    sb.append("    nit: ").append(toIndentedString(nit)).append("\n");
    sb.append("    escolaridad: ").append(toIndentedString(escolaridad)).append("\n");
    sb.append("    tituloAcademico: ").append(toIndentedString(tituloAcademico)).append("\n");
    sb.append("    direccion: ").append(toIndentedString(direccion)).append("\n");
    sb.append("    departamentoDir: ").append(toIndentedString(departamentoDir)).append("\n");
    sb.append("    trabajaActualmente: ").append(toIndentedString(trabajaActualmente)).append("\n");
    sb.append("    experienciaSeguro: ").append(toIndentedString(experienciaSeguro)).append("\n");
    sb.append("    pretensionSalarial: ").append(toIndentedString(pretensionSalarial)).append("\n");
    sb.append("    areaRrhh: ").append(toIndentedString(areaRrhh)).append("\n");
    sb.append("    noSolicitud: ").append(toIndentedString(noSolicitud)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

