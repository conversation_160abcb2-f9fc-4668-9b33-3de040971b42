package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycGetUsuariosAsig
 */
@Validated

public class PycGetUsuariosAsig   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("idProceso")
  private Integer idProceso = null;

  @JsonProperty("nombre")
  private String nombre = null;

  @JsonProperty("idAplicacion")
  private Integer idAplicacion = null;

  @JsonProperty("idPerfil")
  private Integer idPerfil = null;

  @JsonProperty("idEstado")
  private Integer idEstado = null;

  @JsonProperty("indAutomatico")
  private String indAutomatico = null;

  @JsonProperty("indAdmin")
  private String indAdmin = null;

  @JsonProperty("urlPerfil")
  private String urlPerfil = null;

  public PycGetUsuariosAsig idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Identificador del usuario
   * @return idUsuario
  **/
  @ApiModelProperty(example = "1234", value = "Identificador del usuario")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycGetUsuariosAsig idProceso(Integer idProceso) {
    this.idProceso = idProceso;
    return this;
  }

  /**
   * Identificador del proceso
   * @return idProceso
  **/
  @ApiModelProperty(example = "1", value = "Identificador del proceso")


  public Integer getIdProceso() {
    return idProceso;
  }

  public void setIdProceso(Integer idProceso) {
    this.idProceso = idProceso;
  }

  public PycGetUsuariosAsig nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre completo del usuario
   * @return nombre
  **/
  @ApiModelProperty(example = "Juan Carlos Pérez", value = "Nombre completo del usuario")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public PycGetUsuariosAsig idAplicacion(Integer idAplicacion) {
    this.idAplicacion = idAplicacion;
    return this;
  }

  /**
   * Identificador de la aplicación
   * @return idAplicacion
  **/
  @ApiModelProperty(example = "2", value = "Identificador de la aplicación")


  public Integer getIdAplicacion() {
    return idAplicacion;
  }

  public void setIdAplicacion(Integer idAplicacion) {
    this.idAplicacion = idAplicacion;
  }

  public PycGetUsuariosAsig idPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
    return this;
  }

  /**
   * Identificador del perfil
   * @return idPerfil
  **/
  @ApiModelProperty(example = "3", value = "Identificador del perfil")


  public Integer getIdPerfil() {
    return idPerfil;
  }

  public void setIdPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
  }

  public PycGetUsuariosAsig idEstado(Integer idEstado) {
    this.idEstado = idEstado;
    return this;
  }

  /**
   * Identificador del estado
   * @return idEstado
  **/
  @ApiModelProperty(example = "1", value = "Identificador del estado")


  public Integer getIdEstado() {
    return idEstado;
  }

  public void setIdEstado(Integer idEstado) {
    this.idEstado = idEstado;
  }

  public PycGetUsuariosAsig indAutomatico(String indAutomatico) {
    this.indAutomatico = indAutomatico;
    return this;
  }

  /**
   * Indicador de asignación automática
   * @return indAutomatico
  **/
  @ApiModelProperty(example = "S", value = "Indicador de asignación automática")


  public String getIndAutomatico() {
    return indAutomatico;
  }

  public void setIndAutomatico(String indAutomatico) {
    this.indAutomatico = indAutomatico;
  }

  public PycGetUsuariosAsig indAdmin(String indAdmin) {
    this.indAdmin = indAdmin;
    return this;
  }

  /**
   * Indicador de administrador
   * @return indAdmin
  **/
  @ApiModelProperty(example = "S", value = "Indicador de administrador")


  public String getIndAdmin() {
    return indAdmin;
  }

  public void setIndAdmin(String indAdmin) {
    this.indAdmin = indAdmin;
  }

  public PycGetUsuariosAsig urlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
    return this;
  }

  /**
   * URL de la imagen de perfil del usuario
   * @return urlPerfil
  **/
  @ApiModelProperty(example = "https://ejemplo.com/perfil/usuario.jpg", value = "URL de la imagen de perfil del usuario")


  public String getUrlPerfil() {
    return urlPerfil;
  }

  public void setUrlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycGetUsuariosAsig pycGetUsuariosAsig = (PycGetUsuariosAsig) o;
    return Objects.equals(this.idUsuario, pycGetUsuariosAsig.idUsuario) &&
        Objects.equals(this.idProceso, pycGetUsuariosAsig.idProceso) &&
        Objects.equals(this.nombre, pycGetUsuariosAsig.nombre) &&
        Objects.equals(this.idAplicacion, pycGetUsuariosAsig.idAplicacion) &&
        Objects.equals(this.idPerfil, pycGetUsuariosAsig.idPerfil) &&
        Objects.equals(this.idEstado, pycGetUsuariosAsig.idEstado) &&
        Objects.equals(this.indAutomatico, pycGetUsuariosAsig.indAutomatico) &&
        Objects.equals(this.indAdmin, pycGetUsuariosAsig.indAdmin) &&
        Objects.equals(this.urlPerfil, pycGetUsuariosAsig.urlPerfil);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, idProceso, nombre, idAplicacion, idPerfil, idEstado, indAutomatico, indAdmin, urlPerfil);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycGetUsuariosAsig {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    idProceso: ").append(toIndentedString(idProceso)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    idAplicacion: ").append(toIndentedString(idAplicacion)).append("\n");
    sb.append("    idPerfil: ").append(toIndentedString(idPerfil)).append("\n");
    sb.append("    idEstado: ").append(toIndentedString(idEstado)).append("\n");
    sb.append("    indAutomatico: ").append(toIndentedString(indAutomatico)).append("\n");
    sb.append("    indAdmin: ").append(toIndentedString(indAdmin)).append("\n");
    sb.append("    urlPerfil: ").append(toIndentedString(urlPerfil)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

