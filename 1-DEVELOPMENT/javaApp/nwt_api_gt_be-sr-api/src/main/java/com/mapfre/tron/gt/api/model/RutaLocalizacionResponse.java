package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * RutaLocalizacionResponse
 */
@Validated

public class RutaLocalizacionResponse   {
  @JsonProperty("ruta")
  private Integer ruta = null;

  @JsonProperty("fecha")
  private String fecha = null;

  @JsonProperty("cantidad")
  private Double cantidad = null;

  @JsonProperty("cantidadQ")
  private Double cantidadQ = null;

  @JsonProperty("cantidadD")
  private Double cantidadD = null;

  @JsonProperty("cantidadCobros")
  private Integer cantidadCobros = null;

  public RutaLocalizacionResponse ruta(Integer ruta) {
    this.ruta = ruta;
    return this;
  }

  /**
   * Id  de la ruta
   * @return ruta
  **/
  @ApiModelProperty(example = "4", value = "Id  de la ruta")


  public Integer getRuta() {
    return ruta;
  }

  public void setRuta(Integer ruta) {
    this.ruta = ruta;
  }

  public RutaLocalizacionResponse fecha(String fecha) {
    this.fecha = fecha;
    return this;
  }

  /**
   * Fecha
   * @return fecha
  **/
  @ApiModelProperty(example = "16-12-2020 03:52 PM", value = "Fecha")


  public String getFecha() {
    return fecha;
  }

  public void setFecha(String fecha) {
    this.fecha = fecha;
  }

  public RutaLocalizacionResponse cantidad(Double cantidad) {
    this.cantidad = cantidad;
    return this;
  }

  /**
   * cantidad
   * @return cantidad
  **/
  @ApiModelProperty(example = "45960.33", value = "cantidad")


  public Double getCantidad() {
    return cantidad;
  }

  public void setCantidad(Double cantidad) {
    this.cantidad = cantidad;
  }

  public RutaLocalizacionResponse cantidadQ(Double cantidadQ) {
    this.cantidadQ = cantidadQ;
    return this;
  }

  /**
   * Cantidad total en quetzales
   * @return cantidadQ
  **/
  @ApiModelProperty(example = "45960.33", value = "Cantidad total en quetzales")


  public Double getCantidadQ() {
    return cantidadQ;
  }

  public void setCantidadQ(Double cantidadQ) {
    this.cantidadQ = cantidadQ;
  }

  public RutaLocalizacionResponse cantidadD(Double cantidadD) {
    this.cantidadD = cantidadD;
    return this;
  }

  /**
   * Cantidad total en dólares
   * @return cantidadD
  **/
  @ApiModelProperty(example = "45960.33", value = "Cantidad total en dólares")


  public Double getCantidadD() {
    return cantidadD;
  }

  public void setCantidadD(Double cantidadD) {
    this.cantidadD = cantidadD;
  }

  public RutaLocalizacionResponse cantidadCobros(Integer cantidadCobros) {
    this.cantidadCobros = cantidadCobros;
    return this;
  }

  /**
   * Cantidad de cobros realizados
   * @return cantidadCobros
  **/
  @ApiModelProperty(example = "9", value = "Cantidad de cobros realizados")


  public Integer getCantidadCobros() {
    return cantidadCobros;
  }

  public void setCantidadCobros(Integer cantidadCobros) {
    this.cantidadCobros = cantidadCobros;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RutaLocalizacionResponse rutaLocalizacionResponse = (RutaLocalizacionResponse) o;
    return Objects.equals(this.ruta, rutaLocalizacionResponse.ruta) &&
        Objects.equals(this.fecha, rutaLocalizacionResponse.fecha) &&
        Objects.equals(this.cantidad, rutaLocalizacionResponse.cantidad) &&
        Objects.equals(this.cantidadQ, rutaLocalizacionResponse.cantidadQ) &&
        Objects.equals(this.cantidadD, rutaLocalizacionResponse.cantidadD) &&
        Objects.equals(this.cantidadCobros, rutaLocalizacionResponse.cantidadCobros);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ruta, fecha, cantidad, cantidadQ, cantidadD, cantidadCobros);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RutaLocalizacionResponse {\n");
    
    sb.append("    ruta: ").append(toIndentedString(ruta)).append("\n");
    sb.append("    fecha: ").append(toIndentedString(fecha)).append("\n");
    sb.append("    cantidad: ").append(toIndentedString(cantidad)).append("\n");
    sb.append("    cantidadQ: ").append(toIndentedString(cantidadQ)).append("\n");
    sb.append("    cantidadD: ").append(toIndentedString(cantidadD)).append("\n");
    sb.append("    cantidadCobros: ").append(toIndentedString(cantidadCobros)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

