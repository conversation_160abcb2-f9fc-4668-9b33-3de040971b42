package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycSrcMultipleOption
 */
@Validated

public class PycSrcMultipleOption   {
  @JsonProperty("hasMultipleOption")
  private Integer hasMultipleOption = null;

  public PycSrcMultipleOption hasMultipleOption(Integer hasMultipleOption) {
    this.hasMultipleOption = hasMultipleOption;
    return this;
  }

  /**
   * Indica si el perfil tiene la opción de selección múltiple (1 = sí)
   * @return hasMultipleOption
  **/
  @ApiModelProperty(example = "1", value = "Indica si el perfil tiene la opción de selección múltiple (1 = sí)")


  public Integer getHasMultipleOption() {
    return hasMultipleOption;
  }

  public void setHasMultipleOption(Integer hasMultipleOption) {
    this.hasMultipleOption = hasMultipleOption;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycSrcMultipleOption pycSrcMultipleOption = (PycSrcMultipleOption) o;
    return Objects.equals(this.hasMultipleOption, pycSrcMultipleOption.hasMultipleOption);
  }

  @Override
  public int hashCode() {
    return Objects.hash(hasMultipleOption);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycSrcMultipleOption {\n");
    
    sb.append("    hasMultipleOption: ").append(toIndentedString(hasMultipleOption)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

