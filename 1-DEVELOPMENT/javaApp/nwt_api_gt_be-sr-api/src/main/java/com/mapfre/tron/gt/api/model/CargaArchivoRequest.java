package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.mapfre.tron.gt.api.model.ArchivoParaCarga;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CargaArchivoRequest
 */
@Validated

public class CargaArchivoRequest   {
  @JsonProperty("idConexion")
  private String idConexion = null;

  @JsonProperty("idDirectorio")
  private String idDirectorio = null;

  @JsonProperty("archivos")
  @Valid
  private List<ArchivoParaCarga> archivos = new ArrayList<>();

  public CargaArchivoRequest idConexion(String idConexion) {
    this.idConexion = idConexion;
    return this;
  }

  /**
   * Identificador de la conexión FTP
   * @return idConexion
  **/
  @ApiModelProperty(example = "FTP_SRV_01", required = true, value = "Identificador de la conexión FTP")
  @NotNull


  public String getIdConexion() {
    return idConexion;
  }

  public void setIdConexion(String idConexion) {
    this.idConexion = idConexion;
  }

  public CargaArchivoRequest idDirectorio(String idDirectorio) {
    this.idDirectorio = idDirectorio;
    return this;
  }

  /**
   * Identificador del directorio destino
   * @return idDirectorio
  **/
  @ApiModelProperty(example = "doc_pycges_tecnicos", required = true, value = "Identificador del directorio destino")
  @NotNull


  public String getIdDirectorio() {
    return idDirectorio;
  }

  public void setIdDirectorio(String idDirectorio) {
    this.idDirectorio = idDirectorio;
  }

  public CargaArchivoRequest archivos(List<ArchivoParaCarga> archivos) {
    this.archivos = archivos;
    return this;
  }

  public CargaArchivoRequest addArchivosItem(ArchivoParaCarga archivosItem) {
    this.archivos.add(archivosItem);
    return this;
  }

  /**
   * Lista de archivos a cargar
   * @return archivos
  **/
  @ApiModelProperty(required = true, value = "Lista de archivos a cargar")
  @NotNull

  @Valid

  public List<ArchivoParaCarga> getArchivos() {
    return archivos;
  }

  public void setArchivos(List<ArchivoParaCarga> archivos) {
    this.archivos = archivos;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CargaArchivoRequest cargaArchivoRequest = (CargaArchivoRequest) o;
    return Objects.equals(this.idConexion, cargaArchivoRequest.idConexion) &&
        Objects.equals(this.idDirectorio, cargaArchivoRequest.idDirectorio) &&
        Objects.equals(this.archivos, cargaArchivoRequest.archivos);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idConexion, idDirectorio, archivos);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CargaArchivoRequest {\n");
    
    sb.append("    idConexion: ").append(toIndentedString(idConexion)).append("\n");
    sb.append("    idDirectorio: ").append(toIndentedString(idDirectorio)).append("\n");
    sb.append("    archivos: ").append(toIndentedString(archivos)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

