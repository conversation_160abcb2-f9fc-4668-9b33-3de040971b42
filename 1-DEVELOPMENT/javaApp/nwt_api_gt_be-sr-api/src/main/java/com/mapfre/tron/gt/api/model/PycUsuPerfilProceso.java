package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUsuPerfilProceso
 */
@Validated

public class PycUsuPerfilProceso   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("nombre")
  private String nombre = null;

  @JsonProperty("indDefault")
  private String indDefault = null;

  public PycUsuPerfilProceso idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Identificador del usuario
   * @return idUsuario
  **/
  @ApiModelProperty(example = "1234", value = "Identificador del usuario")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycUsuPerfilProceso nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre completo del usuario
   * @return nombre
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre completo del usuario")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public PycUsuPerfilProceso indDefault(String indDefault) {
    this.indDefault = indDefault;
    return this;
  }

  /**
   * Indicador si es el usuario por defecto
   * @return indDefault
  **/
  @ApiModelProperty(example = "S", value = "Indicador si es el usuario por defecto")


  public String getIndDefault() {
    return indDefault;
  }

  public void setIndDefault(String indDefault) {
    this.indDefault = indDefault;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUsuPerfilProceso pycUsuPerfilProceso = (PycUsuPerfilProceso) o;
    return Objects.equals(this.idUsuario, pycUsuPerfilProceso.idUsuario) &&
        Objects.equals(this.nombre, pycUsuPerfilProceso.nombre) &&
        Objects.equals(this.indDefault, pycUsuPerfilProceso.indDefault);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, nombre, indDefault);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUsuPerfilProceso {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    indDefault: ").append(toIndentedString(indDefault)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

