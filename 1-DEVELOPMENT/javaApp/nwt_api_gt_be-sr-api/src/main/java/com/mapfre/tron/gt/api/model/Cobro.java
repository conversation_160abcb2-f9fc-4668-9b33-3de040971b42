package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * Cobro
 */
@Validated

public class Cobro   {
  @JsonProperty("codigo")
  private String codigo = null;

  @JsonProperty("descripcion")
  private String descripcion = null;

  public Cobro codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Codigo de opcion de cobro
   * @return codigo
  **/
  @ApiModelProperty(example = "006", value = "Codigo de opcion de cobro")


  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public Cobro descripcion(String descripcion) {
    this.descripcion = descripcion;
    return this;
  }

  /**
   * Descripcion de la opcion de cobro
   * @return descripcion
  **/
  @ApiModelProperty(example = "NOMBRE DEL BANCO", value = "Descripcion de la opcion de cobro")


  public String getDescripcion() {
    return descripcion;
  }

  public void setDescripcion(String descripcion) {
    this.descripcion = descripcion;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Cobro cobro = (Cobro) o;
    return Objects.equals(this.codigo, cobro.codigo) &&
        Objects.equals(this.descripcion, cobro.descripcion);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codigo, descripcion);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Cobro {\n");
    
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("    descripcion: ").append(toIndentedString(descripcion)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

