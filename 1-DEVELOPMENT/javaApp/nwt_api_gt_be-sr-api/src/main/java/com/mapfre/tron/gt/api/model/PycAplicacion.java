package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycAplicacion
 */
@Validated

public class PycAplicacion   {
  @JsonProperty("idAplicacion")
  private Integer idAplicacion = null;

  @JsonProperty("nombreAplicacion")
  private String nombreAplicacion = null;

  public PycAplicacion idAplicacion(Integer idAplicacion) {
    this.idAplicacion = idAplicacion;
    return this;
  }

  /**
   * Identificador de la aplicación
   * @return idAplicacion
  **/
  @ApiModelProperty(example = "1", value = "Identificador de la aplicación")


  public Integer getIdAplicacion() {
    return idAplicacion;
  }

  public void setIdAplicacion(Integer idAplicacion) {
    this.idAplicacion = idAplicacion;
  }

  public PycAplicacion nombreAplicacion(String nombreAplicacion) {
    this.nombreAplicacion = nombreAplicacion;
    return this;
  }

  /**
   * Nombre de la aplicación
   * @return nombreAplicacion
  **/
  @ApiModelProperty(example = "Aplicación de Gestión", value = "Nombre de la aplicación")


  public String getNombreAplicacion() {
    return nombreAplicacion;
  }

  public void setNombreAplicacion(String nombreAplicacion) {
    this.nombreAplicacion = nombreAplicacion;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycAplicacion pycAplicacion = (PycAplicacion) o;
    return Objects.equals(this.idAplicacion, pycAplicacion.idAplicacion) &&
        Objects.equals(this.nombreAplicacion, pycAplicacion.nombreAplicacion);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idAplicacion, nombreAplicacion);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycAplicacion {\n");
    
    sb.append("    idAplicacion: ").append(toIndentedString(idAplicacion)).append("\n");
    sb.append("    nombreAplicacion: ").append(toIndentedString(nombreAplicacion)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

