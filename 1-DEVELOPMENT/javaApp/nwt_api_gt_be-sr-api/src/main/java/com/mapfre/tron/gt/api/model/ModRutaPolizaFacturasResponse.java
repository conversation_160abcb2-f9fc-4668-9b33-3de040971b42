package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ModRutaPolizaFacturasResponse
 */
@Validated

public class ModRutaPolizaFacturasResponse   {
  @JsonProperty("polizaCodigo")
  private String polizaCodigo = null;

  @JsonProperty("polizaNumero")
  private String polizaNumero = null;

  @JsonProperty("certificado")
  private String certificado = null;

  @JsonProperty("asegurado")
  private String asegurado = null;

  @JsonProperty("direccion")
  private String direccion = null;

  @JsonProperty("estadoCod")
  private String estadoCod = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("ruta")
  private Integer ruta = null;

  @JsonProperty("idePol")
  private String idePol = null;

  public ModRutaPolizaFacturasResponse polizaCodigo(String polizaCodigo) {
    this.polizaCodigo = polizaCodigo;
    return this;
  }

  /**
   * Codigo de la poliza
   * @return polizaCodigo
  **/
  @ApiModelProperty(example = "AUOL", value = "Codigo de la poliza")


  public String getPolizaCodigo() {
    return polizaCodigo;
  }

  public void setPolizaCodigo(String polizaCodigo) {
    this.polizaCodigo = polizaCodigo;
  }

  public ModRutaPolizaFacturasResponse polizaNumero(String polizaNumero) {
    this.polizaNumero = polizaNumero;
    return this;
  }

  /**
   * Numero de poliza
   * @return polizaNumero
  **/
  @ApiModelProperty(example = "30020111007991", value = "Numero de poliza")


  public String getPolizaNumero() {
    return polizaNumero;
  }

  public void setPolizaNumero(String polizaNumero) {
    this.polizaNumero = polizaNumero;
  }

  public ModRutaPolizaFacturasResponse certificado(String certificado) {
    this.certificado = certificado;
    return this;
  }

  /**
   * Certificado
   * @return certificado
  **/
  @ApiModelProperty(example = "1", value = "Certificado")


  public String getCertificado() {
    return certificado;
  }

  public void setCertificado(String certificado) {
    this.certificado = certificado;
  }

  public ModRutaPolizaFacturasResponse asegurado(String asegurado) {
    this.asegurado = asegurado;
    return this;
  }

  /**
   * Nombre del asegurado
   * @return asegurado
  **/
  @ApiModelProperty(example = "Sociedad Anonima", value = "Nombre del asegurado")


  public String getAsegurado() {
    return asegurado;
  }

  public void setAsegurado(String asegurado) {
    this.asegurado = asegurado;
  }

  public ModRutaPolizaFacturasResponse direccion(String direccion) {
    this.direccion = direccion;
    return this;
  }

  /**
   * Direccion
   * @return direccion
  **/
  @ApiModelProperty(example = "Ciudad de Guatemala", value = "Direccion")


  public String getDireccion() {
    return direccion;
  }

  public void setDireccion(String direccion) {
    this.direccion = direccion;
  }

  public ModRutaPolizaFacturasResponse estadoCod(String estadoCod) {
    this.estadoCod = estadoCod;
    return this;
  }

  /**
   * codigo del estado
   * @return estadoCod
  **/
  @ApiModelProperty(example = "AVI", value = "codigo del estado")


  public String getEstadoCod() {
    return estadoCod;
  }

  public void setEstadoCod(String estadoCod) {
    this.estadoCod = estadoCod;
  }

  public ModRutaPolizaFacturasResponse estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Nombre del estado
   * @return estado
  **/
  @ApiModelProperty(example = "AVISADO", value = "Nombre del estado")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public ModRutaPolizaFacturasResponse ruta(Integer ruta) {
    this.ruta = ruta;
    return this;
  }

  /**
   * Id  de la ruta
   * @return ruta
  **/
  @ApiModelProperty(example = "4", value = "Id  de la ruta")


  public Integer getRuta() {
    return ruta;
  }

  public void setRuta(Integer ruta) {
    this.ruta = ruta;
  }

  public ModRutaPolizaFacturasResponse idePol(String idePol) {
    this.idePol = idePol;
    return this;
  }

  /**
   * Ide poliza
   * @return idePol
  **/
  @ApiModelProperty(example = "3002000007993", value = "Ide poliza")


  public String getIdePol() {
    return idePol;
  }

  public void setIdePol(String idePol) {
    this.idePol = idePol;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ModRutaPolizaFacturasResponse modRutaPolizaFacturasResponse = (ModRutaPolizaFacturasResponse) o;
    return Objects.equals(this.polizaCodigo, modRutaPolizaFacturasResponse.polizaCodigo) &&
        Objects.equals(this.polizaNumero, modRutaPolizaFacturasResponse.polizaNumero) &&
        Objects.equals(this.certificado, modRutaPolizaFacturasResponse.certificado) &&
        Objects.equals(this.asegurado, modRutaPolizaFacturasResponse.asegurado) &&
        Objects.equals(this.direccion, modRutaPolizaFacturasResponse.direccion) &&
        Objects.equals(this.estadoCod, modRutaPolizaFacturasResponse.estadoCod) &&
        Objects.equals(this.estado, modRutaPolizaFacturasResponse.estado) &&
        Objects.equals(this.ruta, modRutaPolizaFacturasResponse.ruta) &&
        Objects.equals(this.idePol, modRutaPolizaFacturasResponse.idePol);
  }

  @Override
  public int hashCode() {
    return Objects.hash(polizaCodigo, polizaNumero, certificado, asegurado, direccion, estadoCod, estado, ruta, idePol);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ModRutaPolizaFacturasResponse {\n");
    
    sb.append("    polizaCodigo: ").append(toIndentedString(polizaCodigo)).append("\n");
    sb.append("    polizaNumero: ").append(toIndentedString(polizaNumero)).append("\n");
    sb.append("    certificado: ").append(toIndentedString(certificado)).append("\n");
    sb.append("    asegurado: ").append(toIndentedString(asegurado)).append("\n");
    sb.append("    direccion: ").append(toIndentedString(direccion)).append("\n");
    sb.append("    estadoCod: ").append(toIndentedString(estadoCod)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    ruta: ").append(toIndentedString(ruta)).append("\n");
    sb.append("    idePol: ").append(toIndentedString(idePol)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

