package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * Message
 */
@Validated

public class Message   {
  @JsonProperty("code")
  private String code = null;

  @JsonProperty("title")
  private String title = null;

  @JsonProperty("message")
  private String message = null;

  @JsonProperty("type")
  private String type = null;

  public Message code(String code) {
    this.code = code;
    return this;
  }

  /**
   * A code that represents the result of the operation.
   * @return code
  **/
  @ApiModelProperty(example = "000", required = true, value = "A code that represents the result of the operation.")
  @NotNull


  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public Message title(String title) {
    this.title = title;
    return this;
  }

  /**
   * A concise title summarizing the operation.
   * @return title
  **/
  @ApiModelProperty(example = "Operation successfully completed", required = true, value = "A concise title summarizing the operation.")
  @NotNull


  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public Message message(String message) {
    this.message = message;
    return this;
  }

  /**
   * A detailed description of the operation performed.
   * @return message
  **/
  @ApiModelProperty(example = "Description of the operation performed", required = true, value = "A detailed description of the operation performed.")
  @NotNull


  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public Message type(String type) {
    this.type = type;
    return this;
  }

  /**
   * The type of response provided for the operation.
   * @return type
  **/
  @ApiModelProperty(example = "success", required = true, value = "The type of response provided for the operation.")
  @NotNull


  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Message message = (Message) o;
    return Objects.equals(this.code, message.code) &&
        Objects.equals(this.title, message.title) &&
        Objects.equals(this.message, message.message) &&
        Objects.equals(this.type, message.type);
  }

  @Override
  public int hashCode() {
    return Objects.hash(code, title, message, type);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Message {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

