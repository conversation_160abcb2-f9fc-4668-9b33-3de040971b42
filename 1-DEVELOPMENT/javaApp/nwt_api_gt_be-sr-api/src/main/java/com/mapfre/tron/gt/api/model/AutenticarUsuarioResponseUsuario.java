package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * Información del usuario autenticado
 */
@ApiModel(description = "Información del usuario autenticado")
@Validated

public class AutenticarUsuarioResponseUsuario   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("idTipo")
  private Integer idTipo = null;

  @JsonProperty("esInterno")
  private Integer esInterno = null;

  @JsonProperty("nombres")
  private String nombres = null;

  @JsonProperty("apellidos")
  private String apellidos = null;

  @JsonProperty("nombreUnicoUsuario")
  private String nombreUnicoUsuario = null;

  @JsonProperty("cobrador")
  private String cobrador = null;

  @JsonProperty("imgPerfil")
  private String imgPerfil = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("tipo")
  private String tipo = null;

  @JsonProperty("codigo")
  private String codigo = null;

  public AutenticarUsuarioResponseUsuario idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * ID del usuario
   * @return idUsuario
  **/
  @ApiModelProperty(example = "123", value = "ID del usuario")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public AutenticarUsuarioResponseUsuario idTipo(Integer idTipo) {
    this.idTipo = idTipo;
    return this;
  }

  /**
   * ID del tipo de usuario
   * @return idTipo
  **/
  @ApiModelProperty(example = "1", value = "ID del tipo de usuario")


  public Integer getIdTipo() {
    return idTipo;
  }

  public void setIdTipo(Integer idTipo) {
    this.idTipo = idTipo;
  }

  public AutenticarUsuarioResponseUsuario esInterno(Integer esInterno) {
    this.esInterno = esInterno;
    return this;
  }

  /**
   * verifica si el usuario recuperado es interno o externo (0-1)
   * @return esInterno
  **/
  @ApiModelProperty(example = "1", value = "verifica si el usuario recuperado es interno o externo (0-1)")


  public Integer getEsInterno() {
    return esInterno;
  }

  public void setEsInterno(Integer esInterno) {
    this.esInterno = esInterno;
  }

  public AutenticarUsuarioResponseUsuario nombres(String nombres) {
    this.nombres = nombres;
    return this;
  }

  /**
   * Nombres del usuario
   * @return nombres
  **/
  @ApiModelProperty(example = "Juan Carlos", value = "Nombres del usuario")


  public String getNombres() {
    return nombres;
  }

  public void setNombres(String nombres) {
    this.nombres = nombres;
  }

  public AutenticarUsuarioResponseUsuario apellidos(String apellidos) {
    this.apellidos = apellidos;
    return this;
  }

  /**
   * Apellidos del usuario
   * @return apellidos
  **/
  @ApiModelProperty(example = "Pérez García", value = "Apellidos del usuario")


  public String getApellidos() {
    return apellidos;
  }

  public void setApellidos(String apellidos) {
    this.apellidos = apellidos;
  }

  public AutenticarUsuarioResponseUsuario nombreUnicoUsuario(String nombreUnicoUsuario) {
    this.nombreUnicoUsuario = nombreUnicoUsuario;
    return this;
  }

  /**
   * Nombre único de usuario
   * @return nombreUnicoUsuario
  **/
  @ApiModelProperty(example = "admin", value = "Nombre único de usuario")


  public String getNombreUnicoUsuario() {
    return nombreUnicoUsuario;
  }

  public void setNombreUnicoUsuario(String nombreUnicoUsuario) {
    this.nombreUnicoUsuario = nombreUnicoUsuario;
  }

  public AutenticarUsuarioResponseUsuario cobrador(String cobrador) {
    this.cobrador = cobrador;
    return this;
  }

  /**
   * Indicador si es cobrador
   * @return cobrador
  **/
  @ApiModelProperty(example = "S", value = "Indicador si es cobrador")


  public String getCobrador() {
    return cobrador;
  }

  public void setCobrador(String cobrador) {
    this.cobrador = cobrador;
  }

  public AutenticarUsuarioResponseUsuario imgPerfil(String imgPerfil) {
    this.imgPerfil = imgPerfil;
    return this;
  }

  /**
   * URL o path de la imagen de perfil
   * @return imgPerfil
  **/
  @ApiModelProperty(example = "/images/perfil/admin.jpg", value = "URL o path de la imagen de perfil")


  public String getImgPerfil() {
    return imgPerfil;
  }

  public void setImgPerfil(String imgPerfil) {
    this.imgPerfil = imgPerfil;
  }

  public AutenticarUsuarioResponseUsuario estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado del usuario
   * @return estado
  **/
  @ApiModelProperty(example = "A", value = "Estado del usuario")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public AutenticarUsuarioResponseUsuario tipo(String tipo) {
    this.tipo = tipo;
    return this;
  }

  /**
   * Nombre del tipo de usuario
   * @return tipo
  **/
  @ApiModelProperty(example = "ADMINISTRADOR", value = "Nombre del tipo de usuario")


  public String getTipo() {
    return tipo;
  }

  public void setTipo(String tipo) {
    this.tipo = tipo;
  }

  public AutenticarUsuarioResponseUsuario codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Código del tipo de usuario
   * @return codigo
  **/
  @ApiModelProperty(example = "ADM", value = "Código del tipo de usuario")


  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AutenticarUsuarioResponseUsuario autenticarUsuarioResponseUsuario = (AutenticarUsuarioResponseUsuario) o;
    return Objects.equals(this.idUsuario, autenticarUsuarioResponseUsuario.idUsuario) &&
        Objects.equals(this.idTipo, autenticarUsuarioResponseUsuario.idTipo) &&
        Objects.equals(this.esInterno, autenticarUsuarioResponseUsuario.esInterno) &&
        Objects.equals(this.nombres, autenticarUsuarioResponseUsuario.nombres) &&
        Objects.equals(this.apellidos, autenticarUsuarioResponseUsuario.apellidos) &&
        Objects.equals(this.nombreUnicoUsuario, autenticarUsuarioResponseUsuario.nombreUnicoUsuario) &&
        Objects.equals(this.cobrador, autenticarUsuarioResponseUsuario.cobrador) &&
        Objects.equals(this.imgPerfil, autenticarUsuarioResponseUsuario.imgPerfil) &&
        Objects.equals(this.estado, autenticarUsuarioResponseUsuario.estado) &&
        Objects.equals(this.tipo, autenticarUsuarioResponseUsuario.tipo) &&
        Objects.equals(this.codigo, autenticarUsuarioResponseUsuario.codigo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, idTipo, esInterno, nombres, apellidos, nombreUnicoUsuario, cobrador, imgPerfil, estado, tipo, codigo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AutenticarUsuarioResponseUsuario {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    idTipo: ").append(toIndentedString(idTipo)).append("\n");
    sb.append("    esInterno: ").append(toIndentedString(esInterno)).append("\n");
    sb.append("    nombres: ").append(toIndentedString(nombres)).append("\n");
    sb.append("    apellidos: ").append(toIndentedString(apellidos)).append("\n");
    sb.append("    nombreUnicoUsuario: ").append(toIndentedString(nombreUnicoUsuario)).append("\n");
    sb.append("    cobrador: ").append(toIndentedString(cobrador)).append("\n");
    sb.append("    imgPerfil: ").append(toIndentedString(imgPerfil)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    tipo: ").append(toIndentedString(tipo)).append("\n");
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

