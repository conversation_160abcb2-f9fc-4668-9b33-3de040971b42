package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycGetDatVarFormSec
 */
@Validated

public class PycGetDatVarFormSec   {
  @JsonProperty("idFormulario")
  private Integer idFormulario = null;

  @JsonProperty("nombreFormulario")
  private String nombreFormulario = null;

  @JsonProperty("idSeccion")
  private Integer idSeccion = null;

  @JsonProperty("nombreSeccion")
  private String nombreSeccion = null;

  @JsonProperty("iconoSeccion")
  private String iconoSeccion = null;

  @JsonProperty("ordenSeccion")
  private Integer ordenSeccion = null;

  @JsonProperty("idDato")
  private Integer idDato = null;

  @JsonProperty("descripcion")
  private String descripcion = null;

  @JsonProperty("valDefault")
  private String valDefault = null;

  @JsonProperty("orden")
  private Integer orden = null;

  @JsonProperty("tipo")
  private String tipo = null;

  @JsonProperty("tipoHtml")
  private String tipoHtml = null;

  @JsonProperty("class")
  private String propertyClass = null;

  @JsonProperty("classForm")
  private String classForm = null;

  @JsonProperty("javascript")
  private String javascript = null;

  @JsonProperty("icono")
  private String icono = null;

  @JsonProperty("placeholder")
  private String placeholder = null;

  @JsonProperty("valMin")
  private String valMin = null;

  @JsonProperty("valMax")
  private String valMax = null;

  @JsonProperty("idDependencia")
  private Integer idDependencia = null;

  @JsonProperty("indVisible")
  private String indVisible = null;

  @JsonProperty("indObligatorio")
  private String indObligatorio = null;

  @JsonProperty("indEditable")
  private String indEditable = null;

  public PycGetDatVarFormSec idFormulario(Integer idFormulario) {
    this.idFormulario = idFormulario;
    return this;
  }

  /**
   * Identificador único del formulario
   * @return idFormulario
  **/
  @ApiModelProperty(example = "1", value = "Identificador único del formulario")


  public Integer getIdFormulario() {
    return idFormulario;
  }

  public void setIdFormulario(Integer idFormulario) {
    this.idFormulario = idFormulario;
  }

  public PycGetDatVarFormSec nombreFormulario(String nombreFormulario) {
    this.nombreFormulario = nombreFormulario;
    return this;
  }

  /**
   * Nombre descriptivo del formulario
   * @return nombreFormulario
  **/
  @ApiModelProperty(example = "Formulario de Solicitud", value = "Nombre descriptivo del formulario")


  public String getNombreFormulario() {
    return nombreFormulario;
  }

  public void setNombreFormulario(String nombreFormulario) {
    this.nombreFormulario = nombreFormulario;
  }

  public PycGetDatVarFormSec idSeccion(Integer idSeccion) {
    this.idSeccion = idSeccion;
    return this;
  }

  /**
   * Identificador único de la sección
   * @return idSeccion
  **/
  @ApiModelProperty(example = "2", value = "Identificador único de la sección")


  public Integer getIdSeccion() {
    return idSeccion;
  }

  public void setIdSeccion(Integer idSeccion) {
    this.idSeccion = idSeccion;
  }

  public PycGetDatVarFormSec nombreSeccion(String nombreSeccion) {
    this.nombreSeccion = nombreSeccion;
    return this;
  }

  /**
   * Nombre descriptivo de la sección
   * @return nombreSeccion
  **/
  @ApiModelProperty(example = "Datos Personales", value = "Nombre descriptivo de la sección")


  public String getNombreSeccion() {
    return nombreSeccion;
  }

  public void setNombreSeccion(String nombreSeccion) {
    this.nombreSeccion = nombreSeccion;
  }

  public PycGetDatVarFormSec iconoSeccion(String iconoSeccion) {
    this.iconoSeccion = iconoSeccion;
    return this;
  }

  /**
   * Icono asociado a la sección
   * @return iconoSeccion
  **/
  @ApiModelProperty(example = "fa-user", value = "Icono asociado a la sección")


  public String getIconoSeccion() {
    return iconoSeccion;
  }

  public void setIconoSeccion(String iconoSeccion) {
    this.iconoSeccion = iconoSeccion;
  }

  public PycGetDatVarFormSec ordenSeccion(Integer ordenSeccion) {
    this.ordenSeccion = ordenSeccion;
    return this;
  }

  /**
   * Orden de visualización de la sección
   * @return ordenSeccion
  **/
  @ApiModelProperty(example = "1", value = "Orden de visualización de la sección")


  public Integer getOrdenSeccion() {
    return ordenSeccion;
  }

  public void setOrdenSeccion(Integer ordenSeccion) {
    this.ordenSeccion = ordenSeccion;
  }

  public PycGetDatVarFormSec idDato(Integer idDato) {
    this.idDato = idDato;
    return this;
  }

  /**
   * Identificador único del dato variable
   * @return idDato
  **/
  @ApiModelProperty(example = "101", value = "Identificador único del dato variable")


  public Integer getIdDato() {
    return idDato;
  }

  public void setIdDato(Integer idDato) {
    this.idDato = idDato;
  }

  public PycGetDatVarFormSec descripcion(String descripcion) {
    this.descripcion = descripcion;
    return this;
  }

  /**
   * Descripción del dato variable
   * @return descripcion
  **/
  @ApiModelProperty(example = "Nombre completo del solicitante", value = "Descripción del dato variable")


  public String getDescripcion() {
    return descripcion;
  }

  public void setDescripcion(String descripcion) {
    this.descripcion = descripcion;
  }

  public PycGetDatVarFormSec valDefault(String valDefault) {
    this.valDefault = valDefault;
    return this;
  }

  /**
   * Valor por defecto del dato variable
   * @return valDefault
  **/
  @ApiModelProperty(example = "Sin especificar", value = "Valor por defecto del dato variable")


  public String getValDefault() {
    return valDefault;
  }

  public void setValDefault(String valDefault) {
    this.valDefault = valDefault;
  }

  public PycGetDatVarFormSec orden(Integer orden) {
    this.orden = orden;
    return this;
  }

  /**
   * Orden de visualización del dato variable
   * @return orden
  **/
  @ApiModelProperty(example = "1", value = "Orden de visualización del dato variable")


  public Integer getOrden() {
    return orden;
  }

  public void setOrden(Integer orden) {
    this.orden = orden;
  }

  public PycGetDatVarFormSec tipo(String tipo) {
    this.tipo = tipo;
    return this;
  }

  /**
   * Tipo de dato variable
   * @return tipo
  **/
  @ApiModelProperty(example = "VARCHAR2", value = "Tipo de dato variable")


  public String getTipo() {
    return tipo;
  }

  public void setTipo(String tipo) {
    this.tipo = tipo;
  }

  public PycGetDatVarFormSec tipoHtml(String tipoHtml) {
    this.tipoHtml = tipoHtml;
    return this;
  }

  /**
   * Tipo de control HTML para el dato variable
   * @return tipoHtml
  **/
  @ApiModelProperty(example = "text", value = "Tipo de control HTML para el dato variable")


  public String getTipoHtml() {
    return tipoHtml;
  }

  public void setTipoHtml(String tipoHtml) {
    this.tipoHtml = tipoHtml;
  }

  public PycGetDatVarFormSec propertyClass(String propertyClass) {
    this.propertyClass = propertyClass;
    return this;
  }

  /**
   * Clase CSS del control
   * @return propertyClass
  **/
  @ApiModelProperty(example = "form-control", value = "Clase CSS del control")


  public String getPropertyClass() {
    return propertyClass;
  }

  public void setPropertyClass(String propertyClass) {
    this.propertyClass = propertyClass;
  }

  public PycGetDatVarFormSec classForm(String classForm) {
    this.classForm = classForm;
    return this;
  }

  /**
   * Clase CSS del formulario
   * @return classForm
  **/
  @ApiModelProperty(example = "col-md-6", value = "Clase CSS del formulario")


  public String getClassForm() {
    return classForm;
  }

  public void setClassForm(String classForm) {
    this.classForm = classForm;
  }

  public PycGetDatVarFormSec javascript(String javascript) {
    this.javascript = javascript;
    return this;
  }

  /**
   * Código JavaScript asociado al control
   * @return javascript
  **/
  @ApiModelProperty(example = "onchange='validarCampo()'", value = "Código JavaScript asociado al control")


  public String getJavascript() {
    return javascript;
  }

  public void setJavascript(String javascript) {
    this.javascript = javascript;
  }

  public PycGetDatVarFormSec icono(String icono) {
    this.icono = icono;
    return this;
  }

  /**
   * Icono del control
   * @return icono
  **/
  @ApiModelProperty(example = "fa-user", value = "Icono del control")


  public String getIcono() {
    return icono;
  }

  public void setIcono(String icono) {
    this.icono = icono;
  }

  public PycGetDatVarFormSec placeholder(String placeholder) {
    this.placeholder = placeholder;
    return this;
  }

  /**
   * Texto de placeholder del control
   * @return placeholder
  **/
  @ApiModelProperty(example = "Ingrese su nombre completo", value = "Texto de placeholder del control")


  public String getPlaceholder() {
    return placeholder;
  }

  public void setPlaceholder(String placeholder) {
    this.placeholder = placeholder;
  }

  public PycGetDatVarFormSec valMin(String valMin) {
    this.valMin = valMin;
    return this;
  }

  /**
   * Valor mínimo permitido
   * @return valMin
  **/
  @ApiModelProperty(example = "1", value = "Valor mínimo permitido")


  public String getValMin() {
    return valMin;
  }

  public void setValMin(String valMin) {
    this.valMin = valMin;
  }

  public PycGetDatVarFormSec valMax(String valMax) {
    this.valMax = valMax;
    return this;
  }

  /**
   * Valor máximo permitido
   * @return valMax
  **/
  @ApiModelProperty(example = "100", value = "Valor máximo permitido")


  public String getValMax() {
    return valMax;
  }

  public void setValMax(String valMax) {
    this.valMax = valMax;
  }

  public PycGetDatVarFormSec idDependencia(Integer idDependencia) {
    this.idDependencia = idDependencia;
    return this;
  }

  /**
   * ID del dato variable del cual depende este control
   * @return idDependencia
  **/
  @ApiModelProperty(example = "50", value = "ID del dato variable del cual depende este control")


  public Integer getIdDependencia() {
    return idDependencia;
  }

  public void setIdDependencia(Integer idDependencia) {
    this.idDependencia = idDependencia;
  }

  public PycGetDatVarFormSec indVisible(String indVisible) {
    this.indVisible = indVisible;
    return this;
  }

  /**
   * Indicador de visibilidad del control (S/N)
   * @return indVisible
  **/
  @ApiModelProperty(example = "S", value = "Indicador de visibilidad del control (S/N)")


  public String getIndVisible() {
    return indVisible;
  }

  public void setIndVisible(String indVisible) {
    this.indVisible = indVisible;
  }

  public PycGetDatVarFormSec indObligatorio(String indObligatorio) {
    this.indObligatorio = indObligatorio;
    return this;
  }

  /**
   * Indicador si el control es obligatorio (S/N)
   * @return indObligatorio
  **/
  @ApiModelProperty(example = "S", value = "Indicador si el control es obligatorio (S/N)")


  public String getIndObligatorio() {
    return indObligatorio;
  }

  public void setIndObligatorio(String indObligatorio) {
    this.indObligatorio = indObligatorio;
  }

  public PycGetDatVarFormSec indEditable(String indEditable) {
    this.indEditable = indEditable;
    return this;
  }

  /**
   * Indicador si el control es editable (S/N)
   * @return indEditable
  **/
  @ApiModelProperty(example = "S", value = "Indicador si el control es editable (S/N)")


  public String getIndEditable() {
    return indEditable;
  }

  public void setIndEditable(String indEditable) {
    this.indEditable = indEditable;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycGetDatVarFormSec pycGetDatVarFormSec = (PycGetDatVarFormSec) o;
    return Objects.equals(this.idFormulario, pycGetDatVarFormSec.idFormulario) &&
        Objects.equals(this.nombreFormulario, pycGetDatVarFormSec.nombreFormulario) &&
        Objects.equals(this.idSeccion, pycGetDatVarFormSec.idSeccion) &&
        Objects.equals(this.nombreSeccion, pycGetDatVarFormSec.nombreSeccion) &&
        Objects.equals(this.iconoSeccion, pycGetDatVarFormSec.iconoSeccion) &&
        Objects.equals(this.ordenSeccion, pycGetDatVarFormSec.ordenSeccion) &&
        Objects.equals(this.idDato, pycGetDatVarFormSec.idDato) &&
        Objects.equals(this.descripcion, pycGetDatVarFormSec.descripcion) &&
        Objects.equals(this.valDefault, pycGetDatVarFormSec.valDefault) &&
        Objects.equals(this.orden, pycGetDatVarFormSec.orden) &&
        Objects.equals(this.tipo, pycGetDatVarFormSec.tipo) &&
        Objects.equals(this.tipoHtml, pycGetDatVarFormSec.tipoHtml) &&
        Objects.equals(this.propertyClass, pycGetDatVarFormSec.propertyClass) &&
        Objects.equals(this.classForm, pycGetDatVarFormSec.classForm) &&
        Objects.equals(this.javascript, pycGetDatVarFormSec.javascript) &&
        Objects.equals(this.icono, pycGetDatVarFormSec.icono) &&
        Objects.equals(this.placeholder, pycGetDatVarFormSec.placeholder) &&
        Objects.equals(this.valMin, pycGetDatVarFormSec.valMin) &&
        Objects.equals(this.valMax, pycGetDatVarFormSec.valMax) &&
        Objects.equals(this.idDependencia, pycGetDatVarFormSec.idDependencia) &&
        Objects.equals(this.indVisible, pycGetDatVarFormSec.indVisible) &&
        Objects.equals(this.indObligatorio, pycGetDatVarFormSec.indObligatorio) &&
        Objects.equals(this.indEditable, pycGetDatVarFormSec.indEditable);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idFormulario, nombreFormulario, idSeccion, nombreSeccion, iconoSeccion, ordenSeccion, idDato, descripcion, valDefault, orden, tipo, tipoHtml, propertyClass, classForm, javascript, icono, placeholder, valMin, valMax, idDependencia, indVisible, indObligatorio, indEditable);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycGetDatVarFormSec {\n");
    
    sb.append("    idFormulario: ").append(toIndentedString(idFormulario)).append("\n");
    sb.append("    nombreFormulario: ").append(toIndentedString(nombreFormulario)).append("\n");
    sb.append("    idSeccion: ").append(toIndentedString(idSeccion)).append("\n");
    sb.append("    nombreSeccion: ").append(toIndentedString(nombreSeccion)).append("\n");
    sb.append("    iconoSeccion: ").append(toIndentedString(iconoSeccion)).append("\n");
    sb.append("    ordenSeccion: ").append(toIndentedString(ordenSeccion)).append("\n");
    sb.append("    idDato: ").append(toIndentedString(idDato)).append("\n");
    sb.append("    descripcion: ").append(toIndentedString(descripcion)).append("\n");
    sb.append("    valDefault: ").append(toIndentedString(valDefault)).append("\n");
    sb.append("    orden: ").append(toIndentedString(orden)).append("\n");
    sb.append("    tipo: ").append(toIndentedString(tipo)).append("\n");
    sb.append("    tipoHtml: ").append(toIndentedString(tipoHtml)).append("\n");
    sb.append("    propertyClass: ").append(toIndentedString(propertyClass)).append("\n");
    sb.append("    classForm: ").append(toIndentedString(classForm)).append("\n");
    sb.append("    javascript: ").append(toIndentedString(javascript)).append("\n");
    sb.append("    icono: ").append(toIndentedString(icono)).append("\n");
    sb.append("    placeholder: ").append(toIndentedString(placeholder)).append("\n");
    sb.append("    valMin: ").append(toIndentedString(valMin)).append("\n");
    sb.append("    valMax: ").append(toIndentedString(valMax)).append("\n");
    sb.append("    idDependencia: ").append(toIndentedString(idDependencia)).append("\n");
    sb.append("    indVisible: ").append(toIndentedString(indVisible)).append("\n");
    sb.append("    indObligatorio: ").append(toIndentedString(indObligatorio)).append("\n");
    sb.append("    indEditable: ").append(toIndentedString(indEditable)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

