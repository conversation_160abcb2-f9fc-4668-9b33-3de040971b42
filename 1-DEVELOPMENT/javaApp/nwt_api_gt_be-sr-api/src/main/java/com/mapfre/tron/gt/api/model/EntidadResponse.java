package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.mapfre.tron.gt.api.model.Entidad;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * EntidadResponse
 */
@Validated

public class EntidadResponse   {
  @JsonProperty("listaEntidades")
  @Valid
  private List<Entidad> listaEntidades = null;

  @JsonProperty("codigo")
  private String codigo = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  public EntidadResponse listaEntidades(List<Entidad> listaEntidades) {
    this.listaEntidades = listaEntidades;
    return this;
  }

  public EntidadResponse addListaEntidadesItem(Entidad listaEntidadesItem) {
    if (this.listaEntidades == null) {
      this.listaEntidades = new ArrayList<>();
    }
    this.listaEntidades.add(listaEntidadesItem);
    return this;
  }

  /**
   * Lista de entidades bancarias
   * @return listaEntidades
  **/
  @ApiModelProperty(value = "Lista de entidades bancarias")

  @Valid

  public List<Entidad> getListaEntidades() {
    return listaEntidades;
  }

  public void setListaEntidades(List<Entidad> listaEntidades) {
    this.listaEntidades = listaEntidades;
  }

  public EntidadResponse codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Código de resultado de la operación
   * @return codigo
  **/
  @ApiModelProperty(example = "200", value = "Código de resultado de la operación")


  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public EntidadResponse mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje descriptivo del resultado de la operación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Operación exitosa", value = "Mensaje descriptivo del resultado de la operación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EntidadResponse entidadResponse = (EntidadResponse) o;
    return Objects.equals(this.listaEntidades, entidadResponse.listaEntidades) &&
        Objects.equals(this.codigo, entidadResponse.codigo) &&
        Objects.equals(this.mensaje, entidadResponse.mensaje);
  }

  @Override
  public int hashCode() {
    return Objects.hash(listaEntidades, codigo, mensaje);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EntidadResponse {\n");
    
    sb.append("    listaEntidades: ").append(toIndentedString(listaEntidades)).append("\n");
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

