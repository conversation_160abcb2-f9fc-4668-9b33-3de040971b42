package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycCatAct
 */
@Validated

public class PycCatAct   {
  @JsonProperty("idCategoria")
  private Integer idCategoria = null;

  @JsonProperty("nombre")
  private String nombre = null;

  public PycCatAct idCategoria(Integer idCategoria) {
    this.idCategoria = idCategoria;
    return this;
  }

  /**
   * Identificador de la categoría del tablero
   * @return idCategoria
  **/
  @ApiModelProperty(example = "1", value = "Identificador de la categoría del tablero")


  public Integer getIdCategoria() {
    return idCategoria;
  }

  public void setIdCategoria(Integer idCategoria) {
    this.idCategoria = idCategoria;
  }

  public PycCatAct nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre de la categoría en mayúsculas
   * @return nombre
  **/
  @ApiModelProperty(example = "DESARROLLO", value = "Nombre de la categoría en mayúsculas")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycCatAct pycCatAct = (PycCatAct) o;
    return Objects.equals(this.idCategoria, pycCatAct.idCategoria) &&
        Objects.equals(this.nombre, pycCatAct.nombre);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idCategoria, nombre);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycCatAct {\n");
    
    sb.append("    idCategoria: ").append(toIndentedString(idCategoria)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

