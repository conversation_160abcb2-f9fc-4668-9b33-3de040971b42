package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * InfoPromedioResponse
 */
@Validated

public class InfoPromedioResponse   {
  @JsonProperty("promedio")
  private String promedio = null;

  public InfoPromedioResponse promedio(String promedio) {
    this.promedio = promedio;
    return this;
  }

  /**
   * Promedio
   * @return promedio
  **/
  @ApiModelProperty(example = "45", value = "Promedio")


  public String getPromedio() {
    return promedio;
  }

  public void setPromedio(String promedio) {
    this.promedio = promedio;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InfoPromedioResponse infoPromedioResponse = (InfoPromedioResponse) o;
    return Objects.equals(this.promedio, infoPromedioResponse.promedio);
  }

  @Override
  public int hashCode() {
    return Objects.hash(promedio);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InfoPromedioResponse {\n");
    
    sb.append("    promedio: ").append(toIndentedString(promedio)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

