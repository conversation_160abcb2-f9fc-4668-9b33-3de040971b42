package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycEstadoPerfil
 */
@Validated

public class PycEstadoPerfil   {
  @JsonProperty("idEstado")
  private Integer idEstado = null;

  @JsonProperty("nombreEstado")
  private String nombreEstado = null;

  @JsonProperty("codigoEstado")
  private String codigoEstado = null;

  public PycEstadoPerfil idEstado(Integer idEstado) {
    this.idEstado = idEstado;
    return this;
  }

  /**
   * Identificador único del estado
   * @return idEstado
  **/
  @ApiModelProperty(example = "2", value = "Identificador único del estado")


  public Integer getIdEstado() {
    return idEstado;
  }

  public void setIdEstado(Integer idEstado) {
    this.idEstado = idEstado;
  }

  public PycEstadoPerfil nombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
    return this;
  }

  /**
   * Nombre descriptivo del estado
   * @return nombreEstado
  **/
  @ApiModelProperty(example = "En Proceso", value = "Nombre descriptivo del estado")


  public String getNombreEstado() {
    return nombreEstado;
  }

  public void setNombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
  }

  public PycEstadoPerfil codigoEstado(String codigoEstado) {
    this.codigoEstado = codigoEstado;
    return this;
  }

  /**
   * Código del estado
   * @return codigoEstado
  **/
  @ApiModelProperty(example = "EP", value = "Código del estado")


  public String getCodigoEstado() {
    return codigoEstado;
  }

  public void setCodigoEstado(String codigoEstado) {
    this.codigoEstado = codigoEstado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycEstadoPerfil pycEstadoPerfil = (PycEstadoPerfil) o;
    return Objects.equals(this.idEstado, pycEstadoPerfil.idEstado) &&
        Objects.equals(this.nombreEstado, pycEstadoPerfil.nombreEstado) &&
        Objects.equals(this.codigoEstado, pycEstadoPerfil.codigoEstado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idEstado, nombreEstado, codigoEstado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycEstadoPerfil {\n");
    
    sb.append("    idEstado: ").append(toIndentedString(idEstado)).append("\n");
    sb.append("    nombreEstado: ").append(toIndentedString(nombreEstado)).append("\n");
    sb.append("    codigoEstado: ").append(toIndentedString(codigoEstado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

