package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycRamo
 */
@Validated

public class PycRamo   {
  @JsonProperty("codCia")
  private String codCia = null;

  @JsonProperty("codModalidad")
  private String codModalidad = null;

  @JsonProperty("codRamo")
  private String codRamo = null;

  @JsonProperty("idTipoPeticion")
  private Integer idTipoPeticion = null;

  @JsonProperty("descripcionRamo")
  private String descripcionRamo = null;

  @JsonProperty("estado")
  private String estado = null;

  public PycRamo codCia(String codCia) {
    this.codCia = codCia;
    return this;
  }

  /**
   * Código de la compañía
   * @return codCia
  **/
  @ApiModelProperty(example = "2", value = "Código de la compañía")


  public String getCodCia() {
    return codCia;
  }

  public void setCodCia(String codCia) {
    this.codCia = codCia;
  }

  public PycRamo codModalidad(String codModalidad) {
    this.codModalidad = codModalidad;
    return this;
  }

  /**
   * Código de modalidad
   * @return codModalidad
  **/
  @ApiModelProperty(example = "999", value = "Código de modalidad")


  public String getCodModalidad() {
    return codModalidad;
  }

  public void setCodModalidad(String codModalidad) {
    this.codModalidad = codModalidad;
  }

  public PycRamo codRamo(String codRamo) {
    this.codRamo = codRamo;
    return this;
  }

  /**
   * Código del ramo
   * @return codRamo
  **/
  @ApiModelProperty(example = "300", value = "Código del ramo")


  public String getCodRamo() {
    return codRamo;
  }

  public void setCodRamo(String codRamo) {
    this.codRamo = codRamo;
  }

  public PycRamo idTipoPeticion(Integer idTipoPeticion) {
    this.idTipoPeticion = idTipoPeticion;
    return this;
  }

  /**
   * Identificador del tipo de petición
   * @return idTipoPeticion
  **/
  @ApiModelProperty(example = "1", value = "Identificador del tipo de petición")


  public Integer getIdTipoPeticion() {
    return idTipoPeticion;
  }

  public void setIdTipoPeticion(Integer idTipoPeticion) {
    this.idTipoPeticion = idTipoPeticion;
  }

  public PycRamo descripcionRamo(String descripcionRamo) {
    this.descripcionRamo = descripcionRamo;
    return this;
  }

  /**
   * Descripción del ramo
   * @return descripcionRamo
  **/
  @ApiModelProperty(example = "Automóviles", value = "Descripción del ramo")


  public String getDescripcionRamo() {
    return descripcionRamo;
  }

  public void setDescripcionRamo(String descripcionRamo) {
    this.descripcionRamo = descripcionRamo;
  }

  public PycRamo estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado del ramo
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", value = "Estado del ramo")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycRamo pycRamo = (PycRamo) o;
    return Objects.equals(this.codCia, pycRamo.codCia) &&
        Objects.equals(this.codModalidad, pycRamo.codModalidad) &&
        Objects.equals(this.codRamo, pycRamo.codRamo) &&
        Objects.equals(this.idTipoPeticion, pycRamo.idTipoPeticion) &&
        Objects.equals(this.descripcionRamo, pycRamo.descripcionRamo) &&
        Objects.equals(this.estado, pycRamo.estado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codCia, codModalidad, codRamo, idTipoPeticion, descripcionRamo, estado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycRamo {\n");
    
    sb.append("    codCia: ").append(toIndentedString(codCia)).append("\n");
    sb.append("    codModalidad: ").append(toIndentedString(codModalidad)).append("\n");
    sb.append("    codRamo: ").append(toIndentedString(codRamo)).append("\n");
    sb.append("    idTipoPeticion: ").append(toIndentedString(idTipoPeticion)).append("\n");
    sb.append("    descripcionRamo: ").append(toIndentedString(descripcionRamo)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

