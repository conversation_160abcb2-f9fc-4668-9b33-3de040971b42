package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycActDocumento
 */
@Validated

public class PycActDocumento   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("idDocumento")
  private Integer idDocumento = null;

  @JsonProperty("localizacion")
  private String localizacion = null;

  @JsonProperty("documento")
  private String documento = null;

  @JsonProperty("ftpWeb")
  private String ftpWeb = null;

  @JsonProperty("nomArch")
  private String nomArch = null;

  @JsonProperty("numaOUsuario")
  private String numaOUsuario = null;

  public PycActDocumento idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", required = true, value = "Identificador de la petición")
  @NotNull


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycActDocumento idDocumento(Integer idDocumento) {
    this.idDocumento = idDocumento;
    return this;
  }

  /**
   * Identificador del documento
   * @return idDocumento
  **/
  @ApiModelProperty(example = "5", required = true, value = "Identificador del documento")
  @NotNull


  public Integer getIdDocumento() {
    return idDocumento;
  }

  public void setIdDocumento(Integer idDocumento) {
    this.idDocumento = idDocumento;
  }

  public PycActDocumento localizacion(String localizacion) {
    this.localizacion = localizacion;
    return this;
  }

  /**
   * Localización del documento
   * @return localizacion
  **/
  @ApiModelProperty(example = "/documentos/peticion_1001/documento_5.pdf", required = true, value = "Localización del documento")
  @NotNull


  public String getLocalizacion() {
    return localizacion;
  }

  public void setLocalizacion(String localizacion) {
    this.localizacion = localizacion;
  }

  public PycActDocumento documento(String documento) {
    this.documento = documento;
    return this;
  }

  /**
   * Nombre del documento (opcional)
   * @return documento
  **/
  @ApiModelProperty(example = "Documento de soporte", value = "Nombre del documento (opcional)")


  public String getDocumento() {
    return documento;
  }

  public void setDocumento(String documento) {
    this.documento = documento;
  }

  public PycActDocumento ftpWeb(String ftpWeb) {
    this.ftpWeb = ftpWeb;
    return this;
  }

  /**
   * Indicador de FTP/Web (S/N, por defecto 'N')
   * @return ftpWeb
  **/
  @ApiModelProperty(example = "N", value = "Indicador de FTP/Web (S/N, por defecto 'N')")


  public String getFtpWeb() {
    return ftpWeb;
  }

  public void setFtpWeb(String ftpWeb) {
    this.ftpWeb = ftpWeb;
  }

  public PycActDocumento nomArch(String nomArch) {
    this.nomArch = nomArch;
    return this;
  }

  /**
   * Nombre del archivo (opcional)
   * @return nomArch
  **/
  @ApiModelProperty(example = "soporte_documento.pdf", value = "Nombre del archivo (opcional)")


  public String getNomArch() {
    return nomArch;
  }

  public void setNomArch(String nomArch) {
    this.nomArch = nomArch;
  }

  public PycActDocumento numaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
    return this;
  }

  /**
   * Número de usuario para auditoría (opcional)
   * @return numaOUsuario
  **/
  @ApiModelProperty(example = "USR001", value = "Número de usuario para auditoría (opcional)")


  public String getNumaOUsuario() {
    return numaOUsuario;
  }

  public void setNumaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycActDocumento pycActDocumento = (PycActDocumento) o;
    return Objects.equals(this.idPeticion, pycActDocumento.idPeticion) &&
        Objects.equals(this.idDocumento, pycActDocumento.idDocumento) &&
        Objects.equals(this.localizacion, pycActDocumento.localizacion) &&
        Objects.equals(this.documento, pycActDocumento.documento) &&
        Objects.equals(this.ftpWeb, pycActDocumento.ftpWeb) &&
        Objects.equals(this.nomArch, pycActDocumento.nomArch) &&
        Objects.equals(this.numaOUsuario, pycActDocumento.numaOUsuario);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, idDocumento, localizacion, documento, ftpWeb, nomArch, numaOUsuario);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycActDocumento {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    idDocumento: ").append(toIndentedString(idDocumento)).append("\n");
    sb.append("    localizacion: ").append(toIndentedString(localizacion)).append("\n");
    sb.append("    documento: ").append(toIndentedString(documento)).append("\n");
    sb.append("    ftpWeb: ").append(toIndentedString(ftpWeb)).append("\n");
    sb.append("    nomArch: ").append(toIndentedString(nomArch)).append("\n");
    sb.append("    numaOUsuario: ").append(toIndentedString(numaOUsuario)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

