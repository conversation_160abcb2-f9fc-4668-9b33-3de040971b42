package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * TipoDocumentoPago
 */
@Validated

public class TipoDocumentoPago   {
  @JsonProperty("noDocto")
  private String noDocto = null;

  @JsonProperty("mcaAviso")
  private String mcaAviso = null;

  @JsonProperty("monto")
  private Double monto = null;

  public TipoDocumentoPago noDocto(String noDocto) {
    this.noDocto = noDocto;
    return this;
  }

  /**
   * Código del requerimiento o documento encontrado
   * @return noDocto
  **/
  @ApiModelProperty(example = "string", value = "Código del requerimiento o documento encontrado")


  public String getNoDocto() {
    return noDocto;
  }

  public void setNoDocto(String noDocto) {
    this.noDocto = noDocto;
  }

  public TipoDocumentoPago mcaAviso(String mcaAviso) {
    this.mcaAviso = mcaAviso;
    return this;
  }

  /**
   * Indicador si es aviso ('S' o 'N')
   * @return mcaAviso
  **/
  @ApiModelProperty(example = "string", value = "Indicador si es aviso ('S' o 'N')")


  public String getMcaAviso() {
    return mcaAviso;
  }

  public void setMcaAviso(String mcaAviso) {
    this.mcaAviso = mcaAviso;
  }

  public TipoDocumentoPago monto(Double monto) {
    this.monto = monto;
    return this;
  }

  /**
   * Monto asociado al documento
   * @return monto
  **/
  @ApiModelProperty(example = "number", value = "Monto asociado al documento")


  public Double getMonto() {
    return monto;
  }

  public void setMonto(Double monto) {
    this.monto = monto;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TipoDocumentoPago tipoDocumentoPago = (TipoDocumentoPago) o;
    return Objects.equals(this.noDocto, tipoDocumentoPago.noDocto) &&
        Objects.equals(this.mcaAviso, tipoDocumentoPago.mcaAviso) &&
        Objects.equals(this.monto, tipoDocumentoPago.monto);
  }

  @Override
  public int hashCode() {
    return Objects.hash(noDocto, mcaAviso, monto);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TipoDocumentoPago {\n");
    
    sb.append("    noDocto: ").append(toIndentedString(noDocto)).append("\n");
    sb.append("    mcaAviso: ").append(toIndentedString(mcaAviso)).append("\n");
    sb.append("    monto: ").append(toIndentedString(monto)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

