package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycInfoUsuario
 */
@Validated

public class PycInfoUsuario   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("primerNombre")
  private String primerNombre = null;

  @JsonProperty("segundoNombre")
  private String segundoNombre = null;

  @JsonProperty("primerApellido")
  private String primerApellido = null;

  @JsonProperty("segundoApellido")
  private String segundoApellido = null;

  @JsonProperty("fechaCreacion")
  private String fechaCreacion = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("fechaBaja")
  private String fechaBaja = null;

  @JsonProperty("numaOUsuario")
  private String numaOUsuario = null;

  @JsonProperty("email")
  private String email = null;

  @JsonProperty("genero")
  private String genero = null;

  @JsonProperty("idArea")
  private Integer idArea = null;

  @JsonProperty("area")
  private String area = null;

  @JsonProperty("idDepartamento")
  private Integer idDepartamento = null;

  @JsonProperty("departamento")
  private String departamento = null;

  @JsonProperty("idPerfil")
  private Integer idPerfil = null;

  @JsonProperty("nombrePerfil")
  private String nombrePerfil = null;

  @JsonProperty("indDefault")
  private String indDefault = null;

  @JsonProperty("idProceso")
  private Integer idProceso = null;

  @JsonProperty("urlPerfil")
  private String urlPerfil = null;

  @JsonProperty("pathPerfil")
  private String pathPerfil = null;

  @JsonProperty("urlInicio")
  private String urlInicio = null;

  public PycInfoUsuario idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Identificador del usuario
   * @return idUsuario
  **/
  @ApiModelProperty(example = "1234", value = "Identificador del usuario")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycInfoUsuario primerNombre(String primerNombre) {
    this.primerNombre = primerNombre;
    return this;
  }

  /**
   * Primer nombre del usuario
   * @return primerNombre
  **/
  @ApiModelProperty(example = "Juan", value = "Primer nombre del usuario")


  public String getPrimerNombre() {
    return primerNombre;
  }

  public void setPrimerNombre(String primerNombre) {
    this.primerNombre = primerNombre;
  }

  public PycInfoUsuario segundoNombre(String segundoNombre) {
    this.segundoNombre = segundoNombre;
    return this;
  }

  /**
   * Segundo nombre del usuario
   * @return segundoNombre
  **/
  @ApiModelProperty(example = "Carlos", value = "Segundo nombre del usuario")


  public String getSegundoNombre() {
    return segundoNombre;
  }

  public void setSegundoNombre(String segundoNombre) {
    this.segundoNombre = segundoNombre;
  }

  public PycInfoUsuario primerApellido(String primerApellido) {
    this.primerApellido = primerApellido;
    return this;
  }

  /**
   * Primer apellido del usuario
   * @return primerApellido
  **/
  @ApiModelProperty(example = "Pérez", value = "Primer apellido del usuario")


  public String getPrimerApellido() {
    return primerApellido;
  }

  public void setPrimerApellido(String primerApellido) {
    this.primerApellido = primerApellido;
  }

  public PycInfoUsuario segundoApellido(String segundoApellido) {
    this.segundoApellido = segundoApellido;
    return this;
  }

  /**
   * Segundo apellido del usuario
   * @return segundoApellido
  **/
  @ApiModelProperty(example = "González", value = "Segundo apellido del usuario")


  public String getSegundoApellido() {
    return segundoApellido;
  }

  public void setSegundoApellido(String segundoApellido) {
    this.segundoApellido = segundoApellido;
  }

  public PycInfoUsuario fechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
    return this;
  }

  /**
   * Fecha de creación del usuario
   * @return fechaCreacion
  **/
  @ApiModelProperty(example = "2025-01-15T10:30:00Z", value = "Fecha de creación del usuario")


  public String getFechaCreacion() {
    return fechaCreacion;
  }

  public void setFechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
  }

  public PycInfoUsuario estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado del usuario
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", value = "Estado del usuario")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycInfoUsuario fechaBaja(String fechaBaja) {
    this.fechaBaja = fechaBaja;
    return this;
  }

  /**
   * Fecha de baja del usuario (si aplica)
   * @return fechaBaja
  **/
  @ApiModelProperty(example = "2025-12-31T00:00:00Z", value = "Fecha de baja del usuario (si aplica)")


  public String getFechaBaja() {
    return fechaBaja;
  }

  public void setFechaBaja(String fechaBaja) {
    this.fechaBaja = fechaBaja;
  }

  public PycInfoUsuario numaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
    return this;
  }

  /**
   * Nombre o número de usuario
   * @return numaOUsuario
  **/
  @ApiModelProperty(example = "jperez", value = "Nombre o número de usuario")


  public String getNumaOUsuario() {
    return numaOUsuario;
  }

  public void setNumaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
  }

  public PycInfoUsuario email(String email) {
    this.email = email;
    return this;
  }

  /**
   * Correo electrónico del usuario
   * @return email
  **/
  @ApiModelProperty(example = "<EMAIL>", value = "Correo electrónico del usuario")


  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public PycInfoUsuario genero(String genero) {
    this.genero = genero;
    return this;
  }

  /**
   * Género del usuario
   * @return genero
  **/
  @ApiModelProperty(example = "M", value = "Género del usuario")


  public String getGenero() {
    return genero;
  }

  public void setGenero(String genero) {
    this.genero = genero;
  }

  public PycInfoUsuario idArea(Integer idArea) {
    this.idArea = idArea;
    return this;
  }

  /**
   * Identificador del área
   * @return idArea
  **/
  @ApiModelProperty(example = "5", value = "Identificador del área")


  public Integer getIdArea() {
    return idArea;
  }

  public void setIdArea(Integer idArea) {
    this.idArea = idArea;
  }

  public PycInfoUsuario area(String area) {
    this.area = area;
    return this;
  }

  /**
   * Nombre del área
   * @return area
  **/
  @ApiModelProperty(example = "Tecnología", value = "Nombre del área")


  public String getArea() {
    return area;
  }

  public void setArea(String area) {
    this.area = area;
  }

  public PycInfoUsuario idDepartamento(Integer idDepartamento) {
    this.idDepartamento = idDepartamento;
    return this;
  }

  /**
   * Identificador del departamento
   * @return idDepartamento
  **/
  @ApiModelProperty(example = "10", value = "Identificador del departamento")


  public Integer getIdDepartamento() {
    return idDepartamento;
  }

  public void setIdDepartamento(Integer idDepartamento) {
    this.idDepartamento = idDepartamento;
  }

  public PycInfoUsuario departamento(String departamento) {
    this.departamento = departamento;
    return this;
  }

  /**
   * Nombre del departamento
   * @return departamento
  **/
  @ApiModelProperty(example = "Desarrollo de Software", value = "Nombre del departamento")


  public String getDepartamento() {
    return departamento;
  }

  public void setDepartamento(String departamento) {
    this.departamento = departamento;
  }

  public PycInfoUsuario idPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
    return this;
  }

  /**
   * Identificador del perfil
   * @return idPerfil
  **/
  @ApiModelProperty(example = "3", value = "Identificador del perfil")


  public Integer getIdPerfil() {
    return idPerfil;
  }

  public void setIdPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
  }

  public PycInfoUsuario nombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
    return this;
  }

  /**
   * Nombre del perfil
   * @return nombrePerfil
  **/
  @ApiModelProperty(example = "Desarrollador", value = "Nombre del perfil")


  public String getNombrePerfil() {
    return nombrePerfil;
  }

  public void setNombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
  }

  public PycInfoUsuario indDefault(String indDefault) {
    this.indDefault = indDefault;
    return this;
  }

  /**
   * Indicador si es el perfil por defecto
   * @return indDefault
  **/
  @ApiModelProperty(example = "S", value = "Indicador si es el perfil por defecto")


  public String getIndDefault() {
    return indDefault;
  }

  public void setIndDefault(String indDefault) {
    this.indDefault = indDefault;
  }

  public PycInfoUsuario idProceso(Integer idProceso) {
    this.idProceso = idProceso;
    return this;
  }

  /**
   * Identificador del proceso
   * @return idProceso
  **/
  @ApiModelProperty(example = "7", value = "Identificador del proceso")


  public Integer getIdProceso() {
    return idProceso;
  }

  public void setIdProceso(Integer idProceso) {
    this.idProceso = idProceso;
  }

  public PycInfoUsuario urlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
    return this;
  }

  /**
   * URL del perfil del usuario
   * @return urlPerfil
  **/
  @ApiModelProperty(example = "https://ejemplo.com/perfil/jperez", value = "URL del perfil del usuario")


  public String getUrlPerfil() {
    return urlPerfil;
  }

  public void setUrlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
  }

  public PycInfoUsuario pathPerfil(String pathPerfil) {
    this.pathPerfil = pathPerfil;
    return this;
  }

  /**
   * Ruta del perfil del usuario
   * @return pathPerfil
  **/
  @ApiModelProperty(example = "/perfiles/jperez", value = "Ruta del perfil del usuario")


  public String getPathPerfil() {
    return pathPerfil;
  }

  public void setPathPerfil(String pathPerfil) {
    this.pathPerfil = pathPerfil;
  }

  public PycInfoUsuario urlInicio(String urlInicio) {
    this.urlInicio = urlInicio;
    return this;
  }

  /**
   * URL de inicio del usuario
   * @return urlInicio
  **/
  @ApiModelProperty(example = "https://ejemplo.com/dashboard", value = "URL de inicio del usuario")


  public String getUrlInicio() {
    return urlInicio;
  }

  public void setUrlInicio(String urlInicio) {
    this.urlInicio = urlInicio;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycInfoUsuario pycInfoUsuario = (PycInfoUsuario) o;
    return Objects.equals(this.idUsuario, pycInfoUsuario.idUsuario) &&
        Objects.equals(this.primerNombre, pycInfoUsuario.primerNombre) &&
        Objects.equals(this.segundoNombre, pycInfoUsuario.segundoNombre) &&
        Objects.equals(this.primerApellido, pycInfoUsuario.primerApellido) &&
        Objects.equals(this.segundoApellido, pycInfoUsuario.segundoApellido) &&
        Objects.equals(this.fechaCreacion, pycInfoUsuario.fechaCreacion) &&
        Objects.equals(this.estado, pycInfoUsuario.estado) &&
        Objects.equals(this.fechaBaja, pycInfoUsuario.fechaBaja) &&
        Objects.equals(this.numaOUsuario, pycInfoUsuario.numaOUsuario) &&
        Objects.equals(this.email, pycInfoUsuario.email) &&
        Objects.equals(this.genero, pycInfoUsuario.genero) &&
        Objects.equals(this.idArea, pycInfoUsuario.idArea) &&
        Objects.equals(this.area, pycInfoUsuario.area) &&
        Objects.equals(this.idDepartamento, pycInfoUsuario.idDepartamento) &&
        Objects.equals(this.departamento, pycInfoUsuario.departamento) &&
        Objects.equals(this.idPerfil, pycInfoUsuario.idPerfil) &&
        Objects.equals(this.nombrePerfil, pycInfoUsuario.nombrePerfil) &&
        Objects.equals(this.indDefault, pycInfoUsuario.indDefault) &&
        Objects.equals(this.idProceso, pycInfoUsuario.idProceso) &&
        Objects.equals(this.urlPerfil, pycInfoUsuario.urlPerfil) &&
        Objects.equals(this.pathPerfil, pycInfoUsuario.pathPerfil) &&
        Objects.equals(this.urlInicio, pycInfoUsuario.urlInicio);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, primerNombre, segundoNombre, primerApellido, segundoApellido, fechaCreacion, estado, fechaBaja, numaOUsuario, email, genero, idArea, area, idDepartamento, departamento, idPerfil, nombrePerfil, indDefault, idProceso, urlPerfil, pathPerfil, urlInicio);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycInfoUsuario {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    primerNombre: ").append(toIndentedString(primerNombre)).append("\n");
    sb.append("    segundoNombre: ").append(toIndentedString(segundoNombre)).append("\n");
    sb.append("    primerApellido: ").append(toIndentedString(primerApellido)).append("\n");
    sb.append("    segundoApellido: ").append(toIndentedString(segundoApellido)).append("\n");
    sb.append("    fechaCreacion: ").append(toIndentedString(fechaCreacion)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    fechaBaja: ").append(toIndentedString(fechaBaja)).append("\n");
    sb.append("    numaOUsuario: ").append(toIndentedString(numaOUsuario)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    genero: ").append(toIndentedString(genero)).append("\n");
    sb.append("    idArea: ").append(toIndentedString(idArea)).append("\n");
    sb.append("    area: ").append(toIndentedString(area)).append("\n");
    sb.append("    idDepartamento: ").append(toIndentedString(idDepartamento)).append("\n");
    sb.append("    departamento: ").append(toIndentedString(departamento)).append("\n");
    sb.append("    idPerfil: ").append(toIndentedString(idPerfil)).append("\n");
    sb.append("    nombrePerfil: ").append(toIndentedString(nombrePerfil)).append("\n");
    sb.append("    indDefault: ").append(toIndentedString(indDefault)).append("\n");
    sb.append("    idProceso: ").append(toIndentedString(idProceso)).append("\n");
    sb.append("    urlPerfil: ").append(toIndentedString(urlPerfil)).append("\n");
    sb.append("    pathPerfil: ").append(toIndentedString(pathPerfil)).append("\n");
    sb.append("    urlInicio: ").append(toIndentedString(urlInicio)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

