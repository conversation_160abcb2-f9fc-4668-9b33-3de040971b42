package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycDatoVarEquiv
 */
@Validated

public class PycDatoVarEquiv   {
  @JsonProperty("codCia")
  private String codCia = null;

  @JsonProperty("codModalidad")
  private Integer codModalidad = null;

  @JsonProperty("codRamo")
  private String codRamo = null;

  @JsonProperty("idFormulario")
  private Integer idFormulario = null;

  @JsonProperty("idSeccion")
  private Integer idSeccion = null;

  @JsonProperty("datVarId")
  private String datVarId = null;

  @JsonProperty("datVar")
  private String datVar = null;

  @JsonProperty("datVarNm")
  private String datVarNm = null;

  @JsonProperty("datVarReef")
  private String datVarReef = null;

  public PycDatoVarEquiv codCia(String codCia) {
    this.codCia = codCia;
    return this;
  }

  /**
   * Código de la compañía
   * @return codCia
  **/
  @ApiModelProperty(example = "2", value = "Código de la compañía")


  public String getCodCia() {
    return codCia;
  }

  public void setCodCia(String codCia) {
    this.codCia = codCia;
  }

  public PycDatoVarEquiv codModalidad(Integer codModalidad) {
    this.codModalidad = codModalidad;
    return this;
  }

  /**
   * Código de modalidad
   * @return codModalidad
  **/
  @ApiModelProperty(example = "999", value = "Código de modalidad")


  public Integer getCodModalidad() {
    return codModalidad;
  }

  public void setCodModalidad(Integer codModalidad) {
    this.codModalidad = codModalidad;
  }

  public PycDatoVarEquiv codRamo(String codRamo) {
    this.codRamo = codRamo;
    return this;
  }

  /**
   * Código del ramo
   * @return codRamo
  **/
  @ApiModelProperty(example = "300", value = "Código del ramo")


  public String getCodRamo() {
    return codRamo;
  }

  public void setCodRamo(String codRamo) {
    this.codRamo = codRamo;
  }

  public PycDatoVarEquiv idFormulario(Integer idFormulario) {
    this.idFormulario = idFormulario;
    return this;
  }

  /**
   * Identificador del formulario
   * @return idFormulario
  **/
  @ApiModelProperty(example = "1", value = "Identificador del formulario")


  public Integer getIdFormulario() {
    return idFormulario;
  }

  public void setIdFormulario(Integer idFormulario) {
    this.idFormulario = idFormulario;
  }

  public PycDatoVarEquiv idSeccion(Integer idSeccion) {
    this.idSeccion = idSeccion;
    return this;
  }

  /**
   * Identificador de la sección
   * @return idSeccion
  **/
  @ApiModelProperty(example = "2", value = "Identificador de la sección")


  public Integer getIdSeccion() {
    return idSeccion;
  }

  public void setIdSeccion(Integer idSeccion) {
    this.idSeccion = idSeccion;
  }

  public PycDatoVarEquiv datVarId(String datVarId) {
    this.datVarId = datVarId;
    return this;
  }

  /**
   * Identificador del dato variable
   * @return datVarId
  **/
  @ApiModelProperty(example = "DAT_VAR_ID_001", value = "Identificador del dato variable")


  public String getDatVarId() {
    return datVarId;
  }

  public void setDatVarId(String datVarId) {
    this.datVarId = datVarId;
  }

  public PycDatoVarEquiv datVar(String datVar) {
    this.datVar = datVar;
    return this;
  }

  /**
   * Dato variable
   * @return datVar
  **/
  @ApiModelProperty(example = "VARIABLE_001", value = "Dato variable")


  public String getDatVar() {
    return datVar;
  }

  public void setDatVar(String datVar) {
    this.datVar = datVar;
  }

  public PycDatoVarEquiv datVarNm(String datVarNm) {
    this.datVarNm = datVarNm;
    return this;
  }

  /**
   * Nombre del dato variable
   * @return datVarNm
  **/
  @ApiModelProperty(example = "NOMBRE_VARIABLE_001", value = "Nombre del dato variable")


  public String getDatVarNm() {
    return datVarNm;
  }

  public void setDatVarNm(String datVarNm) {
    this.datVarNm = datVarNm;
  }

  public PycDatoVarEquiv datVarReef(String datVarReef) {
    this.datVarReef = datVarReef;
    return this;
  }

  /**
   * Dato variable REEF
   * @return datVarReef
  **/
  @ApiModelProperty(example = "VARIABLE_REEF_001", value = "Dato variable REEF")


  public String getDatVarReef() {
    return datVarReef;
  }

  public void setDatVarReef(String datVarReef) {
    this.datVarReef = datVarReef;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycDatoVarEquiv pycDatoVarEquiv = (PycDatoVarEquiv) o;
    return Objects.equals(this.codCia, pycDatoVarEquiv.codCia) &&
        Objects.equals(this.codModalidad, pycDatoVarEquiv.codModalidad) &&
        Objects.equals(this.codRamo, pycDatoVarEquiv.codRamo) &&
        Objects.equals(this.idFormulario, pycDatoVarEquiv.idFormulario) &&
        Objects.equals(this.idSeccion, pycDatoVarEquiv.idSeccion) &&
        Objects.equals(this.datVarId, pycDatoVarEquiv.datVarId) &&
        Objects.equals(this.datVar, pycDatoVarEquiv.datVar) &&
        Objects.equals(this.datVarNm, pycDatoVarEquiv.datVarNm) &&
        Objects.equals(this.datVarReef, pycDatoVarEquiv.datVarReef);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codCia, codModalidad, codRamo, idFormulario, idSeccion, datVarId, datVar, datVarNm, datVarReef);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycDatoVarEquiv {\n");
    
    sb.append("    codCia: ").append(toIndentedString(codCia)).append("\n");
    sb.append("    codModalidad: ").append(toIndentedString(codModalidad)).append("\n");
    sb.append("    codRamo: ").append(toIndentedString(codRamo)).append("\n");
    sb.append("    idFormulario: ").append(toIndentedString(idFormulario)).append("\n");
    sb.append("    idSeccion: ").append(toIndentedString(idSeccion)).append("\n");
    sb.append("    datVarId: ").append(toIndentedString(datVarId)).append("\n");
    sb.append("    datVar: ").append(toIndentedString(datVar)).append("\n");
    sb.append("    datVarNm: ").append(toIndentedString(datVarNm)).append("\n");
    sb.append("    datVarReef: ").append(toIndentedString(datVarReef)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

