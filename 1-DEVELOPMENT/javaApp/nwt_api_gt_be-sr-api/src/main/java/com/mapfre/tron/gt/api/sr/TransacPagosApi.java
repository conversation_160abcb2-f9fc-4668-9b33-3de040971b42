/**
 * NOTE: This class is auto generated by the swagger code generator program (2.4.14).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.mapfre.tron.gt.api.sr;

import com.mapfre.tron.gt.api.model.AvisoPagoPolizaGrupoResponse;
import com.mapfre.tron.gt.api.model.ContratosPolizaGrupoResponse;
import com.mapfre.tron.gt.api.model.Error;
import com.mapfre.tron.gt.api.model.InfoDeducibleResponse;
import com.mapfre.tron.gt.api.model.InfoReciboResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleFacResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleResponse;
import com.mapfre.tron.gt.api.model.ResponsablePagoResponse;
import com.mapfre.tron.gt.api.model.TipoDocumentoPagoResponse;
import com.mapfre.tron.gt.api.model.ValidaEstadoDeducibleResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Api(value = "transacPagos", description = "the transacPagos API")
@RequestMapping(value = "/newtron/api")
public interface TransacPagosApi {

    Logger log = LoggerFactory.getLogger(TransacPagosApi.class);

    default Optional<ObjectMapper> getObjectMapper() {
        return Optional.empty();
    }

    default Optional<HttpServletRequest> getRequest() {
        return Optional.empty();
    }

    default Optional<String> getAcceptHeader() {
        return getRequest().map(r -> r.getHeader("Accept"));
    }

    @ApiOperation(value = "Obtener avisos de pago de póliza grupo", nickname = "getAvisoPagoPolizaGrupo", notes = "Obtiene los avisos de pago de póliza grupo", response = AvisoPagoPolizaGrupoResponse.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "TransacPagos", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = AvisoPagoPolizaGrupoResponse.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/transacPagos/get_aviso_pago_poliza_grupo",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<AvisoPagoPolizaGrupoResponse>> getAvisoPagoPolizaGrupo(@NotNull @ApiParam(value = "Código de compañía", required = true) @Valid @RequestParam(value = "codCia", required = true) Integer codCia,@NotNull @ApiParam(value = "Número de póliza grupo", required = true) @Valid @RequestParam(value = "numPolizaGrupo", required = true) String numPolizaGrupo,@ApiParam(value = "Número de contrato") @Valid @RequestParam(value = "numContrato", required = false) Integer numContrato) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"codActTercero\" : \"integer\",  \"tipEstado\" : \"string\",  \"numPoliza\" : \"string\",  \"codDocumPago\" : \"string\",  \"fecMvto\" : \"string\",  \"codDocum\" : \"string\",  \"valCambio\" : \"number\",  \"numContrato\" : \"integer\",  \"codAgt\" : \"string\",  \"codMon\" : \"integer\",  \"codGestor\" : \"integer\",  \"tipDocum\" : \"string\",  \"numMvto\" : \"integer\",  \"impDocum\" : \"number\",  \"numPolizaGrupo\" : \"string\",  \"tipGestor\" : \"string\",  \"codCia\" : \"integer\",  \"numPolizaCliente\" : \"string\",  \"fecVcto\" : \"string\",  \"tipDocumPago\" : \"string\"}, {  \"codActTercero\" : \"integer\",  \"tipEstado\" : \"string\",  \"numPoliza\" : \"string\",  \"codDocumPago\" : \"string\",  \"fecMvto\" : \"string\",  \"codDocum\" : \"string\",  \"valCambio\" : \"number\",  \"numContrato\" : \"integer\",  \"codAgt\" : \"string\",  \"codMon\" : \"integer\",  \"codGestor\" : \"integer\",  \"tipDocum\" : \"string\",  \"numMvto\" : \"integer\",  \"impDocum\" : \"number\",  \"numPolizaGrupo\" : \"string\",  \"tipGestor\" : \"string\",  \"codCia\" : \"integer\",  \"numPolizaCliente\" : \"string\",  \"fecVcto\" : \"string\",  \"tipDocumPago\" : \"string\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TransacPagosApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener contratos de póliza grupo", nickname = "getContratosPolizaGrupo", notes = "Obtiene los contratos de póliza grupo", response = ContratosPolizaGrupoResponse.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "TransacPagos", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = ContratosPolizaGrupoResponse.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/transacPagos/get_contratos_poliza_grupo",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<ContratosPolizaGrupoResponse>> getContratosPolizaGrupo(@NotNull @ApiParam(value = "Código de compañía", required = true) @Valid @RequestParam(value = "codCia", required = true) Integer codCia,@NotNull @ApiParam(value = "Número de póliza grupo", required = true) @Valid @RequestParam(value = "numPolizaGrupo", required = true) String numPolizaGrupo) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"numPoliza\" : \"string\",  \"fecVctoPoliza\" : \"string\",  \"tipPoliza\" : \"string\",  \"mcaRiesgos\" : \"string\",  \"codCia\" : \"integer\",  \"numContrato\" : \"integer\",  \"cantPoliza\" : \"integer\",  \"cantAviso\" : \"integer\"}, {  \"numPoliza\" : \"string\",  \"fecVctoPoliza\" : \"string\",  \"tipPoliza\" : \"string\",  \"mcaRiesgos\" : \"string\",  \"codCia\" : \"integer\",  \"numContrato\" : \"integer\",  \"cantPoliza\" : \"integer\",  \"cantAviso\" : \"integer\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TransacPagosApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener información del deducible", nickname = "getInfoDeducible", notes = "Obtiene la información de liquidación del deducible", response = InfoDeducibleResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "TransacPagos", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = InfoDeducibleResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/transacPagos/get_info_deducible",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<InfoDeducibleResponse> getInfoDeducible(@NotNull @ApiParam(value = "Código de compañía", required = true) @Valid @RequestParam(value = "codCia", required = true) Integer codCia,@NotNull @ApiParam(value = "Número de liquidación", required = true) @Valid @RequestParam(value = "numLiq", required = true) String numLiq) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"obs\" : \"string\",  \"codDocum\" : \"string\",  \"valCambio\" : \"number\",  \"nomActTercero\" : \"string\",  \"impIva\" : \"number\",  \"impLiq\" : \"number\",  \"codTercero\" : \"string\",  \"codMonPago\" : \"integer\",  \"numSini\" : \"string\",  \"impLiqNeto\" : \"number\",  \"fecPago\" : \"string\",  \"codSector\" : \"integer\",  \"numDecimales\" : \"integer\",  \"tipDocto\" : \"string\",  \"nomTercero\" : \"string\",  \"codActTercero\" : \"integer\",  \"numPoliza\" : \"string\",  \"nomSector\" : \"string\",  \"codNivel3\" : \"string\",  \"numExp\" : \"integer\",  \"fecEstPago\" : \"string\",  \"tipDocum\" : \"string\",  \"codRamo\" : \"integer\",  \"numLiq\" : \"string\",  \"nomNivel3\" : \"string\",  \"fecLiq\" : \"string\",  \"codMonLiqIso\" : \"string\",  \"codMonPagoIso\" : \"string\",  \"codMonLiq\" : \"integer\"}", InfoDeducibleResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TransacPagosApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener información de recibo", nickname = "getInfoRecibo", notes = "Obtiene la información del recibo", response = InfoReciboResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "TransacPagos", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = InfoReciboResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/transacPagos/get_info_recibo",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<InfoReciboResponse> getInfoRecibo(@NotNull @ApiParam(value = "Código de compañía", required = true) @Valid @RequestParam(value = "codCia", required = true) Integer codCia,@NotNull @ApiParam(value = "Número de recibo", required = true) @Valid @RequestParam(value = "numRecibo", required = true) Integer numRecibo) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"numPoliza\" : \"string\",  \"impRecibo\" : \"number\",  \"fecVctoRecibo\" : \"string\",  \"fecSituacion\" : \"string\",  \"codCia\" : \"integer\",  \"tipSituacion\" : \"string\",  \"codMon\" : \"integer\",  \"numRecibo\" : \"integer\"}", InfoReciboResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TransacPagosApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener monto del deducible", nickname = "getMontoDeducible", notes = "Obtiene el monto del deducible de un siniestro específico", response = MontoDeducibleResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "TransacPagos", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = MontoDeducibleResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/transacPagos/get_mto_deducible",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<MontoDeducibleResponse> getMontoDeducible(@NotNull @ApiParam(value = "Código de compañía", required = true) @Valid @RequestParam(value = "codCia", required = true) Integer codCia,@NotNull @ApiParam(value = "Número de siniestro", required = true) @Valid @RequestParam(value = "numSini", required = true) String numSini) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"monto\" : \"number\"}", MontoDeducibleResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TransacPagosApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener monto del deducible para facturación", nickname = "getMontoDeducibleFac", notes = "Obtiene el monto del deducible para facturación de un siniestro específico", response = MontoDeducibleFacResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "TransacPagos", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = MontoDeducibleFacResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/transacPagos/get_mto_deducible_fac",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<MontoDeducibleFacResponse> getMontoDeducibleFac(@NotNull @ApiParam(value = "Código de compañía", required = true) @Valid @RequestParam(value = "codCia", required = true) Integer codCia,@NotNull @ApiParam(value = "Número de siniestro", required = true) @Valid @RequestParam(value = "numSini", required = true) String numSini,@NotNull @ApiParam(value = "Marca de factura ('S' o 'N')", required = true) @Valid @RequestParam(value = "mcaFactura", required = true) String mcaFactura,@NotNull @ApiParam(value = "Número de cuota", required = true) @Valid @RequestParam(value = "numCuota", required = true) Integer numCuota) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"mtoDeducible\" : \"number\",  \"moneda\" : \"string\",  \"mtoCuotas\" : \"number\",  \"mtoIva\" : \"number\",  \"mtoTotal\" : \"number\"}", MontoDeducibleFacResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TransacPagosApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener responsable de pago", nickname = "getResponsablePago", notes = "Obtiene el responsable de pago", response = ResponsablePagoResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "TransacPagos", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = ResponsablePagoResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/transacPagos/get_responsable_pago",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<ResponsablePagoResponse> getResponsablePago(@NotNull @ApiParam(value = "Código de compañía", required = true) @Valid @RequestParam(value = "codCia", required = true) Integer codCia,@NotNull @ApiParam(value = "Tipo de documento", required = true) @Valid @RequestParam(value = "tipDocum", required = true) String tipDocum,@NotNull @ApiParam(value = "Código de documento", required = true) @Valid @RequestParam(value = "codDocum", required = true) String codDocum,@ApiParam(value = "Código de actividad del tercero") @Valid @RequestParam(value = "codActTercero", required = false) Integer codActTercero) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codActTercero\" : \"integer\",  \"tipDocum\" : \"string\",  \"codDocum\" : \"string\",  \"direcCobro\" : \"string\",  \"codCia\" : \"integer\",  \"nombreCompleto\" : \"string\",  \"email\" : \"string\"}", ResponsablePagoResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TransacPagosApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Validar estado del deducible", nickname = "validaEstadoDeducible", notes = "Valida el estado del deducible para un siniestro específico", response = ValidaEstadoDeducibleResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "TransacPagos", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = ValidaEstadoDeducibleResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/transacPagos/valida_estado_deducible",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<ValidaEstadoDeducibleResponse> validaEstadoDeducible(@NotNull @ApiParam(value = "Código de compañía", required = true) @Valid @RequestParam(value = "codCia", required = true) Integer codCia,@NotNull @ApiParam(value = "Número de siniestro", required = true) @Valid @RequestParam(value = "numSini", required = true) String numSini) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"resp\" : \"string\"}", ValidaEstadoDeducibleResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TransacPagosApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Validar tipo de documento", nickname = "validaTipoDocumento", notes = "Valida los tipos de documentos de pago disponibles según el requerimiento", response = TipoDocumentoPagoResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "TransacPagos", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = TipoDocumentoPagoResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/transacPagos/valida_tipo_documento",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<TipoDocumentoPagoResponse> validaTipoDocumento(@NotNull @ApiParam(value = "Código de requerimiento", required = true) @Valid @RequestParam(value = "codDocto", required = true) String codDocto) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"string\",  \"tiposDocumento\" : [ {    \"monto\" : \"number\",    \"noDocto\" : \"string\",    \"mcaAviso\" : \"string\"  }, {    \"monto\" : \"number\",    \"noDocto\" : \"string\",    \"mcaAviso\" : \"string\"  } ],  \"mensaje\" : \"string\"}", TipoDocumentoPagoResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TransacPagosApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

}
