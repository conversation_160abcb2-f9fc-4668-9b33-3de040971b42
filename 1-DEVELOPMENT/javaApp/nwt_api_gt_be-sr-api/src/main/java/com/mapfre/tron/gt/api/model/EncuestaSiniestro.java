package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * EncuestaSiniestro
 */
@Validated

public class EncuestaSiniestro   {
  @JsonProperty("idEncuesta")
  private Integer idEncuesta = null;

  @JsonProperty("encuesta")
  private String encuesta = null;

  @JsonProperty("descripcion")
  private String descripcion = null;

  @JsonProperty("idPregunta")
  private Integer idPregunta = null;

  @JsonProperty("pregunta")
  private String pregunta = null;

  @JsonProperty("idRespuesta")
  private Integer idRespuesta = null;

  @JsonProperty("respuesta")
  private String respuesta = null;

  public EncuestaSiniestro idEncuesta(Integer idEncuesta) {
    this.idEncuesta = idEncuesta;
    return this;
  }

  /**
   * Identificador de la encuesta
   * @return idEncuesta
  **/
  @ApiModelProperty(example = "1", value = "Identificador de la encuesta")


  public Integer getIdEncuesta() {
    return idEncuesta;
  }

  public void setIdEncuesta(Integer idEncuesta) {
    this.idEncuesta = idEncuesta;
  }

  public EncuestaSiniestro encuesta(String encuesta) {
    this.encuesta = encuesta;
    return this;
  }

  /**
   * Nombre de la encuesta
   * @return encuesta
  **/
  @ApiModelProperty(example = "Encuesta de satisfacción", value = "Nombre de la encuesta")


  public String getEncuesta() {
    return encuesta;
  }

  public void setEncuesta(String encuesta) {
    this.encuesta = encuesta;
  }

  public EncuestaSiniestro descripcion(String descripcion) {
    this.descripcion = descripcion;
    return this;
  }

  /**
   * Descripción de la encuesta
   * @return descripcion
  **/
  @ApiModelProperty(example = "Encuesta para evaluar la satisfacción del cliente", value = "Descripción de la encuesta")


  public String getDescripcion() {
    return descripcion;
  }

  public void setDescripcion(String descripcion) {
    this.descripcion = descripcion;
  }

  public EncuestaSiniestro idPregunta(Integer idPregunta) {
    this.idPregunta = idPregunta;
    return this;
  }

  /**
   * Identificador de la pregunta
   * @return idPregunta
  **/
  @ApiModelProperty(example = "1", value = "Identificador de la pregunta")


  public Integer getIdPregunta() {
    return idPregunta;
  }

  public void setIdPregunta(Integer idPregunta) {
    this.idPregunta = idPregunta;
  }

  public EncuestaSiniestro pregunta(String pregunta) {
    this.pregunta = pregunta;
    return this;
  }

  /**
   * Texto de la pregunta
   * @return pregunta
  **/
  @ApiModelProperty(example = "¿Cómo calificaría nuestro servicio?", value = "Texto de la pregunta")


  public String getPregunta() {
    return pregunta;
  }

  public void setPregunta(String pregunta) {
    this.pregunta = pregunta;
  }

  public EncuestaSiniestro idRespuesta(Integer idRespuesta) {
    this.idRespuesta = idRespuesta;
    return this;
  }

  /**
   * Identificador de la respuesta
   * @return idRespuesta
  **/
  @ApiModelProperty(example = "1", value = "Identificador de la respuesta")


  public Integer getIdRespuesta() {
    return idRespuesta;
  }

  public void setIdRespuesta(Integer idRespuesta) {
    this.idRespuesta = idRespuesta;
  }

  public EncuestaSiniestro respuesta(String respuesta) {
    this.respuesta = respuesta;
    return this;
  }

  /**
   * Texto de la respuesta
   * @return respuesta
  **/
  @ApiModelProperty(example = "Excelente", value = "Texto de la respuesta")


  public String getRespuesta() {
    return respuesta;
  }

  public void setRespuesta(String respuesta) {
    this.respuesta = respuesta;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EncuestaSiniestro encuestaSiniestro = (EncuestaSiniestro) o;
    return Objects.equals(this.idEncuesta, encuestaSiniestro.idEncuesta) &&
        Objects.equals(this.encuesta, encuestaSiniestro.encuesta) &&
        Objects.equals(this.descripcion, encuestaSiniestro.descripcion) &&
        Objects.equals(this.idPregunta, encuestaSiniestro.idPregunta) &&
        Objects.equals(this.pregunta, encuestaSiniestro.pregunta) &&
        Objects.equals(this.idRespuesta, encuestaSiniestro.idRespuesta) &&
        Objects.equals(this.respuesta, encuestaSiniestro.respuesta);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idEncuesta, encuesta, descripcion, idPregunta, pregunta, idRespuesta, respuesta);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EncuestaSiniestro {\n");
    
    sb.append("    idEncuesta: ").append(toIndentedString(idEncuesta)).append("\n");
    sb.append("    encuesta: ").append(toIndentedString(encuesta)).append("\n");
    sb.append("    descripcion: ").append(toIndentedString(descripcion)).append("\n");
    sb.append("    idPregunta: ").append(toIndentedString(idPregunta)).append("\n");
    sb.append("    pregunta: ").append(toIndentedString(pregunta)).append("\n");
    sb.append("    idRespuesta: ").append(toIndentedString(idRespuesta)).append("\n");
    sb.append("    respuesta: ").append(toIndentedString(respuesta)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

