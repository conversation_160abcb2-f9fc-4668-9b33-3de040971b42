package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * MontoDeducibleFacResponse
 */
@Validated

public class MontoDeducibleFacResponse   {
  @JsonProperty("moneda")
  private String moneda = null;

  @JsonProperty("mtoDeducible")
  private Double mtoDeducible = null;

  @JsonProperty("mtoIva")
  private Double mtoIva = null;

  @JsonProperty("mtoCuotas")
  private Double mtoCuotas = null;

  @JsonProperty("mtoTotal")
  private Double mtoTotal = null;

  public MontoDeducibleFacResponse moneda(String moneda) {
    this.moneda = moneda;
    return this;
  }

  /**
   * <PERSON><PERSON><PERSON> de la moneda
   * @return moneda
  **/
  @ApiModelProperty(example = "string", value = "Código de la moneda")


  public String getMoneda() {
    return moneda;
  }

  public void setMoneda(String moneda) {
    this.moneda = moneda;
  }

  public MontoDeducibleFacResponse mtoDeducible(Double mtoDeducible) {
    this.mtoDeducible = mtoDeducible;
    return this;
  }

  /**
   * Monto del deducible
   * @return mtoDeducible
  **/
  @ApiModelProperty(example = "number", value = "Monto del deducible")


  public Double getMtoDeducible() {
    return mtoDeducible;
  }

  public void setMtoDeducible(Double mtoDeducible) {
    this.mtoDeducible = mtoDeducible;
  }

  public MontoDeducibleFacResponse mtoIva(Double mtoIva) {
    this.mtoIva = mtoIva;
    return this;
  }

  /**
   * Monto del IVA
   * @return mtoIva
  **/
  @ApiModelProperty(example = "number", value = "Monto del IVA")


  public Double getMtoIva() {
    return mtoIva;
  }

  public void setMtoIva(Double mtoIva) {
    this.mtoIva = mtoIva;
  }

  public MontoDeducibleFacResponse mtoCuotas(Double mtoCuotas) {
    this.mtoCuotas = mtoCuotas;
    return this;
  }

  /**
   * Monto de las cuotas
   * @return mtoCuotas
  **/
  @ApiModelProperty(example = "number", value = "Monto de las cuotas")


  public Double getMtoCuotas() {
    return mtoCuotas;
  }

  public void setMtoCuotas(Double mtoCuotas) {
    this.mtoCuotas = mtoCuotas;
  }

  public MontoDeducibleFacResponse mtoTotal(Double mtoTotal) {
    this.mtoTotal = mtoTotal;
    return this;
  }

  /**
   * Monto total
   * @return mtoTotal
  **/
  @ApiModelProperty(example = "number", value = "Monto total")


  public Double getMtoTotal() {
    return mtoTotal;
  }

  public void setMtoTotal(Double mtoTotal) {
    this.mtoTotal = mtoTotal;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MontoDeducibleFacResponse montoDeducibleFacResponse = (MontoDeducibleFacResponse) o;
    return Objects.equals(this.moneda, montoDeducibleFacResponse.moneda) &&
        Objects.equals(this.mtoDeducible, montoDeducibleFacResponse.mtoDeducible) &&
        Objects.equals(this.mtoIva, montoDeducibleFacResponse.mtoIva) &&
        Objects.equals(this.mtoCuotas, montoDeducibleFacResponse.mtoCuotas) &&
        Objects.equals(this.mtoTotal, montoDeducibleFacResponse.mtoTotal);
  }

  @Override
  public int hashCode() {
    return Objects.hash(moneda, mtoDeducible, mtoIva, mtoCuotas, mtoTotal);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MontoDeducibleFacResponse {\n");
    
    sb.append("    moneda: ").append(toIndentedString(moneda)).append("\n");
    sb.append("    mtoDeducible: ").append(toIndentedString(mtoDeducible)).append("\n");
    sb.append("    mtoIva: ").append(toIndentedString(mtoIva)).append("\n");
    sb.append("    mtoCuotas: ").append(toIndentedString(mtoCuotas)).append("\n");
    sb.append("    mtoTotal: ").append(toIndentedString(mtoTotal)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

