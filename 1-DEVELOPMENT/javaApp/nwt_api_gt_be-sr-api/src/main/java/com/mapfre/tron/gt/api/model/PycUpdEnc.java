package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUpdEnc
 */
@Validated

public class PycUpdEnc   {
  @JsonProperty("encuCalifi")
  private String encuCalifi = null;

  @JsonProperty("encuComent")
  private String encuComent = null;

  @JsonProperty("encuComentAdi")
  private String encuComentAdi = null;

  @JsonProperty("idPeticion")
  private String idPeticion = null;

  public PycUpdEnc encuCalifi(String encuCalifi) {
    this.encuCalifi = encuCalifi;
    return this;
  }

  /**
   * Calificación de la encuesta de satisfacción
   * @return encuCalifi
  **/
  @ApiModelProperty(example = "5", required = true, value = "Calificación de la encuesta de satisfacción")
  @NotNull


  public String getEncuCalifi() {
    return encuCalifi;
  }

  public void setEncuCalifi(String encuCalifi) {
    this.encuCalifi = encuCalifi;
  }

  public PycUpdEnc encuComent(String encuComent) {
    this.encuComent = encuComent;
    return this;
  }

  /**
   * Comentario principal de la encuesta
   * @return encuComent
  **/
  @ApiModelProperty(example = "Excelente servicio, muy satisfecho con la atención recibida", required = true, value = "Comentario principal de la encuesta")
  @NotNull


  public String getEncuComent() {
    return encuComent;
  }

  public void setEncuComent(String encuComent) {
    this.encuComent = encuComent;
  }

  public PycUpdEnc encuComentAdi(String encuComentAdi) {
    this.encuComentAdi = encuComentAdi;
    return this;
  }

  /**
   * Comentario adicional de la encuesta (opcional)
   * @return encuComentAdi
  **/
  @ApiModelProperty(example = "Recomendaría este servicio a otros usuarios", value = "Comentario adicional de la encuesta (opcional)")


  public String getEncuComentAdi() {
    return encuComentAdi;
  }

  public void setEncuComentAdi(String encuComentAdi) {
    this.encuComentAdi = encuComentAdi;
  }

  public PycUpdEnc idPeticion(String idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición a evaluar
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", required = true, value = "Identificador de la petición a evaluar")
  @NotNull


  public String getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(String idPeticion) {
    this.idPeticion = idPeticion;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUpdEnc pycUpdEnc = (PycUpdEnc) o;
    return Objects.equals(this.encuCalifi, pycUpdEnc.encuCalifi) &&
        Objects.equals(this.encuComent, pycUpdEnc.encuComent) &&
        Objects.equals(this.encuComentAdi, pycUpdEnc.encuComentAdi) &&
        Objects.equals(this.idPeticion, pycUpdEnc.idPeticion);
  }

  @Override
  public int hashCode() {
    return Objects.hash(encuCalifi, encuComent, encuComentAdi, idPeticion);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUpdEnc {\n");
    
    sb.append("    encuCalifi: ").append(toIndentedString(encuCalifi)).append("\n");
    sb.append("    encuComent: ").append(toIndentedString(encuComent)).append("\n");
    sb.append("    encuComentAdi: ").append(toIndentedString(encuComentAdi)).append("\n");
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

