package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycDepartamento
 */
@Validated

public class PycDepartamento   {
  @JsonProperty("idDepartamento")
  private Integer idDepartamento = null;

  @JsonProperty("nombreDepartamento")
  private String nombreDepartamento = null;

  public PycDepartamento idDepartamento(Integer idDepartamento) {
    this.idDepartamento = idDepartamento;
    return this;
  }

  /**
   * Identificador del departamento
   * @return idDepartamento
  **/
  @ApiModelProperty(example = "1", value = "Identificador del departamento")


  public Integer getIdDepartamento() {
    return idDepartamento;
  }

  public void setIdDepartamento(Integer idDepartamento) {
    this.idDepartamento = idDepartamento;
  }

  public PycDepartamento nombreDepartamento(String nombreDepartamento) {
    this.nombreDepartamento = nombreDepartamento;
    return this;
  }

  /**
   * Nombre del departamento
   * @return nombreDepartamento
  **/
  @ApiModelProperty(example = "Desarrollo de Software", value = "Nombre del departamento")


  public String getNombreDepartamento() {
    return nombreDepartamento;
  }

  public void setNombreDepartamento(String nombreDepartamento) {
    this.nombreDepartamento = nombreDepartamento;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycDepartamento pycDepartamento = (PycDepartamento) o;
    return Objects.equals(this.idDepartamento, pycDepartamento.idDepartamento) &&
        Objects.equals(this.nombreDepartamento, pycDepartamento.nombreDepartamento);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idDepartamento, nombreDepartamento);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycDepartamento {\n");
    
    sb.append("    idDepartamento: ").append(toIndentedString(idDepartamento)).append("\n");
    sb.append("    nombreDepartamento: ").append(toIndentedString(nombreDepartamento)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

