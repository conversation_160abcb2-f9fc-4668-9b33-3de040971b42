package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycSubordinados
 */
@Validated

public class PycSubordinados   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("subordinado")
  private String subordinado = null;

  @JsonProperty("nsubs")
  private String nsubs = null;

  public PycSubordinados idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Identificador del usuario subordinado
   * @return idUsuario
  **/
  @ApiModelProperty(example = "1234", value = "Identificador del usuario subordinado")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycSubordinados subordinado(String subordinado) {
    this.subordinado = subordinado;
    return this;
  }

  /**
   * Nombre completo del subordinado (concatenación de nombres y apellidos)
   * @return subordinado
  **/
  @ApiModelProperty(example = "Juan Carlos Pérez González", value = "Nombre completo del subordinado (concatenación de nombres y apellidos)")


  public String getSubordinado() {
    return subordinado;
  }

  public void setSubordinado(String subordinado) {
    this.subordinado = subordinado;
  }

  public PycSubordinados nsubs(String nsubs) {
    this.nsubs = nsubs;
    return this;
  }

  /**
   * Número de subordinados o indicador de validación
   * @return nsubs
  **/
  @ApiModelProperty(example = "S", value = "Número de subordinados o indicador de validación")


  public String getNsubs() {
    return nsubs;
  }

  public void setNsubs(String nsubs) {
    this.nsubs = nsubs;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycSubordinados pycSubordinados = (PycSubordinados) o;
    return Objects.equals(this.idUsuario, pycSubordinados.idUsuario) &&
        Objects.equals(this.subordinado, pycSubordinados.subordinado) &&
        Objects.equals(this.nsubs, pycSubordinados.nsubs);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, subordinado, nsubs);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycSubordinados {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    subordinado: ").append(toIndentedString(subordinado)).append("\n");
    sb.append("    nsubs: ").append(toIndentedString(nsubs)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

