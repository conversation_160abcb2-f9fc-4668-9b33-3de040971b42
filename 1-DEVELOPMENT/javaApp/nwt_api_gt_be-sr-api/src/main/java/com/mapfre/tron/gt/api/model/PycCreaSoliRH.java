package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycCreaSoliRH
 */
@Validated

public class PycCreaSoliRH   {
  @JsonProperty("nomCli")
  private String nomCli = null;

  @JsonProperty("telCli")
  private String telCli = null;

  @JsonProperty("emailCli")
  private String emailCli = null;

  @JsonProperty("fecNac")
  private String fecNac = null;

  @JsonProperty("dpi")
  private String dpi = null;

  @JsonProperty("nit")
  private String nit = null;

  @JsonProperty("descEsc")
  private String descEsc = null;

  @JsonProperty("codEsc")
  private String codEsc = null;

  @JsonProperty("titulo")
  private String titulo = null;

  @JsonProperty("direccion")
  private String direccion = null;

  @JsonProperty("codDpto")
  private String codDpto = null;

  @JsonProperty("descDpto")
  private String descDpto = null;

  @JsonProperty("codTrb")
  private String codTrb = null;

  @JsonProperty("descTrb")
  private String descTrb = null;

  @JsonProperty("codSeg")
  private String codSeg = null;

  @JsonProperty("descSeg")
  private String descSeg = null;

  @JsonProperty("preten")
  private String preten = null;

  @JsonProperty("codPlaza")
  private String codPlaza = null;

  @JsonProperty("descPlaz")
  private String descPlaz = null;

  public PycCreaSoliRH nomCli(String nomCli) {
    this.nomCli = nomCli;
    return this;
  }

  /**
   * Nombre del cliente
   * @return nomCli
  **/
  @ApiModelProperty(example = "Juan Carlos Pérez García", required = true, value = "Nombre del cliente")
  @NotNull


  public String getNomCli() {
    return nomCli;
  }

  public void setNomCli(String nomCli) {
    this.nomCli = nomCli;
  }

  public PycCreaSoliRH telCli(String telCli) {
    this.telCli = telCli;
    return this;
  }

  /**
   * Teléfono del cliente
   * @return telCli
  **/
  @ApiModelProperty(example = "2345-6789", required = true, value = "Teléfono del cliente")
  @NotNull


  public String getTelCli() {
    return telCli;
  }

  public void setTelCli(String telCli) {
    this.telCli = telCli;
  }

  public PycCreaSoliRH emailCli(String emailCli) {
    this.emailCli = emailCli;
    return this;
  }

  /**
   * Correo electrónico del cliente
   * @return emailCli
  **/
  @ApiModelProperty(example = "<EMAIL>", required = true, value = "Correo electrónico del cliente")
  @NotNull


  public String getEmailCli() {
    return emailCli;
  }

  public void setEmailCli(String emailCli) {
    this.emailCli = emailCli;
  }

  public PycCreaSoliRH fecNac(String fecNac) {
    this.fecNac = fecNac;
    return this;
  }

  /**
   * Fecha de nacimiento (formato VARCHAR2)
   * @return fecNac
  **/
  @ApiModelProperty(example = "15/03/1990", required = true, value = "Fecha de nacimiento (formato VARCHAR2)")
  @NotNull


  public String getFecNac() {
    return fecNac;
  }

  public void setFecNac(String fecNac) {
    this.fecNac = fecNac;
  }

  public PycCreaSoliRH dpi(String dpi) {
    this.dpi = dpi;
    return this;
  }

  /**
   * Número de DPI (Documento Personal de Identificación)
   * @return dpi
  **/
  @ApiModelProperty(example = "1234567890101", required = true, value = "Número de DPI (Documento Personal de Identificación)")
  @NotNull


  public String getDpi() {
    return dpi;
  }

  public void setDpi(String dpi) {
    this.dpi = dpi;
  }

  public PycCreaSoliRH nit(String nit) {
    this.nit = nit;
    return this;
  }

  /**
   * Número de NIT (Número de Identificación Tributaria)
   * @return nit
  **/
  @ApiModelProperty(example = "123456789", required = true, value = "Número de NIT (Número de Identificación Tributaria)")
  @NotNull


  public String getNit() {
    return nit;
  }

  public void setNit(String nit) {
    this.nit = nit;
  }

  public PycCreaSoliRH descEsc(String descEsc) {
    this.descEsc = descEsc;
    return this;
  }

  /**
   * Descripción de escolaridad
   * @return descEsc
  **/
  @ApiModelProperty(example = "Licenciatura en Ingeniería en Sistemas", required = true, value = "Descripción de escolaridad")
  @NotNull


  public String getDescEsc() {
    return descEsc;
  }

  public void setDescEsc(String descEsc) {
    this.descEsc = descEsc;
  }

  public PycCreaSoliRH codEsc(String codEsc) {
    this.codEsc = codEsc;
    return this;
  }

  /**
   * Código de escolaridad
   * @return codEsc
  **/
  @ApiModelProperty(example = "LIC_ING_SIS", required = true, value = "Código de escolaridad")
  @NotNull


  public String getCodEsc() {
    return codEsc;
  }

  public void setCodEsc(String codEsc) {
    this.codEsc = codEsc;
  }

  public PycCreaSoliRH titulo(String titulo) {
    this.titulo = titulo;
    return this;
  }

  /**
   * Título académico
   * @return titulo
  **/
  @ApiModelProperty(example = "Ingeniero en Sistemas", required = true, value = "Título académico")
  @NotNull


  public String getTitulo() {
    return titulo;
  }

  public void setTitulo(String titulo) {
    this.titulo = titulo;
  }

  public PycCreaSoliRH direccion(String direccion) {
    this.direccion = direccion;
    return this;
  }

  /**
   * Dirección de residencia
   * @return direccion
  **/
  @ApiModelProperty(example = "5ta Avenida 12-34 Zona 10, Ciudad de Guatemala", required = true, value = "Dirección de residencia")
  @NotNull


  public String getDireccion() {
    return direccion;
  }

  public void setDireccion(String direccion) {
    this.direccion = direccion;
  }

  public PycCreaSoliRH codDpto(String codDpto) {
    this.codDpto = codDpto;
    return this;
  }

  /**
   * Código de departamento
   * @return codDpto
  **/
  @ApiModelProperty(example = "GT", required = true, value = "Código de departamento")
  @NotNull


  public String getCodDpto() {
    return codDpto;
  }

  public void setCodDpto(String codDpto) {
    this.codDpto = codDpto;
  }

  public PycCreaSoliRH descDpto(String descDpto) {
    this.descDpto = descDpto;
    return this;
  }

  /**
   * Descripción de departamento
   * @return descDpto
  **/
  @ApiModelProperty(example = "Guatemala", required = true, value = "Descripción de departamento")
  @NotNull


  public String getDescDpto() {
    return descDpto;
  }

  public void setDescDpto(String descDpto) {
    this.descDpto = descDpto;
  }

  public PycCreaSoliRH codTrb(String codTrb) {
    this.codTrb = codTrb;
    return this;
  }

  /**
   * Código de trabajo
   * @return codTrb
  **/
  @ApiModelProperty(example = "DEV_SR", required = true, value = "Código de trabajo")
  @NotNull


  public String getCodTrb() {
    return codTrb;
  }

  public void setCodTrb(String codTrb) {
    this.codTrb = codTrb;
  }

  public PycCreaSoliRH descTrb(String descTrb) {
    this.descTrb = descTrb;
    return this;
  }

  /**
   * Descripción de trabajo
   * @return descTrb
  **/
  @ApiModelProperty(example = "Desarrollador Senior", required = true, value = "Descripción de trabajo")
  @NotNull


  public String getDescTrb() {
    return descTrb;
  }

  public void setDescTrb(String descTrb) {
    this.descTrb = descTrb;
  }

  public PycCreaSoliRH codSeg(String codSeg) {
    this.codSeg = codSeg;
    return this;
  }

  /**
   * Código de seguro
   * @return codSeg
  **/
  @ApiModelProperty(example = "IGSS", required = true, value = "Código de seguro")
  @NotNull


  public String getCodSeg() {
    return codSeg;
  }

  public void setCodSeg(String codSeg) {
    this.codSeg = codSeg;
  }

  public PycCreaSoliRH descSeg(String descSeg) {
    this.descSeg = descSeg;
    return this;
  }

  /**
   * Descripción de seguro
   * @return descSeg
  **/
  @ApiModelProperty(example = "Instituto Guatemalteco de Seguridad Social", required = true, value = "Descripción de seguro")
  @NotNull


  public String getDescSeg() {
    return descSeg;
  }

  public void setDescSeg(String descSeg) {
    this.descSeg = descSeg;
  }

  public PycCreaSoliRH preten(String preten) {
    this.preten = preten;
    return this;
  }

  /**
   * Pretensión salarial
   * @return preten
  **/
  @ApiModelProperty(example = "15,000.00", required = true, value = "Pretensión salarial")
  @NotNull


  public String getPreten() {
    return preten;
  }

  public void setPreten(String preten) {
    this.preten = preten;
  }

  public PycCreaSoliRH codPlaza(String codPlaza) {
    this.codPlaza = codPlaza;
    return this;
  }

  /**
   * Código de plaza
   * @return codPlaza
  **/
  @ApiModelProperty(example = "PLZ_001", required = true, value = "Código de plaza")
  @NotNull


  public String getCodPlaza() {
    return codPlaza;
  }

  public void setCodPlaza(String codPlaza) {
    this.codPlaza = codPlaza;
  }

  public PycCreaSoliRH descPlaz(String descPlaz) {
    this.descPlaz = descPlaz;
    return this;
  }

  /**
   * Descripción de plaza
   * @return descPlaz
  **/
  @ApiModelProperty(example = "Plaza para Desarrollador Senior - Área de Tecnología", required = true, value = "Descripción de plaza")
  @NotNull


  public String getDescPlaz() {
    return descPlaz;
  }

  public void setDescPlaz(String descPlaz) {
    this.descPlaz = descPlaz;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycCreaSoliRH pycCreaSoliRH = (PycCreaSoliRH) o;
    return Objects.equals(this.nomCli, pycCreaSoliRH.nomCli) &&
        Objects.equals(this.telCli, pycCreaSoliRH.telCli) &&
        Objects.equals(this.emailCli, pycCreaSoliRH.emailCli) &&
        Objects.equals(this.fecNac, pycCreaSoliRH.fecNac) &&
        Objects.equals(this.dpi, pycCreaSoliRH.dpi) &&
        Objects.equals(this.nit, pycCreaSoliRH.nit) &&
        Objects.equals(this.descEsc, pycCreaSoliRH.descEsc) &&
        Objects.equals(this.codEsc, pycCreaSoliRH.codEsc) &&
        Objects.equals(this.titulo, pycCreaSoliRH.titulo) &&
        Objects.equals(this.direccion, pycCreaSoliRH.direccion) &&
        Objects.equals(this.codDpto, pycCreaSoliRH.codDpto) &&
        Objects.equals(this.descDpto, pycCreaSoliRH.descDpto) &&
        Objects.equals(this.codTrb, pycCreaSoliRH.codTrb) &&
        Objects.equals(this.descTrb, pycCreaSoliRH.descTrb) &&
        Objects.equals(this.codSeg, pycCreaSoliRH.codSeg) &&
        Objects.equals(this.descSeg, pycCreaSoliRH.descSeg) &&
        Objects.equals(this.preten, pycCreaSoliRH.preten) &&
        Objects.equals(this.codPlaza, pycCreaSoliRH.codPlaza) &&
        Objects.equals(this.descPlaz, pycCreaSoliRH.descPlaz);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nomCli, telCli, emailCli, fecNac, dpi, nit, descEsc, codEsc, titulo, direccion, codDpto, descDpto, codTrb, descTrb, codSeg, descSeg, preten, codPlaza, descPlaz);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycCreaSoliRH {\n");
    
    sb.append("    nomCli: ").append(toIndentedString(nomCli)).append("\n");
    sb.append("    telCli: ").append(toIndentedString(telCli)).append("\n");
    sb.append("    emailCli: ").append(toIndentedString(emailCli)).append("\n");
    sb.append("    fecNac: ").append(toIndentedString(fecNac)).append("\n");
    sb.append("    dpi: ").append(toIndentedString(dpi)).append("\n");
    sb.append("    nit: ").append(toIndentedString(nit)).append("\n");
    sb.append("    descEsc: ").append(toIndentedString(descEsc)).append("\n");
    sb.append("    codEsc: ").append(toIndentedString(codEsc)).append("\n");
    sb.append("    titulo: ").append(toIndentedString(titulo)).append("\n");
    sb.append("    direccion: ").append(toIndentedString(direccion)).append("\n");
    sb.append("    codDpto: ").append(toIndentedString(codDpto)).append("\n");
    sb.append("    descDpto: ").append(toIndentedString(descDpto)).append("\n");
    sb.append("    codTrb: ").append(toIndentedString(codTrb)).append("\n");
    sb.append("    descTrb: ").append(toIndentedString(descTrb)).append("\n");
    sb.append("    codSeg: ").append(toIndentedString(codSeg)).append("\n");
    sb.append("    descSeg: ").append(toIndentedString(descSeg)).append("\n");
    sb.append("    preten: ").append(toIndentedString(preten)).append("\n");
    sb.append("    codPlaza: ").append(toIndentedString(codPlaza)).append("\n");
    sb.append("    descPlaz: ").append(toIndentedString(descPlaz)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

