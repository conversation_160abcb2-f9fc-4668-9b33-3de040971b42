package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycPeticionTab
 */
@Validated

public class PycPeticionTab   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("nombrePeticion")
  private String nombrePeticion = null;

  @JsonProperty("descripcionTipoPeticion")
  private String descripcionTipoPeticion = null;

  @JsonProperty("nombreEstado")
  private String nombreEstado = null;

  @JsonProperty("primerNombre")
  private String primerNombre = null;

  public PycPeticionTab idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador único de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador único de la petición")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycPeticionTab nombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
    return this;
  }

  /**
   * Nombre descriptivo de la petición
   * @return nombrePeticion
  **/
  @ApiModelProperty(example = "Desarrollo de nueva funcionalidad", value = "Nombre descriptivo de la petición")


  public String getNombrePeticion() {
    return nombrePeticion;
  }

  public void setNombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
  }

  public PycPeticionTab descripcionTipoPeticion(String descripcionTipoPeticion) {
    this.descripcionTipoPeticion = descripcionTipoPeticion;
    return this;
  }

  /**
   * Descripción del tipo de petición
   * @return descripcionTipoPeticion
  **/
  @ApiModelProperty(example = "Desarrollo de Software", value = "Descripción del tipo de petición")


  public String getDescripcionTipoPeticion() {
    return descripcionTipoPeticion;
  }

  public void setDescripcionTipoPeticion(String descripcionTipoPeticion) {
    this.descripcionTipoPeticion = descripcionTipoPeticion;
  }

  public PycPeticionTab nombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
    return this;
  }

  /**
   * Nombre del estado actual de la petición
   * @return nombreEstado
  **/
  @ApiModelProperty(example = "En Proceso", value = "Nombre del estado actual de la petición")


  public String getNombreEstado() {
    return nombreEstado;
  }

  public void setNombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
  }

  public PycPeticionTab primerNombre(String primerNombre) {
    this.primerNombre = primerNombre;
    return this;
  }

  /**
   * Primer nombre del usuario solicitante
   * @return primerNombre
  **/
  @ApiModelProperty(example = "Juan", value = "Primer nombre del usuario solicitante")


  public String getPrimerNombre() {
    return primerNombre;
  }

  public void setPrimerNombre(String primerNombre) {
    this.primerNombre = primerNombre;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycPeticionTab pycPeticionTab = (PycPeticionTab) o;
    return Objects.equals(this.idPeticion, pycPeticionTab.idPeticion) &&
        Objects.equals(this.nombrePeticion, pycPeticionTab.nombrePeticion) &&
        Objects.equals(this.descripcionTipoPeticion, pycPeticionTab.descripcionTipoPeticion) &&
        Objects.equals(this.nombreEstado, pycPeticionTab.nombreEstado) &&
        Objects.equals(this.primerNombre, pycPeticionTab.primerNombre);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, nombrePeticion, descripcionTipoPeticion, nombreEstado, primerNombre);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycPeticionTab {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    nombrePeticion: ").append(toIndentedString(nombrePeticion)).append("\n");
    sb.append("    descripcionTipoPeticion: ").append(toIndentedString(descripcionTipoPeticion)).append("\n");
    sb.append("    nombreEstado: ").append(toIndentedString(nombreEstado)).append("\n");
    sb.append("    primerNombre: ").append(toIndentedString(primerNombre)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

