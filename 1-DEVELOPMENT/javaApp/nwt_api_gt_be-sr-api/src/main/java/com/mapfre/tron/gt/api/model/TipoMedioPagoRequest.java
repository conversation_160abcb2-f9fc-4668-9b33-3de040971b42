package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * TipoMedioPagoRequest
 */
@Validated

public class TipoMedioPagoRequest   {
  @JsonProperty("idRuta")
  private Integer idRuta = null;

  @JsonProperty("tipo")
  private String tipo = null;

  @JsonProperty("entidadFinanciera")
  private String entidadFinanciera = null;

  @JsonProperty("entidadFinancieraDesc")
  private String entidadFinancieraDesc = null;

  @JsonProperty("numeroDocumento")
  private String numeroDocumento = null;

  @JsonProperty("entidadTarjeta")
  private String entidadTarjeta = null;

  @JsonProperty("fechaDeposito")
  private String fechaDeposito = null;

  @JsonProperty("fechaVencimiento")
  private String fechaVencimiento = null;

  @JsonProperty("noAutorizacion")
  private String noAutorizacion = null;

  @JsonProperty("noReferencia")
  private String noReferencia = null;

  @JsonProperty("moneda")
  private String moneda = null;

  @JsonProperty("monto")
  private String monto = null;

  @JsonProperty("comentario")
  private String comentario = null;

  @JsonProperty("idUsuarioCobra")
  private Integer idUsuarioCobra = null;

  public TipoMedioPagoRequest idRuta(Integer idRuta) {
    this.idRuta = idRuta;
    return this;
  }

  /**
   * ID de la ruta
   * @return idRuta
  **/
  @ApiModelProperty(example = "1", required = true, value = "ID de la ruta")
  @NotNull


  public Integer getIdRuta() {
    return idRuta;
  }

  public void setIdRuta(Integer idRuta) {
    this.idRuta = idRuta;
  }

  public TipoMedioPagoRequest tipo(String tipo) {
    this.tipo = tipo;
    return this;
  }

  /**
   * Tipo de medio de pago
   * @return tipo
  **/
  @ApiModelProperty(example = "EFECTIVO", required = true, value = "Tipo de medio de pago")
  @NotNull


  public String getTipo() {
    return tipo;
  }

  public void setTipo(String tipo) {
    this.tipo = tipo;
  }

  public TipoMedioPagoRequest entidadFinanciera(String entidadFinanciera) {
    this.entidadFinanciera = entidadFinanciera;
    return this;
  }

  /**
   * Código de la entidad financiera
   * @return entidadFinanciera
  **/
  @ApiModelProperty(example = "001", value = "Código de la entidad financiera")


  public String getEntidadFinanciera() {
    return entidadFinanciera;
  }

  public void setEntidadFinanciera(String entidadFinanciera) {
    this.entidadFinanciera = entidadFinanciera;
  }

  public TipoMedioPagoRequest entidadFinancieraDesc(String entidadFinancieraDesc) {
    this.entidadFinancieraDesc = entidadFinancieraDesc;
    return this;
  }

  /**
   * Descripción de la entidad financiera
   * @return entidadFinancieraDesc
  **/
  @ApiModelProperty(example = "Banco Industrial", value = "Descripción de la entidad financiera")


  public String getEntidadFinancieraDesc() {
    return entidadFinancieraDesc;
  }

  public void setEntidadFinancieraDesc(String entidadFinancieraDesc) {
    this.entidadFinancieraDesc = entidadFinancieraDesc;
  }

  public TipoMedioPagoRequest numeroDocumento(String numeroDocumento) {
    this.numeroDocumento = numeroDocumento;
    return this;
  }

  /**
   * Número de documento
   * @return numeroDocumento
  **/
  @ApiModelProperty(example = "123456789", value = "Número de documento")


  public String getNumeroDocumento() {
    return numeroDocumento;
  }

  public void setNumeroDocumento(String numeroDocumento) {
    this.numeroDocumento = numeroDocumento;
  }

  public TipoMedioPagoRequest entidadTarjeta(String entidadTarjeta) {
    this.entidadTarjeta = entidadTarjeta;
    return this;
  }

  /**
   * Entidad de la tarjeta
   * @return entidadTarjeta
  **/
  @ApiModelProperty(example = "VISA", value = "Entidad de la tarjeta")


  public String getEntidadTarjeta() {
    return entidadTarjeta;
  }

  public void setEntidadTarjeta(String entidadTarjeta) {
    this.entidadTarjeta = entidadTarjeta;
  }

  public TipoMedioPagoRequest fechaDeposito(String fechaDeposito) {
    this.fechaDeposito = fechaDeposito;
    return this;
  }

  /**
   * Fecha de depósito (formato yyyy-MM-dd)
   * @return fechaDeposito
  **/
  @ApiModelProperty(example = "2024-12-15", value = "Fecha de depósito (formato yyyy-MM-dd)")


  public String getFechaDeposito() {
    return fechaDeposito;
  }

  public void setFechaDeposito(String fechaDeposito) {
    this.fechaDeposito = fechaDeposito;
  }

  public TipoMedioPagoRequest fechaVencimiento(String fechaVencimiento) {
    this.fechaVencimiento = fechaVencimiento;
    return this;
  }

  /**
   * Fecha de vencimiento (formato yyyy-MM-dd)
   * @return fechaVencimiento
  **/
  @ApiModelProperty(example = "2024-12-31", value = "Fecha de vencimiento (formato yyyy-MM-dd)")


  public String getFechaVencimiento() {
    return fechaVencimiento;
  }

  public void setFechaVencimiento(String fechaVencimiento) {
    this.fechaVencimiento = fechaVencimiento;
  }

  public TipoMedioPagoRequest noAutorizacion(String noAutorizacion) {
    this.noAutorizacion = noAutorizacion;
    return this;
  }

  /**
   * Número de autorización
   * @return noAutorizacion
  **/
  @ApiModelProperty(example = "AUTH123456", value = "Número de autorización")


  public String getNoAutorizacion() {
    return noAutorizacion;
  }

  public void setNoAutorizacion(String noAutorizacion) {
    this.noAutorizacion = noAutorizacion;
  }

  public TipoMedioPagoRequest noReferencia(String noReferencia) {
    this.noReferencia = noReferencia;
    return this;
  }

  /**
   * Número de referencia
   * @return noReferencia
  **/
  @ApiModelProperty(example = "REF789012", value = "Número de referencia")


  public String getNoReferencia() {
    return noReferencia;
  }

  public void setNoReferencia(String noReferencia) {
    this.noReferencia = noReferencia;
  }

  public TipoMedioPagoRequest moneda(String moneda) {
    this.moneda = moneda;
    return this;
  }

  /**
   * Código de moneda
   * @return moneda
  **/
  @ApiModelProperty(example = "Q", required = true, value = "Código de moneda")
  @NotNull


  public String getMoneda() {
    return moneda;
  }

  public void setMoneda(String moneda) {
    this.moneda = moneda;
  }

  public TipoMedioPagoRequest monto(String monto) {
    this.monto = monto;
    return this;
  }

  /**
   * Monto del pago
   * @return monto
  **/
  @ApiModelProperty(example = "1500.00", required = true, value = "Monto del pago")
  @NotNull


  public String getMonto() {
    return monto;
  }

  public void setMonto(String monto) {
    this.monto = monto;
  }

  public TipoMedioPagoRequest comentario(String comentario) {
    this.comentario = comentario;
    return this;
  }

  /**
   * Comentarios adicionales
   * @return comentario
  **/
  @ApiModelProperty(example = "Pago realizado en efectivo", value = "Comentarios adicionales")


  public String getComentario() {
    return comentario;
  }

  public void setComentario(String comentario) {
    this.comentario = comentario;
  }

  public TipoMedioPagoRequest idUsuarioCobra(Integer idUsuarioCobra) {
    this.idUsuarioCobra = idUsuarioCobra;
    return this;
  }

  /**
   * ID del usuario que realiza el cobro
   * @return idUsuarioCobra
  **/
  @ApiModelProperty(example = "123", required = true, value = "ID del usuario que realiza el cobro")
  @NotNull


  public Integer getIdUsuarioCobra() {
    return idUsuarioCobra;
  }

  public void setIdUsuarioCobra(Integer idUsuarioCobra) {
    this.idUsuarioCobra = idUsuarioCobra;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TipoMedioPagoRequest tipoMedioPagoRequest = (TipoMedioPagoRequest) o;
    return Objects.equals(this.idRuta, tipoMedioPagoRequest.idRuta) &&
        Objects.equals(this.tipo, tipoMedioPagoRequest.tipo) &&
        Objects.equals(this.entidadFinanciera, tipoMedioPagoRequest.entidadFinanciera) &&
        Objects.equals(this.entidadFinancieraDesc, tipoMedioPagoRequest.entidadFinancieraDesc) &&
        Objects.equals(this.numeroDocumento, tipoMedioPagoRequest.numeroDocumento) &&
        Objects.equals(this.entidadTarjeta, tipoMedioPagoRequest.entidadTarjeta) &&
        Objects.equals(this.fechaDeposito, tipoMedioPagoRequest.fechaDeposito) &&
        Objects.equals(this.fechaVencimiento, tipoMedioPagoRequest.fechaVencimiento) &&
        Objects.equals(this.noAutorizacion, tipoMedioPagoRequest.noAutorizacion) &&
        Objects.equals(this.noReferencia, tipoMedioPagoRequest.noReferencia) &&
        Objects.equals(this.moneda, tipoMedioPagoRequest.moneda) &&
        Objects.equals(this.monto, tipoMedioPagoRequest.monto) &&
        Objects.equals(this.comentario, tipoMedioPagoRequest.comentario) &&
        Objects.equals(this.idUsuarioCobra, tipoMedioPagoRequest.idUsuarioCobra);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idRuta, tipo, entidadFinanciera, entidadFinancieraDesc, numeroDocumento, entidadTarjeta, fechaDeposito, fechaVencimiento, noAutorizacion, noReferencia, moneda, monto, comentario, idUsuarioCobra);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TipoMedioPagoRequest {\n");
    
    sb.append("    idRuta: ").append(toIndentedString(idRuta)).append("\n");
    sb.append("    tipo: ").append(toIndentedString(tipo)).append("\n");
    sb.append("    entidadFinanciera: ").append(toIndentedString(entidadFinanciera)).append("\n");
    sb.append("    entidadFinancieraDesc: ").append(toIndentedString(entidadFinancieraDesc)).append("\n");
    sb.append("    numeroDocumento: ").append(toIndentedString(numeroDocumento)).append("\n");
    sb.append("    entidadTarjeta: ").append(toIndentedString(entidadTarjeta)).append("\n");
    sb.append("    fechaDeposito: ").append(toIndentedString(fechaDeposito)).append("\n");
    sb.append("    fechaVencimiento: ").append(toIndentedString(fechaVencimiento)).append("\n");
    sb.append("    noAutorizacion: ").append(toIndentedString(noAutorizacion)).append("\n");
    sb.append("    noReferencia: ").append(toIndentedString(noReferencia)).append("\n");
    sb.append("    moneda: ").append(toIndentedString(moneda)).append("\n");
    sb.append("    monto: ").append(toIndentedString(monto)).append("\n");
    sb.append("    comentario: ").append(toIndentedString(comentario)).append("\n");
    sb.append("    idUsuarioCobra: ").append(toIndentedString(idUsuarioCobra)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

