package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * LocalizacionPagosResponse
 */
@Validated

public class LocalizacionPagosResponse   {
  @JsonProperty("idDetalle")
  private Integer idDetalle = null;

  @JsonProperty("asegurado")
  private String asegurado = null;

  @JsonProperty("idRuta")
  private Integer idRuta = null;

  @JsonProperty("idePol")
  private String idePol = null;

  @JsonProperty("recibo")
  private String recibo = null;

  @JsonProperty("certificado")
  private String certificado = null;

  @JsonProperty("moneda")
  private String moneda = null;

  @JsonProperty("total")
  private String total = null;

  @JsonProperty("cuota")
  private String cuota = null;

  @JsonProperty("latitud")
  private String latitud = null;

  @JsonProperty("longitud")
  private String longitud = null;

  @JsonProperty("nombre")
  private String nombre = null;

  @JsonProperty("fechaCobro")
  private String fechaCobro = null;

  public LocalizacionPagosResponse idDetalle(Integer idDetalle) {
    this.idDetalle = idDetalle;
    return this;
  }

  /**
   * Id  de la ruta
   * @return idDetalle
  **/
  @ApiModelProperty(example = "4", value = "Id  de la ruta")


  public Integer getIdDetalle() {
    return idDetalle;
  }

  public void setIdDetalle(Integer idDetalle) {
    this.idDetalle = idDetalle;
  }

  public LocalizacionPagosResponse asegurado(String asegurado) {
    this.asegurado = asegurado;
    return this;
  }

  /**
   * Nombre asegurado
   * @return asegurado
  **/
  @ApiModelProperty(example = "Sociedad anonima", value = "Nombre asegurado")


  public String getAsegurado() {
    return asegurado;
  }

  public void setAsegurado(String asegurado) {
    this.asegurado = asegurado;
  }

  public LocalizacionPagosResponse idRuta(Integer idRuta) {
    this.idRuta = idRuta;
    return this;
  }

  /**
   * Id  de la ruta
   * @return idRuta
  **/
  @ApiModelProperty(example = "4", value = "Id  de la ruta")


  public Integer getIdRuta() {
    return idRuta;
  }

  public void setIdRuta(Integer idRuta) {
    this.idRuta = idRuta;
  }

  public LocalizacionPagosResponse idePol(String idePol) {
    this.idePol = idePol;
    return this;
  }

  /**
   * Id poliza
   * @return idePol
  **/
  @ApiModelProperty(example = "12234567", value = "Id poliza")


  public String getIdePol() {
    return idePol;
  }

  public void setIdePol(String idePol) {
    this.idePol = idePol;
  }

  public LocalizacionPagosResponse recibo(String recibo) {
    this.recibo = recibo;
    return this;
  }

  /**
   * Numero recibo
   * @return recibo
  **/
  @ApiModelProperty(example = "44268442", value = "Numero recibo")


  public String getRecibo() {
    return recibo;
  }

  public void setRecibo(String recibo) {
    this.recibo = recibo;
  }

  public LocalizacionPagosResponse certificado(String certificado) {
    this.certificado = certificado;
    return this;
  }

  /**
   * Numbero certificado
   * @return certificado
  **/
  @ApiModelProperty(example = "1", value = "Numbero certificado")


  public String getCertificado() {
    return certificado;
  }

  public void setCertificado(String certificado) {
    this.certificado = certificado;
  }

  public LocalizacionPagosResponse moneda(String moneda) {
    this.moneda = moneda;
    return this;
  }

  /**
   * Moneda
   * @return moneda
  **/
  @ApiModelProperty(example = "Q", value = "Moneda")


  public String getMoneda() {
    return moneda;
  }

  public void setMoneda(String moneda) {
    this.moneda = moneda;
  }

  public LocalizacionPagosResponse total(String total) {
    this.total = total;
    return this;
  }

  /**
   * total
   * @return total
  **/
  @ApiModelProperty(example = "90.1", value = "total")


  public String getTotal() {
    return total;
  }

  public void setTotal(String total) {
    this.total = total;
  }

  public LocalizacionPagosResponse cuota(String cuota) {
    this.cuota = cuota;
    return this;
  }

  /**
   * cuota
   * @return cuota
  **/
  @ApiModelProperty(example = "4/6", value = "cuota")


  public String getCuota() {
    return cuota;
  }

  public void setCuota(String cuota) {
    this.cuota = cuota;
  }

  public LocalizacionPagosResponse latitud(String latitud) {
    this.latitud = latitud;
    return this;
  }

  /**
   * latitud
   * @return latitud
  **/
  @ApiModelProperty(example = "14.5880933", value = "latitud")


  public String getLatitud() {
    return latitud;
  }

  public void setLatitud(String latitud) {
    this.latitud = latitud;
  }

  public LocalizacionPagosResponse longitud(String longitud) {
    this.longitud = longitud;
    return this;
  }

  /**
   * latitud
   * @return longitud
  **/
  @ApiModelProperty(example = "-90.5925073", value = "latitud")


  public String getLongitud() {
    return longitud;
  }

  public void setLongitud(String longitud) {
    this.longitud = longitud;
  }

  public LocalizacionPagosResponse nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre de la persona
   * @return nombre
  **/
  @ApiModelProperty(example = "Juan Fernandez", value = "Nombre de la persona")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public LocalizacionPagosResponse fechaCobro(String fechaCobro) {
    this.fechaCobro = fechaCobro;
    return this;
  }

  /**
   * Fecha
   * @return fechaCobro
  **/
  @ApiModelProperty(example = "16-12-2020 03:52 PM", value = "Fecha")


  public String getFechaCobro() {
    return fechaCobro;
  }

  public void setFechaCobro(String fechaCobro) {
    this.fechaCobro = fechaCobro;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LocalizacionPagosResponse localizacionPagosResponse = (LocalizacionPagosResponse) o;
    return Objects.equals(this.idDetalle, localizacionPagosResponse.idDetalle) &&
        Objects.equals(this.asegurado, localizacionPagosResponse.asegurado) &&
        Objects.equals(this.idRuta, localizacionPagosResponse.idRuta) &&
        Objects.equals(this.idePol, localizacionPagosResponse.idePol) &&
        Objects.equals(this.recibo, localizacionPagosResponse.recibo) &&
        Objects.equals(this.certificado, localizacionPagosResponse.certificado) &&
        Objects.equals(this.moneda, localizacionPagosResponse.moneda) &&
        Objects.equals(this.total, localizacionPagosResponse.total) &&
        Objects.equals(this.cuota, localizacionPagosResponse.cuota) &&
        Objects.equals(this.latitud, localizacionPagosResponse.latitud) &&
        Objects.equals(this.longitud, localizacionPagosResponse.longitud) &&
        Objects.equals(this.nombre, localizacionPagosResponse.nombre) &&
        Objects.equals(this.fechaCobro, localizacionPagosResponse.fechaCobro);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idDetalle, asegurado, idRuta, idePol, recibo, certificado, moneda, total, cuota, latitud, longitud, nombre, fechaCobro);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LocalizacionPagosResponse {\n");
    
    sb.append("    idDetalle: ").append(toIndentedString(idDetalle)).append("\n");
    sb.append("    asegurado: ").append(toIndentedString(asegurado)).append("\n");
    sb.append("    idRuta: ").append(toIndentedString(idRuta)).append("\n");
    sb.append("    idePol: ").append(toIndentedString(idePol)).append("\n");
    sb.append("    recibo: ").append(toIndentedString(recibo)).append("\n");
    sb.append("    certificado: ").append(toIndentedString(certificado)).append("\n");
    sb.append("    moneda: ").append(toIndentedString(moneda)).append("\n");
    sb.append("    total: ").append(toIndentedString(total)).append("\n");
    sb.append("    cuota: ").append(toIndentedString(cuota)).append("\n");
    sb.append("    latitud: ").append(toIndentedString(latitud)).append("\n");
    sb.append("    longitud: ").append(toIndentedString(longitud)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    fechaCobro: ").append(toIndentedString(fechaCobro)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

