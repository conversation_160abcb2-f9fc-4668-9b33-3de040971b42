package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycControlProc
 */
@Validated

public class PycControlProc   {
  @JsonProperty("tipo")
  private String tipo = null;

  @JsonProperty("idControl")
  private Integer idControl = null;

  @JsonProperty("idHtml")
  private String idHtml = null;

  @JsonProperty("dataJs")
  private String dataJs = null;

  @JsonProperty("nombre")
  private String nombre = null;

  @JsonProperty("icono")
  private String icono = null;

  @JsonProperty("descripcion")
  private String descripcion = null;

  @JsonProperty("lval")
  private String lval = null;

  @JsonProperty("visible")
  private String visible = null;

  @JsonProperty("obligatorio")
  private String obligatorio = null;

  @JsonProperty("defaultVal")
  private String defaultVal = null;

  @JsonProperty("orden")
  private Integer orden = null;

  public PycControlProc tipo(String tipo) {
    this.tipo = tipo;
    return this;
  }

  /**
   * Nombre del tipo de control
   * @return tipo
  **/
  @ApiModelProperty(example = "FORMULARIO", value = "Nombre del tipo de control")


  public String getTipo() {
    return tipo;
  }

  public void setTipo(String tipo) {
    this.tipo = tipo;
  }

  public PycControlProc idControl(Integer idControl) {
    this.idControl = idControl;
    return this;
  }

  /**
   * Identificador único del control
   * @return idControl
  **/
  @ApiModelProperty(example = "101", value = "Identificador único del control")


  public Integer getIdControl() {
    return idControl;
  }

  public void setIdControl(Integer idControl) {
    this.idControl = idControl;
  }

  public PycControlProc idHtml(String idHtml) {
    this.idHtml = idHtml;
    return this;
  }

  /**
   * Identificador HTML del control
   * @return idHtml
  **/
  @ApiModelProperty(example = "input_nombre", value = "Identificador HTML del control")


  public String getIdHtml() {
    return idHtml;
  }

  public void setIdHtml(String idHtml) {
    this.idHtml = idHtml;
  }

  public PycControlProc dataJs(String dataJs) {
    this.dataJs = dataJs;
    return this;
  }

  /**
   * Datos JavaScript asociados al control
   * @return dataJs
  **/
  @ApiModelProperty(example = "{'validation': 'required'}", value = "Datos JavaScript asociados al control")


  public String getDataJs() {
    return dataJs;
  }

  public void setDataJs(String dataJs) {
    this.dataJs = dataJs;
  }

  public PycControlProc nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre del control
   * @return nombre
  **/
  @ApiModelProperty(example = "Nombre del Cliente", value = "Nombre del control")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public PycControlProc icono(String icono) {
    this.icono = icono;
    return this;
  }

  /**
   * Icono asociado al control
   * @return icono
  **/
  @ApiModelProperty(example = "fa-user", value = "Icono asociado al control")


  public String getIcono() {
    return icono;
  }

  public void setIcono(String icono) {
    this.icono = icono;
  }

  public PycControlProc descripcion(String descripcion) {
    this.descripcion = descripcion;
    return this;
  }

  /**
   * Descripción del control
   * @return descripcion
  **/
  @ApiModelProperty(example = "Campo para ingresar el nombre del cliente", value = "Descripción del control")


  public String getDescripcion() {
    return descripcion;
  }

  public void setDescripcion(String descripcion) {
    this.descripcion = descripcion;
  }

  public PycControlProc lval(String lval) {
    this.lval = lval;
    return this;
  }

  /**
   * Lista de valores del control
   * @return lval
  **/
  @ApiModelProperty(example = "OPCION1,OPCION2,OPCION3", value = "Lista de valores del control")


  public String getLval() {
    return lval;
  }

  public void setLval(String lval) {
    this.lval = lval;
  }

  public PycControlProc visible(String visible) {
    this.visible = visible;
    return this;
  }

  /**
   * Indicador de visibilidad del control
   * @return visible
  **/
  @ApiModelProperty(example = "S", value = "Indicador de visibilidad del control")


  public String getVisible() {
    return visible;
  }

  public void setVisible(String visible) {
    this.visible = visible;
  }

  public PycControlProc obligatorio(String obligatorio) {
    this.obligatorio = obligatorio;
    return this;
  }

  /**
   * Indicador si el control es obligatorio
   * @return obligatorio
  **/
  @ApiModelProperty(example = "S", value = "Indicador si el control es obligatorio")


  public String getObligatorio() {
    return obligatorio;
  }

  public void setObligatorio(String obligatorio) {
    this.obligatorio = obligatorio;
  }

  public PycControlProc defaultVal(String defaultVal) {
    this.defaultVal = defaultVal;
    return this;
  }

  /**
   * Valor por defecto del control
   * @return defaultVal
  **/
  @ApiModelProperty(example = "Valor inicial", value = "Valor por defecto del control")


  public String getDefaultVal() {
    return defaultVal;
  }

  public void setDefaultVal(String defaultVal) {
    this.defaultVal = defaultVal;
  }

  public PycControlProc orden(Integer orden) {
    this.orden = orden;
    return this;
  }

  /**
   * Orden de visualización del control
   * @return orden
  **/
  @ApiModelProperty(example = "1", value = "Orden de visualización del control")


  public Integer getOrden() {
    return orden;
  }

  public void setOrden(Integer orden) {
    this.orden = orden;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycControlProc pycControlProc = (PycControlProc) o;
    return Objects.equals(this.tipo, pycControlProc.tipo) &&
        Objects.equals(this.idControl, pycControlProc.idControl) &&
        Objects.equals(this.idHtml, pycControlProc.idHtml) &&
        Objects.equals(this.dataJs, pycControlProc.dataJs) &&
        Objects.equals(this.nombre, pycControlProc.nombre) &&
        Objects.equals(this.icono, pycControlProc.icono) &&
        Objects.equals(this.descripcion, pycControlProc.descripcion) &&
        Objects.equals(this.lval, pycControlProc.lval) &&
        Objects.equals(this.visible, pycControlProc.visible) &&
        Objects.equals(this.obligatorio, pycControlProc.obligatorio) &&
        Objects.equals(this.defaultVal, pycControlProc.defaultVal) &&
        Objects.equals(this.orden, pycControlProc.orden);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tipo, idControl, idHtml, dataJs, nombre, icono, descripcion, lval, visible, obligatorio, defaultVal, orden);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycControlProc {\n");
    
    sb.append("    tipo: ").append(toIndentedString(tipo)).append("\n");
    sb.append("    idControl: ").append(toIndentedString(idControl)).append("\n");
    sb.append("    idHtml: ").append(toIndentedString(idHtml)).append("\n");
    sb.append("    dataJs: ").append(toIndentedString(dataJs)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    icono: ").append(toIndentedString(icono)).append("\n");
    sb.append("    descripcion: ").append(toIndentedString(descripcion)).append("\n");
    sb.append("    lval: ").append(toIndentedString(lval)).append("\n");
    sb.append("    visible: ").append(toIndentedString(visible)).append("\n");
    sb.append("    obligatorio: ").append(toIndentedString(obligatorio)).append("\n");
    sb.append("    defaultVal: ").append(toIndentedString(defaultVal)).append("\n");
    sb.append("    orden: ").append(toIndentedString(orden)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

