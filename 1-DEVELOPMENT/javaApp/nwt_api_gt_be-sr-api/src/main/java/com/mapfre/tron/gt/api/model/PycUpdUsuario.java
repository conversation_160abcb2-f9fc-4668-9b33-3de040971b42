package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUpdUsuario
 */
@Validated

public class PycUpdUsuario   {
  @JsonProperty("primerNombre")
  private String primerNombre = null;

  @JsonProperty("segundoNombre")
  private String segundoNombre = null;

  @JsonProperty("primerApellido")
  private String primerApellido = null;

  @JsonProperty("segundoApellido")
  private String segundoApellido = null;

  @JsonProperty("numaOUsuario")
  private String numaOUsuario = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("email")
  private String email = null;

  @JsonProperty("genero")
  private String genero = null;

  @JsonProperty("idArea")
  private Integer idArea = null;

  @JsonProperty("idDepartamento")
  private Integer idDepartamento = null;

  public PycUpdUsuario primerNombre(String primerNombre) {
    this.primerNombre = primerNombre;
    return this;
  }

  /**
   * Primer nombre del usuario
   * @return primerNombre
  **/
  @ApiModelProperty(example = "Juan", required = true, value = "Primer nombre del usuario")
  @NotNull


  public String getPrimerNombre() {
    return primerNombre;
  }

  public void setPrimerNombre(String primerNombre) {
    this.primerNombre = primerNombre;
  }

  public PycUpdUsuario segundoNombre(String segundoNombre) {
    this.segundoNombre = segundoNombre;
    return this;
  }

  /**
   * Segundo nombre del usuario
   * @return segundoNombre
  **/
  @ApiModelProperty(example = "Carlos", value = "Segundo nombre del usuario")


  public String getSegundoNombre() {
    return segundoNombre;
  }

  public void setSegundoNombre(String segundoNombre) {
    this.segundoNombre = segundoNombre;
  }

  public PycUpdUsuario primerApellido(String primerApellido) {
    this.primerApellido = primerApellido;
    return this;
  }

  /**
   * Primer apellido del usuario
   * @return primerApellido
  **/
  @ApiModelProperty(example = "Pérez", required = true, value = "Primer apellido del usuario")
  @NotNull


  public String getPrimerApellido() {
    return primerApellido;
  }

  public void setPrimerApellido(String primerApellido) {
    this.primerApellido = primerApellido;
  }

  public PycUpdUsuario segundoApellido(String segundoApellido) {
    this.segundoApellido = segundoApellido;
    return this;
  }

  /**
   * Segundo apellido del usuario
   * @return segundoApellido
  **/
  @ApiModelProperty(example = "García", value = "Segundo apellido del usuario")


  public String getSegundoApellido() {
    return segundoApellido;
  }

  public void setSegundoApellido(String segundoApellido) {
    this.segundoApellido = segundoApellido;
  }

  public PycUpdUsuario numaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
    return this;
  }

  /**
   * Nombre o número de usuario
   * @return numaOUsuario
  **/
  @ApiModelProperty(example = "jperez", required = true, value = "Nombre o número de usuario")
  @NotNull


  public String getNumaOUsuario() {
    return numaOUsuario;
  }

  public void setNumaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
  }

  public PycUpdUsuario estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado del usuario (ACT, INA)
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", required = true, value = "Estado del usuario (ACT, INA)")
  @NotNull


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycUpdUsuario email(String email) {
    this.email = email;
    return this;
  }

  /**
   * Correo electrónico del usuario
   * @return email
  **/
  @ApiModelProperty(example = "<EMAIL>", value = "Correo electrónico del usuario")


  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public PycUpdUsuario genero(String genero) {
    this.genero = genero;
    return this;
  }

  /**
   * Género del usuario (M, F)
   * @return genero
  **/
  @ApiModelProperty(example = "M", value = "Género del usuario (M, F)")


  public String getGenero() {
    return genero;
  }

  public void setGenero(String genero) {
    this.genero = genero;
  }

  public PycUpdUsuario idArea(Integer idArea) {
    this.idArea = idArea;
    return this;
  }

  /**
   * Identificador del área a la que pertenece el usuario
   * @return idArea
  **/
  @ApiModelProperty(example = "1", value = "Identificador del área a la que pertenece el usuario")


  public Integer getIdArea() {
    return idArea;
  }

  public void setIdArea(Integer idArea) {
    this.idArea = idArea;
  }

  public PycUpdUsuario idDepartamento(Integer idDepartamento) {
    this.idDepartamento = idDepartamento;
    return this;
  }

  /**
   * Identificador del departamento al que pertenece el usuario
   * @return idDepartamento
  **/
  @ApiModelProperty(example = "2", value = "Identificador del departamento al que pertenece el usuario")


  public Integer getIdDepartamento() {
    return idDepartamento;
  }

  public void setIdDepartamento(Integer idDepartamento) {
    this.idDepartamento = idDepartamento;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUpdUsuario pycUpdUsuario = (PycUpdUsuario) o;
    return Objects.equals(this.primerNombre, pycUpdUsuario.primerNombre) &&
        Objects.equals(this.segundoNombre, pycUpdUsuario.segundoNombre) &&
        Objects.equals(this.primerApellido, pycUpdUsuario.primerApellido) &&
        Objects.equals(this.segundoApellido, pycUpdUsuario.segundoApellido) &&
        Objects.equals(this.numaOUsuario, pycUpdUsuario.numaOUsuario) &&
        Objects.equals(this.estado, pycUpdUsuario.estado) &&
        Objects.equals(this.email, pycUpdUsuario.email) &&
        Objects.equals(this.genero, pycUpdUsuario.genero) &&
        Objects.equals(this.idArea, pycUpdUsuario.idArea) &&
        Objects.equals(this.idDepartamento, pycUpdUsuario.idDepartamento);
  }

  @Override
  public int hashCode() {
    return Objects.hash(primerNombre, segundoNombre, primerApellido, segundoApellido, numaOUsuario, estado, email, genero, idArea, idDepartamento);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUpdUsuario {\n");
    
    sb.append("    primerNombre: ").append(toIndentedString(primerNombre)).append("\n");
    sb.append("    segundoNombre: ").append(toIndentedString(segundoNombre)).append("\n");
    sb.append("    primerApellido: ").append(toIndentedString(primerApellido)).append("\n");
    sb.append("    segundoApellido: ").append(toIndentedString(segundoApellido)).append("\n");
    sb.append("    numaOUsuario: ").append(toIndentedString(numaOUsuario)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    genero: ").append(toIndentedString(genero)).append("\n");
    sb.append("    idArea: ").append(toIndentedString(idArea)).append("\n");
    sb.append("    idDepartamento: ").append(toIndentedString(idDepartamento)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

