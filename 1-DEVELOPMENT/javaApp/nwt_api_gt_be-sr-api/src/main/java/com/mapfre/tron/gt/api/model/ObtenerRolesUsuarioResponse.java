package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.mapfre.tron.gt.api.model.ObtenerRolesUsuarioResponseRoles;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ObtenerRolesUsuarioResponse
 */
@Validated

public class ObtenerRolesUsuarioResponse   {
  @JsonProperty("codigo")
  private String codigo = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  @JsonProperty("roles")
  @Valid
  private List<ObtenerRolesUsuarioResponseRoles> roles = null;

  public ObtenerRolesUsuarioResponse codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Código de resultado de la operación
   * @return codigo
  **/
  @ApiModelProperty(example = "200", value = "Código de resultado de la operación")


  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public ObtenerRolesUsuarioResponse mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje descriptivo del resultado de la operación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Roles obtenidos exitosamente", value = "Mensaje descriptivo del resultado de la operación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }

  public ObtenerRolesUsuarioResponse roles(List<ObtenerRolesUsuarioResponseRoles> roles) {
    this.roles = roles;
    return this;
  }

  public ObtenerRolesUsuarioResponse addRolesItem(ObtenerRolesUsuarioResponseRoles rolesItem) {
    if (this.roles == null) {
      this.roles = new ArrayList<>();
    }
    this.roles.add(rolesItem);
    return this;
  }

  /**
   * Lista de roles del usuario
   * @return roles
  **/
  @ApiModelProperty(value = "Lista de roles del usuario")

  @Valid

  public List<ObtenerRolesUsuarioResponseRoles> getRoles() {
    return roles;
  }

  public void setRoles(List<ObtenerRolesUsuarioResponseRoles> roles) {
    this.roles = roles;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ObtenerRolesUsuarioResponse obtenerRolesUsuarioResponse = (ObtenerRolesUsuarioResponse) o;
    return Objects.equals(this.codigo, obtenerRolesUsuarioResponse.codigo) &&
        Objects.equals(this.mensaje, obtenerRolesUsuarioResponse.mensaje) &&
        Objects.equals(this.roles, obtenerRolesUsuarioResponse.roles);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codigo, mensaje, roles);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ObtenerRolesUsuarioResponse {\n");
    
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("    roles: ").append(toIndentedString(roles)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

