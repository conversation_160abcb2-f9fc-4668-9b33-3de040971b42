package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * SiniestroVehiculo
 */
@Validated

public class SiniestroVehiculo   {
  @JsonProperty("codEstado")
  private String codEstado = null;

  @JsonProperty("nomEstado")
  private String nomEstado = null;

  @JsonProperty("jsonArchivos")
  private String jsonArchivos = null;

  @JsonProperty("fechaActualizacion")
  private String fechaActualizacion = null;

  @JsonProperty("imagen")
  private String imagen = null;

  public SiniestroVehiculo codEstado(String codEstado) {
    this.codEstado = codEstado;
    return this;
  }

  /**
   * Código de estado del siniestro
   * @return codEstado
  **/
  @ApiModelProperty(example = "TR01", value = "Código de estado del siniestro")


  public String getCodEstado() {
    return codEstado;
  }

  public void setCodEstado(String codEstado) {
    this.codEstado = codEstado;
  }

  public SiniestroVehiculo nomEstado(String nomEstado) {
    this.nomEstado = nomEstado;
    return this;
  }

  /**
   * Nombre del estado del siniestro
   * @return nomEstado
  **/
  @ApiModelProperty(example = "Recepción del vehículo", value = "Nombre del estado del siniestro")


  public String getNomEstado() {
    return nomEstado;
  }

  public void setNomEstado(String nomEstado) {
    this.nomEstado = nomEstado;
  }

  public SiniestroVehiculo jsonArchivos(String jsonArchivos) {
    this.jsonArchivos = jsonArchivos;
    return this;
  }

  /**
   * JSON con información de archivos asociados
   * @return jsonArchivos
  **/
  @ApiModelProperty(example = "{}", value = "JSON con información de archivos asociados")


  public String getJsonArchivos() {
    return jsonArchivos;
  }

  public void setJsonArchivos(String jsonArchivos) {
    this.jsonArchivos = jsonArchivos;
  }

  public SiniestroVehiculo fechaActualizacion(String fechaActualizacion) {
    this.fechaActualizacion = fechaActualizacion;
    return this;
  }

  /**
   * Fecha de actualización del estado
   * @return fechaActualizacion
  **/
  @ApiModelProperty(example = "05/05/2025 21:11:20", value = "Fecha de actualización del estado")


  public String getFechaActualizacion() {
    return fechaActualizacion;
  }

  public void setFechaActualizacion(String fechaActualizacion) {
    this.fechaActualizacion = fechaActualizacion;
  }

  public SiniestroVehiculo imagen(String imagen) {
    this.imagen = imagen;
    return this;
  }

  /**
   * URL de la imagen asociada al estado
   * @return imagen
  **/
  @ApiModelProperty(example = "https://ejemplo.com/imagen.png", value = "URL de la imagen asociada al estado")


  public String getImagen() {
    return imagen;
  }

  public void setImagen(String imagen) {
    this.imagen = imagen;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SiniestroVehiculo siniestroVehiculo = (SiniestroVehiculo) o;
    return Objects.equals(this.codEstado, siniestroVehiculo.codEstado) &&
        Objects.equals(this.nomEstado, siniestroVehiculo.nomEstado) &&
        Objects.equals(this.jsonArchivos, siniestroVehiculo.jsonArchivos) &&
        Objects.equals(this.fechaActualizacion, siniestroVehiculo.fechaActualizacion) &&
        Objects.equals(this.imagen, siniestroVehiculo.imagen);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codEstado, nomEstado, jsonArchivos, fechaActualizacion, imagen);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SiniestroVehiculo {\n");
    
    sb.append("    codEstado: ").append(toIndentedString(codEstado)).append("\n");
    sb.append("    nomEstado: ").append(toIndentedString(nomEstado)).append("\n");
    sb.append("    jsonArchivos: ").append(toIndentedString(jsonArchivos)).append("\n");
    sb.append("    fechaActualizacion: ").append(toIndentedString(fechaActualizacion)).append("\n");
    sb.append("    imagen: ").append(toIndentedString(imagen)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

