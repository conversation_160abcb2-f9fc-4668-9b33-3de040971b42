package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycInsertObs
 */
@Validated

public class PycInsertObs   {
  @JsonProperty("idObservacion")
  private Integer idObservacion = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  @JsonProperty("ejecutado")
  private Boolean ejecutado = null;

  public PycInsertObs idObservacion(Integer idObservacion) {
    this.idObservacion = idObservacion;
    return this;
  }

  /**
   * ID de la observación insertada
   * @return idObservacion
  **/
  @ApiModelProperty(example = "5001", value = "ID de la observación insertada")


  public Integer getIdObservacion() {
    return idObservacion;
  }

  public void setIdObservacion(Integer idObservacion) {
    this.idObservacion = idObservacion;
  }

  public PycInsertObs mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje de confirmación de la operación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Observación insertada exitosamente", value = "Mensaje de confirmación de la operación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }

  public PycInsertObs ejecutado(Boolean ejecutado) {
    this.ejecutado = ejecutado;
    return this;
  }

  /**
   * Indica si la operación se ejecutó correctamente
   * @return ejecutado
  **/
  @ApiModelProperty(example = "true", value = "Indica si la operación se ejecutó correctamente")


  public Boolean isEjecutado() {
    return ejecutado;
  }

  public void setEjecutado(Boolean ejecutado) {
    this.ejecutado = ejecutado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycInsertObs pycInsertObs = (PycInsertObs) o;
    return Objects.equals(this.idObservacion, pycInsertObs.idObservacion) &&
        Objects.equals(this.mensaje, pycInsertObs.mensaje) &&
        Objects.equals(this.ejecutado, pycInsertObs.ejecutado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idObservacion, mensaje, ejecutado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycInsertObs {\n");
    
    sb.append("    idObservacion: ").append(toIndentedString(idObservacion)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("    ejecutado: ").append(toIndentedString(ejecutado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

