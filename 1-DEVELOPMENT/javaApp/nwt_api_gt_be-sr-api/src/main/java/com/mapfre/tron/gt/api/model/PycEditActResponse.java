package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycEditActResponse
 */
@Validated

public class PycEditActResponse   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  @JsonProperty("ejecutado")
  private Boolean ejecutado = null;

  public PycEditActResponse idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición actualizada
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador de la petición actualizada")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycEditActResponse mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje de confirmación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Actividad actualizada exitosamente", value = "Mensaje de confirmación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }

  public PycEditActResponse ejecutado(Boolean ejecutado) {
    this.ejecutado = ejecutado;
    return this;
  }

  /**
   * Indicador de ejecución exitosa
   * @return ejecutado
  **/
  @ApiModelProperty(example = "true", value = "Indicador de ejecución exitosa")


  public Boolean isEjecutado() {
    return ejecutado;
  }

  public void setEjecutado(Boolean ejecutado) {
    this.ejecutado = ejecutado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycEditActResponse pycEditActResponse = (PycEditActResponse) o;
    return Objects.equals(this.idPeticion, pycEditActResponse.idPeticion) &&
        Objects.equals(this.mensaje, pycEditActResponse.mensaje) &&
        Objects.equals(this.ejecutado, pycEditActResponse.ejecutado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, mensaje, ejecutado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycEditActResponse {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("    ejecutado: ").append(toIndentedString(ejecutado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

