package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycGetBitacoraPet
 */
@Validated

public class PycGetBitacoraPet   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("codPerfilInicial")
  private Integer codPerfilInicial = null;

  @JsonProperty("codPerfilFinal")
  private Integer codPerfilFinal = null;

  @JsonProperty("codEstadoInicial")
  private Integer codEstadoInicial = null;

  @JsonProperty("codEstadoFinal")
  private Integer codEstadoFinal = null;

  @JsonProperty("perfilInicial")
  private String perfilInicial = null;

  @JsonProperty("perfilFinal")
  private String perfilFinal = null;

  @JsonProperty("estadoInicial")
  private String estadoInicial = null;

  @JsonProperty("estadoFinal")
  private String estadoFinal = null;

  @JsonProperty("responsables")
  private String responsables = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("fecIni")
  private String fecIni = null;

  @JsonProperty("fecFin")
  private String fecFin = null;

  @JsonProperty("anio")
  private Integer anio = null;

  @JsonProperty("mes")
  private Integer mes = null;

  @JsonProperty("dia")
  private Integer dia = null;

  @JsonProperty("hora")
  private Integer hora = null;

  @JsonProperty("minuto")
  private Integer minuto = null;

  @JsonProperty("segundo")
  private Integer segundo = null;

  @JsonProperty("titulo")
  private String titulo = null;

  @JsonProperty("tiempo")
  private String tiempo = null;

  @JsonProperty("icono")
  private String icono = null;

  @JsonProperty("contenido")
  private String contenido = null;

  @JsonProperty("perfilUsuario")
  private String perfilUsuario = null;

  public PycGetBitacoraPet idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador único de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador único de la petición")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycGetBitacoraPet codPerfilInicial(Integer codPerfilInicial) {
    this.codPerfilInicial = codPerfilInicial;
    return this;
  }

  /**
   * Código del perfil inicial
   * @return codPerfilInicial
  **/
  @ApiModelProperty(example = "1", value = "Código del perfil inicial")


  public Integer getCodPerfilInicial() {
    return codPerfilInicial;
  }

  public void setCodPerfilInicial(Integer codPerfilInicial) {
    this.codPerfilInicial = codPerfilInicial;
  }

  public PycGetBitacoraPet codPerfilFinal(Integer codPerfilFinal) {
    this.codPerfilFinal = codPerfilFinal;
    return this;
  }

  /**
   * Código del perfil final
   * @return codPerfilFinal
  **/
  @ApiModelProperty(example = "2", value = "Código del perfil final")


  public Integer getCodPerfilFinal() {
    return codPerfilFinal;
  }

  public void setCodPerfilFinal(Integer codPerfilFinal) {
    this.codPerfilFinal = codPerfilFinal;
  }

  public PycGetBitacoraPet codEstadoInicial(Integer codEstadoInicial) {
    this.codEstadoInicial = codEstadoInicial;
    return this;
  }

  /**
   * Código del estado inicial
   * @return codEstadoInicial
  **/
  @ApiModelProperty(example = "1", value = "Código del estado inicial")


  public Integer getCodEstadoInicial() {
    return codEstadoInicial;
  }

  public void setCodEstadoInicial(Integer codEstadoInicial) {
    this.codEstadoInicial = codEstadoInicial;
  }

  public PycGetBitacoraPet codEstadoFinal(Integer codEstadoFinal) {
    this.codEstadoFinal = codEstadoFinal;
    return this;
  }

  /**
   * Código del estado final
   * @return codEstadoFinal
  **/
  @ApiModelProperty(example = "2", value = "Código del estado final")


  public Integer getCodEstadoFinal() {
    return codEstadoFinal;
  }

  public void setCodEstadoFinal(Integer codEstadoFinal) {
    this.codEstadoFinal = codEstadoFinal;
  }

  public PycGetBitacoraPet perfilInicial(String perfilInicial) {
    this.perfilInicial = perfilInicial;
    return this;
  }

  /**
   * Nombre del perfil inicial
   * @return perfilInicial
  **/
  @ApiModelProperty(example = "Solicitante", value = "Nombre del perfil inicial")


  public String getPerfilInicial() {
    return perfilInicial;
  }

  public void setPerfilInicial(String perfilInicial) {
    this.perfilInicial = perfilInicial;
  }

  public PycGetBitacoraPet perfilFinal(String perfilFinal) {
    this.perfilFinal = perfilFinal;
    return this;
  }

  /**
   * Nombre del perfil final
   * @return perfilFinal
  **/
  @ApiModelProperty(example = "Analista", value = "Nombre del perfil final")


  public String getPerfilFinal() {
    return perfilFinal;
  }

  public void setPerfilFinal(String perfilFinal) {
    this.perfilFinal = perfilFinal;
  }

  public PycGetBitacoraPet estadoInicial(String estadoInicial) {
    this.estadoInicial = estadoInicial;
    return this;
  }

  /**
   * Nombre del estado inicial
   * @return estadoInicial
  **/
  @ApiModelProperty(example = "Pendiente", value = "Nombre del estado inicial")


  public String getEstadoInicial() {
    return estadoInicial;
  }

  public void setEstadoInicial(String estadoInicial) {
    this.estadoInicial = estadoInicial;
  }

  public PycGetBitacoraPet estadoFinal(String estadoFinal) {
    this.estadoFinal = estadoFinal;
    return this;
  }

  /**
   * Nombre del estado final
   * @return estadoFinal
  **/
  @ApiModelProperty(example = "En Proceso", value = "Nombre del estado final")


  public String getEstadoFinal() {
    return estadoFinal;
  }

  public void setEstadoFinal(String estadoFinal) {
    this.estadoFinal = estadoFinal;
  }

  public PycGetBitacoraPet responsables(String responsables) {
    this.responsables = responsables;
    return this;
  }

  /**
   * Lista de responsables asignados
   * @return responsables
  **/
  @ApiModelProperty(example = "Juan Pérez, María García", value = "Lista de responsables asignados")


  public String getResponsables() {
    return responsables;
  }

  public void setResponsables(String responsables) {
    this.responsables = responsables;
  }

  public PycGetBitacoraPet usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Usuario que realizó el cambio
   * @return usuario
  **/
  @ApiModelProperty(example = "Carlos Martínez", value = "Usuario que realizó el cambio")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public PycGetBitacoraPet fecIni(String fecIni) {
    this.fecIni = fecIni;
    return this;
  }

  /**
   * Fecha y hora de inicio del cambio (formato dd/MM/yyyy HH:mm)
   * @return fecIni
  **/
  @ApiModelProperty(example = "15/01/2024 09:30", value = "Fecha y hora de inicio del cambio (formato dd/MM/yyyy HH:mm)")


  public String getFecIni() {
    return fecIni;
  }

  public void setFecIni(String fecIni) {
    this.fecIni = fecIni;
  }

  public PycGetBitacoraPet fecFin(String fecFin) {
    this.fecFin = fecFin;
    return this;
  }

  /**
   * Fecha y hora de fin del cambio (formato dd/MM/yyyy HH:mm)
   * @return fecFin
  **/
  @ApiModelProperty(example = "15/01/2024 14:45", value = "Fecha y hora de fin del cambio (formato dd/MM/yyyy HH:mm)")


  public String getFecFin() {
    return fecFin;
  }

  public void setFecFin(String fecFin) {
    this.fecFin = fecFin;
  }

  public PycGetBitacoraPet anio(Integer anio) {
    this.anio = anio;
    return this;
  }

  /**
   * Años transcurridos en el cambio
   * @return anio
  **/
  @ApiModelProperty(example = "0", value = "Años transcurridos en el cambio")


  public Integer getAnio() {
    return anio;
  }

  public void setAnio(Integer anio) {
    this.anio = anio;
  }

  public PycGetBitacoraPet mes(Integer mes) {
    this.mes = mes;
    return this;
  }

  /**
   * Meses transcurridos en el cambio
   * @return mes
  **/
  @ApiModelProperty(example = "0", value = "Meses transcurridos en el cambio")


  public Integer getMes() {
    return mes;
  }

  public void setMes(Integer mes) {
    this.mes = mes;
  }

  public PycGetBitacoraPet dia(Integer dia) {
    this.dia = dia;
    return this;
  }

  /**
   * Días transcurridos en el cambio
   * @return dia
  **/
  @ApiModelProperty(example = "0", value = "Días transcurridos en el cambio")


  public Integer getDia() {
    return dia;
  }

  public void setDia(Integer dia) {
    this.dia = dia;
  }

  public PycGetBitacoraPet hora(Integer hora) {
    this.hora = hora;
    return this;
  }

  /**
   * Horas transcurridas en el cambio
   * @return hora
  **/
  @ApiModelProperty(example = "5", value = "Horas transcurridas en el cambio")


  public Integer getHora() {
    return hora;
  }

  public void setHora(Integer hora) {
    this.hora = hora;
  }

  public PycGetBitacoraPet minuto(Integer minuto) {
    this.minuto = minuto;
    return this;
  }

  /**
   * Minutos transcurridos en el cambio
   * @return minuto
  **/
  @ApiModelProperty(example = "15", value = "Minutos transcurridos en el cambio")


  public Integer getMinuto() {
    return minuto;
  }

  public void setMinuto(Integer minuto) {
    this.minuto = minuto;
  }

  public PycGetBitacoraPet segundo(Integer segundo) {
    this.segundo = segundo;
    return this;
  }

  /**
   * Segundos transcurridos en el cambio
   * @return segundo
  **/
  @ApiModelProperty(example = "30", value = "Segundos transcurridos en el cambio")


  public Integer getSegundo() {
    return segundo;
  }

  public void setSegundo(Integer segundo) {
    this.segundo = segundo;
  }

  public PycGetBitacoraPet titulo(String titulo) {
    this.titulo = titulo;
    return this;
  }

  /**
   * Título del evento de bitácora
   * @return titulo
  **/
  @ApiModelProperty(example = "CAMBIO DE ESTADO", value = "Título del evento de bitácora")


  public String getTitulo() {
    return titulo;
  }

  public void setTitulo(String titulo) {
    this.titulo = titulo;
  }

  public PycGetBitacoraPet tiempo(String tiempo) {
    this.tiempo = tiempo;
    return this;
  }

  /**
   * Descripción del tiempo transcurrido
   * @return tiempo
  **/
  @ApiModelProperty(example = "5 Horas y 15 min.", value = "Descripción del tiempo transcurrido")


  public String getTiempo() {
    return tiempo;
  }

  public void setTiempo(String tiempo) {
    this.tiempo = tiempo;
  }

  public PycGetBitacoraPet icono(String icono) {
    this.icono = icono;
    return this;
  }

  /**
   * Icono asociado al estado
   * @return icono
  **/
  @ApiModelProperty(example = "fa-clock-o", value = "Icono asociado al estado")


  public String getIcono() {
    return icono;
  }

  public void setIcono(String icono) {
    this.icono = icono;
  }

  public PycGetBitacoraPet contenido(String contenido) {
    this.contenido = contenido;
    return this;
  }

  /**
   * Contenido HTML formateado con detalles del cambio, observaciones y tiempos
   * @return contenido
  **/
  @ApiModelProperty(example = "DE Pendiente A En Proceso - PETICIÓN ASIGNADA PARA ANÁLISIS - Fecha: 15/01/2024 14:45 - Tiempo: 5h 15m 30s", value = "Contenido HTML formateado con detalles del cambio, observaciones y tiempos")


  public String getContenido() {
    return contenido;
  }

  public void setContenido(String contenido) {
    this.contenido = contenido;
  }

  public PycGetBitacoraPet perfilUsuario(String perfilUsuario) {
    this.perfilUsuario = perfilUsuario;
    return this;
  }

  /**
   * URL del perfil del usuario o imagen por defecto
   * @return perfilUsuario
  **/
  @ApiModelProperty(example = "/images/profile/user_male.png", value = "URL del perfil del usuario o imagen por defecto")


  public String getPerfilUsuario() {
    return perfilUsuario;
  }

  public void setPerfilUsuario(String perfilUsuario) {
    this.perfilUsuario = perfilUsuario;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycGetBitacoraPet pycGetBitacoraPet = (PycGetBitacoraPet) o;
    return Objects.equals(this.idPeticion, pycGetBitacoraPet.idPeticion) &&
        Objects.equals(this.codPerfilInicial, pycGetBitacoraPet.codPerfilInicial) &&
        Objects.equals(this.codPerfilFinal, pycGetBitacoraPet.codPerfilFinal) &&
        Objects.equals(this.codEstadoInicial, pycGetBitacoraPet.codEstadoInicial) &&
        Objects.equals(this.codEstadoFinal, pycGetBitacoraPet.codEstadoFinal) &&
        Objects.equals(this.perfilInicial, pycGetBitacoraPet.perfilInicial) &&
        Objects.equals(this.perfilFinal, pycGetBitacoraPet.perfilFinal) &&
        Objects.equals(this.estadoInicial, pycGetBitacoraPet.estadoInicial) &&
        Objects.equals(this.estadoFinal, pycGetBitacoraPet.estadoFinal) &&
        Objects.equals(this.responsables, pycGetBitacoraPet.responsables) &&
        Objects.equals(this.usuario, pycGetBitacoraPet.usuario) &&
        Objects.equals(this.fecIni, pycGetBitacoraPet.fecIni) &&
        Objects.equals(this.fecFin, pycGetBitacoraPet.fecFin) &&
        Objects.equals(this.anio, pycGetBitacoraPet.anio) &&
        Objects.equals(this.mes, pycGetBitacoraPet.mes) &&
        Objects.equals(this.dia, pycGetBitacoraPet.dia) &&
        Objects.equals(this.hora, pycGetBitacoraPet.hora) &&
        Objects.equals(this.minuto, pycGetBitacoraPet.minuto) &&
        Objects.equals(this.segundo, pycGetBitacoraPet.segundo) &&
        Objects.equals(this.titulo, pycGetBitacoraPet.titulo) &&
        Objects.equals(this.tiempo, pycGetBitacoraPet.tiempo) &&
        Objects.equals(this.icono, pycGetBitacoraPet.icono) &&
        Objects.equals(this.contenido, pycGetBitacoraPet.contenido) &&
        Objects.equals(this.perfilUsuario, pycGetBitacoraPet.perfilUsuario);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, codPerfilInicial, codPerfilFinal, codEstadoInicial, codEstadoFinal, perfilInicial, perfilFinal, estadoInicial, estadoFinal, responsables, usuario, fecIni, fecFin, anio, mes, dia, hora, minuto, segundo, titulo, tiempo, icono, contenido, perfilUsuario);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycGetBitacoraPet {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    codPerfilInicial: ").append(toIndentedString(codPerfilInicial)).append("\n");
    sb.append("    codPerfilFinal: ").append(toIndentedString(codPerfilFinal)).append("\n");
    sb.append("    codEstadoInicial: ").append(toIndentedString(codEstadoInicial)).append("\n");
    sb.append("    codEstadoFinal: ").append(toIndentedString(codEstadoFinal)).append("\n");
    sb.append("    perfilInicial: ").append(toIndentedString(perfilInicial)).append("\n");
    sb.append("    perfilFinal: ").append(toIndentedString(perfilFinal)).append("\n");
    sb.append("    estadoInicial: ").append(toIndentedString(estadoInicial)).append("\n");
    sb.append("    estadoFinal: ").append(toIndentedString(estadoFinal)).append("\n");
    sb.append("    responsables: ").append(toIndentedString(responsables)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    fecIni: ").append(toIndentedString(fecIni)).append("\n");
    sb.append("    fecFin: ").append(toIndentedString(fecFin)).append("\n");
    sb.append("    anio: ").append(toIndentedString(anio)).append("\n");
    sb.append("    mes: ").append(toIndentedString(mes)).append("\n");
    sb.append("    dia: ").append(toIndentedString(dia)).append("\n");
    sb.append("    hora: ").append(toIndentedString(hora)).append("\n");
    sb.append("    minuto: ").append(toIndentedString(minuto)).append("\n");
    sb.append("    segundo: ").append(toIndentedString(segundo)).append("\n");
    sb.append("    titulo: ").append(toIndentedString(titulo)).append("\n");
    sb.append("    tiempo: ").append(toIndentedString(tiempo)).append("\n");
    sb.append("    icono: ").append(toIndentedString(icono)).append("\n");
    sb.append("    contenido: ").append(toIndentedString(contenido)).append("\n");
    sb.append("    perfilUsuario: ").append(toIndentedString(perfilUsuario)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

