package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycSolicitudResponse
 */
@Validated

public class PycSolicitudResponse   {
  @JsonProperty("idSolicitud")
  private Integer idSolicitud = null;

  public PycSolicitudResponse idSolicitud(Integer idSolicitud) {
    this.idSolicitud = idSolicitud;
    return this;
  }

  /**
   * ID de la solicitud creada
   * @return idSolicitud
  **/
  @ApiModelProperty(value = "ID de la solicitud creada")


  public Integer getIdSolicitud() {
    return idSolicitud;
  }

  public void setIdSolicitud(Integer idSolicitud) {
    this.idSolicitud = idSolicitud;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycSolicitudResponse pycSolicitudResponse = (PycSolicitudResponse) o;
    return Objects.equals(this.idSolicitud, pycSolicitudResponse.idSolicitud);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idSolicitud);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycSolicitudResponse {\n");
    
    sb.append("    idSolicitud: ").append(toIndentedString(idSolicitud)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

