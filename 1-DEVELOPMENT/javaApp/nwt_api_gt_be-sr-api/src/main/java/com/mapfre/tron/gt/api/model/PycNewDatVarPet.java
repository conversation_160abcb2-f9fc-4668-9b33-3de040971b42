package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycNewDatVarPet
 */
@Validated

public class PycNewDatVarPet   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("idFormulario")
  private Integer idFormulario = null;

  @JsonProperty("idSeccion")
  private Integer idSeccion = null;

  @JsonProperty("idDatoVar")
  private Integer idDatoVar = null;

  @JsonProperty("valor")
  private String valor = null;

  @JsonProperty("descripcion")
  private String descripcion = null;

  public PycNewDatVarPet idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * ID de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", required = true, value = "ID de la petición")
  @NotNull


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycNewDatVarPet idFormulario(Integer idFormulario) {
    this.idFormulario = idFormulario;
    return this;
  }

  /**
   * ID del formulario
   * @return idFormulario
  **/
  @ApiModelProperty(example = "1", required = true, value = "ID del formulario")
  @NotNull


  public Integer getIdFormulario() {
    return idFormulario;
  }

  public void setIdFormulario(Integer idFormulario) {
    this.idFormulario = idFormulario;
  }

  public PycNewDatVarPet idSeccion(Integer idSeccion) {
    this.idSeccion = idSeccion;
    return this;
  }

  /**
   * ID de la sección del formulario
   * @return idSeccion
  **/
  @ApiModelProperty(example = "14", required = true, value = "ID de la sección del formulario")
  @NotNull


  public Integer getIdSeccion() {
    return idSeccion;
  }

  public void setIdSeccion(Integer idSeccion) {
    this.idSeccion = idSeccion;
  }

  public PycNewDatVarPet idDatoVar(Integer idDatoVar) {
    this.idDatoVar = idDatoVar;
    return this;
  }

  /**
   * ID del dato variable
   * @return idDatoVar
  **/
  @ApiModelProperty(example = "71", required = true, value = "ID del dato variable")
  @NotNull


  public Integer getIdDatoVar() {
    return idDatoVar;
  }

  public void setIdDatoVar(Integer idDatoVar) {
    this.idDatoVar = idDatoVar;
  }

  public PycNewDatVarPet valor(String valor) {
    this.valor = valor;
    return this;
  }

  /**
   * Valor del dato variable
   * @return valor
  **/
  @ApiModelProperty(example = "Juan Pérez García", required = true, value = "Valor del dato variable")
  @NotNull


  public String getValor() {
    return valor;
  }

  public void setValor(String valor) {
    this.valor = valor;
  }

  public PycNewDatVarPet descripcion(String descripcion) {
    this.descripcion = descripcion;
    return this;
  }

  /**
   * Descripción adicional del dato variable (opcional)
   * @return descripcion
  **/
  @ApiModelProperty(example = "Nombre completo del solicitante", value = "Descripción adicional del dato variable (opcional)")


  public String getDescripcion() {
    return descripcion;
  }

  public void setDescripcion(String descripcion) {
    this.descripcion = descripcion;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycNewDatVarPet pycNewDatVarPet = (PycNewDatVarPet) o;
    return Objects.equals(this.idPeticion, pycNewDatVarPet.idPeticion) &&
        Objects.equals(this.idFormulario, pycNewDatVarPet.idFormulario) &&
        Objects.equals(this.idSeccion, pycNewDatVarPet.idSeccion) &&
        Objects.equals(this.idDatoVar, pycNewDatVarPet.idDatoVar) &&
        Objects.equals(this.valor, pycNewDatVarPet.valor) &&
        Objects.equals(this.descripcion, pycNewDatVarPet.descripcion);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, idFormulario, idSeccion, idDatoVar, valor, descripcion);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycNewDatVarPet {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    idFormulario: ").append(toIndentedString(idFormulario)).append("\n");
    sb.append("    idSeccion: ").append(toIndentedString(idSeccion)).append("\n");
    sb.append("    idDatoVar: ").append(toIndentedString(idDatoVar)).append("\n");
    sb.append("    valor: ").append(toIndentedString(valor)).append("\n");
    sb.append("    descripcion: ").append(toIndentedString(descripcion)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

