package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUsuarioPerfil
 */
@Validated

public class PycUsuarioPerfil   {
  @JsonProperty("nombre")
  private String nombre = null;

  @JsonProperty("nombrePerfil")
  private String nombrePerfil = null;

  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("idPerfil")
  private Integer idPerfil = null;

  @JsonProperty("multiperfil")
  private String multiperfil = null;

  @JsonProperty("indDefault")
  private String indDefault = null;

  public PycUsuarioPerfil nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre completo del usuario (primer nombre y primer apellido)
   * @return nombre
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre completo del usuario (primer nombre y primer apellido)")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public PycUsuarioPerfil nombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
    return this;
  }

  /**
   * Nombre del perfil
   * @return nombrePerfil
  **/
  @ApiModelProperty(example = "Administrador", value = "Nombre del perfil")


  public String getNombrePerfil() {
    return nombrePerfil;
  }

  public void setNombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
  }

  public PycUsuarioPerfil idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Identificador del usuario
   * @return idUsuario
  **/
  @ApiModelProperty(example = "1234", value = "Identificador del usuario")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycUsuarioPerfil idPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
    return this;
  }

  /**
   * Identificador del perfil
   * @return idPerfil
  **/
  @ApiModelProperty(example = "1", value = "Identificador del perfil")


  public Integer getIdPerfil() {
    return idPerfil;
  }

  public void setIdPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
  }

  public PycUsuarioPerfil multiperfil(String multiperfil) {
    this.multiperfil = multiperfil;
    return this;
  }

  /**
   * Indicador si el usuario tiene múltiples perfiles
   * @return multiperfil
  **/
  @ApiModelProperty(example = "S", value = "Indicador si el usuario tiene múltiples perfiles")


  public String getMultiperfil() {
    return multiperfil;
  }

  public void setMultiperfil(String multiperfil) {
    this.multiperfil = multiperfil;
  }

  public PycUsuarioPerfil indDefault(String indDefault) {
    this.indDefault = indDefault;
    return this;
  }

  /**
   * Indicador si es el perfil por defecto
   * @return indDefault
  **/
  @ApiModelProperty(example = "S", value = "Indicador si es el perfil por defecto")


  public String getIndDefault() {
    return indDefault;
  }

  public void setIndDefault(String indDefault) {
    this.indDefault = indDefault;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUsuarioPerfil pycUsuarioPerfil = (PycUsuarioPerfil) o;
    return Objects.equals(this.nombre, pycUsuarioPerfil.nombre) &&
        Objects.equals(this.nombrePerfil, pycUsuarioPerfil.nombrePerfil) &&
        Objects.equals(this.idUsuario, pycUsuarioPerfil.idUsuario) &&
        Objects.equals(this.idPerfil, pycUsuarioPerfil.idPerfil) &&
        Objects.equals(this.multiperfil, pycUsuarioPerfil.multiperfil) &&
        Objects.equals(this.indDefault, pycUsuarioPerfil.indDefault);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nombre, nombrePerfil, idUsuario, idPerfil, multiperfil, indDefault);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUsuarioPerfil {\n");
    
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    nombrePerfil: ").append(toIndentedString(nombrePerfil)).append("\n");
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    idPerfil: ").append(toIndentedString(idPerfil)).append("\n");
    sb.append("    multiperfil: ").append(toIndentedString(multiperfil)).append("\n");
    sb.append("    indDefault: ").append(toIndentedString(indDefault)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

