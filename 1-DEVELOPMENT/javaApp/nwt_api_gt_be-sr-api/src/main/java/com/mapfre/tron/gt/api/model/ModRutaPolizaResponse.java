package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ModRutaPolizaResponse
 */
@Validated

public class ModRutaPolizaResponse   {
  @JsonProperty("polizaCodigo")
  private String polizaCodigo = null;

  @JsonProperty("polizaNumero")
  private String polizaNumero = null;

  @JsonProperty("certificado")
  private String certificado = null;

  @JsonProperty("asegurado")
  private String asegurado = null;

  @JsonProperty("direccion")
  private String direccion = null;

  @JsonProperty("estadoCod")
  private String estadoCod = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("ruta")
  private Integer ruta = null;

  @JsonProperty("idePol")
  private String idePol = null;

  public ModRutaPolizaResponse polizaCodigo(String polizaCodigo) {
    this.polizaCodigo = polizaCodigo;
    return this;
  }

  /**
   * Codigo de la poliza
   * @return polizaCodigo
  **/
  @ApiModelProperty(example = "AUOL", value = "Codigo de la poliza")


  public String getPolizaCodigo() {
    return polizaCodigo;
  }

  public void setPolizaCodigo(String polizaCodigo) {
    this.polizaCodigo = polizaCodigo;
  }

  public ModRutaPolizaResponse polizaNumero(String polizaNumero) {
    this.polizaNumero = polizaNumero;
    return this;
  }

  /**
   * Numero de poliza
   * @return polizaNumero
  **/
  @ApiModelProperty(example = "30020111007991", value = "Numero de poliza")


  public String getPolizaNumero() {
    return polizaNumero;
  }

  public void setPolizaNumero(String polizaNumero) {
    this.polizaNumero = polizaNumero;
  }

  public ModRutaPolizaResponse certificado(String certificado) {
    this.certificado = certificado;
    return this;
  }

  /**
   * Certificado
   * @return certificado
  **/
  @ApiModelProperty(example = "1", value = "Certificado")


  public String getCertificado() {
    return certificado;
  }

  public void setCertificado(String certificado) {
    this.certificado = certificado;
  }

  public ModRutaPolizaResponse asegurado(String asegurado) {
    this.asegurado = asegurado;
    return this;
  }

  /**
   * Nombre del asegurado
   * @return asegurado
  **/
  @ApiModelProperty(example = "Sociedad Anonima", value = "Nombre del asegurado")


  public String getAsegurado() {
    return asegurado;
  }

  public void setAsegurado(String asegurado) {
    this.asegurado = asegurado;
  }

  public ModRutaPolizaResponse direccion(String direccion) {
    this.direccion = direccion;
    return this;
  }

  /**
   * Direccion
   * @return direccion
  **/
  @ApiModelProperty(example = "Ciudad de Guatemala", value = "Direccion")


  public String getDireccion() {
    return direccion;
  }

  public void setDireccion(String direccion) {
    this.direccion = direccion;
  }

  public ModRutaPolizaResponse estadoCod(String estadoCod) {
    this.estadoCod = estadoCod;
    return this;
  }

  /**
   * codigo del estado
   * @return estadoCod
  **/
  @ApiModelProperty(example = "AVI", value = "codigo del estado")


  public String getEstadoCod() {
    return estadoCod;
  }

  public void setEstadoCod(String estadoCod) {
    this.estadoCod = estadoCod;
  }

  public ModRutaPolizaResponse estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Nombre del estado
   * @return estado
  **/
  @ApiModelProperty(example = "AVISADO", value = "Nombre del estado")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public ModRutaPolizaResponse ruta(Integer ruta) {
    this.ruta = ruta;
    return this;
  }

  /**
   * Id  de la ruta
   * @return ruta
  **/
  @ApiModelProperty(example = "4", value = "Id  de la ruta")


  public Integer getRuta() {
    return ruta;
  }

  public void setRuta(Integer ruta) {
    this.ruta = ruta;
  }

  public ModRutaPolizaResponse idePol(String idePol) {
    this.idePol = idePol;
    return this;
  }

  /**
   * Id de poliza
   * @return idePol
  **/
  @ApiModelProperty(example = "3002000007993", value = "Id de poliza")


  public String getIdePol() {
    return idePol;
  }

  public void setIdePol(String idePol) {
    this.idePol = idePol;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ModRutaPolizaResponse modRutaPolizaResponse = (ModRutaPolizaResponse) o;
    return Objects.equals(this.polizaCodigo, modRutaPolizaResponse.polizaCodigo) &&
        Objects.equals(this.polizaNumero, modRutaPolizaResponse.polizaNumero) &&
        Objects.equals(this.certificado, modRutaPolizaResponse.certificado) &&
        Objects.equals(this.asegurado, modRutaPolizaResponse.asegurado) &&
        Objects.equals(this.direccion, modRutaPolizaResponse.direccion) &&
        Objects.equals(this.estadoCod, modRutaPolizaResponse.estadoCod) &&
        Objects.equals(this.estado, modRutaPolizaResponse.estado) &&
        Objects.equals(this.ruta, modRutaPolizaResponse.ruta) &&
        Objects.equals(this.idePol, modRutaPolizaResponse.idePol);
  }

  @Override
  public int hashCode() {
    return Objects.hash(polizaCodigo, polizaNumero, certificado, asegurado, direccion, estadoCod, estado, ruta, idePol);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ModRutaPolizaResponse {\n");
    
    sb.append("    polizaCodigo: ").append(toIndentedString(polizaCodigo)).append("\n");
    sb.append("    polizaNumero: ").append(toIndentedString(polizaNumero)).append("\n");
    sb.append("    certificado: ").append(toIndentedString(certificado)).append("\n");
    sb.append("    asegurado: ").append(toIndentedString(asegurado)).append("\n");
    sb.append("    direccion: ").append(toIndentedString(direccion)).append("\n");
    sb.append("    estadoCod: ").append(toIndentedString(estadoCod)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    ruta: ").append(toIndentedString(ruta)).append("\n");
    sb.append("    idePol: ").append(toIndentedString(idePol)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

