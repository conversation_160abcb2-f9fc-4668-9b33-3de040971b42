package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycReportePeti
 */
@Validated

public class PycReportePeti   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("nombrePeticion")
  private String nombrePeticion = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("fechaInicio")
  private String fechaInicio = null;

  @JsonProperty("fechaFin")
  private String fechaFin = null;

  @JsonProperty("prioridad")
  private String prioridad = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("analista")
  private String analista = null;

  @JsonProperty("codClarity")
  private String codClarity = null;

  @JsonProperty("nombreArea")
  private String nombreArea = null;

  @JsonProperty("nombreDepartamento")
  private String nombreDepartamento = null;

  @JsonProperty("observaciones")
  private String observaciones = null;

  public PycReportePeti idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador de la petición")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycReportePeti nombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
    return this;
  }

  /**
   * Nombre de la petición
   * @return nombrePeticion
  **/
  @ApiModelProperty(example = "Solicitud de cambio de datos", value = "Nombre de la petición")


  public String getNombrePeticion() {
    return nombrePeticion;
  }

  public void setNombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
  }

  public PycReportePeti estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Id estado
   * @return estado
  **/
  @ApiModelProperty(example = "1", value = "Id estado")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycReportePeti fechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
    return this;
  }

  /**
   * Fecha de inicio de la petición (formato DD/MM/YYYY)
   * @return fechaInicio
  **/
  @ApiModelProperty(example = "15/01/2024", value = "Fecha de inicio de la petición (formato DD/MM/YYYY)")


  public String getFechaInicio() {
    return fechaInicio;
  }

  public void setFechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
  }

  public PycReportePeti fechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
    return this;
  }

  /**
   * Fecha de fin de la petición (formato DD/MM/YYYY)
   * @return fechaFin
  **/
  @ApiModelProperty(example = "20/02/2024", value = "Fecha de fin de la petición (formato DD/MM/YYYY)")


  public String getFechaFin() {
    return fechaFin;
  }

  public void setFechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
  }

  public PycReportePeti prioridad(String prioridad) {
    this.prioridad = prioridad;
    return this;
  }

  /**
   * Descripción de la prioridad de la petición
   * @return prioridad
  **/
  @ApiModelProperty(example = "Alta", value = "Descripción de la prioridad de la petición")


  public String getPrioridad() {
    return prioridad;
  }

  public void setPrioridad(String prioridad) {
    this.prioridad = prioridad;
  }

  public PycReportePeti usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Nombre completo del usuario solicitante (primer nombre y primer apellido)
   * @return usuario
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre completo del usuario solicitante (primer nombre y primer apellido)")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public PycReportePeti analista(String analista) {
    this.analista = analista;
    return this;
  }

  /**
   * Nombre completo del analista asignado (primer nombre y primer apellido)
   * @return analista
  **/
  @ApiModelProperty(example = "María García", value = "Nombre completo del analista asignado (primer nombre y primer apellido)")


  public String getAnalista() {
    return analista;
  }

  public void setAnalista(String analista) {
    this.analista = analista;
  }

  public PycReportePeti codClarity(String codClarity) {
    this.codClarity = codClarity;
    return this;
  }

  /**
   * Código Clarity de la petición
   * @return codClarity
  **/
  @ApiModelProperty(example = "MU-2024-001234", value = "Código Clarity de la petición")


  public String getCodClarity() {
    return codClarity;
  }

  public void setCodClarity(String codClarity) {
    this.codClarity = codClarity;
  }

  public PycReportePeti nombreArea(String nombreArea) {
    this.nombreArea = nombreArea;
    return this;
  }

  /**
   * Nombre del área
   * @return nombreArea
  **/
  @ApiModelProperty(example = "Tecnología", value = "Nombre del área")


  public String getNombreArea() {
    return nombreArea;
  }

  public void setNombreArea(String nombreArea) {
    this.nombreArea = nombreArea;
  }

  public PycReportePeti nombreDepartamento(String nombreDepartamento) {
    this.nombreDepartamento = nombreDepartamento;
    return this;
  }

  /**
   * Nombre del departamento
   * @return nombreDepartamento
  **/
  @ApiModelProperty(example = "Desarrollo de Software", value = "Nombre del departamento")


  public String getNombreDepartamento() {
    return nombreDepartamento;
  }

  public void setNombreDepartamento(String nombreDepartamento) {
    this.nombreDepartamento = nombreDepartamento;
  }

  public PycReportePeti observaciones(String observaciones) {
    this.observaciones = observaciones;
    return this;
  }

  /**
   * Última observación registrada para la petición
   * @return observaciones
  **/
  @ApiModelProperty(example = "Petición en proceso de revisión", value = "Última observación registrada para la petición")


  public String getObservaciones() {
    return observaciones;
  }

  public void setObservaciones(String observaciones) {
    this.observaciones = observaciones;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycReportePeti pycReportePeti = (PycReportePeti) o;
    return Objects.equals(this.idPeticion, pycReportePeti.idPeticion) &&
        Objects.equals(this.nombrePeticion, pycReportePeti.nombrePeticion) &&
        Objects.equals(this.estado, pycReportePeti.estado) &&
        Objects.equals(this.fechaInicio, pycReportePeti.fechaInicio) &&
        Objects.equals(this.fechaFin, pycReportePeti.fechaFin) &&
        Objects.equals(this.prioridad, pycReportePeti.prioridad) &&
        Objects.equals(this.usuario, pycReportePeti.usuario) &&
        Objects.equals(this.analista, pycReportePeti.analista) &&
        Objects.equals(this.codClarity, pycReportePeti.codClarity) &&
        Objects.equals(this.nombreArea, pycReportePeti.nombreArea) &&
        Objects.equals(this.nombreDepartamento, pycReportePeti.nombreDepartamento) &&
        Objects.equals(this.observaciones, pycReportePeti.observaciones);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, nombrePeticion, estado, fechaInicio, fechaFin, prioridad, usuario, analista, codClarity, nombreArea, nombreDepartamento, observaciones);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycReportePeti {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    nombrePeticion: ").append(toIndentedString(nombrePeticion)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    fechaInicio: ").append(toIndentedString(fechaInicio)).append("\n");
    sb.append("    fechaFin: ").append(toIndentedString(fechaFin)).append("\n");
    sb.append("    prioridad: ").append(toIndentedString(prioridad)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    analista: ").append(toIndentedString(analista)).append("\n");
    sb.append("    codClarity: ").append(toIndentedString(codClarity)).append("\n");
    sb.append("    nombreArea: ").append(toIndentedString(nombreArea)).append("\n");
    sb.append("    nombreDepartamento: ").append(toIndentedString(nombreDepartamento)).append("\n");
    sb.append("    observaciones: ").append(toIndentedString(observaciones)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

