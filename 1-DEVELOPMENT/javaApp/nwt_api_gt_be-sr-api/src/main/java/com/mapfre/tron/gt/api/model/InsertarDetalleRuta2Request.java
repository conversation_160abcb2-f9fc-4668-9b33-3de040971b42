package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * InsertarDetalleRuta2Request
 */
@Validated

public class InsertarDetalleRuta2Request   {
  @JsonProperty("ruta")
  private Integer ruta = null;

  @JsonProperty("idPol")
  private String idPol = null;

  @JsonProperty("codPol")
  private String codPol = null;

  @JsonProperty("numPol")
  private String numPol = null;

  @JsonProperty("numCert")
  private String numCert = null;

  @JsonProperty("numReq")
  private String numReq = null;

  @JsonProperty("moneda")
  private String moneda = null;

  @JsonProperty("total")
  private String total = null;

  @JsonProperty("cuota")
  private String cuota = null;

  @JsonProperty("sistema")
  private String sistema = null;

  @JsonProperty("asegurado")
  private String asegurado = null;

  @JsonProperty("direccion")
  private String direccion = null;

  @JsonProperty("correo")
  private String correo = null;

  @JsonProperty("fechaVencimiento")
  private String fechaVencimiento = null;

  @JsonProperty("esAviso")
  private String esAviso = null;

  public InsertarDetalleRuta2Request ruta(Integer ruta) {
    this.ruta = ruta;
    return this;
  }

  /**
   * Número de ruta
   * @return ruta
  **/
  @ApiModelProperty(example = "26", required = true, value = "Número de ruta")
  @NotNull


  public Integer getRuta() {
    return ruta;
  }

  public void setRuta(Integer ruta) {
    this.ruta = ruta;
  }

  public InsertarDetalleRuta2Request idPol(String idPol) {
    this.idPol = idPol;
    return this;
  }

  /**
   * ID de la póliza
   * @return idPol
  **/
  @ApiModelProperty(example = "9783443", required = true, value = "ID de la póliza")
  @NotNull


  public String getIdPol() {
    return idPol;
  }

  public void setIdPol(String idPol) {
    this.idPol = idPol;
  }

  public InsertarDetalleRuta2Request codPol(String codPol) {
    this.codPol = codPol;
    return this;
  }

  /**
   * Código de la póliza
   * @return codPol
  **/
  @ApiModelProperty(example = "SCKD", required = true, value = "Código de la póliza")
  @NotNull


  public String getCodPol() {
    return codPol;
  }

  public void setCodPol(String codPol) {
    this.codPol = codPol;
  }

  public InsertarDetalleRuta2Request numPol(String numPol) {
    this.numPol = numPol;
    return this;
  }

  /**
   * Número de la póliza
   * @return numPol
  **/
  @ApiModelProperty(example = "1707", required = true, value = "Número de la póliza")
  @NotNull


  public String getNumPol() {
    return numPol;
  }

  public void setNumPol(String numPol) {
    this.numPol = numPol;
  }

  public InsertarDetalleRuta2Request numCert(String numCert) {
    this.numCert = numCert;
    return this;
  }

  /**
   * Número de certificado
   * @return numCert
  **/
  @ApiModelProperty(example = "1", value = "Número de certificado")


  public String getNumCert() {
    return numCert;
  }

  public void setNumCert(String numCert) {
    this.numCert = numCert;
  }

  public InsertarDetalleRuta2Request numReq(String numReq) {
    this.numReq = numReq;
    return this;
  }

  /**
   * Número de requerimiento
   * @return numReq
  **/
  @ApiModelProperty(example = "001", required = true, value = "Número de requerimiento")
  @NotNull


  public String getNumReq() {
    return numReq;
  }

  public void setNumReq(String numReq) {
    this.numReq = numReq;
  }

  public InsertarDetalleRuta2Request moneda(String moneda) {
    this.moneda = moneda;
    return this;
  }

  /**
   * Código de moneda
   * @return moneda
  **/
  @ApiModelProperty(example = "Q", required = true, value = "Código de moneda")
  @NotNull


  public String getMoneda() {
    return moneda;
  }

  public void setMoneda(String moneda) {
    this.moneda = moneda;
  }

  public InsertarDetalleRuta2Request total(String total) {
    this.total = total;
    return this;
  }

  /**
   * Monto total
   * @return total
  **/
  @ApiModelProperty(example = "230", required = true, value = "Monto total")
  @NotNull


  public String getTotal() {
    return total;
  }

  public void setTotal(String total) {
    this.total = total;
  }

  public InsertarDetalleRuta2Request cuota(String cuota) {
    this.cuota = cuota;
    return this;
  }

  /**
   * Número de cuota
   * @return cuota
  **/
  @ApiModelProperty(example = "1/12", required = true, value = "Número de cuota")
  @NotNull


  public String getCuota() {
    return cuota;
  }

  public void setCuota(String cuota) {
    this.cuota = cuota;
  }

  public InsertarDetalleRuta2Request sistema(String sistema) {
    this.sistema = sistema;
    return this;
  }

  /**
   * Sistema origen
   * @return sistema
  **/
  @ApiModelProperty(example = "T", required = true, value = "Sistema origen")
  @NotNull


  public String getSistema() {
    return sistema;
  }

  public void setSistema(String sistema) {
    this.sistema = sistema;
  }

  public InsertarDetalleRuta2Request asegurado(String asegurado) {
    this.asegurado = asegurado;
    return this;
  }

  /**
   * Nombre del asegurado
   * @return asegurado
  **/
  @ApiModelProperty(example = "Juan Pérez", required = true, value = "Nombre del asegurado")
  @NotNull


  public String getAsegurado() {
    return asegurado;
  }

  public void setAsegurado(String asegurado) {
    this.asegurado = asegurado;
  }

  public InsertarDetalleRuta2Request direccion(String direccion) {
    this.direccion = direccion;
    return this;
  }

  /**
   * Dirección del asegurado
   * @return direccion
  **/
  @ApiModelProperty(example = "Zona 10, Ciudad de Guatemala", required = true, value = "Dirección del asegurado")
  @NotNull


  public String getDireccion() {
    return direccion;
  }

  public void setDireccion(String direccion) {
    this.direccion = direccion;
  }

  public InsertarDetalleRuta2Request correo(String correo) {
    this.correo = correo;
    return this;
  }

  /**
   * Correo electrónico del asegurado
   * @return correo
  **/
  @ApiModelProperty(example = "<EMAIL>", required = true, value = "Correo electrónico del asegurado")
  @NotNull


  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }

  public InsertarDetalleRuta2Request fechaVencimiento(String fechaVencimiento) {
    this.fechaVencimiento = fechaVencimiento;
    return this;
  }

  /**
   * Fecha de vencimiento (formato DD-MM-YYYY)
   * @return fechaVencimiento
  **/
  @ApiModelProperty(example = "01/02/2025", required = true, value = "Fecha de vencimiento (formato DD-MM-YYYY)")
  @NotNull


  public String getFechaVencimiento() {
    return fechaVencimiento;
  }

  public void setFechaVencimiento(String fechaVencimiento) {
    this.fechaVencimiento = fechaVencimiento;
  }

  public InsertarDetalleRuta2Request esAviso(String esAviso) {
    this.esAviso = esAviso;
    return this;
  }

  /**
   * indica si es aviso(poliza grupo)
   * @return esAviso
  **/
  @ApiModelProperty(example = "N", required = true, value = "indica si es aviso(poliza grupo)")
  @NotNull


  public String getEsAviso() {
    return esAviso;
  }

  public void setEsAviso(String esAviso) {
    this.esAviso = esAviso;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InsertarDetalleRuta2Request insertarDetalleRuta2Request = (InsertarDetalleRuta2Request) o;
    return Objects.equals(this.ruta, insertarDetalleRuta2Request.ruta) &&
        Objects.equals(this.idPol, insertarDetalleRuta2Request.idPol) &&
        Objects.equals(this.codPol, insertarDetalleRuta2Request.codPol) &&
        Objects.equals(this.numPol, insertarDetalleRuta2Request.numPol) &&
        Objects.equals(this.numCert, insertarDetalleRuta2Request.numCert) &&
        Objects.equals(this.numReq, insertarDetalleRuta2Request.numReq) &&
        Objects.equals(this.moneda, insertarDetalleRuta2Request.moneda) &&
        Objects.equals(this.total, insertarDetalleRuta2Request.total) &&
        Objects.equals(this.cuota, insertarDetalleRuta2Request.cuota) &&
        Objects.equals(this.sistema, insertarDetalleRuta2Request.sistema) &&
        Objects.equals(this.asegurado, insertarDetalleRuta2Request.asegurado) &&
        Objects.equals(this.direccion, insertarDetalleRuta2Request.direccion) &&
        Objects.equals(this.correo, insertarDetalleRuta2Request.correo) &&
        Objects.equals(this.fechaVencimiento, insertarDetalleRuta2Request.fechaVencimiento) &&
        Objects.equals(this.esAviso, insertarDetalleRuta2Request.esAviso);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ruta, idPol, codPol, numPol, numCert, numReq, moneda, total, cuota, sistema, asegurado, direccion, correo, fechaVencimiento, esAviso);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InsertarDetalleRuta2Request {\n");
    
    sb.append("    ruta: ").append(toIndentedString(ruta)).append("\n");
    sb.append("    idPol: ").append(toIndentedString(idPol)).append("\n");
    sb.append("    codPol: ").append(toIndentedString(codPol)).append("\n");
    sb.append("    numPol: ").append(toIndentedString(numPol)).append("\n");
    sb.append("    numCert: ").append(toIndentedString(numCert)).append("\n");
    sb.append("    numReq: ").append(toIndentedString(numReq)).append("\n");
    sb.append("    moneda: ").append(toIndentedString(moneda)).append("\n");
    sb.append("    total: ").append(toIndentedString(total)).append("\n");
    sb.append("    cuota: ").append(toIndentedString(cuota)).append("\n");
    sb.append("    sistema: ").append(toIndentedString(sistema)).append("\n");
    sb.append("    asegurado: ").append(toIndentedString(asegurado)).append("\n");
    sb.append("    direccion: ").append(toIndentedString(direccion)).append("\n");
    sb.append("    correo: ").append(toIndentedString(correo)).append("\n");
    sb.append("    fechaVencimiento: ").append(toIndentedString(fechaVencimiento)).append("\n");
    sb.append("    esAviso: ").append(toIndentedString(esAviso)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

