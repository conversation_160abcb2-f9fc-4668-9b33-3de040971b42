package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ArchivoCargado
 */
@Validated

public class ArchivoCargado   {
  @JsonProperty("nombre")
  private String nombre = null;

  @JsonProperty("ruta")
  private String ruta = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("error")
  private String error = null;

  public ArchivoCargado nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre del archivo cargado
   * @return nombre
  **/
  @ApiModelProperty(example = "documento.pdf", value = "Nombre del archivo cargado")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public ArchivoCargado ruta(String ruta) {
    this.ruta = ruta;
    return this;
  }

  /**
   * Ruta donde se guardó el archivo
   * @return ruta
  **/
  @ApiModelProperty(example = "/doc_pycges_tecnicos/archivo_123.pdf", value = "Ruta donde se guardó el archivo")


  public String getRuta() {
    return ruta;
  }

  public void setRuta(String ruta) {
    this.ruta = ruta;
  }

  public ArchivoCargado estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado de la carga (SUCCESS, ERROR)
   * @return estado
  **/
  @ApiModelProperty(example = "SUCCESS", value = "Estado de la carga (SUCCESS, ERROR)")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public ArchivoCargado error(String error) {
    this.error = error;
    return this;
  }

  /**
   * Mensaje de error si la carga falló
   * @return error
  **/
  @ApiModelProperty(example = "Error al subir archivo", value = "Mensaje de error si la carga falló")


  public String getError() {
    return error;
  }

  public void setError(String error) {
    this.error = error;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ArchivoCargado archivoCargado = (ArchivoCargado) o;
    return Objects.equals(this.nombre, archivoCargado.nombre) &&
        Objects.equals(this.ruta, archivoCargado.ruta) &&
        Objects.equals(this.estado, archivoCargado.estado) &&
        Objects.equals(this.error, archivoCargado.error);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nombre, ruta, estado, error);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ArchivoCargado {\n");
    
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    ruta: ").append(toIndentedString(ruta)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    error: ").append(toIndentedString(error)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

