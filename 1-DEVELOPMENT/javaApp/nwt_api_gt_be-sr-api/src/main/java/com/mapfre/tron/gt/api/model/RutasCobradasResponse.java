package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * RutasCobradasResponse
 */
@Validated

public class RutasCobradasResponse   {
  @JsonProperty("idRutas")
  @Valid
  private List<String> idRutas = null;

  public RutasCobradasResponse idRutas(List<String> idRutas) {
    this.idRutas = idRutas;
    return this;
  }

  public RutasCobradasResponse addIdRutasItem(String idRutasItem) {
    if (this.idRutas == null) {
      this.idRutas = new ArrayList<>();
    }
    this.idRutas.add(idRutasItem);
    return this;
  }

  /**
   * Lista de IDs de las rutas
   * @return idRutas
  **/
  @ApiModelProperty(example = "[\"218\",\"217\"]", value = "Lista de IDs de las rutas")


  public List<String> getIdRutas() {
    return idRutas;
  }

  public void setIdRutas(List<String> idRutas) {
    this.idRutas = idRutas;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RutasCobradasResponse rutasCobradasResponse = (RutasCobradasResponse) o;
    return Objects.equals(this.idRutas, rutasCobradasResponse.idRutas);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idRutas);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RutasCobradasResponse {\n");
    
    sb.append("    idRutas: ").append(toIndentedString(idRutas)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

