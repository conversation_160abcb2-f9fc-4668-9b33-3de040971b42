package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycSesionResponse
 */
@Validated

public class PycSesionResponse   {
  @JsonProperty("resultado")
  private Integer resultado = null;

  public PycSesionResponse resultado(Integer resultado) {
    this.resultado = resultado;
    return this;
  }

  /**
   * Resultado de la operación de actualización de sesión (1=éxito, 0=error)
   * @return resultado
  **/
  @ApiModelProperty(example = "1", value = "Resultado de la operación de actualización de sesión (1=éxito, 0=error)")


  public Integer getResultado() {
    return resultado;
  }

  public void setResultado(Integer resultado) {
    this.resultado = resultado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycSesionResponse pycSesionResponse = (PycSesionResponse) o;
    return Objects.equals(this.resultado, pycSesionResponse.resultado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(resultado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycSesionResponse {\n");
    
    sb.append("    resultado: ").append(toIndentedString(resultado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

