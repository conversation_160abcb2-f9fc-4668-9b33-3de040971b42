package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * RutaDiariaRequest
 */
@Validated

public class RutaDiariaRequest   {
  @JsonProperty("idUsuarioCrea")
  private Integer idUsuarioCrea = null;

  @JsonProperty("idUsuarioAsignado")
  private Integer idUsuarioAsignado = null;

  public RutaDiariaRequest idUsuarioCrea(Integer idUsuarioCrea) {
    this.idUsuarioCrea = idUsuarioCrea;
    return this;
  }

  /**
   * id de usuario que crea la nueva ruta
   * @return idUsuarioCrea
  **/
  @ApiModelProperty(example = "1", required = true, value = "id de usuario que crea la nueva ruta")
  @NotNull


  public Integer getIdUsuarioCrea() {
    return idUsuarioCrea;
  }

  public void setIdUsuarioCrea(Integer idUsuarioCrea) {
    this.idUsuarioCrea = idUsuarioCrea;
  }

  public RutaDiariaRequest idUsuarioAsignado(Integer idUsuarioAsignado) {
    this.idUsuarioAsignado = idUsuarioAsignado;
    return this;
  }

  /**
   * id de usuario al que se asigna la nueva ruta
   * @return idUsuarioAsignado
  **/
  @ApiModelProperty(example = "1", required = true, value = "id de usuario al que se asigna la nueva ruta")
  @NotNull


  public Integer getIdUsuarioAsignado() {
    return idUsuarioAsignado;
  }

  public void setIdUsuarioAsignado(Integer idUsuarioAsignado) {
    this.idUsuarioAsignado = idUsuarioAsignado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RutaDiariaRequest rutaDiariaRequest = (RutaDiariaRequest) o;
    return Objects.equals(this.idUsuarioCrea, rutaDiariaRequest.idUsuarioCrea) &&
        Objects.equals(this.idUsuarioAsignado, rutaDiariaRequest.idUsuarioAsignado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuarioCrea, idUsuarioAsignado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RutaDiariaRequest {\n");
    
    sb.append("    idUsuarioCrea: ").append(toIndentedString(idUsuarioCrea)).append("\n");
    sb.append("    idUsuarioAsignado: ").append(toIndentedString(idUsuarioAsignado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

