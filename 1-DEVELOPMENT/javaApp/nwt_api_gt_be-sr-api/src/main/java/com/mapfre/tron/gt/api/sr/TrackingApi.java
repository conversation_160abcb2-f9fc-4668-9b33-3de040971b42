/**
 * NOTE: This class is auto generated by the swagger code generator program (2.4.14).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.mapfre.tron.gt.api.sr;

import com.mapfre.tron.gt.api.model.DetalleSiniestro;
import com.mapfre.tron.gt.api.model.EncuestaSiniestro;
import com.mapfre.tron.gt.api.model.EncuestaSiniestroRequest;
import com.mapfre.tron.gt.api.model.EncuestaSiniestroResponse;
import com.mapfre.tron.gt.api.model.Error;
import com.mapfre.tron.gt.api.model.EtapaSiniestro;
import java.time.LocalDate;
import com.mapfre.tron.gt.api.model.RamoSiniestro;
import com.mapfre.tron.gt.api.model.Siniestro;
import com.mapfre.tron.gt.api.model.SiniestroNit;
import com.mapfre.tron.gt.api.model.SiniestroVehiculo;
import com.mapfre.tron.gt.api.model.UrlSiniestro;
import com.mapfre.tron.gt.api.model.ValidaIngresoEncuestaResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Api(value = "tracking", description = "the tracking API")
@RequestMapping(value = "/newtron/api")
public interface TrackingApi {

    Logger log = LoggerFactory.getLogger(TrackingApi.class);

    default Optional<ObjectMapper> getObjectMapper() {
        return Optional.empty();
    }

    default Optional<HttpServletRequest> getRequest() {
        return Optional.empty();
    }

    default Optional<String> getAcceptHeader() {
        return getRequest().map(r -> r.getHeader("Accept"));
    }

    @ApiOperation(value = "Obtener encuesta de siniestro", nickname = "getEncuestaSiniestro", notes = "Recupera la información de encuesta de siniestro con sus preguntas y respuestas", response = EncuestaSiniestro.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Tracking", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = EncuestaSiniestro.class, responseContainer = "List") })
    @RequestMapping(value = "/tracking/get_encuesta_siniestro",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<EncuestaSiniestro>> getEncuestaSiniestro() {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"descripcion\" : \"Encuesta para evaluar la satisfacción del cliente\",  \"encuesta\" : \"Encuesta de satisfacción\",  \"idEncuesta\" : 1,  \"respuesta\" : \"Excelente\",  \"idRespuesta\" : 1,  \"idPregunta\" : 1,  \"pregunta\" : \"¿Cómo calificaría nuestro servicio?\"}, {  \"descripcion\" : \"Encuesta para evaluar la satisfacción del cliente\",  \"encuesta\" : \"Encuesta de satisfacción\",  \"idEncuesta\" : 1,  \"respuesta\" : \"Excelente\",  \"idRespuesta\" : 1,  \"idPregunta\" : 1,  \"pregunta\" : \"¿Cómo calificaría nuestro servicio?\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TrackingApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener ramo de un siniestro", nickname = "getRamoSiniestro", notes = "Obtiene el código de ramo de un siniestro específico", response = RamoSiniestro.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Tracking", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = RamoSiniestro.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/tracking/get_ramo_siniestro",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<RamoSiniestro> getRamoSiniestro(@NotNull @ApiParam(value = "Número de siniestro", required = true) @Valid @RequestParam(value = "numSiniestro", required = true) String numSiniestro) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"ramo\" : 300}", RamoSiniestro.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TrackingApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener siniestro por documento", nickname = "getSiniestroByDoc", notes = "Obtiene la información de un siniestro según su número, documento y tipo de documento", response = SiniestroNit.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Tracking", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = SiniestroNit.class, responseContainer = "List") })
    @RequestMapping(value = "/tracking/get_siniestro_by_doc",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<SiniestroNit>> getSiniestroByDoc(@NotNull @ApiParam(value = "Número de siniestro", required = true) @Valid @RequestParam(value = "numSiniestro", required = true) String numSiniestro,@NotNull @ApiParam(value = "Número de documento del tomador", required = true) @Valid @RequestParam(value = "numDoc", required = true) String numDoc,@NotNull @ApiParam(value = "Tipo de documento (NIT, DPI, etc.)", required = true) @Valid @RequestParam(value = "tipoDoc", required = true) String tipoDoc) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"siniestro\" : \"300252001000007\",  \"poliza\" : \"0230025018359\"}, {  \"siniestro\" : \"300252001000007\",  \"poliza\" : \"0230025018359\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TrackingApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener información de siniestro de vehículo", nickname = "getSiniestroVehiculo", notes = "Obtiene la información de seguimiento de un siniestro de vehículo", response = SiniestroVehiculo.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Tracking", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = SiniestroVehiculo.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/tracking/get_siniestro_vehiculo",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<SiniestroVehiculo>> getSiniestroVehiculo(@NotNull @ApiParam(value = "Número de siniestro", required = true) @Valid @RequestParam(value = "numSiniestro", required = true) String numSiniestro,@NotNull @ApiParam(value = "Código de compañía", required = true) @Valid @RequestParam(value = "codCia", required = true) Integer codCia,@NotNull @ApiParam(value = "Número de póliza", required = true) @Valid @RequestParam(value = "numPoliza", required = true) String numPoliza,@NotNull @ApiParam(value = "Código de fase", required = true) @Valid @RequestParam(value = "codFase", required = true) String codFase,@NotNull @ApiParam(value = "Código de ramo", required = true) @Valid @RequestParam(value = "codRamo", required = true) Integer codRamo) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"jsonArchivos\" : \"{}\",  \"nomEstado\" : \"Recepción del vehículo\",  \"fechaActualizacion\" : \"05/05/2025 21:11:20\",  \"imagen\" : \"https://ejemplo.com/imagen.png\",  \"codEstado\" : \"TR01\"}, {  \"jsonArchivos\" : \"{}\",  \"nomEstado\" : \"Recepción del vehículo\",  \"fechaActualizacion\" : \"05/05/2025 21:11:20\",  \"imagen\" : \"https://ejemplo.com/imagen.png\",  \"codEstado\" : \"TR01\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TrackingApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener listado de siniestros", nickname = "getSiniestros", notes = "Obtiene la lista de siniestros según los criterios de búsqueda", response = Siniestro.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Tracking", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = Siniestro.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/tracking/get_siniestros",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<Siniestro>> getSiniestros(@NotNull @ApiParam(value = "Código de ramo", required = true) @Valid @RequestParam(value = "codRamo", required = true) Integer codRamo,@ApiParam(value = "Código de intermediario") @Valid @RequestParam(value = "codInter", required = false) Integer codInter,@ApiParam(value = "Número de póliza") @Valid @RequestParam(value = "numPoliza", required = false) String numPoliza,@ApiParam(value = "Número de siniestro") @Valid @RequestParam(value = "numSini", required = false) String numSini,@ApiParam(value = "Fecha de inicio para la búsqueda (formato yyyy-MM-dd)") @Valid @RequestParam(value = "fechaInicio", required = false) LocalDate fechaInicio,@ApiParam(value = "Fecha de fin para la búsqueda (formato yyyy-MM-dd)") @Valid @RequestParam(value = "fechaFin", required = false) LocalDate fechaFin) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"envioCorreo\" : \"ENV\",  \"fechaGrabacion\" : \"27-APR-25\",  \"codInter\" : 1,  \"agente\" : \"\",  \"estado\" : \"PENDIENTE\",  \"siniestroId\" : \"300252001000007\",  \"expediente\" : 1,  \"proceso\" : \"GRABACION DEL SINIESTRO\",  \"asegurado\" : \"CASTELLANOS GARCIA HUGO LEONEL\",  \"siniestro\" : \"300252001000007\",  \"numOrden\" : 1,  \"poliza\" : \"0230025018359\",  \"certificado\" : 1,  \"tipExp\" : \"RCA\",  \"codigoFase\" : \"GS\",  \"correo\" : \"\",  \"siniRef\" : \"\",  \"placa\" : \"P786JRV\"}, {  \"envioCorreo\" : \"ENV\",  \"fechaGrabacion\" : \"27-APR-25\",  \"codInter\" : 1,  \"agente\" : \"\",  \"estado\" : \"PENDIENTE\",  \"siniestroId\" : \"300252001000007\",  \"expediente\" : 1,  \"proceso\" : \"GRABACION DEL SINIESTRO\",  \"asegurado\" : \"CASTELLANOS GARCIA HUGO LEONEL\",  \"siniestro\" : \"300252001000007\",  \"numOrden\" : 1,  \"poliza\" : \"0230025018359\",  \"certificado\" : 1,  \"tipExp\" : \"RCA\",  \"codigoFase\" : \"GS\",  \"correo\" : \"\",  \"siniRef\" : \"\",  \"placa\" : \"P786JRV\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TrackingApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener detalle de un siniestro", nickname = "getTrackingDetalleSiniestro", notes = "Recupera el detalle de un siniestro con sus etapas, descripciones e imágenes", response = DetalleSiniestro.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Tracking", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = DetalleSiniestro.class, responseContainer = "List") })
    @RequestMapping(value = "/tracking/get_tracking_detalle_siniestro",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<DetalleSiniestro>> getTrackingDetalleSiniestro(@NotNull @ApiParam(value = "Número de siniestro", required = true) @Valid @RequestParam(value = "numSiniestro", required = true) String numSiniestro,@NotNull @ApiParam(value = "Código de ramo", required = true) @Valid @RequestParam(value = "codRamo", required = true) Integer codRamo) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"descripcion\" : \"Tu siniestro ha sido aperturado\",  \"fecha\" : \"05/05/2025 21:11:20\",  \"subTracking\" : \"N\",  \"codEtapa\" : \"GRABACION DEL SINIESTRO\",  \"etapa\" : \"Apertura Siniestro\",  \"imagen\" : \"https://app2.mapfre.com.gt/CDN/images/mapfre/tracking/reclamos/autos/apertura.png\",  \"numOrden\" : 1}, {  \"descripcion\" : \"Tu siniestro ha sido aperturado\",  \"fecha\" : \"05/05/2025 21:11:20\",  \"subTracking\" : \"N\",  \"codEtapa\" : \"GRABACION DEL SINIESTRO\",  \"etapa\" : \"Apertura Siniestro\",  \"imagen\" : \"https://app2.mapfre.com.gt/CDN/images/mapfre/tracking/reclamos/autos/apertura.png\",  \"numOrden\" : 1} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TrackingApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener etapas de un siniestro", nickname = "getTrackingEtapaSiniestro", notes = "Obtiene las etapas de un siniestro específico según su número y ramo", response = EtapaSiniestro.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Tracking", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = EtapaSiniestro.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/tracking/get_tracking_etapa_siniestro",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<EtapaSiniestro>> getTrackingEtapaSiniestro(@NotNull @ApiParam(value = "Número de siniestro", required = true) @Valid @RequestParam(value = "numSiniestro", required = true) String numSiniestro,@NotNull @ApiParam(value = "Código de ramo", required = true) @Valid @RequestParam(value = "codRamo", required = true) Integer codRamo) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"taller\" : \"taller\",  \"numPoliza\" : \"0230025018359\",  \"perito\" : \"perito\",  \"etapa\" : \"GS\",  \"numSini\" : \"300252001000007\",  \"numOrden\" : 1,  \"deducible\" : 0.0,  \"codigoFase\" : \"GS\",  \"fecha\" : \"27/04/2025 17:26:26\",  \"codRamo\" : 300,  \"monto\" : 0.8008281904610115,  \"cuenta\" : \"cuenta\",  \"codCia\" : 2,  \"cheque\" : \"cheque\"}, {  \"taller\" : \"taller\",  \"numPoliza\" : \"0230025018359\",  \"perito\" : \"perito\",  \"etapa\" : \"GS\",  \"numSini\" : \"300252001000007\",  \"numOrden\" : 1,  \"deducible\" : 0.0,  \"codigoFase\" : \"GS\",  \"fecha\" : \"27/04/2025 17:26:26\",  \"codRamo\" : 300,  \"monto\" : 0.8008281904610115,  \"cuenta\" : \"cuenta\",  \"codCia\" : 2,  \"cheque\" : \"cheque\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TrackingApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener URL encriptada de siniestro", nickname = "getUrlSiniestro", notes = "Obtiene una URL encriptada para acceder a un siniestro", response = UrlSiniestro.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Tracking", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = UrlSiniestro.class) })
    @RequestMapping(value = "/tracking/get_url_siniestro",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<UrlSiniestro> getUrlSiniestro(@NotNull @ApiParam(value = "URL base", required = true) @Valid @RequestParam(value = "url", required = true) String url,@NotNull @ApiParam(value = "Número de siniestro", required = true) @Valid @RequestParam(value = "numSiniestro", required = true) String numSiniestro,@NotNull @ApiParam(value = "Tipo de cliente (T, A, etc.)", required = true) @Valid @RequestParam(value = "tipoCliente", required = true) String tipoCliente) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"url\" : \"http://localhost/siniestro?token=abc123def456\"}", UrlSiniestro.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TrackingApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Guardar encuesta de siniestro", nickname = "saveEncuesta", notes = "Guarda las respuestas de una encuesta de siniestro", response = EncuestaSiniestroResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Tracking", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = EncuestaSiniestroResponse.class) })
    @RequestMapping(value = "/tracking/save_encuesta",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<EncuestaSiniestroResponse> saveEncuesta(@ApiParam(value = "Datos de la encuesta a guardar" ,required=true )  @Valid @RequestBody EncuestaSiniestroRequest body) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"resultado\" : \"OK\"}", EncuestaSiniestroResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TrackingApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Validar ingreso de encuesta", nickname = "validaIngresoEncuesta", notes = "Valida si un siniestro puede tener una encuesta", response = ValidaIngresoEncuestaResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Tracking", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = ValidaIngresoEncuestaResponse.class) })
    @RequestMapping(value = "/tracking/valida_ingreso_encuesta",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<ValidaIngresoEncuestaResponse> validaIngresoEncuesta(@NotNull @ApiParam(value = "Número de siniestro", required = true) @Valid @RequestParam(value = "numSiniestro", required = true) String numSiniestro) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"resultado\" : \"OK\"}", ValidaIngresoEncuestaResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TrackingApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

}
