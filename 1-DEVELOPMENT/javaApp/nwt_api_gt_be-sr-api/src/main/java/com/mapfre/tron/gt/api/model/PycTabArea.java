package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycTabArea
 */
@Validated

public class PycTabArea   {
  @JsonProperty("idArea")
  private Integer idArea = null;

  @JsonProperty("nombreArea")
  private String nombreArea = null;

  public PycTabArea idArea(Integer idArea) {
    this.idArea = idArea;
    return this;
  }

  /**
   * Identificador del área
   * @return idArea
  **/
  @ApiModelProperty(example = "1", value = "Identificador del área")


  public Integer getIdArea() {
    return idArea;
  }

  public void setIdArea(Integer idArea) {
    this.idArea = idArea;
  }

  public PycTabArea nombreArea(String nombreArea) {
    this.nombreArea = nombreArea;
    return this;
  }

  /**
   * Nombre del área
   * @return nombreArea
  **/
  @ApiModelProperty(example = "Tecnología", value = "Nombre del área")


  public String getNombreArea() {
    return nombreArea;
  }

  public void setNombreArea(String nombreArea) {
    this.nombreArea = nombreArea;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycTabArea pycTabArea = (PycTabArea) o;
    return Objects.equals(this.idArea, pycTabArea.idArea) &&
        Objects.equals(this.nombreArea, pycTabArea.nombreArea);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idArea, nombreArea);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycTabArea {\n");
    
    sb.append("    idArea: ").append(toIndentedString(idArea)).append("\n");
    sb.append("    nombreArea: ").append(toIndentedString(nombreArea)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

