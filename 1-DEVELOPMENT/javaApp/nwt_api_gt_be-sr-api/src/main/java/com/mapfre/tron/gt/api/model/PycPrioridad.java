package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycPrioridad
 */
@Validated

public class PycPrioridad   {
  @JsonProperty("codigo")
  private String codigo = null;

  @JsonProperty("prioridad")
  private String prioridad = null;

  public PycPrioridad codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Código de la prioridad
   * @return codigo
  **/
  @ApiModelProperty(example = "1", value = "Código de la prioridad")


  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public PycPrioridad prioridad(String prioridad) {
    this.prioridad = prioridad;
    return this;
  }

  /**
   * Descripción de la prioridad
   * @return prioridad
  **/
  @ApiModelProperty(example = "Alta", value = "Descripción de la prioridad")


  public String getPrioridad() {
    return prioridad;
  }

  public void setPrioridad(String prioridad) {
    this.prioridad = prioridad;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycPrioridad pycPrioridad = (PycPrioridad) o;
    return Objects.equals(this.codigo, pycPrioridad.codigo) &&
        Objects.equals(this.prioridad, pycPrioridad.prioridad);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codigo, prioridad);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycPrioridad {\n");
    
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("    prioridad: ").append(toIndentedString(prioridad)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

