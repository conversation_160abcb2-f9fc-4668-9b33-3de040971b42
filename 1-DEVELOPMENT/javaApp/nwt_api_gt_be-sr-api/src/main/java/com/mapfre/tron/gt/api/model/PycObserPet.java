package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycObserPet
 */
@Validated

public class PycObserPet   {
  @JsonProperty("publico")
  private String publico = null;

  @JsonProperty("urlPerfil")
  private String urlPerfil = null;

  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("nombre")
  private String nombre = null;

  @JsonProperty("nombrePeticion")
  private String nombrePeticion = null;

  @JsonProperty("idObservacion")
  private Integer idObservacion = null;

  @JsonProperty("observacion")
  private String observacion = null;

  @JsonProperty("fechaHora")
  private String fechaHora = null;

  @JsonProperty("nombreArea")
  private String nombreArea = null;

  @JsonProperty("nombreDepartamento")
  private String nombreDepartamento = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("genero")
  private String genero = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("indicador")
  private String indicador = null;

  @JsonProperty("connected")
  private String connected = null;

  public PycObserPet publico(String publico) {
    this.publico = publico;
    return this;
  }

  /**
   * Indicador si la observación es pública (S/N)
   * @return publico
  **/
  @ApiModelProperty(example = "S", value = "Indicador si la observación es pública (S/N)")


  public String getPublico() {
    return publico;
  }

  public void setPublico(String publico) {
    this.publico = publico;
  }

  public PycObserPet urlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
    return this;
  }

  /**
   * URL del perfil del usuario o imagen por defecto según género
   * @return urlPerfil
  **/
  @ApiModelProperty(example = "/images/profile/user_male.png", value = "URL del perfil del usuario o imagen por defecto según género")


  public String getUrlPerfil() {
    return urlPerfil;
  }

  public void setUrlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
  }

  public PycObserPet idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador de la petición")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycObserPet nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre completo del usuario (primer nombre + primer apellido)
   * @return nombre
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre completo del usuario (primer nombre + primer apellido)")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public PycObserPet nombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
    return this;
  }

  /**
   * Nombre de la petición
   * @return nombrePeticion
  **/
  @ApiModelProperty(example = "Solicitud de cambio de datos", value = "Nombre de la petición")


  public String getNombrePeticion() {
    return nombrePeticion;
  }

  public void setNombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
  }

  public PycObserPet idObservacion(Integer idObservacion) {
    this.idObservacion = idObservacion;
    return this;
  }

  /**
   * Identificador único de la observación
   * @return idObservacion
  **/
  @ApiModelProperty(example = "123", value = "Identificador único de la observación")


  public Integer getIdObservacion() {
    return idObservacion;
  }

  public void setIdObservacion(Integer idObservacion) {
    this.idObservacion = idObservacion;
  }

  public PycObserPet observacion(String observacion) {
    this.observacion = observacion;
    return this;
  }

  /**
   * Texto de la observación en mayúsculas
   * @return observacion
  **/
  @ApiModelProperty(example = "SE REQUIERE DOCUMENTACIÓN ADICIONAL", value = "Texto de la observación en mayúsculas")


  public String getObservacion() {
    return observacion;
  }

  public void setObservacion(String observacion) {
    this.observacion = observacion;
  }

  public PycObserPet fechaHora(String fechaHora) {
    this.fechaHora = fechaHora;
    return this;
  }

  /**
   * Fecha y hora formateada de manera amigable (ej. 'Hace un momento', 'Hoy 14:30', 'Ayer 09:15')
   * @return fechaHora
  **/
  @ApiModelProperty(example = "Hoy 14:30", value = "Fecha y hora formateada de manera amigable (ej. 'Hace un momento', 'Hoy 14:30', 'Ayer 09:15')")


  public String getFechaHora() {
    return fechaHora;
  }

  public void setFechaHora(String fechaHora) {
    this.fechaHora = fechaHora;
  }

  public PycObserPet nombreArea(String nombreArea) {
    this.nombreArea = nombreArea;
    return this;
  }

  /**
   * Nombre del área del usuario
   * @return nombreArea
  **/
  @ApiModelProperty(example = "Tecnología", value = "Nombre del área del usuario")


  public String getNombreArea() {
    return nombreArea;
  }

  public void setNombreArea(String nombreArea) {
    this.nombreArea = nombreArea;
  }

  public PycObserPet nombreDepartamento(String nombreDepartamento) {
    this.nombreDepartamento = nombreDepartamento;
    return this;
  }

  /**
   * Nombre del departamento del usuario
   * @return nombreDepartamento
  **/
  @ApiModelProperty(example = "Desarrollo de Software", value = "Nombre del departamento del usuario")


  public String getNombreDepartamento() {
    return nombreDepartamento;
  }

  public void setNombreDepartamento(String nombreDepartamento) {
    this.nombreDepartamento = nombreDepartamento;
  }

  public PycObserPet estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado de la observación
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", value = "Estado de la observación")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycObserPet genero(String genero) {
    this.genero = genero;
    return this;
  }

  /**
   * Género del usuario (M/F)
   * @return genero
  **/
  @ApiModelProperty(example = "M", value = "Género del usuario (M/F)")


  public String getGenero() {
    return genero;
  }

  public void setGenero(String genero) {
    this.genero = genero;
  }

  public PycObserPet usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Usuario de base de datos
   * @return usuario
  **/
  @ApiModelProperty(example = "jperez", value = "Usuario de base de datos")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public PycObserPet indicador(String indicador) {
    this.indicador = indicador;
    return this;
  }

  /**
   * Indicador de posición del mensaje ('right' si es del usuario actual, 'left' si es de otro)
   * @return indicador
  **/
  @ApiModelProperty(example = "left", value = "Indicador de posición del mensaje ('right' si es del usuario actual, 'left' si es de otro)")


  public String getIndicador() {
    return indicador;
  }

  public void setIndicador(String indicador) {
    this.indicador = indicador;
  }

  public PycObserPet connected(String connected) {
    this.connected = connected;
    return this;
  }

  /**
   * Estado de conexión del usuario ('on' si está conectado, 'off' si está desconectado)
   * @return connected
  **/
  @ApiModelProperty(example = "on", value = "Estado de conexión del usuario ('on' si está conectado, 'off' si está desconectado)")


  public String getConnected() {
    return connected;
  }

  public void setConnected(String connected) {
    this.connected = connected;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycObserPet pycObserPet = (PycObserPet) o;
    return Objects.equals(this.publico, pycObserPet.publico) &&
        Objects.equals(this.urlPerfil, pycObserPet.urlPerfil) &&
        Objects.equals(this.idPeticion, pycObserPet.idPeticion) &&
        Objects.equals(this.nombre, pycObserPet.nombre) &&
        Objects.equals(this.nombrePeticion, pycObserPet.nombrePeticion) &&
        Objects.equals(this.idObservacion, pycObserPet.idObservacion) &&
        Objects.equals(this.observacion, pycObserPet.observacion) &&
        Objects.equals(this.fechaHora, pycObserPet.fechaHora) &&
        Objects.equals(this.nombreArea, pycObserPet.nombreArea) &&
        Objects.equals(this.nombreDepartamento, pycObserPet.nombreDepartamento) &&
        Objects.equals(this.estado, pycObserPet.estado) &&
        Objects.equals(this.genero, pycObserPet.genero) &&
        Objects.equals(this.usuario, pycObserPet.usuario) &&
        Objects.equals(this.indicador, pycObserPet.indicador) &&
        Objects.equals(this.connected, pycObserPet.connected);
  }

  @Override
  public int hashCode() {
    return Objects.hash(publico, urlPerfil, idPeticion, nombre, nombrePeticion, idObservacion, observacion, fechaHora, nombreArea, nombreDepartamento, estado, genero, usuario, indicador, connected);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycObserPet {\n");
    
    sb.append("    publico: ").append(toIndentedString(publico)).append("\n");
    sb.append("    urlPerfil: ").append(toIndentedString(urlPerfil)).append("\n");
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    nombrePeticion: ").append(toIndentedString(nombrePeticion)).append("\n");
    sb.append("    idObservacion: ").append(toIndentedString(idObservacion)).append("\n");
    sb.append("    observacion: ").append(toIndentedString(observacion)).append("\n");
    sb.append("    fechaHora: ").append(toIndentedString(fechaHora)).append("\n");
    sb.append("    nombreArea: ").append(toIndentedString(nombreArea)).append("\n");
    sb.append("    nombreDepartamento: ").append(toIndentedString(nombreDepartamento)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    genero: ").append(toIndentedString(genero)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    indicador: ").append(toIndentedString(indicador)).append("\n");
    sb.append("    connected: ").append(toIndentedString(connected)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

