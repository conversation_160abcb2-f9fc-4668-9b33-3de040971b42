package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycRespPeti
 */
@Validated

public class PycRespPeti   {
  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("nombre")
  private String nombre = null;

  @JsonProperty("cargo")
  private String cargo = null;

  @JsonProperty("title")
  private String title = null;

  @JsonProperty("urlPerfil")
  private String urlPerfil = null;

  @JsonProperty("genero")
  private String genero = null;

  @JsonProperty("indicador")
  private String indicador = null;

  @JsonProperty("fecha")
  private String fecha = null;

  @JsonProperty("connected")
  private String connected = null;

  public PycRespPeti usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Usuario base de datos del responsable
   * @return usuario
  **/
  @ApiModelProperty(example = "JPEREZ", value = "Usuario base de datos del responsable")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public PycRespPeti nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre completo del responsable
   * @return nombre
  **/
  @ApiModelProperty(example = "Juan Pérez García", value = "Nombre completo del responsable")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public PycRespPeti cargo(String cargo) {
    this.cargo = cargo;
    return this;
  }

  /**
   * Cargo o rol del responsable en la petición
   * @return cargo
  **/
  @ApiModelProperty(example = "SOLICITANTE", value = "Cargo o rol del responsable en la petición")


  public String getCargo() {
    return cargo;
  }

  public void setCargo(String cargo) {
    this.cargo = cargo;
  }

  public PycRespPeti title(String title) {
    this.title = title;
    return this;
  }

  /**
   * Información detallada del responsable con fechas y contexto
   * @return title
  **/
  @ApiModelProperty(example = "INFORMACION DE LA SOLICITUD&#013;&#013;FECHA REGISTRO: 15/03/2024 10:30", value = "Información detallada del responsable con fechas y contexto")


  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public PycRespPeti urlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
    return this;
  }

  /**
   * URL de la imagen de perfil del usuario
   * @return urlPerfil
  **/
  @ApiModelProperty(example = "/images/profiles/user_male.png", value = "URL de la imagen de perfil del usuario")


  public String getUrlPerfil() {
    return urlPerfil;
  }

  public void setUrlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
  }

  public PycRespPeti genero(String genero) {
    this.genero = genero;
    return this;
  }

  /**
   * Género del usuario (M/F)
   * @return genero
  **/
  @ApiModelProperty(example = "M", value = "Género del usuario (M/F)")


  public String getGenero() {
    return genero;
  }

  public void setGenero(String genero) {
    this.genero = genero;
  }

  public PycRespPeti indicador(String indicador) {
    this.indicador = indicador;
    return this;
  }

  /**
   * Indicador de posición en la interfaz (right/left)
   * @return indicador
  **/
  @ApiModelProperty(example = "left", value = "Indicador de posición en la interfaz (right/left)")


  public String getIndicador() {
    return indicador;
  }

  public void setIndicador(String indicador) {
    this.indicador = indicador;
  }

  public PycRespPeti fecha(String fecha) {
    this.fecha = fecha;
    return this;
  }

  /**
   * Fecha de registro o asignación
   * @return fecha
  **/
  @ApiModelProperty(example = "2025-05-24 02:09:48.0", value = "Fecha de registro o asignación")


  public String getFecha() {
    return fecha;
  }

  public void setFecha(String fecha) {
    this.fecha = fecha;
  }

  public PycRespPeti connected(String connected) {
    this.connected = connected;
    return this;
  }

  /**
   * Estado de conexión del usuario (on/off)
   * @return connected
  **/
  @ApiModelProperty(example = "on", value = "Estado de conexión del usuario (on/off)")


  public String getConnected() {
    return connected;
  }

  public void setConnected(String connected) {
    this.connected = connected;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycRespPeti pycRespPeti = (PycRespPeti) o;
    return Objects.equals(this.usuario, pycRespPeti.usuario) &&
        Objects.equals(this.nombre, pycRespPeti.nombre) &&
        Objects.equals(this.cargo, pycRespPeti.cargo) &&
        Objects.equals(this.title, pycRespPeti.title) &&
        Objects.equals(this.urlPerfil, pycRespPeti.urlPerfil) &&
        Objects.equals(this.genero, pycRespPeti.genero) &&
        Objects.equals(this.indicador, pycRespPeti.indicador) &&
        Objects.equals(this.fecha, pycRespPeti.fecha) &&
        Objects.equals(this.connected, pycRespPeti.connected);
  }

  @Override
  public int hashCode() {
    return Objects.hash(usuario, nombre, cargo, title, urlPerfil, genero, indicador, fecha, connected);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycRespPeti {\n");
    
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    cargo: ").append(toIndentedString(cargo)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    urlPerfil: ").append(toIndentedString(urlPerfil)).append("\n");
    sb.append("    genero: ").append(toIndentedString(genero)).append("\n");
    sb.append("    indicador: ").append(toIndentedString(indicador)).append("\n");
    sb.append("    fecha: ").append(toIndentedString(fecha)).append("\n");
    sb.append("    connected: ").append(toIndentedString(connected)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

