package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * EncuestaSiniestroRequest
 */
@Validated

public class EncuestaSiniestroRequest   {
  @JsonProperty("numSiniestro")
  private String numSiniestro = null;

  @JsonProperty("idEncuesta")
  private Integer idEncuesta = null;

  @JsonProperty("archXml")
  private String archXml = null;

  public EncuestaSiniestroRequest numSiniestro(String numSiniestro) {
    this.numSiniestro = numSiniestro;
    return this;
  }

  /**
   * Número de siniestro
   * @return numSiniestro
  **/
  @ApiModelProperty(example = "300252001000007", required = true, value = "Número de siniestro")
  @NotNull


  public String getNumSiniestro() {
    return numSiniestro;
  }

  public void setNumSiniestro(String numSiniestro) {
    this.numSiniestro = numSiniestro;
  }

  public EncuestaSiniestroRequest idEncuesta(Integer idEncuesta) {
    this.idEncuesta = idEncuesta;
    return this;
  }

  /**
   * Identificador de la encuesta
   * @return idEncuesta
  **/
  @ApiModelProperty(example = "1", required = true, value = "Identificador de la encuesta")
  @NotNull


  public Integer getIdEncuesta() {
    return idEncuesta;
  }

  public void setIdEncuesta(Integer idEncuesta) {
    this.idEncuesta = idEncuesta;
  }

  public EncuestaSiniestroRequest archXml(String archXml) {
    this.archXml = archXml;
    return this;
  }

  /**
   * XML con las respuestas de la encuesta
   * @return archXml
  **/
  @ApiModelProperty(example = "<Encuesta><listado><id_pregunta>1</id_pregunta><id_respuesta>2</id_respuesta></listado></Encuesta>", required = true, value = "XML con las respuestas de la encuesta")
  @NotNull


  public String getArchXml() {
    return archXml;
  }

  public void setArchXml(String archXml) {
    this.archXml = archXml;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EncuestaSiniestroRequest encuestaSiniestroRequest = (EncuestaSiniestroRequest) o;
    return Objects.equals(this.numSiniestro, encuestaSiniestroRequest.numSiniestro) &&
        Objects.equals(this.idEncuesta, encuestaSiniestroRequest.idEncuesta) &&
        Objects.equals(this.archXml, encuestaSiniestroRequest.archXml);
  }

  @Override
  public int hashCode() {
    return Objects.hash(numSiniestro, idEncuesta, archXml);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EncuestaSiniestroRequest {\n");
    
    sb.append("    numSiniestro: ").append(toIndentedString(numSiniestro)).append("\n");
    sb.append("    idEncuesta: ").append(toIndentedString(idEncuesta)).append("\n");
    sb.append("    archXml: ").append(toIndentedString(archXml)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

