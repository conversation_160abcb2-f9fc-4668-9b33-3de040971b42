package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * BusquedaAsegurado
 */
@Validated

public class BusquedaAsegurado   {
  @JsonProperty("Nombre")
  private String nombre = null;

  @JsonProperty("tipDocum")
  private String tipDocum = null;

  @JsonProperty("codDocum")
  private String codDocum = null;

  public BusquedaAsegurado nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Get nombre
   * @return nombre
  **/
  @ApiModelProperty(value = "")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public BusquedaAsegurado tipDocum(String tipDocum) {
    this.tipDocum = tipDocum;
    return this;
  }

  /**
   * Get tipDocum
   * @return tipDocum
  **/
  @ApiModelProperty(value = "")


  public String getTipDocum() {
    return tipDocum;
  }

  public void setTipDocum(String tipDocum) {
    this.tipDocum = tipDocum;
  }

  public BusquedaAsegurado codDocum(String codDocum) {
    this.codDocum = codDocum;
    return this;
  }

  /**
   * Get codDocum
   * @return codDocum
  **/
  @ApiModelProperty(value = "")


  public String getCodDocum() {
    return codDocum;
  }

  public void setCodDocum(String codDocum) {
    this.codDocum = codDocum;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BusquedaAsegurado busquedaAsegurado = (BusquedaAsegurado) o;
    return Objects.equals(this.nombre, busquedaAsegurado.nombre) &&
        Objects.equals(this.tipDocum, busquedaAsegurado.tipDocum) &&
        Objects.equals(this.codDocum, busquedaAsegurado.codDocum);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nombre, tipDocum, codDocum);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BusquedaAsegurado {\n");
    
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    tipDocum: ").append(toIndentedString(tipDocum)).append("\n");
    sb.append("    codDocum: ").append(toIndentedString(codDocum)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

