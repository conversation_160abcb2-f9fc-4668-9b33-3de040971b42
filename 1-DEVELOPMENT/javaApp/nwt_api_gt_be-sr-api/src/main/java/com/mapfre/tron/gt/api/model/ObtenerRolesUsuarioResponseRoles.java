package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ObtenerRolesUsuarioResponseRoles
 */
@Validated

public class ObtenerRolesUsuarioResponseRoles   {
  @JsonProperty("idRol")
  private Integer idRol = null;

  @JsonProperty("nombre")
  private String nombre = null;

  public ObtenerRolesUsuarioResponseRoles idRol(Integer idRol) {
    this.idRol = idRol;
    return this;
  }

  /**
   * ID del rol
   * @return idRol
  **/
  @ApiModelProperty(example = "1", value = "ID del rol")


  public Integer getIdRol() {
    return idRol;
  }

  public void setIdRol(Integer idRol) {
    this.idRol = idRol;
  }

  public ObtenerRolesUsuarioResponseRoles nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre del rol
   * @return nombre
  **/
  @ApiModelProperty(example = "ADMINISTRADOR", value = "Nombre del rol")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ObtenerRolesUsuarioResponseRoles obtenerRolesUsuarioResponseRoles = (ObtenerRolesUsuarioResponseRoles) o;
    return Objects.equals(this.idRol, obtenerRolesUsuarioResponseRoles.idRol) &&
        Objects.equals(this.nombre, obtenerRolesUsuarioResponseRoles.nombre);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idRol, nombre);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ObtenerRolesUsuarioResponseRoles {\n");
    
    sb.append("    idRol: ").append(toIndentedString(idRol)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

