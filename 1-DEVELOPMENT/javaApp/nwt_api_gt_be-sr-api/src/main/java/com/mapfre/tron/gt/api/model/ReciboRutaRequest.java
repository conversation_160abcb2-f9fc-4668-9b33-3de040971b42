package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ReciboRutaRequest
 */
@Validated

public class ReciboRutaRequest   {
  @JsonProperty("ruta")
  private Integer ruta = null;

  @JsonProperty("recibo")
  private String recibo = null;

  @JsonProperty("latitud")
  private String latitud = null;

  @JsonProperty("longitud")
  private String longitud = null;

  @JsonProperty("usuario")
  private String usuario = null;

  public ReciboRutaRequest ruta(Integer ruta) {
    this.ruta = ruta;
    return this;
  }

  /**
   * Número de ruta
   * @return ruta
  **/
  @ApiModelProperty(example = "1", required = true, value = "Número de ruta")
  @NotNull


  public Integer getRuta() {
    return ruta;
  }

  public void setRuta(Integer ruta) {
    this.ruta = ruta;
  }

  public ReciboRutaRequest recibo(String recibo) {
    this.recibo = recibo;
    return this;
  }

  /**
   * Número de recibo
   * @return recibo
  **/
  @ApiModelProperty(example = "REC123456", required = true, value = "Número de recibo")
  @NotNull


  public String getRecibo() {
    return recibo;
  }

  public void setRecibo(String recibo) {
    this.recibo = recibo;
  }

  public ReciboRutaRequest latitud(String latitud) {
    this.latitud = latitud;
    return this;
  }

  /**
   * Coordenada de latitud
   * @return latitud
  **/
  @ApiModelProperty(example = "14.6349", required = true, value = "Coordenada de latitud")
  @NotNull


  public String getLatitud() {
    return latitud;
  }

  public void setLatitud(String latitud) {
    this.latitud = latitud;
  }

  public ReciboRutaRequest longitud(String longitud) {
    this.longitud = longitud;
    return this;
  }

  /**
   * Coordenada de longitud
   * @return longitud
  **/
  @ApiModelProperty(example = "-90.5069", required = true, value = "Coordenada de longitud")
  @NotNull


  public String getLongitud() {
    return longitud;
  }

  public void setLongitud(String longitud) {
    this.longitud = longitud;
  }

  public ReciboRutaRequest usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Usuario que realiza la actualización
   * @return usuario
  **/
  @ApiModelProperty(example = "user123", required = true, value = "Usuario que realiza la actualización")
  @NotNull


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReciboRutaRequest reciboRutaRequest = (ReciboRutaRequest) o;
    return Objects.equals(this.ruta, reciboRutaRequest.ruta) &&
        Objects.equals(this.recibo, reciboRutaRequest.recibo) &&
        Objects.equals(this.latitud, reciboRutaRequest.latitud) &&
        Objects.equals(this.longitud, reciboRutaRequest.longitud) &&
        Objects.equals(this.usuario, reciboRutaRequest.usuario);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ruta, recibo, latitud, longitud, usuario);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReciboRutaRequest {\n");
    
    sb.append("    ruta: ").append(toIndentedString(ruta)).append("\n");
    sb.append("    recibo: ").append(toIndentedString(recibo)).append("\n");
    sb.append("    latitud: ").append(toIndentedString(latitud)).append("\n");
    sb.append("    longitud: ").append(toIndentedString(longitud)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

