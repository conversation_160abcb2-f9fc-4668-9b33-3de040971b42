package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycInfoProceso
 */
@Validated

public class PycInfoProceso   {
  @JsonProperty("idProceso")
  private Integer idProceso = null;

  @JsonProperty("nombreProceso")
  private String nombreProceso = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("fechaHora")
  private String fechaHora = null;

  @JsonProperty("icono")
  private String icono = null;

  @JsonProperty("utilizaFiltros")
  private String utilizaFiltros = null;

  @JsonProperty("columInicio")
  private Integer columInicio = null;

  @JsonProperty("columFin")
  private Integer columFin = null;

  @JsonProperty("mcaRefId")
  private String mcaRefId = null;

  @JsonProperty("mcaUpdSol")
  private String mcaUpdSol = null;

  @JsonProperty("mcaAsigEstCom")
  private String mcaAsigEstCom = null;

  @JsonProperty("mcaInhSecAct")
  private String mcaInhSecAct = null;

  @JsonProperty("mcaInhSecObs")
  private String mcaInhSecObs = null;

  @JsonProperty("mcaInhSecInv")
  private String mcaInhSecInv = null;

  @JsonProperty("dirFtpWeb")
  private String dirFtpWeb = null;

  @JsonProperty("sqlSelect")
  private String sqlSelect = null;

  @JsonProperty("sqlTable")
  private String sqlTable = null;

  @JsonProperty("sqlWhere")
  private String sqlWhere = null;

  @JsonProperty("datatableHtml")
  private String datatableHtml = null;

  @JsonProperty("datatableJs")
  private String datatableJs = null;

  @JsonProperty("mcaIgnorInter")
  private String mcaIgnorInter = null;

  @JsonProperty("datatableDef")
  private String datatableDef = null;

  @JsonProperty("extraHtml")
  private String extraHtml = null;

  @JsonProperty("emailCc")
  private String emailCc = null;

  @JsonProperty("mcaCommentOblig")
  private String mcaCommentOblig = null;

  @JsonProperty("mcaSecuIdReferencia")
  private String mcaSecuIdReferencia = null;

  @JsonProperty("mcaAsigAutom")
  private String mcaAsigAutom = null;

  @JsonProperty("mcaCambioSol")
  private String mcaCambioSol = null;

  public PycInfoProceso idProceso(Integer idProceso) {
    this.idProceso = idProceso;
    return this;
  }

  /**
   * Identificador único del proceso
   * @return idProceso
  **/
  @ApiModelProperty(example = "7", value = "Identificador único del proceso")


  public Integer getIdProceso() {
    return idProceso;
  }

  public void setIdProceso(Integer idProceso) {
    this.idProceso = idProceso;
  }

  public PycInfoProceso nombreProceso(String nombreProceso) {
    this.nombreProceso = nombreProceso;
    return this;
  }

  /**
   * Nombre del proceso
   * @return nombreProceso
  **/
  @ApiModelProperty(example = "Gestión de Peticiones", value = "Nombre del proceso")


  public String getNombreProceso() {
    return nombreProceso;
  }

  public void setNombreProceso(String nombreProceso) {
    this.nombreProceso = nombreProceso;
  }

  public PycInfoProceso estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado del proceso
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", value = "Estado del proceso")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycInfoProceso usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Usuario del proceso
   * @return usuario
  **/
  @ApiModelProperty(example = "admin", value = "Usuario del proceso")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public PycInfoProceso fechaHora(String fechaHora) {
    this.fechaHora = fechaHora;
    return this;
  }

  /**
   * Fecha y hora del proceso
   * @return fechaHora
  **/
  @ApiModelProperty(example = "2024-01-15T10:30:00Z", value = "Fecha y hora del proceso")


  public String getFechaHora() {
    return fechaHora;
  }

  public void setFechaHora(String fechaHora) {
    this.fechaHora = fechaHora;
  }

  public PycInfoProceso icono(String icono) {
    this.icono = icono;
    return this;
  }

  /**
   * Icono asociado al proceso
   * @return icono
  **/
  @ApiModelProperty(example = "fa-tasks", value = "Icono asociado al proceso")


  public String getIcono() {
    return icono;
  }

  public void setIcono(String icono) {
    this.icono = icono;
  }

  public PycInfoProceso utilizaFiltros(String utilizaFiltros) {
    this.utilizaFiltros = utilizaFiltros;
    return this;
  }

  /**
   * Indicador si utiliza filtros
   * @return utilizaFiltros
  **/
  @ApiModelProperty(example = "S", value = "Indicador si utiliza filtros")


  public String getUtilizaFiltros() {
    return utilizaFiltros;
  }

  public void setUtilizaFiltros(String utilizaFiltros) {
    this.utilizaFiltros = utilizaFiltros;
  }

  public PycInfoProceso columInicio(Integer columInicio) {
    this.columInicio = columInicio;
    return this;
  }

  /**
   * Columna de inicio
   * @return columInicio
  **/
  @ApiModelProperty(example = "1", value = "Columna de inicio")


  public Integer getColumInicio() {
    return columInicio;
  }

  public void setColumInicio(Integer columInicio) {
    this.columInicio = columInicio;
  }

  public PycInfoProceso columFin(Integer columFin) {
    this.columFin = columFin;
    return this;
  }

  /**
   * Columna de fin
   * @return columFin
  **/
  @ApiModelProperty(example = "12", value = "Columna de fin")


  public Integer getColumFin() {
    return columFin;
  }

  public void setColumFin(Integer columFin) {
    this.columFin = columFin;
  }

  public PycInfoProceso mcaRefId(String mcaRefId) {
    this.mcaRefId = mcaRefId;
    return this;
  }

  /**
   * Marca de referencia ID
   * @return mcaRefId
  **/
  @ApiModelProperty(example = "S", value = "Marca de referencia ID")


  public String getMcaRefId() {
    return mcaRefId;
  }

  public void setMcaRefId(String mcaRefId) {
    this.mcaRefId = mcaRefId;
  }

  public PycInfoProceso mcaUpdSol(String mcaUpdSol) {
    this.mcaUpdSol = mcaUpdSol;
    return this;
  }

  /**
   * Marca de actualización de solicitud
   * @return mcaUpdSol
  **/
  @ApiModelProperty(example = "N", value = "Marca de actualización de solicitud")


  public String getMcaUpdSol() {
    return mcaUpdSol;
  }

  public void setMcaUpdSol(String mcaUpdSol) {
    this.mcaUpdSol = mcaUpdSol;
  }

  public PycInfoProceso mcaAsigEstCom(String mcaAsigEstCom) {
    this.mcaAsigEstCom = mcaAsigEstCom;
    return this;
  }

  /**
   * Marca de asignación de estado completo
   * @return mcaAsigEstCom
  **/
  @ApiModelProperty(example = "S", value = "Marca de asignación de estado completo")


  public String getMcaAsigEstCom() {
    return mcaAsigEstCom;
  }

  public void setMcaAsigEstCom(String mcaAsigEstCom) {
    this.mcaAsigEstCom = mcaAsigEstCom;
  }

  public PycInfoProceso mcaInhSecAct(String mcaInhSecAct) {
    this.mcaInhSecAct = mcaInhSecAct;
    return this;
  }

  /**
   * Marca de inhibición de sección de actividades
   * @return mcaInhSecAct
  **/
  @ApiModelProperty(example = "N", value = "Marca de inhibición de sección de actividades")


  public String getMcaInhSecAct() {
    return mcaInhSecAct;
  }

  public void setMcaInhSecAct(String mcaInhSecAct) {
    this.mcaInhSecAct = mcaInhSecAct;
  }

  public PycInfoProceso mcaInhSecObs(String mcaInhSecObs) {
    this.mcaInhSecObs = mcaInhSecObs;
    return this;
  }

  /**
   * Marca de inhibición de sección de observaciones
   * @return mcaInhSecObs
  **/
  @ApiModelProperty(example = "N", value = "Marca de inhibición de sección de observaciones")


  public String getMcaInhSecObs() {
    return mcaInhSecObs;
  }

  public void setMcaInhSecObs(String mcaInhSecObs) {
    this.mcaInhSecObs = mcaInhSecObs;
  }

  public PycInfoProceso mcaInhSecInv(String mcaInhSecInv) {
    this.mcaInhSecInv = mcaInhSecInv;
    return this;
  }

  /**
   * Marca de inhibición de sección de involucrados
   * @return mcaInhSecInv
  **/
  @ApiModelProperty(example = "N", value = "Marca de inhibición de sección de involucrados")


  public String getMcaInhSecInv() {
    return mcaInhSecInv;
  }

  public void setMcaInhSecInv(String mcaInhSecInv) {
    this.mcaInhSecInv = mcaInhSecInv;
  }

  public PycInfoProceso dirFtpWeb(String dirFtpWeb) {
    this.dirFtpWeb = dirFtpWeb;
    return this;
  }

  /**
   * Directorio FTP web
   * @return dirFtpWeb
  **/
  @ApiModelProperty(example = "/ftp/procesos/", value = "Directorio FTP web")


  public String getDirFtpWeb() {
    return dirFtpWeb;
  }

  public void setDirFtpWeb(String dirFtpWeb) {
    this.dirFtpWeb = dirFtpWeb;
  }

  public PycInfoProceso sqlSelect(String sqlSelect) {
    this.sqlSelect = sqlSelect;
    return this;
  }

  /**
   * Consulta SQL SELECT personalizada del proceso
   * @return sqlSelect
  **/
  @ApiModelProperty(example = "SELECT campo1, campo2", value = "Consulta SQL SELECT personalizada del proceso")


  public String getSqlSelect() {
    return sqlSelect;
  }

  public void setSqlSelect(String sqlSelect) {
    this.sqlSelect = sqlSelect;
  }

  public PycInfoProceso sqlTable(String sqlTable) {
    this.sqlTable = sqlTable;
    return this;
  }

  /**
   * Tablas SQL adicionales del proceso
   * @return sqlTable
  **/
  @ApiModelProperty(example = "tabla_adicional t", value = "Tablas SQL adicionales del proceso")


  public String getSqlTable() {
    return sqlTable;
  }

  public void setSqlTable(String sqlTable) {
    this.sqlTable = sqlTable;
  }

  public PycInfoProceso sqlWhere(String sqlWhere) {
    this.sqlWhere = sqlWhere;
    return this;
  }

  /**
   * Condiciones WHERE adicionales del proceso
   * @return sqlWhere
  **/
  @ApiModelProperty(example = "AND t.estado = 'ACT'", value = "Condiciones WHERE adicionales del proceso")


  public String getSqlWhere() {
    return sqlWhere;
  }

  public void setSqlWhere(String sqlWhere) {
    this.sqlWhere = sqlWhere;
  }

  public PycInfoProceso datatableHtml(String datatableHtml) {
    this.datatableHtml = datatableHtml;
    return this;
  }

  /**
   * HTML del datatable
   * @return datatableHtml
  **/
  @ApiModelProperty(example = "<div>HTML content</div>", value = "HTML del datatable")


  public String getDatatableHtml() {
    return datatableHtml;
  }

  public void setDatatableHtml(String datatableHtml) {
    this.datatableHtml = datatableHtml;
  }

  public PycInfoProceso datatableJs(String datatableJs) {
    this.datatableJs = datatableJs;
    return this;
  }

  /**
   * JavaScript del datatable
   * @return datatableJs
  **/
  @ApiModelProperty(example = "$(document).ready(function(){});", value = "JavaScript del datatable")


  public String getDatatableJs() {
    return datatableJs;
  }

  public void setDatatableJs(String datatableJs) {
    this.datatableJs = datatableJs;
  }

  public PycInfoProceso mcaIgnorInter(String mcaIgnorInter) {
    this.mcaIgnorInter = mcaIgnorInter;
    return this;
  }

  /**
   * Marca de ignorar interacción
   * @return mcaIgnorInter
  **/
  @ApiModelProperty(example = "N", value = "Marca de ignorar interacción")


  public String getMcaIgnorInter() {
    return mcaIgnorInter;
  }

  public void setMcaIgnorInter(String mcaIgnorInter) {
    this.mcaIgnorInter = mcaIgnorInter;
  }

  public PycInfoProceso datatableDef(String datatableDef) {
    this.datatableDef = datatableDef;
    return this;
  }

  /**
   * Definición del datatable
   * @return datatableDef
  **/
  @ApiModelProperty(example = "{'columns': []}", value = "Definición del datatable")


  public String getDatatableDef() {
    return datatableDef;
  }

  public void setDatatableDef(String datatableDef) {
    this.datatableDef = datatableDef;
  }

  public PycInfoProceso extraHtml(String extraHtml) {
    this.extraHtml = extraHtml;
    return this;
  }

  /**
   * HTML extra
   * @return extraHtml
  **/
  @ApiModelProperty(example = "<div>Extra content</div>", value = "HTML extra")


  public String getExtraHtml() {
    return extraHtml;
  }

  public void setExtraHtml(String extraHtml) {
    this.extraHtml = extraHtml;
  }

  public PycInfoProceso emailCc(String emailCc) {
    this.emailCc = emailCc;
    return this;
  }

  /**
   * Emails en copia
   * @return emailCc
  **/
  @ApiModelProperty(example = "<EMAIL>,<EMAIL>", value = "Emails en copia")


  public String getEmailCc() {
    return emailCc;
  }

  public void setEmailCc(String emailCc) {
    this.emailCc = emailCc;
  }

  public PycInfoProceso mcaCommentOblig(String mcaCommentOblig) {
    this.mcaCommentOblig = mcaCommentOblig;
    return this;
  }

  /**
   * Marca de comentario obligatorio
   * @return mcaCommentOblig
  **/
  @ApiModelProperty(example = "S", value = "Marca de comentario obligatorio")


  public String getMcaCommentOblig() {
    return mcaCommentOblig;
  }

  public void setMcaCommentOblig(String mcaCommentOblig) {
    this.mcaCommentOblig = mcaCommentOblig;
  }

  public PycInfoProceso mcaSecuIdReferencia(String mcaSecuIdReferencia) {
    this.mcaSecuIdReferencia = mcaSecuIdReferencia;
    return this;
  }

  /**
   * Marca de secuencia ID referencia
   * @return mcaSecuIdReferencia
  **/
  @ApiModelProperty(example = "S", value = "Marca de secuencia ID referencia")


  public String getMcaSecuIdReferencia() {
    return mcaSecuIdReferencia;
  }

  public void setMcaSecuIdReferencia(String mcaSecuIdReferencia) {
    this.mcaSecuIdReferencia = mcaSecuIdReferencia;
  }

  public PycInfoProceso mcaAsigAutom(String mcaAsigAutom) {
    this.mcaAsigAutom = mcaAsigAutom;
    return this;
  }

  /**
   * Marca de asignación automática
   * @return mcaAsigAutom
  **/
  @ApiModelProperty(example = "N", value = "Marca de asignación automática")


  public String getMcaAsigAutom() {
    return mcaAsigAutom;
  }

  public void setMcaAsigAutom(String mcaAsigAutom) {
    this.mcaAsigAutom = mcaAsigAutom;
  }

  public PycInfoProceso mcaCambioSol(String mcaCambioSol) {
    this.mcaCambioSol = mcaCambioSol;
    return this;
  }

  /**
   * Marca de cambio de solicitud
   * @return mcaCambioSol
  **/
  @ApiModelProperty(example = "S", value = "Marca de cambio de solicitud")


  public String getMcaCambioSol() {
    return mcaCambioSol;
  }

  public void setMcaCambioSol(String mcaCambioSol) {
    this.mcaCambioSol = mcaCambioSol;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycInfoProceso pycInfoProceso = (PycInfoProceso) o;
    return Objects.equals(this.idProceso, pycInfoProceso.idProceso) &&
        Objects.equals(this.nombreProceso, pycInfoProceso.nombreProceso) &&
        Objects.equals(this.estado, pycInfoProceso.estado) &&
        Objects.equals(this.usuario, pycInfoProceso.usuario) &&
        Objects.equals(this.fechaHora, pycInfoProceso.fechaHora) &&
        Objects.equals(this.icono, pycInfoProceso.icono) &&
        Objects.equals(this.utilizaFiltros, pycInfoProceso.utilizaFiltros) &&
        Objects.equals(this.columInicio, pycInfoProceso.columInicio) &&
        Objects.equals(this.columFin, pycInfoProceso.columFin) &&
        Objects.equals(this.mcaRefId, pycInfoProceso.mcaRefId) &&
        Objects.equals(this.mcaUpdSol, pycInfoProceso.mcaUpdSol) &&
        Objects.equals(this.mcaAsigEstCom, pycInfoProceso.mcaAsigEstCom) &&
        Objects.equals(this.mcaInhSecAct, pycInfoProceso.mcaInhSecAct) &&
        Objects.equals(this.mcaInhSecObs, pycInfoProceso.mcaInhSecObs) &&
        Objects.equals(this.mcaInhSecInv, pycInfoProceso.mcaInhSecInv) &&
        Objects.equals(this.dirFtpWeb, pycInfoProceso.dirFtpWeb) &&
        Objects.equals(this.sqlSelect, pycInfoProceso.sqlSelect) &&
        Objects.equals(this.sqlTable, pycInfoProceso.sqlTable) &&
        Objects.equals(this.sqlWhere, pycInfoProceso.sqlWhere) &&
        Objects.equals(this.datatableHtml, pycInfoProceso.datatableHtml) &&
        Objects.equals(this.datatableJs, pycInfoProceso.datatableJs) &&
        Objects.equals(this.mcaIgnorInter, pycInfoProceso.mcaIgnorInter) &&
        Objects.equals(this.datatableDef, pycInfoProceso.datatableDef) &&
        Objects.equals(this.extraHtml, pycInfoProceso.extraHtml) &&
        Objects.equals(this.emailCc, pycInfoProceso.emailCc) &&
        Objects.equals(this.mcaCommentOblig, pycInfoProceso.mcaCommentOblig) &&
        Objects.equals(this.mcaSecuIdReferencia, pycInfoProceso.mcaSecuIdReferencia) &&
        Objects.equals(this.mcaAsigAutom, pycInfoProceso.mcaAsigAutom) &&
        Objects.equals(this.mcaCambioSol, pycInfoProceso.mcaCambioSol);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idProceso, nombreProceso, estado, usuario, fechaHora, icono, utilizaFiltros, columInicio, columFin, mcaRefId, mcaUpdSol, mcaAsigEstCom, mcaInhSecAct, mcaInhSecObs, mcaInhSecInv, dirFtpWeb, sqlSelect, sqlTable, sqlWhere, datatableHtml, datatableJs, mcaIgnorInter, datatableDef, extraHtml, emailCc, mcaCommentOblig, mcaSecuIdReferencia, mcaAsigAutom, mcaCambioSol);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycInfoProceso {\n");
    
    sb.append("    idProceso: ").append(toIndentedString(idProceso)).append("\n");
    sb.append("    nombreProceso: ").append(toIndentedString(nombreProceso)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    fechaHora: ").append(toIndentedString(fechaHora)).append("\n");
    sb.append("    icono: ").append(toIndentedString(icono)).append("\n");
    sb.append("    utilizaFiltros: ").append(toIndentedString(utilizaFiltros)).append("\n");
    sb.append("    columInicio: ").append(toIndentedString(columInicio)).append("\n");
    sb.append("    columFin: ").append(toIndentedString(columFin)).append("\n");
    sb.append("    mcaRefId: ").append(toIndentedString(mcaRefId)).append("\n");
    sb.append("    mcaUpdSol: ").append(toIndentedString(mcaUpdSol)).append("\n");
    sb.append("    mcaAsigEstCom: ").append(toIndentedString(mcaAsigEstCom)).append("\n");
    sb.append("    mcaInhSecAct: ").append(toIndentedString(mcaInhSecAct)).append("\n");
    sb.append("    mcaInhSecObs: ").append(toIndentedString(mcaInhSecObs)).append("\n");
    sb.append("    mcaInhSecInv: ").append(toIndentedString(mcaInhSecInv)).append("\n");
    sb.append("    dirFtpWeb: ").append(toIndentedString(dirFtpWeb)).append("\n");
    sb.append("    sqlSelect: ").append(toIndentedString(sqlSelect)).append("\n");
    sb.append("    sqlTable: ").append(toIndentedString(sqlTable)).append("\n");
    sb.append("    sqlWhere: ").append(toIndentedString(sqlWhere)).append("\n");
    sb.append("    datatableHtml: ").append(toIndentedString(datatableHtml)).append("\n");
    sb.append("    datatableJs: ").append(toIndentedString(datatableJs)).append("\n");
    sb.append("    mcaIgnorInter: ").append(toIndentedString(mcaIgnorInter)).append("\n");
    sb.append("    datatableDef: ").append(toIndentedString(datatableDef)).append("\n");
    sb.append("    extraHtml: ").append(toIndentedString(extraHtml)).append("\n");
    sb.append("    emailCc: ").append(toIndentedString(emailCc)).append("\n");
    sb.append("    mcaCommentOblig: ").append(toIndentedString(mcaCommentOblig)).append("\n");
    sb.append("    mcaSecuIdReferencia: ").append(toIndentedString(mcaSecuIdReferencia)).append("\n");
    sb.append("    mcaAsigAutom: ").append(toIndentedString(mcaAsigAutom)).append("\n");
    sb.append("    mcaCambioSol: ").append(toIndentedString(mcaCambioSol)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

