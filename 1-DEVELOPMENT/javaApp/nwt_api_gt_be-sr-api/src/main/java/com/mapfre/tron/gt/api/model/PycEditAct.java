package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycEditAct
 */
@Validated

public class PycEditAct   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("idActividad")
  private Integer idActividad = null;

  @JsonProperty("nomActividad")
  private String nomActividad = null;

  @JsonProperty("descActividad")
  private String descActividad = null;

  @JsonProperty("hrsBase")
  private String hrsBase = null;

  @JsonProperty("categoria")
  private String categoria = null;

  @JsonProperty("fecIni")
  private String fecIni = null;

  @JsonProperty("fecFin")
  private String fecFin = null;

  @JsonProperty("asigna")
  private String asigna = null;

  public PycEditAct idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", required = true, value = "Identificador de la petición")
  @NotNull


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycEditAct idActividad(Integer idActividad) {
    this.idActividad = idActividad;
    return this;
  }

  /**
   * Identificador de la actividad a editar
   * @return idActividad
  **/
  @ApiModelProperty(example = "5001", required = true, value = "Identificador de la actividad a editar")
  @NotNull


  public Integer getIdActividad() {
    return idActividad;
  }

  public void setIdActividad(Integer idActividad) {
    this.idActividad = idActividad;
  }

  public PycEditAct nomActividad(String nomActividad) {
    this.nomActividad = nomActividad;
    return this;
  }

  /**
   * Nombre de la actividad
   * @return nomActividad
  **/
  @ApiModelProperty(example = "Análisis de requerimientos actualizado", required = true, value = "Nombre de la actividad")
  @NotNull


  public String getNomActividad() {
    return nomActividad;
  }

  public void setNomActividad(String nomActividad) {
    this.nomActividad = nomActividad;
  }

  public PycEditAct descActividad(String descActividad) {
    this.descActividad = descActividad;
    return this;
  }

  /**
   * Descripción de la actividad
   * @return descActividad
  **/
  @ApiModelProperty(example = "Análisis detallado y actualizado de los requerimientos del cliente", required = true, value = "Descripción de la actividad")
  @NotNull


  public String getDescActividad() {
    return descActividad;
  }

  public void setDescActividad(String descActividad) {
    this.descActividad = descActividad;
  }

  public PycEditAct hrsBase(String hrsBase) {
    this.hrsBase = hrsBase;
    return this;
  }

  /**
   * Horas base estimadas para la actividad
   * @return hrsBase
  **/
  @ApiModelProperty(example = "45", required = true, value = "Horas base estimadas para la actividad")
  @NotNull


  public String getHrsBase() {
    return hrsBase;
  }

  public void setHrsBase(String hrsBase) {
    this.hrsBase = hrsBase;
  }

  public PycEditAct categoria(String categoria) {
    this.categoria = categoria;
    return this;
  }

  /**
   * Identificador de la categoría del tablero
   * @return categoria
  **/
  @ApiModelProperty(example = "2", required = true, value = "Identificador de la categoría del tablero")
  @NotNull


  public String getCategoria() {
    return categoria;
  }

  public void setCategoria(String categoria) {
    this.categoria = categoria;
  }

  public PycEditAct fecIni(String fecIni) {
    this.fecIni = fecIni;
    return this;
  }

  /**
   * Fecha de inicio de la actividad (formato DD/MM/YYYY)
   * @return fecIni
  **/
  @ApiModelProperty(example = "16/01/2024", required = true, value = "Fecha de inicio de la actividad (formato DD/MM/YYYY)")
  @NotNull


  public String getFecIni() {
    return fecIni;
  }

  public void setFecIni(String fecIni) {
    this.fecIni = fecIni;
  }

  public PycEditAct fecFin(String fecFin) {
    this.fecFin = fecFin;
    return this;
  }

  /**
   * Fecha de fin de la actividad (formato DD/MM/YYYY)
   * @return fecFin
  **/
  @ApiModelProperty(example = "22/01/2024", required = true, value = "Fecha de fin de la actividad (formato DD/MM/YYYY)")
  @NotNull


  public String getFecFin() {
    return fecFin;
  }

  public void setFecFin(String fecFin) {
    this.fecFin = fecFin;
  }

  public PycEditAct asigna(String asigna) {
    this.asigna = asigna;
    return this;
  }

  /**
   * Usuario asignado a la actividad (opcional)
   * @return asigna
  **/
  @ApiModelProperty(example = "mgarcia", value = "Usuario asignado a la actividad (opcional)")


  public String getAsigna() {
    return asigna;
  }

  public void setAsigna(String asigna) {
    this.asigna = asigna;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycEditAct pycEditAct = (PycEditAct) o;
    return Objects.equals(this.idPeticion, pycEditAct.idPeticion) &&
        Objects.equals(this.idActividad, pycEditAct.idActividad) &&
        Objects.equals(this.nomActividad, pycEditAct.nomActividad) &&
        Objects.equals(this.descActividad, pycEditAct.descActividad) &&
        Objects.equals(this.hrsBase, pycEditAct.hrsBase) &&
        Objects.equals(this.categoria, pycEditAct.categoria) &&
        Objects.equals(this.fecIni, pycEditAct.fecIni) &&
        Objects.equals(this.fecFin, pycEditAct.fecFin) &&
        Objects.equals(this.asigna, pycEditAct.asigna);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, idActividad, nomActividad, descActividad, hrsBase, categoria, fecIni, fecFin, asigna);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycEditAct {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    idActividad: ").append(toIndentedString(idActividad)).append("\n");
    sb.append("    nomActividad: ").append(toIndentedString(nomActividad)).append("\n");
    sb.append("    descActividad: ").append(toIndentedString(descActividad)).append("\n");
    sb.append("    hrsBase: ").append(toIndentedString(hrsBase)).append("\n");
    sb.append("    categoria: ").append(toIndentedString(categoria)).append("\n");
    sb.append("    fecIni: ").append(toIndentedString(fecIni)).append("\n");
    sb.append("    fecFin: ").append(toIndentedString(fecFin)).append("\n");
    sb.append("    asigna: ").append(toIndentedString(asigna)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

