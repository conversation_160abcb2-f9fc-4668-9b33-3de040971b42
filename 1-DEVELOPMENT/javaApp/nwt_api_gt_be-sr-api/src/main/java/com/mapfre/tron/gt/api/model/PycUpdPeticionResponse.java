package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUpdPeticionResponse
 */
@Validated

public class PycUpdPeticionResponse   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  @JsonProperty("ejecutado")
  private Boolean ejecutado = null;

  public PycUpdPeticionResponse idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * ID de la petición actualizada
   * @return idPeticion
  **/
  @ApiModelProperty(example = "123", value = "ID de la petición actualizada")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycUpdPeticionResponse mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje de confirmación de la operación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Petición actualizada exitosamente", value = "Mensaje de confirmación de la operación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }

  public PycUpdPeticionResponse ejecutado(Boolean ejecutado) {
    this.ejecutado = ejecutado;
    return this;
  }

  /**
   * Indica si la operación se ejecutó correctamente
   * @return ejecutado
  **/
  @ApiModelProperty(example = "true", value = "Indica si la operación se ejecutó correctamente")


  public Boolean isEjecutado() {
    return ejecutado;
  }

  public void setEjecutado(Boolean ejecutado) {
    this.ejecutado = ejecutado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUpdPeticionResponse pycUpdPeticionResponse = (PycUpdPeticionResponse) o;
    return Objects.equals(this.idPeticion, pycUpdPeticionResponse.idPeticion) &&
        Objects.equals(this.mensaje, pycUpdPeticionResponse.mensaje) &&
        Objects.equals(this.ejecutado, pycUpdPeticionResponse.ejecutado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, mensaje, ejecutado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUpdPeticionResponse {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("    ejecutado: ").append(toIndentedString(ejecutado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

