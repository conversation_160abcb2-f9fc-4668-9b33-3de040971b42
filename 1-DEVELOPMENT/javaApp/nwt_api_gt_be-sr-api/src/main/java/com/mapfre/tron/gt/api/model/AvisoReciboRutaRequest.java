package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * AvisoReciboRutaRequest
 */
@Validated

public class AvisoReciboRutaRequest   {
  @JsonProperty("ruta")
  private String ruta = null;

  @JsonProperty("recibo")
  private String recibo = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("firma")
  private String firma = null;

  @JsonProperty("comentario")
  private String comentario = null;

  @JsonProperty("fecha")
  private String fecha = null;

  @JsonProperty("latitud")
  private String latitud = null;

  @JsonProperty("longitud")
  private String longitud = null;

  public AvisoReciboRutaRequest ruta(String ruta) {
    this.ruta = ruta;
    return this;
  }

  /**
   * Número de ruta
   * @return ruta
  **/
  @ApiModelProperty(example = "001", required = true, value = "Número de ruta")
  @NotNull


  public String getRuta() {
    return ruta;
  }

  public void setRuta(String ruta) {
    this.ruta = ruta;
  }

  public AvisoReciboRutaRequest recibo(String recibo) {
    this.recibo = recibo;
    return this;
  }

  /**
   * Número de recibo
   * @return recibo
  **/
  @ApiModelProperty(example = "REC123456", required = true, value = "Número de recibo")
  @NotNull


  public String getRecibo() {
    return recibo;
  }

  public void setRecibo(String recibo) {
    this.recibo = recibo;
  }

  public AvisoReciboRutaRequest usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Usuario que realiza la actualización
   * @return usuario
  **/
  @ApiModelProperty(example = "user123", required = true, value = "Usuario que realiza la actualización")
  @NotNull


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public AvisoReciboRutaRequest firma(String firma) {
    this.firma = firma;
    return this;
  }

  /**
   * Firma digital en formato Base64 (BLOB)
   * @return firma
  **/
  @ApiModelProperty(example = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAA", value = "Firma digital en formato Base64 (BLOB)")


  public String getFirma() {
    return firma;
  }

  public void setFirma(String firma) {
    this.firma = firma;
  }

  public AvisoReciboRutaRequest comentario(String comentario) {
    this.comentario = comentario;
    return this;
  }

  /**
   * Comentarios adicionales
   * @return comentario
  **/
  @ApiModelProperty(example = "Recibo entregado correctamente", value = "Comentarios adicionales")


  public String getComentario() {
    return comentario;
  }

  public void setComentario(String comentario) {
    this.comentario = comentario;
  }

  public AvisoReciboRutaRequest fecha(String fecha) {
    this.fecha = fecha;
    return this;
  }

  /**
   * Fecha de la actualización (formato yyyy-MM-dd HH:mm:ss)
   * @return fecha
  **/
  @ApiModelProperty(example = "2024-12-15 14:30:00", required = true, value = "Fecha de la actualización (formato yyyy-MM-dd HH:mm:ss)")
  @NotNull


  public String getFecha() {
    return fecha;
  }

  public void setFecha(String fecha) {
    this.fecha = fecha;
  }

  public AvisoReciboRutaRequest latitud(String latitud) {
    this.latitud = latitud;
    return this;
  }

  /**
   * Coordenada de latitud
   * @return latitud
  **/
  @ApiModelProperty(example = "14.6349", required = true, value = "Coordenada de latitud")
  @NotNull


  public String getLatitud() {
    return latitud;
  }

  public void setLatitud(String latitud) {
    this.latitud = latitud;
  }

  public AvisoReciboRutaRequest longitud(String longitud) {
    this.longitud = longitud;
    return this;
  }

  /**
   * Coordenada de longitud
   * @return longitud
  **/
  @ApiModelProperty(example = "-90.5069", required = true, value = "Coordenada de longitud")
  @NotNull


  public String getLongitud() {
    return longitud;
  }

  public void setLongitud(String longitud) {
    this.longitud = longitud;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AvisoReciboRutaRequest avisoReciboRutaRequest = (AvisoReciboRutaRequest) o;
    return Objects.equals(this.ruta, avisoReciboRutaRequest.ruta) &&
        Objects.equals(this.recibo, avisoReciboRutaRequest.recibo) &&
        Objects.equals(this.usuario, avisoReciboRutaRequest.usuario) &&
        Objects.equals(this.firma, avisoReciboRutaRequest.firma) &&
        Objects.equals(this.comentario, avisoReciboRutaRequest.comentario) &&
        Objects.equals(this.fecha, avisoReciboRutaRequest.fecha) &&
        Objects.equals(this.latitud, avisoReciboRutaRequest.latitud) &&
        Objects.equals(this.longitud, avisoReciboRutaRequest.longitud);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ruta, recibo, usuario, firma, comentario, fecha, latitud, longitud);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AvisoReciboRutaRequest {\n");
    
    sb.append("    ruta: ").append(toIndentedString(ruta)).append("\n");
    sb.append("    recibo: ").append(toIndentedString(recibo)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    firma: ").append(toIndentedString(firma)).append("\n");
    sb.append("    comentario: ").append(toIndentedString(comentario)).append("\n");
    sb.append("    fecha: ").append(toIndentedString(fecha)).append("\n");
    sb.append("    latitud: ").append(toIndentedString(latitud)).append("\n");
    sb.append("    longitud: ").append(toIndentedString(longitud)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

