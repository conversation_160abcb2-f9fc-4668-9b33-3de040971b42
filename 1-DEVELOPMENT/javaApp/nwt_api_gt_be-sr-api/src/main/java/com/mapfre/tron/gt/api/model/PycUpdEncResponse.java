package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUpdEncResponse
 */
@Validated

public class PycUpdEncResponse   {
  @JsonProperty("idPeticion")
  private String idPeticion = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  @JsonProperty("ejecutado")
  private Boolean ejecutado = null;

  public PycUpdEncResponse idPeticion(String idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición actualizada
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador de la petición actualizada")


  public String getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(String idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycUpdEncResponse mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje de confirmación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Encuesta de satisfacción actualizada exitosamente", value = "Mensaje de confirmación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }

  public PycUpdEncResponse ejecutado(Boolean ejecutado) {
    this.ejecutado = ejecutado;
    return this;
  }

  /**
   * Indicador de ejecución exitosa
   * @return ejecutado
  **/
  @ApiModelProperty(example = "true", value = "Indicador de ejecución exitosa")


  public Boolean isEjecutado() {
    return ejecutado;
  }

  public void setEjecutado(Boolean ejecutado) {
    this.ejecutado = ejecutado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUpdEncResponse pycUpdEncResponse = (PycUpdEncResponse) o;
    return Objects.equals(this.idPeticion, pycUpdEncResponse.idPeticion) &&
        Objects.equals(this.mensaje, pycUpdEncResponse.mensaje) &&
        Objects.equals(this.ejecutado, pycUpdEncResponse.ejecutado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, mensaje, ejecutado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUpdEncResponse {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("    ejecutado: ").append(toIndentedString(ejecutado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

