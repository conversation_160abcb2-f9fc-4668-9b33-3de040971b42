/**
 * NOTE: This class is auto generated by the swagger code generator program (2.4.14).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.mapfre.tron.gt.api.sr;

import com.mapfre.tron.gt.api.model.Error;
import java.time.LocalDate;
import com.mapfre.tron.gt.api.model.PycActDocumento;
import com.mapfre.tron.gt.api.model.PycActPeticion;
import com.mapfre.tron.gt.api.model.PycAnalista;
import com.mapfre.tron.gt.api.model.PycAnalistaActi;
import com.mapfre.tron.gt.api.model.PycAplicacion;
import com.mapfre.tron.gt.api.model.PycAreaPeti;
import com.mapfre.tron.gt.api.model.PycAreaUsuario;
import com.mapfre.tron.gt.api.model.PycAsignaPetDesResponse;
import com.mapfre.tron.gt.api.model.PycAsignaPeticionDesa;
import com.mapfre.tron.gt.api.model.PycBitacoraAccion;
import com.mapfre.tron.gt.api.model.PycBitacoraResponse;
import com.mapfre.tron.gt.api.model.PycCanal;
import com.mapfre.tron.gt.api.model.PycCatAct;
import com.mapfre.tron.gt.api.model.PycCategoriaPet;
import com.mapfre.tron.gt.api.model.PycConEstado;
import com.mapfre.tron.gt.api.model.PycControlProc;
import com.mapfre.tron.gt.api.model.PycCreaSoliRH;
import com.mapfre.tron.gt.api.model.PycCreaSoliRHResponse;
import com.mapfre.tron.gt.api.model.PycCreaSolicitud;
import com.mapfre.tron.gt.api.model.PycDatVarForSeccTippe;
import com.mapfre.tron.gt.api.model.PycDatVarPetResponse;
import com.mapfre.tron.gt.api.model.PycDatoVarEquiv;
import com.mapfre.tron.gt.api.model.PycDepartamento;
import com.mapfre.tron.gt.api.model.PycDocNoObli;
import com.mapfre.tron.gt.api.model.PycDocPeticion;
import com.mapfre.tron.gt.api.model.PycDocXPeticion;
import com.mapfre.tron.gt.api.model.PycEditAct;
import com.mapfre.tron.gt.api.model.PycEditActResponse;
import com.mapfre.tron.gt.api.model.PycEstadistEstadoSig;
import com.mapfre.tron.gt.api.model.PycEstadisticaEstado;
import com.mapfre.tron.gt.api.model.PycEstadoPerfil;
import com.mapfre.tron.gt.api.model.PycEstadoPeticionResponse;
import com.mapfre.tron.gt.api.model.PycEstadoTransc;
import com.mapfre.tron.gt.api.model.PycEstadoUsu;
import com.mapfre.tron.gt.api.model.PycGestUsrAsigAuto;
import com.mapfre.tron.gt.api.model.PycGestUsrAsigAutoResponse;
import com.mapfre.tron.gt.api.model.PycGetBitacoraPet;
import com.mapfre.tron.gt.api.model.PycGetDatVarFormSec;
import com.mapfre.tron.gt.api.model.PycGetEstadoPerfilCod;
import com.mapfre.tron.gt.api.model.PycGetPerfiles;
import com.mapfre.tron.gt.api.model.PycGetQueryDatVar;
import com.mapfre.tron.gt.api.model.PycGetUsuariosAsig;
import com.mapfre.tron.gt.api.model.PycInfoProceso;
import com.mapfre.tron.gt.api.model.PycInfoUsuario;
import com.mapfre.tron.gt.api.model.PycInserAct;
import com.mapfre.tron.gt.api.model.PycInserActResponse;
import com.mapfre.tron.gt.api.model.PycInsertObs;
import com.mapfre.tron.gt.api.model.PycInsertObservacion;
import com.mapfre.tron.gt.api.model.PycInsertPeticion;
import com.mapfre.tron.gt.api.model.PycInsertUserPro;
import com.mapfre.tron.gt.api.model.PycInsertUsuario;
import com.mapfre.tron.gt.api.model.PycListVal;
import com.mapfre.tron.gt.api.model.PycListadoAsigna;
import com.mapfre.tron.gt.api.model.PycNewDatVarPet;
import com.mapfre.tron.gt.api.model.PycObserPet;
import com.mapfre.tron.gt.api.model.PycOficina;
import com.mapfre.tron.gt.api.model.PycOpcionUsu;
import com.mapfre.tron.gt.api.model.PycPathPerfilResponse;
import com.mapfre.tron.gt.api.model.PycPerfilUsu;
import com.mapfre.tron.gt.api.model.PycPetAvance;
import com.mapfre.tron.gt.api.model.PycPetAvanceSig;
import com.mapfre.tron.gt.api.model.PycPetSinProgramador;
import com.mapfre.tron.gt.api.model.PycPetiFiltro;
import com.mapfre.tron.gt.api.model.PycPeticionPerfil;
import com.mapfre.tron.gt.api.model.PycPeticionResponse;
import com.mapfre.tron.gt.api.model.PycPeticionTab;
import com.mapfre.tron.gt.api.model.PycPrioridad;
import com.mapfre.tron.gt.api.model.PycProcesoPorTipo;
import com.mapfre.tron.gt.api.model.PycRamo;
import com.mapfre.tron.gt.api.model.PycReportePetProg;
import com.mapfre.tron.gt.api.model.PycReportePeti;
import com.mapfre.tron.gt.api.model.PycRespPeti;
import com.mapfre.tron.gt.api.model.PycResumenActi;
import com.mapfre.tron.gt.api.model.PycSesionResponse;
import com.mapfre.tron.gt.api.model.PycSolicitudResponse;
import com.mapfre.tron.gt.api.model.PycSrcMultipleOption;
import com.mapfre.tron.gt.api.model.PycSubordinados;
import com.mapfre.tron.gt.api.model.PycTabAnios;
import com.mapfre.tron.gt.api.model.PycTabArea;
import com.mapfre.tron.gt.api.model.PycTabEstado;
import com.mapfre.tron.gt.api.model.PycTipoPeticion;
import com.mapfre.tron.gt.api.model.PycUpdAct;
import com.mapfre.tron.gt.api.model.PycUpdActResponse;
import com.mapfre.tron.gt.api.model.PycUpdEnc;
import com.mapfre.tron.gt.api.model.PycUpdEncResponse;
import com.mapfre.tron.gt.api.model.PycUpdEstadoPet;
import com.mapfre.tron.gt.api.model.PycUpdPathPerfil;
import com.mapfre.tron.gt.api.model.PycUpdPerfil;
import com.mapfre.tron.gt.api.model.PycUpdPeticion;
import com.mapfre.tron.gt.api.model.PycUpdPeticionResponse;
import com.mapfre.tron.gt.api.model.PycUpdSesion;
import com.mapfre.tron.gt.api.model.PycUpdUsuario;
import com.mapfre.tron.gt.api.model.PycUsuPerfilPeti;
import com.mapfre.tron.gt.api.model.PycUsuPerfilProceso;
import com.mapfre.tron.gt.api.model.PycUsuProcesos;
import com.mapfre.tron.gt.api.model.PycUsuario;
import com.mapfre.tron.gt.api.model.PycUsuarioPerfil;
import com.mapfre.tron.gt.api.model.PycUsuarioResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Api(value = "pycges", description = "the pycges API")
@RequestMapping(value = "/newtron/api")
public interface PycgesApi {

    Logger log = LoggerFactory.getLogger(PycgesApi.class);

    default Optional<ObjectMapper> getObjectMapper() {
        return Optional.empty();
    }

    default Optional<HttpServletRequest> getRequest() {
        return Optional.empty();
    }

    default Optional<String> getAcceptHeader() {
        return getRequest().map(r -> r.getHeader("Accept"));
    }

    @ApiOperation(value = "Actualizar documento de petición", nickname = "actDocumento", notes = "Actualiza la información de un documento asociado a una petición en el sistema PYCGES", response = PycPeticionResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycPeticionResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/act_documento",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycPeticionResponse> actDocumento(@ApiParam(value = "Datos del documento a actualizar" ,required=true )  @Valid @RequestBody PycActDocumento documentoData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"idPeticion\" : 0}", PycPeticionResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Actualizar una petición existente (POST)", nickname = "actualizarPeticion", notes = "Actualiza los datos de una petición existente usando método POST. Funcionalidad idéntica a update_peticion pero siguiendo el patrón POST del proyecto. ", response = PycUpdPeticionResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Petición actualizada exitosamente", response = PycUpdPeticionResponse.class),
        @ApiResponse(code = 400, message = "Bad request - Datos inválidos", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Petición no encontrada", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/actualizar_peticion",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycUpdPeticionResponse> actualizarPeticion(@ApiParam(value = "Datos de la petición a actualizar" ,required=true )  @Valid @RequestBody PycUpdPeticion peticionData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"ejecutado\" : true,  \"mensaje\" : \"Petición actualizada exitosamente\",  \"idPeticion\" : 123}", PycUpdPeticionResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Asignar petición a desarrollador", nickname = "asignaPeticionDesa", notes = "Asigna una petición específica a un desarrollador/analista, actualizando las tablas de asignación y responsabilidad según la configuración del proceso", response = PycAsignaPetDesResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycAsignaPetDesResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/asigna_peticion_desa",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycAsignaPetDesResponse> asignaPeticionDesa(@ApiParam(value = "Datos de la asignación de petición a desarrollador" ,required=true )  @Valid @RequestBody PycAsignaPeticionDesa asignacionData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"ejecutado\" : true,  \"mensaje\" : \"Petición asignada exitosamente al desarrollador\",  \"idPeticion\" : 1001}", PycAsignaPetDesResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Registrar acción en bitácora", nickname = "bitacoraAccion", notes = "Registra una acción específica en la bitácora del sistema PYCGES con información de auditoría automática", response = PycBitacoraResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycBitacoraResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/bitacora_accion",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycBitacoraResponse> bitacoraAccion(@ApiParam(value = "Datos de la acción a registrar en la bitácora" ,required=true )  @Valid @RequestBody PycBitacoraAccion bitacoraData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"ejecutado\" : true,  \"mensaje\" : \"Acción registrada exitosamente en la bitácora\"}", PycBitacoraResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Crear solicitud", nickname = "creaSolicitud", notes = "Crea una nueva solicitud en el sistema PYCGES", response = PycSolicitudResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycSolicitudResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/crm_comercial/crea_solicitud",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycSolicitudResponse> creaSolicitud(@ApiParam(value = "Datos de la solicitud a crear" ,required=true )  @Valid @RequestBody PycCreaSolicitud solicitudData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"idSolicitud\" : 0}", PycSolicitudResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Crear solicitud de Recursos Humanos", nickname = "creaSolicitudRH", notes = "Crea una nueva solicitud de Recursos Humanos en el sistema PYCGES. ", response = PycCreaSoliRHResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Solicitud de RH creada exitosamente", response = PycCreaSoliRHResponse.class),
        @ApiResponse(code = 400, message = "Parámetros de entrada inválidos", response = Error.class),
        @ApiResponse(code = 401, message = "No autorizado", response = Error.class),
        @ApiResponse(code = 403, message = "Acceso prohibido", response = Error.class),
        @ApiResponse(code = 404, message = "Recurso no encontrado", response = Error.class),
        @ApiResponse(code = 500, message = "Error interno del servidor", response = Error.class) })
    @RequestMapping(value = "/pycges/rrhh/crea_solicitud",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycCreaSoliRHResponse> creaSolicitudRH(@ApiParam(value = "Datos de la solicitud de RH a crear" ,required=true )  @Valid @RequestBody PycCreaSoliRH solicitudData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"ejecutado\" : true,  \"idSolicitud\" : 456,  \"mensaje\" : \"Solicitud de Recursos Humanos creada exitosamente\"}", PycCreaSoliRHResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Editar actividad existente de una petición", nickname = "editActividad", notes = "Actualiza los datos de una actividad existente asociada a una petición específica. Automáticamente recalcula y actualiza las fechas de inicio y fin de la petición basándose en todas las actividades asociadas (MIN fecha_inicio, MAX fecha_fin). Permite modificar nombre, descripción, horas base, categoría, fechas y asignación. ", response = PycEditActResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Actividad actualizada exitosamente", response = PycEditActResponse.class),
        @ApiResponse(code = 400, message = "Bad request - Datos inválidos", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Actividad o petición no encontrada", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/edit_actividad",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycEditActResponse> editActividad(@ApiParam(value = "Datos de la actividad a actualizar" ,required=true )  @Valid @RequestBody PycEditAct actividadData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"ejecutado\" : true,  \"mensaje\" : \"Actividad actualizada exitosamente\",  \"idPeticion\" : 1001}", PycEditActResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Gestionar asignación automática de usuario", nickname = "gestionUsrAsignacionAuto", notes = "Gestiona la asignación automática de usuarios para procesos, perfiles, aplicaciones y estados específicos en el sistema PYCGES", response = PycGestUsrAsigAutoResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycGestUsrAsigAutoResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/gestion_usr_asignacion_auto",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycGestUsrAsigAutoResponse> gestionUsrAsignacionAuto(@ApiParam(value = "Datos de la asignación automática de usuario a gestionar" ,required=true )  @Valid @RequestBody PycGestUsrAsigAuto asignacionData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"ejecutado\" : true,  \"mensaje\" : \"Asignación automática de usuario gestionada exitosamente\"}", PycGestUsrAsigAutoResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener actividades de petición", nickname = "getActPeticion", notes = "Obtiene la lista de actividades asociadas a una petición específica con información detallada de horas, fechas, usuarios asignados y categorías", response = PycActPeticion.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycActPeticion.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_act_peticion",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycActPeticion>> getActPeticion(@NotNull @ApiParam(value = "ID de la petición", required = true) @Valid @RequestParam(value = "idPeticion", required = true) Integer idPeticion,@ApiParam(value = "ID de la actividad (opcional)") @Valid @RequestParam(value = "idActividad", required = false) Integer idActividad) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"estado\" : \"EN_PROGRESO\",  \"idActividad\" : 1,  \"nombreActividad\" : \"Análisis de requerimientos\",  \"idCategoriaTablero\" : 2,  \"idUsuario\" : 456,  \"horasReal\" : 35.5,  \"fechaFin\" : \"25/01/2025\",  \"nombre\" : \"Desarrollo\",  \"perfilAsignado\" : \"/images/profiles/user_male.png\",  \"asignadoA\" : \"Juan Carlos Pérez\",  \"fechaInicio\" : \"15/01/2025\",  \"descripcionActividad\" : \"Análisis detallado de los requerimientos del cliente\",  \"idPeticion\" : 123,  \"horasPendientes\" : 4.5,  \"horasBase\" : 40.0}, {  \"estado\" : \"EN_PROGRESO\",  \"idActividad\" : 1,  \"nombreActividad\" : \"Análisis de requerimientos\",  \"idCategoriaTablero\" : 2,  \"idUsuario\" : 456,  \"horasReal\" : 35.5,  \"fechaFin\" : \"25/01/2025\",  \"nombre\" : \"Desarrollo\",  \"perfilAsignado\" : \"/images/profiles/user_male.png\",  \"asignadoA\" : \"Juan Carlos Pérez\",  \"fechaInicio\" : \"15/01/2025\",  \"descripcionActividad\" : \"Análisis detallado de los requerimientos del cliente\",  \"idPeticion\" : 123,  \"horasPendientes\" : 4.5,  \"horasBase\" : 40.0} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener analistas por actividad", nickname = "getAnalistaActividad", notes = "Obtiene la lista de analistas disponibles para una actividad específica de una petición en el sistema PYCGES", response = PycAnalistaActi.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycAnalistaActi.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/analista_actividad",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycAnalistaActi>> getAnalistaActividad(@NotNull @ApiParam(value = "ID de la petición", required = true) @Valid @RequestParam(value = "peticion", required = true) Integer peticion,@NotNull @ApiParam(value = "ID de la actividad", required = true) @Valid @RequestParam(value = "actividad", required = true) Integer actividad) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idUsuario\" : 123,  \"estadoUsuario\" : \"ACT\",  \"asigna\" : \"1\",  \"nombre\" : \"Juan Pérez\",  \"urlPerfil\" : \"/images/profiles/user123.jpg\"}, {  \"idUsuario\" : 123,  \"estadoUsuario\" : \"ACT\",  \"asigna\" : \"1\",  \"nombre\" : \"Juan Pérez\",  \"urlPerfil\" : \"/images/profiles/user123.jpg\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener aplicaciones por proceso", nickname = "getAplicaciones", notes = "Obtiene la lista de aplicaciones asociadas a un proceso", response = PycAplicacion.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycAplicacion.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_aplicaciones",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycAplicacion>> getAplicaciones(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idAplicacion\" : 1,  \"nombreAplicacion\" : \"Aplicación de Gestión\"}, {  \"idAplicacion\" : 1,  \"nombreAplicacion\" : \"Aplicación de Gestión\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener bitácora de petición", nickname = "getBitacoraPeticion", notes = "Obtiene la bitácora completa de una petición con información detallada de cambios de estado, responsables, observaciones y tiempos", response = PycGetBitacoraPet.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycGetBitacoraPet.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_bitacora_peticion",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycGetBitacoraPet>> getBitacoraPeticion(@NotNull @ApiParam(value = "ID de la petición", required = true) @Valid @RequestParam(value = "idPeticion", required = true) Integer idPeticion) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"icono\" : \"fa-clock-o\",  \"estadoFinal\" : \"En Proceso\",  \"contenido\" : \"DE Pendiente A En Proceso - PETICIÓN ASIGNADA PARA ANÁLISIS - Fecha: 15/01/2024 14:45 - Tiempo: 5h 15m 30s\",  \"responsables\" : \"Juan Pérez, María García\",  \"perfilUsuario\" : \"/images/profile/user_male.png\",  \"codPerfilInicial\" : 1,  \"minuto\" : 15,  \"codEstadoInicial\" : 1,  \"hora\" : 5,  \"titulo\" : \"CAMBIO DE ESTADO\",  \"fecIni\" : \"15/01/2024 09:30\",  \"codEstadoFinal\" : 2,  \"estadoInicial\" : \"Pendiente\",  \"fecFin\" : \"15/01/2024 14:45\",  \"perfilInicial\" : \"Solicitante\",  \"segundo\" : 30,  \"codPerfilFinal\" : 2,  \"perfilFinal\" : \"Analista\",  \"tiempo\" : \"5 Horas y 15 min.\",  \"usuario\" : \"Carlos Martínez\",  \"mes\" : 0,  \"idPeticion\" : 1001,  \"dia\" : 0,  \"anio\" : 0}, {  \"icono\" : \"fa-clock-o\",  \"estadoFinal\" : \"En Proceso\",  \"contenido\" : \"DE Pendiente A En Proceso - PETICIÓN ASIGNADA PARA ANÁLISIS - Fecha: 15/01/2024 14:45 - Tiempo: 5h 15m 30s\",  \"responsables\" : \"Juan Pérez, María García\",  \"perfilUsuario\" : \"/images/profile/user_male.png\",  \"codPerfilInicial\" : 1,  \"minuto\" : 15,  \"codEstadoInicial\" : 1,  \"hora\" : 5,  \"titulo\" : \"CAMBIO DE ESTADO\",  \"fecIni\" : \"15/01/2024 09:30\",  \"codEstadoFinal\" : 2,  \"estadoInicial\" : \"Pendiente\",  \"fecFin\" : \"15/01/2024 14:45\",  \"perfilInicial\" : \"Solicitante\",  \"segundo\" : 30,  \"codPerfilFinal\" : 2,  \"perfilFinal\" : \"Analista\",  \"tiempo\" : \"5 Horas y 15 min.\",  \"usuario\" : \"Carlos Martínez\",  \"mes\" : 0,  \"idPeticion\" : 1001,  \"dia\" : 0,  \"anio\" : 0} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener estados de transición disponibles", nickname = "getCambioEstado", notes = "Obtiene la lista de estados de transición disponibles para un perfil, proceso y estado específicos", response = PycEstadoTransc.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycEstadoTransc.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_cambio_estado",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycEstadoTransc>> getCambioEstado(@NotNull @ApiParam(value = "ID del perfil", required = true) @Valid @RequestParam(value = "idPerfil", required = true) Integer idPerfil,@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso,@NotNull @ApiParam(value = "ID del estado inicial", required = true) @Valid @RequestParam(value = "idEstado", required = true) Integer idEstado) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idEstadoInicial\" : 1,  \"idEstado\" : 2,  \"nombreEstado\" : \"En Proceso\"}, {  \"idEstadoInicial\" : 1,  \"idEstado\" : 2,  \"nombreEstado\" : \"En Proceso\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener canales por proceso, usuario y oficina", nickname = "getCanales", notes = "Obtiene la lista de canales disponibles para un usuario específico en un proceso y oficina determinados, considerando los indicadores de canal y oficina del usuario", response = PycCanal.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycCanal.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/crm_comercial/get_canales",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycCanal>> getCanales(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso,@NotNull @ApiParam(value = "ID del usuario", required = true) @Valid @RequestParam(value = "idUsuario", required = true) Integer idUsuario,@NotNull @ApiParam(value = "ID de la oficina", required = true) @Valid @RequestParam(value = "idOficina", required = true) Integer idOficina) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"codigo\" : 1,  \"canal\" : \"Canal Web\"}, {  \"codigo\" : 1,  \"canal\" : \"Canal Web\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener categorías activas por proceso", nickname = "getCategoriaAct", notes = "Obtiene la lista de categorías activas del tablero asociadas a un proceso específico", response = PycCatAct.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycCatAct.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "No autorizado", response = Error.class),
        @ApiResponse(code = 403, message = "Acceso prohibido", response = Error.class),
        @ApiResponse(code = 404, message = "Recurso no encontrado", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_categoria_act",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycCatAct>> getCategoriaAct(@NotNull @ApiParam(value = "Identificador del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idCategoria\" : 1,  \"nombre\" : \"DESARROLLO\"}, {  \"idCategoria\" : 1,  \"nombre\" : \"DESARROLLO\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener categorías por petición", nickname = "getCategoriaPeticion", notes = "Obtiene la lista de categorías de actividades asociadas a una petición específica con estadísticas de avance", response = PycCategoriaPet.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycCategoriaPet.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_categoria_peticion",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycCategoriaPet>> getCategoriaPeticion(@NotNull @ApiParam(value = "ID de la petición", required = true) @Valid @RequestParam(value = "idPeticion", required = true) Integer idPeticion) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idCategoriaTablero\" : 5,  \"horasReal\" : 135.75,  \"nombre\" : \"Desarrollo\",  \"fechaFin\" : \"2024-02-20\",  \"total\" : 15,  \"fechaRealPendiente\" : \"2024-02-20\",  \"fechaInicio\" : \"2024-01-15\",  \"fechaRealTerminado\" : \"2024-02-18\",  \"terminadas\" : 8,  \"pendientes\" : 7,  \"porcentaje\" : 85.25,  \"idPeticion\" : 1001,  \"horasBase\" : 120.5}, {  \"idCategoriaTablero\" : 5,  \"horasReal\" : 135.75,  \"nombre\" : \"Desarrollo\",  \"fechaFin\" : \"2024-02-20\",  \"total\" : 15,  \"fechaRealPendiente\" : \"2024-02-20\",  \"fechaInicio\" : \"2024-01-15\",  \"fechaRealTerminado\" : \"2024-02-18\",  \"terminadas\" : 8,  \"pendientes\" : 7,  \"porcentaje\" : 85.25,  \"idPeticion\" : 1001,  \"horasBase\" : 120.5} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener analistas por solicitante y prioridad", nickname = "getConAnalista", notes = "Obtiene la lista de analistas disponibles filtrados por solicitante y prioridad de petición", response = PycAnalista.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycAnalista.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_con_analista",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycAnalista>> getConAnalista(@NotNull @ApiParam(value = "ID del usuario solicitante", required = true) @Valid @RequestParam(value = "idSolicitante", required = true) Integer idSolicitante,@ApiParam(value = "Prioridad de la petición (opcional)") @Valid @RequestParam(value = "prioridad", required = false) String prioridad) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idUsuario\" : 1234,  \"usuario\" : \"María García\"}, {  \"idUsuario\" : 1234,  \"usuario\" : \"María García\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener áreas con peticiones", nickname = "getConArea", notes = "Obtiene la lista de áreas que tienen usuarios con peticiones asociadas", response = PycAreaPeti.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycAreaPeti.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_con_area",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycAreaPeti>> getConArea() {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"nombreArea\" : \"Área de Desarrollo\",  \"idArea\" : 1}, {  \"nombreArea\" : \"Área de Desarrollo\",  \"idArea\" : 1} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener departamentos por área", nickname = "getConDepartamento", notes = "Obtiene la lista de departamentos que tienen usuarios con peticiones, filtrados por área", response = PycDepartamento.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycDepartamento.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_con_departamento",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycDepartamento>> getConDepartamento(@ApiParam(value = "ID del área (opcional)") @Valid @RequestParam(value = "idArea", required = false) Integer idArea) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idDepartamento\" : 1,  \"nombreDepartamento\" : \"Desarrollo de Software\"}, {  \"idDepartamento\" : 1,  \"nombreDepartamento\" : \"Desarrollo de Software\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener estados por solicitante, prioridad y analista", nickname = "getConEstado", notes = "Obtiene la lista de estados de peticiones filtrados por solicitante, prioridad y analista", response = PycConEstado.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycConEstado.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_con_estado",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycConEstado>> getConEstado(@NotNull @ApiParam(value = "ID del usuario solicitante", required = true) @Valid @RequestParam(value = "idSolicitante", required = true) Integer idSolicitante,@NotNull @ApiParam(value = "Prioridad de la petición (usar 'null' para peticiones sin prioridad)", required = true) @Valid @RequestParam(value = "prioridad", required = true) String prioridad,@ApiParam(value = "ID del analista asignado (opcional)") @Valid @RequestParam(value = "idAnalista", required = false) Integer idAnalista) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idEstado\" : 1,  \"nombreEstado\" : \"Pendiente\"}, {  \"idEstado\" : 1,  \"nombreEstado\" : \"Pendiente\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener prioridades por solicitante, área y departamento", nickname = "getConPrioridad", notes = "Obtiene la lista de prioridades de peticiones filtradas por solicitante, área y departamento", response = PycPrioridad.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycPrioridad.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_con_prioridad",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycPrioridad>> getConPrioridad(@ApiParam(value = "ID del usuario solicitante (opcional)") @Valid @RequestParam(value = "idSolicitante", required = false) Integer idSolicitante,@ApiParam(value = "ID del área (opcional)") @Valid @RequestParam(value = "idArea", required = false) Integer idArea,@ApiParam(value = "ID del departamento (opcional)") @Valid @RequestParam(value = "idDepartamento", required = false) Integer idDepartamento) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"codigo\" : \"1\",  \"prioridad\" : \"Alta\"}, {  \"codigo\" : \"1\",  \"prioridad\" : \"Alta\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener usuarios solicitantes por área y departamento", nickname = "getConSolicitante", notes = "Obtiene la lista de usuarios que han realizado peticiones, filtrados por área y/o departamento", response = PycAreaUsuario.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycAreaUsuario.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_con_solicitante",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycAreaUsuario>> getConSolicitante(@ApiParam(value = "ID del área (opcional)") @Valid @RequestParam(value = "idArea", required = false) Integer idArea,@ApiParam(value = "ID del departamento (opcional)") @Valid @RequestParam(value = "idDepartamento", required = false) Integer idDepartamento) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idUsuario\" : 1234,  \"usuario\" : \"Juan Pérez\"}, {  \"idUsuario\" : 1234,  \"usuario\" : \"Juan Pérez\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener controles de proceso", nickname = "getControlesProceso", notes = "Obtiene la lista de controles asociados a un proceso específico y tipo de control", response = PycControlProc.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycControlProc.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_controles_proceso",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycControlProc>> getControlesProceso(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso,@NotNull @ApiParam(value = "Código del tipo de control", required = true) @Valid @RequestParam(value = "tipoControl", required = true) String tipoControl) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"icono\" : \"fa-user\",  \"descripcion\" : \"Campo para ingresar el nombre del cliente\",  \"tipo\" : \"FORMULARIO\",  \"dataJs\" : \"{'validation': 'required'}\",  \"visible\" : \"S\",  \"defaultVal\" : \"Valor inicial\",  \"idControl\" : 101,  \"idHtml\" : \"input_nombre\",  \"lval\" : \"OPCION1,OPCION2,OPCION3\",  \"orden\" : 1,  \"obligatorio\" : \"S\",  \"nombre\" : \"Nombre del Cliente\"}, {  \"icono\" : \"fa-user\",  \"descripcion\" : \"Campo para ingresar el nombre del cliente\",  \"tipo\" : \"FORMULARIO\",  \"dataJs\" : \"{'validation': 'required'}\",  \"visible\" : \"S\",  \"defaultVal\" : \"Valor inicial\",  \"idControl\" : 101,  \"idHtml\" : \"input_nombre\",  \"lval\" : \"OPCION1,OPCION2,OPCION3\",  \"orden\" : 1,  \"obligatorio\" : \"S\",  \"nombre\" : \"Nombre del Cliente\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener datos variables de formulario por sección", nickname = "getDatVarFormSecc", notes = "Obtiene la lista de datos variables de un formulario específico filtrados por proceso, formulario y sección", response = PycGetDatVarFormSec.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycGetDatVarFormSec.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_dat_var_form_secc",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycGetDatVarFormSec>> getDatVarFormSecc(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso,@NotNull @ApiParam(value = "ID del formulario", required = true) @Valid @RequestParam(value = "idFormulario", required = true) Integer idFormulario,@NotNull @ApiParam(value = "ID de la sección", required = true) @Valid @RequestParam(value = "idSeccion", required = true) Integer idSeccion,@ApiParam(value = "ID de la petición (opcional)") @Valid @RequestParam(value = "idPeticion", required = false) Integer idPeticion) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"descripcion\" : \"Nombre completo del solicitante\",  \"icono\" : \"fa-user\",  \"tipo\" : \"VARCHAR2\",  \"ordenSeccion\" : 1,  \"valMax\" : \"100\",  \"indObligatorio\" : \"S\",  \"nombreFormulario\" : \"Formulario de Solicitud\",  \"idFormulario\" : 1,  \"tipoHtml\" : \"text\",  \"nombreSeccion\" : \"Datos Personales\",  \"javascript\" : \"onchange='validarCampo()'\",  \"iconoSeccion\" : \"fa-user\",  \"valDefault\" : \"Sin especificar\",  \"valMin\" : \"1\",  \"indVisible\" : \"S\",  \"indEditable\" : \"S\",  \"classForm\" : \"col-md-6\",  \"orden\" : 1,  \"placeholder\" : \"Ingrese su nombre completo\",  \"idSeccion\" : 2,  \"idDependencia\" : 50,  \"idDato\" : 101,  \"class\" : \"form-control\"}, {  \"descripcion\" : \"Nombre completo del solicitante\",  \"icono\" : \"fa-user\",  \"tipo\" : \"VARCHAR2\",  \"ordenSeccion\" : 1,  \"valMax\" : \"100\",  \"indObligatorio\" : \"S\",  \"nombreFormulario\" : \"Formulario de Solicitud\",  \"idFormulario\" : 1,  \"tipoHtml\" : \"text\",  \"nombreSeccion\" : \"Datos Personales\",  \"javascript\" : \"onchange='validarCampo()'\",  \"iconoSeccion\" : \"fa-user\",  \"valDefault\" : \"Sin especificar\",  \"valMin\" : \"1\",  \"indVisible\" : \"S\",  \"indEditable\" : \"S\",  \"classForm\" : \"col-md-6\",  \"orden\" : 1,  \"placeholder\" : \"Ingrese su nombre completo\",  \"idSeccion\" : 2,  \"idDependencia\" : 50,  \"idDato\" : 101,  \"class\" : \"form-control\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener datos variables de formulario por proceso y tipo", nickname = "getDatVarFormSeccTippe", notes = "Obtiene la lista de datos variables de formulario basándose en el proceso y tipo de petición, resolviendo automáticamente el formulario y sección correspondientes", response = PycDatVarForSeccTippe.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycDatVarForSeccTippe.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_dat_var_form_secc_tippe",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycDatVarForSeccTippe>> getDatVarFormSeccTippe(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso,@NotNull @ApiParam(value = "ID del tipo de petición", required = true) @Valid @RequestParam(value = "idTipo", required = true) Integer idTipo) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"ordenSeccion\" : 1,  \"mensajeError\" : \"Solo se permiten letras y espacios\",  \"longitudMaxima\" : 100,  \"nombreFormulario\" : \"FORMULARIO SOLICITUD COTIZACION\",  \"tipoControl\" : \"INPUT\",  \"nombreSeccion\" : \"DATOS PERSONALES\",  \"valorPeticion\" : \"Juan Pérez\",  \"patronValidacion\" : \"^[A-Za-z\\s]+$\",  \"valorDependencia\" : \"valorDependencia\",  \"indVisible\" : \"S\",  \"longitudMinima\" : 1,  \"propiedadesControl\" : \"placeholder='Ingrese su nombre completo'\",  \"orden\" : 1,  \"placeholder\" : \"Ingrese su nombre completo\",  \"idProceso\" : 1,  \"idSeccion\" : 1,  \"descripcionDatoVariable\" : \"Nombre completo del solicitante\",  \"tipoDato\" : \"TEXT\",  \"idDatoVariable\" : 10,  \"icono\" : \"fa fa-user\",  \"editable\" : \"S\",  \"idFormulario\" : 1,  \"obligatorio\" : \"S\",  \"nombreDatoVariable\" : \"NOMBRE_SOLICITANTE\",  \"javascript\" : \"onchange='validarCampo()'\",  \"classField\" : \"form-control\",  \"iconoSeccion\" : \"fa fa-user\",  \"dependeDe\" : 0,  \"classForm\" : \"form-group-sm col-md-6\",  \"valorDefecto\" : \"\"}, {  \"ordenSeccion\" : 1,  \"mensajeError\" : \"Solo se permiten letras y espacios\",  \"longitudMaxima\" : 100,  \"nombreFormulario\" : \"FORMULARIO SOLICITUD COTIZACION\",  \"tipoControl\" : \"INPUT\",  \"nombreSeccion\" : \"DATOS PERSONALES\",  \"valorPeticion\" : \"Juan Pérez\",  \"patronValidacion\" : \"^[A-Za-z\\s]+$\",  \"valorDependencia\" : \"valorDependencia\",  \"indVisible\" : \"S\",  \"longitudMinima\" : 1,  \"propiedadesControl\" : \"placeholder='Ingrese su nombre completo'\",  \"orden\" : 1,  \"placeholder\" : \"Ingrese su nombre completo\",  \"idProceso\" : 1,  \"idSeccion\" : 1,  \"descripcionDatoVariable\" : \"Nombre completo del solicitante\",  \"tipoDato\" : \"TEXT\",  \"idDatoVariable\" : 10,  \"icono\" : \"fa fa-user\",  \"editable\" : \"S\",  \"idFormulario\" : 1,  \"obligatorio\" : \"S\",  \"nombreDatoVariable\" : \"NOMBRE_SOLICITANTE\",  \"javascript\" : \"onchange='validarCampo()'\",  \"classField\" : \"form-control\",  \"iconoSeccion\" : \"fa fa-user\",  \"dependeDe\" : 0,  \"classForm\" : \"form-group-sm col-md-6\",  \"valorDefecto\" : \"\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener documentos no obligatorios por petición", nickname = "getDocNoObligatorios", notes = "Obtiene la lista de documentos no obligatorios que faltan por adjuntar a una petición específica", response = PycDocNoObli.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycDocNoObli.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "No autorizado", response = Error.class),
        @ApiResponse(code = 403, message = "Acceso prohibido", response = Error.class),
        @ApiResponse(code = 404, message = "Recurso no encontrado", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_doc_no_obligatorios",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycDocNoObli>> getDocNoObligatorios(@NotNull @ApiParam(value = "Identificador de la petición", required = true) @Valid @RequestParam(value = "idPeticion", required = true) Integer idPeticion) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"descripcionDocumento\" : \"Carta de autorización firmada por el solicitante para proceder con la gestión\",  \"idDocumento\" : 12,  \"nombreDocumento\" : \"Carta de autorización\",  \"idTipoPeticion\" : 5}, {  \"descripcionDocumento\" : \"Carta de autorización firmada por el solicitante para proceder con la gestión\",  \"idDocumento\" : 12,  \"nombreDocumento\" : \"Carta de autorización\",  \"idTipoPeticion\" : 5} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener documentos requeridos por tipo de petición", nickname = "getDocXPeticion", notes = "Obtiene la lista de documentos requeridos para un tipo de petición específico, ordenados por obligatoriedad y orden", response = PycDocXPeticion.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycDocXPeticion.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_doc_x_peticion",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycDocXPeticion>> getDocXPeticion(@NotNull @ApiParam(value = "ID del tipo de petición", required = true) @Valid @RequestParam(value = "idTipo", required = true) Integer idTipo) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"descripcionDocumento\" : \"Documento de identificación personal\",  \"estado\" : \"ACT\",  \"extension\" : \"PDF,JPG,PNG\",  \"idDocumento\" : 5,  \"nombreDocumento\" : \"Cédula de Identidad\",  \"fechaHora\" : \"2024-01-15 10:30:00\",  \"usuario\" : \"admin\",  \"tamanoMaximo\" : 5120,  \"obligatorio\" : \"S\",  \"multiArchivos\" : \"S\"}, {  \"descripcionDocumento\" : \"Documento de identificación personal\",  \"estado\" : \"ACT\",  \"extension\" : \"PDF,JPG,PNG\",  \"idDocumento\" : 5,  \"nombreDocumento\" : \"Cédula de Identidad\",  \"fechaHora\" : \"2024-01-15 10:30:00\",  \"usuario\" : \"admin\",  \"tamanoMaximo\" : 5120,  \"obligatorio\" : \"S\",  \"multiArchivos\" : \"S\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener documentos de petición", nickname = "getDocumentoPeticion", notes = "Obtiene la lista de documentos asociados a una petición específica, incluyendo documentos existentes y disponibles para agregar", response = PycDocPeticion.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycDocPeticion.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_documento_peticion",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycDocPeticion>> getDocumentoPeticion(@NotNull @ApiParam(value = "ID de la petición", required = true) @Valid @RequestParam(value = "idPeticion", required = true) Integer idPeticion,@ApiParam(value = "ID del perfil actual (opcional)") @Valid @RequestParam(value = "perfilAct", required = false) Integer perfilAct) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"fecha\" : \"2025-05-28 01:37:45\",  \"descripcionDocumento\" : \"Documento de identificación personal\",  \"idDocumento\" : 5,  \"nombreDocumento\" : \"Cédula de Identidad-2\",  \"idUsuario\" : 25,  \"ftpWeb\" : \"WEB\",  \"localizacion\" : \"/documents/cedula_123456.pdf\",  \"usuario\" : \"Juan Pérez\",  \"usuarioBaseDatos\" : \"user_admin\",  \"idPeticion\" : 1001,  \"version\" : \"1.0\",  \"fechaRecepcion\" : \"15/01/2024 14:30\"}, {  \"fecha\" : \"2025-05-28 01:37:45\",  \"descripcionDocumento\" : \"Documento de identificación personal\",  \"idDocumento\" : 5,  \"nombreDocumento\" : \"Cédula de Identidad-2\",  \"idUsuario\" : 25,  \"ftpWeb\" : \"WEB\",  \"localizacion\" : \"/documents/cedula_123456.pdf\",  \"usuario\" : \"Juan Pérez\",  \"usuarioBaseDatos\" : \"user_admin\",  \"idPeticion\" : 1001,  \"version\" : \"1.0\",  \"fechaRecepcion\" : \"15/01/2024 14:30\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener estadísticas de estados de peticiones", nickname = "getEstadisticaEstados", notes = "Obtiene estadísticas agrupadas por estado de peticiones con conteos, totales y porcentajes, filtradas por proceso, área, estado, año y compañía", response = PycEstadisticaEstado.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycEstadisticaEstado.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_estadistica_estados",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycEstadisticaEstado>> getEstadisticaEstados(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "proceso", required = true) Integer proceso,@NotNull @ApiParam(value = "ID del área", required = true) @Valid @RequestParam(value = "area", required = true) Integer area,@NotNull @ApiParam(value = "Año de filtro", required = true) @Valid @RequestParam(value = "anio", required = true) Integer anio,@ApiParam(value = "Lista de IDs de estados separados por coma (ej. \"1,2,3\"). Si no se especifica, se incluyen todos los estados") @Valid @RequestParam(value = "estado", required = false) String estado,@ApiParam(value = "Código de compañía (opcional)") @Valid @RequestParam(value = "codCia", required = false) String codCia) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"colorEstado\" : \"#FFA500\",  \"idEstado\" : 2,  \"nombreEstado\" : \"En Proceso\",  \"porcentajeEstado\" : 15.0,  \"cantidadEstado\" : 15,  \"totalEstado\" : 100}, {  \"colorEstado\" : \"#FFA500\",  \"idEstado\" : 2,  \"nombreEstado\" : \"En Proceso\",  \"porcentajeEstado\" : 15.0,  \"cantidadEstado\" : 15,  \"totalEstado\" : 100} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener estadísticas de estados de peticiones siguientes", nickname = "getEstadisticaEstadosSiguientes", notes = "Obtiene estadísticas agrupadas por estado de peticiones siguientes con conteos, totales y porcentajes, filtradas por proceso, área, estado, año y compañía", response = PycEstadistEstadoSig.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycEstadistEstadoSig.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_estadistica_estados_siguientes",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycEstadistEstadoSig>> getEstadisticaEstadosSiguientes(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "proceso", required = true) Integer proceso,@NotNull @ApiParam(value = "ID del área", required = true) @Valid @RequestParam(value = "area", required = true) Integer area,@NotNull @ApiParam(value = "Año de filtro", required = true) @Valid @RequestParam(value = "anio", required = true) Integer anio,@ApiParam(value = "Lista de IDs de estados separados por coma (ej. \"1,2,3\"). Si no se especifica, se incluyen todos los estados") @Valid @RequestParam(value = "estado", required = false) String estado,@ApiParam(value = "Código de compañía (opcional)") @Valid @RequestParam(value = "codCia", required = false) String codCia) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"colorEstado\" : \"#FFA500\",  \"idEstado\" : 2,  \"nombreEstado\" : \"En Proceso\",  \"porcentajeEstado\" : 15.0,  \"cantidadEstado\" : 15,  \"totalEstado\" : 100}, {  \"colorEstado\" : \"#FFA500\",  \"idEstado\" : 2,  \"nombreEstado\" : \"En Proceso\",  \"porcentajeEstado\" : 15.0,  \"cantidadEstado\" : 15,  \"totalEstado\" : 100} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener estados por perfil y proceso", nickname = "getEstadoPerfil", notes = "Obtiene la lista de estados disponibles para un perfil específico en un proceso determinado", response = PycEstadoPerfil.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycEstadoPerfil.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_estado_perfil",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycEstadoPerfil>> getEstadoPerfil(@NotNull @ApiParam(value = "ID del perfil", required = true) @Valid @RequestParam(value = "idPerfil", required = true) Integer idPerfil,@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idEstado\" : 2,  \"nombreEstado\" : \"En Proceso\",  \"codigoEstado\" : \"EP\"}, {  \"idEstado\" : 2,  \"nombreEstado\" : \"En Proceso\",  \"codigoEstado\" : \"EP\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener estados por proceso y perfil", nickname = "getEstadoPerfilCod", notes = "Obtiene la lista de estados asociados a un perfil específico dentro de un proceso, excluyendo perfiles configurados en la lista de valores ND-PERFIL-EXCLUYE", response = PycGetEstadoPerfilCod.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycGetEstadoPerfilCod.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_estado_perfil_cod",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycGetEstadoPerfilCod>> getEstadoPerfilCod(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso,@NotNull @ApiParam(value = "ID del perfil", required = true) @Valid @RequestParam(value = "idPerfil", required = true) Integer idPerfil) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idEstado\" : 2,  \"idPerfil\" : 5,  \"nombreEstado\" : \"En Proceso\",  \"nombrePerfil\" : \"Analista Senior\"}, {  \"idEstado\" : 2,  \"idPerfil\" : 5,  \"nombreEstado\" : \"En Proceso\",  \"nombrePerfil\" : \"Analista Senior\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener estado de usuario por credenciales", nickname = "getEstadoUsuario", notes = "Obtiene el estado de un usuario específico mediante sus credenciales de acceso", response = PycEstadoUsu.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycEstadoUsu.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_estado_usuario",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycEstadoUsu>> getEstadoUsuario(@NotNull @ApiParam(value = "Nombre de usuario", required = true) @Valid @RequestParam(value = "usuario", required = true) String usuario,@NotNull @ApiParam(value = "Clave del usuario", required = true) @Valid @RequestParam(value = "clave", required = true) String clave) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"numaOUsuario\" : \"jperez\",  \"estado\" : \"ACT\"}, {  \"numaOUsuario\" : \"jperez\",  \"estado\" : \"ACT\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener información de proceso", nickname = "getInfoProceso", notes = "Obtiene la información completa de un proceso específico", response = PycInfoProceso.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycInfoProceso.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_info_proceso",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycInfoProceso>> getInfoProceso(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"emailCc\" : \"<EMAIL>,<EMAIL>\",  \"estado\" : \"ACT\",  \"nombreProceso\" : \"Gestión de Peticiones\",  \"columFin\" : 12,  \"fechaHora\" : \"2024-01-15T10:30:00Z\",  \"mcaCommentOblig\" : \"S\",  \"datatableHtml\" : \"<div>HTML content</div>\",  \"mcaUpdSol\" : \"N\",  \"mcaCambioSol\" : \"S\",  \"mcaInhSecInv\" : \"N\",  \"sqlSelect\" : \"SELECT campo1, campo2\",  \"mcaSecuIdReferencia\" : \"S\",  \"mcaAsigAutom\" : \"N\",  \"utilizaFiltros\" : \"S\",  \"dirFtpWeb\" : \"/ftp/procesos/\",  \"idProceso\" : 7,  \"icono\" : \"fa-tasks\",  \"mcaIgnorInter\" : \"N\",  \"mcaRefId\" : \"S\",  \"datatableJs\" : \"$(document).ready(function(){});\",  \"datatableDef\" : \"{'columns': []}\",  \"mcaInhSecAct\" : \"N\",  \"mcaInhSecObs\" : \"N\",  \"columInicio\" : 1,  \"sqlWhere\" : \"AND t.estado = 'ACT'\",  \"usuario\" : \"admin\",  \"extraHtml\" : \"<div>Extra content</div>\",  \"mcaAsigEstCom\" : \"S\",  \"sqlTable\" : \"tabla_adicional t\"}, {  \"emailCc\" : \"<EMAIL>,<EMAIL>\",  \"estado\" : \"ACT\",  \"nombreProceso\" : \"Gestión de Peticiones\",  \"columFin\" : 12,  \"fechaHora\" : \"2024-01-15T10:30:00Z\",  \"mcaCommentOblig\" : \"S\",  \"datatableHtml\" : \"<div>HTML content</div>\",  \"mcaUpdSol\" : \"N\",  \"mcaCambioSol\" : \"S\",  \"mcaInhSecInv\" : \"N\",  \"sqlSelect\" : \"SELECT campo1, campo2\",  \"mcaSecuIdReferencia\" : \"S\",  \"mcaAsigAutom\" : \"N\",  \"utilizaFiltros\" : \"S\",  \"dirFtpWeb\" : \"/ftp/procesos/\",  \"idProceso\" : 7,  \"icono\" : \"fa-tasks\",  \"mcaIgnorInter\" : \"N\",  \"mcaRefId\" : \"S\",  \"datatableJs\" : \"$(document).ready(function(){});\",  \"datatableDef\" : \"{'columns': []}\",  \"mcaInhSecAct\" : \"N\",  \"mcaInhSecObs\" : \"N\",  \"columInicio\" : 1,  \"sqlWhere\" : \"AND t.estado = 'ACT'\",  \"usuario\" : \"admin\",  \"extraHtml\" : \"<div>Extra content</div>\",  \"mcaAsigEstCom\" : \"S\",  \"sqlTable\" : \"tabla_adicional t\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener información completa del usuario", nickname = "getInfoUsuario", notes = "Obtiene la información completa de un usuario específico", response = PycInfoUsuario.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycInfoUsuario.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_info_usuario",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycInfoUsuario>> getInfoUsuario(@NotNull @ApiParam(value = "Nombre de usuario", required = true) @Valid @RequestParam(value = "usuario", required = true) String usuario) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"area\" : \"Tecnología\",  \"fechaBaja\" : \"2025-12-31T00:00:00Z\",  \"segundoNombre\" : \"Carlos\",  \"estado\" : \"ACT\",  \"numaOUsuario\" : \"jperez\",  \"primerNombre\" : \"Juan\",  \"primerApellido\" : \"Pérez\",  \"idArea\" : 5,  \"idUsuario\" : 1234,  \"segundoApellido\" : \"González\",  \"urlInicio\" : \"https://ejemplo.com/dashboard\",  \"indDefault\" : \"S\",  \"nombrePerfil\" : \"Desarrollador\",  \"urlPerfil\" : \"https://ejemplo.com/perfil/jperez\",  \"idDepartamento\" : 10,  \"idPerfil\" : 3,  \"genero\" : \"M\",  \"fechaCreacion\" : \"2025-01-15T10:30:00Z\",  \"departamento\" : \"Desarrollo de Software\",  \"pathPerfil\" : \"/perfiles/jperez\",  \"idProceso\" : 7,  \"email\" : \"<EMAIL>\"}, {  \"area\" : \"Tecnología\",  \"fechaBaja\" : \"2025-12-31T00:00:00Z\",  \"segundoNombre\" : \"Carlos\",  \"estado\" : \"ACT\",  \"numaOUsuario\" : \"jperez\",  \"primerNombre\" : \"Juan\",  \"primerApellido\" : \"Pérez\",  \"idArea\" : 5,  \"idUsuario\" : 1234,  \"segundoApellido\" : \"González\",  \"urlInicio\" : \"https://ejemplo.com/dashboard\",  \"indDefault\" : \"S\",  \"nombrePerfil\" : \"Desarrollador\",  \"urlPerfil\" : \"https://ejemplo.com/perfil/jperez\",  \"idDepartamento\" : 10,  \"idPerfil\" : 3,  \"genero\" : \"M\",  \"fechaCreacion\" : \"2025-01-15T10:30:00Z\",  \"departamento\" : \"Desarrollo de Software\",  \"pathPerfil\" : \"/perfiles/jperez\",  \"idProceso\" : 7,  \"email\" : \"<EMAIL>\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener lista de valores por tipo y proceso", nickname = "getListaVal", notes = "Obtiene la lista de valores activos filtrados por tipo y proceso específico desde la tabla PYC_LIST_VAL", response = PycListVal.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycListVal.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_lista_val",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycListVal>> getListaVal(@NotNull @ApiParam(value = "Tipo de valor a consultar", required = true) @Valid @RequestParam(value = "tipo", required = true) String tipo,@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"descripcion\" : \"Opción número uno\",  \"valor\" : \"001\"}, {  \"descripcion\" : \"Opción número uno\",  \"valor\" : \"001\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener listado de asignaciones por perfil", nickname = "getListadoAsigna", notes = "Obtiene la lista de usuarios asignados a un perfil específico", response = PycListadoAsigna.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycListadoAsigna.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_listado_asigna",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycListadoAsigna>> getListadoAsigna(@NotNull @ApiParam(value = "ID del perfil", required = true) @Valid @RequestParam(value = "idPerfil", required = true) Integer idPerfil) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idPerfil\" : 1,  \"idUsuario\" : 1234,  \"nombrePerfil\" : \"Administrador\",  \"nombre\" : \"Juan Pérez\"}, {  \"idPerfil\" : 1,  \"idUsuario\" : 1234,  \"nombrePerfil\" : \"Administrador\",  \"nombre\" : \"Juan Pérez\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener observaciones de petición", nickname = "getObserPeticion", notes = "Obtiene la lista de observaciones asociadas a una petición específica con información del usuario y estado de conexión", response = PycObserPet.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycObserPet.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_obser_peticion",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycObserPet>> getObserPeticion(@NotNull @ApiParam(value = "ID de la petición", required = true) @Valid @RequestParam(value = "idPeticion", required = true) Integer idPeticion,@ApiParam(value = "Nombre o número de usuario") @Valid @RequestParam(value = "numaOUsuario", required = false) String numaOUsuario) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"nombreArea\" : \"Tecnología\",  \"estado\" : \"ACT\",  \"nombreDepartamento\" : \"Desarrollo de Software\",  \"nombrePeticion\" : \"Solicitud de cambio de datos\",  \"fechaHora\" : \"Hoy 14:30\",  \"urlPerfil\" : \"/images/profile/user_male.png\",  \"nombre\" : \"Juan Pérez\",  \"idObservacion\" : 123,  \"indicador\" : \"left\",  \"connected\" : \"on\",  \"genero\" : \"M\",  \"usuario\" : \"jperez\",  \"idPeticion\" : 1001,  \"publico\" : \"S\",  \"observacion\" : \"SE REQUIERE DOCUMENTACIÓN ADICIONAL\"}, {  \"nombreArea\" : \"Tecnología\",  \"estado\" : \"ACT\",  \"nombreDepartamento\" : \"Desarrollo de Software\",  \"nombrePeticion\" : \"Solicitud de cambio de datos\",  \"fechaHora\" : \"Hoy 14:30\",  \"urlPerfil\" : \"/images/profile/user_male.png\",  \"nombre\" : \"Juan Pérez\",  \"idObservacion\" : 123,  \"indicador\" : \"left\",  \"connected\" : \"on\",  \"genero\" : \"M\",  \"usuario\" : \"jperez\",  \"idPeticion\" : 1001,  \"publico\" : \"S\",  \"observacion\" : \"SE REQUIERE DOCUMENTACIÓN ADICIONAL\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener oficinas por proceso y usuario", nickname = "getOficinas", notes = "Obtiene la lista de oficinas disponibles para un usuario específico en un proceso determinado, considerando el indicador de oficina del usuario", response = PycOficina.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycOficina.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/crm_comercial/get_oficinas",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycOficina>> getOficinas(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso,@NotNull @ApiParam(value = "ID del usuario", required = true) @Valid @RequestParam(value = "idUsuario", required = true) Integer idUsuario) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idOficina\" : 1,  \"nombre\" : \"Oficina Central\"}, {  \"idOficina\" : 1,  \"nombre\" : \"Oficina Central\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener opciones de menú por perfil y proceso", nickname = "getOpcionUsuario", notes = "Obtiene la lista de opciones de menú disponibles para un perfil específico en un proceso determinado", response = PycOpcionUsu.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycOpcionUsu.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_opcion_usuario",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycOpcionUsu>> getOpcionUsuario(@NotNull @ApiParam(value = "ID del perfil", required = true) @Valid @RequestParam(value = "idPerfil", required = true) Integer idPerfil,@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso,@NotNull @ApiParam(value = "Nombre o número de usuario", required = true) @Valid @RequestParam(value = "numaOUsuario", required = true) String numaOUsuario) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"codigoMenu\" : 101,  \"estado\" : \"ACT\",  \"funciones\" : \"CREATE,READ,UPDATE,DELETE\",  \"ruta\" : \"/peticiones/lista\",  \"level\" : 1,  \"titulo\" : \"Gestión de Peticiones\",  \"logo\" : \"fa-tasks\",  \"orden\" : 10,  \"codigoMenuPadre\" : 0}, {  \"codigoMenu\" : 101,  \"estado\" : \"ACT\",  \"funciones\" : \"CREATE,READ,UPDATE,DELETE\",  \"ruta\" : \"/peticiones/lista\",  \"level\" : 1,  \"titulo\" : \"Gestión de Peticiones\",  \"logo\" : \"fa-tasks\",  \"orden\" : 10,  \"codigoMenuPadre\" : 0} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener perfiles por proceso", nickname = "getPerfilesPorProceso", notes = "Obtiene la lista de perfiles asociados a un proceso específico, excluyendo perfiles configurados en la lista de valores ND-PERFIL-EXCLUYE", response = PycGetPerfiles.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycGetPerfiles.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_perfiles",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycGetPerfiles>> getPerfilesPorProceso(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"nombreProceso\" : \"Proceso de Solicitudes\",  \"idPerfil\" : 5,  \"nombrePerfil\" : \"Analista Senior\"}, {  \"nombreProceso\" : \"Proceso de Solicitudes\",  \"idPerfil\" : 5,  \"nombrePerfil\" : \"Analista Senior\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener avance de peticiones", nickname = "getPeticionAvance", notes = "Obtiene el avance detallado de peticiones con información de observaciones, usuarios y estados, filtrado por proceso, área, estado, compañía y año", response = PycPetAvance.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycPetAvance.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_peticion_avance",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycPetAvance>> getPeticionAvance(@ApiParam(value = "ID del proceso (opcional)") @Valid @RequestParam(value = "idProceso", required = false) Integer idProceso,@ApiParam(value = "ID del área (opcional)") @Valid @RequestParam(value = "idArea", required = false) Integer idArea,@ApiParam(value = "IDs de estados separados por coma (opcional, ej. \"1,2,3\")") @Valid @RequestParam(value = "estado", required = false) String estado,@ApiParam(value = "Código de compañía (opcional)") @Valid @RequestParam(value = "codcia", required = false) String codcia,@ApiParam(value = "Año de filtro (opcional)") @Valid @RequestParam(value = "anio", required = false) Integer anio) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"nombrePeticion\" : \"Desarrollo de nueva funcionalidad\",  \"idPeticionSiguiente\" : 1002,  \"solicitante\" : \"María García\",  \"generoUsuario\" : \"M\",  \"indicadorUsuario\" : \"left\",  \"nombreUsuario\" : \"Juan Pérez\",  \"fechaFin\" : \"20/02/2024\",  \"idObservacion\" : 123,  \"urlPerfil\" : \"/images/profile/user_male.png\",  \"prioridad\" : \"Alta\",  \"fechaHoraObservacion\" : \"15/01/2024 14:30\",  \"fechaInicio\" : \"15/01/2024\",  \"nombreEstado\" : \"En Progreso\",  \"tipoPeticion\" : \"Desarrollo\",  \"nombrePeticionSiguiente\" : \"Pruebas de funcionalidad\",  \"fechaInicioSiguiente\" : \"21/02/2024\",  \"idPeticion\" : 1001,  \"fechaFinSiguiente\" : \"28/02/2024\",  \"observacion\" : \"Avance del 75% completado\"}, {  \"nombrePeticion\" : \"Desarrollo de nueva funcionalidad\",  \"idPeticionSiguiente\" : 1002,  \"solicitante\" : \"María García\",  \"generoUsuario\" : \"M\",  \"indicadorUsuario\" : \"left\",  \"nombreUsuario\" : \"Juan Pérez\",  \"fechaFin\" : \"20/02/2024\",  \"idObservacion\" : 123,  \"urlPerfil\" : \"/images/profile/user_male.png\",  \"prioridad\" : \"Alta\",  \"fechaHoraObservacion\" : \"15/01/2024 14:30\",  \"fechaInicio\" : \"15/01/2024\",  \"nombreEstado\" : \"En Progreso\",  \"tipoPeticion\" : \"Desarrollo\",  \"nombrePeticionSiguiente\" : \"Pruebas de funcionalidad\",  \"fechaInicioSiguiente\" : \"21/02/2024\",  \"idPeticion\" : 1001,  \"fechaFinSiguiente\" : \"28/02/2024\",  \"observacion\" : \"Avance del 75% completado\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener peticiones con avance siguiente", nickname = "getPeticionAvanceSiguiente", notes = "Obtiene la lista de peticiones con información de su petición siguiente asociada, filtradas por proceso, área, estado, compañía y año", response = PycPetAvanceSig.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycPetAvanceSig.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_peticion_avance_siguiente",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycPetAvanceSig>> getPeticionAvanceSiguiente(@ApiParam(value = "ID del proceso (opcional)") @Valid @RequestParam(value = "idProceso", required = false) Integer idProceso,@ApiParam(value = "ID del área (opcional)") @Valid @RequestParam(value = "idArea", required = false) Integer idArea,@ApiParam(value = "Lista de estados separados por comas (opcional)") @Valid @RequestParam(value = "estado", required = false) String estado,@ApiParam(value = "Código de compañía (opcional)") @Valid @RequestParam(value = "codcia", required = false) String codcia,@ApiParam(value = "Año de filtro (opcional)") @Valid @RequestParam(value = "anio", required = false) Integer anio) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"nombrePeticion\" : \"Solicitud de cambio de datos\",  \"fechaInicio\" : \"2025-01-15\",  \"nombreEstado\" : \"En Proceso\",  \"idPeticionSiguiente\" : 1002,  \"nombrePeticionSiguiente\" : \"Validación de cambio de datos\",  \"fechaInicioSiguiente\" : \"2025-01-21\",  \"nombreEstadoSiguiente\" : \"Pendiente\",  \"idPeticion\" : 1001,  \"fechaFin\" : \"2025-01-20\",  \"fechaFinSiguiente\" : \"2025-01-25\"}, {  \"nombrePeticion\" : \"Solicitud de cambio de datos\",  \"fechaInicio\" : \"2025-01-15\",  \"nombreEstado\" : \"En Proceso\",  \"idPeticionSiguiente\" : 1002,  \"nombrePeticionSiguiente\" : \"Validación de cambio de datos\",  \"fechaInicioSiguiente\" : \"2025-01-21\",  \"nombreEstadoSiguiente\" : \"Pendiente\",  \"idPeticion\" : 1001,  \"fechaFin\" : \"2025-01-20\",  \"fechaFinSiguiente\" : \"2025-01-25\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener peticiones con filtros", nickname = "getPeticionFiltros", notes = "Obtiene la lista de peticiones filtradas por múltiples criterios como área, departamento, perfil, estado, usuario, tipo, fechas y proceso", response = PycPetiFiltro.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycPetiFiltro.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_peticion_filtros",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycPetiFiltro>> getPeticionFiltros(@ApiParam(value = "ID del área (opcional)") @Valid @RequestParam(value = "idArea", required = false) Integer idArea,@ApiParam(value = "ID del departamento (opcional)") @Valid @RequestParam(value = "idDepartamento", required = false) Integer idDepartamento,@ApiParam(value = "ID del perfil (opcional)") @Valid @RequestParam(value = "idPerfil", required = false) Integer idPerfil,@ApiParam(value = "ID del estado (opcional)") @Valid @RequestParam(value = "idEstado", required = false) Integer idEstado,@ApiParam(value = "ID del usuario solicitante (opcional)") @Valid @RequestParam(value = "idUsuarioSolicitante", required = false) Integer idUsuarioSolicitante,@ApiParam(value = "ID del tipo de petición (opcional)") @Valid @RequestParam(value = "idTipo", required = false) Integer idTipo,@ApiParam(value = "Fecha de inicio del filtro (formato YYYY-MM-DD) (opcional)") @Valid @RequestParam(value = "fechaInicio", required = false) LocalDate fechaInicio,@ApiParam(value = "Fecha de fin del filtro (formato YYYY-MM-DD) (opcional)") @Valid @RequestParam(value = "fechaFin", required = false) LocalDate fechaFin,@ApiParam(value = "ID del proceso (opcional)") @Valid @RequestParam(value = "idProceso", required = false) Integer idProceso) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idUsuarioSolicitante\" : 1234,  \"nombreArea\" : \"Tecnología\",  \"nombreDepartamento\" : \"Desarrollo de Software\",  \"nombrePeticion\" : \"Desarrollo de nueva funcionalidad\",  \"descripcionTipoPeticion\" : \"Desarrollo\",  \"idPeticionSiguiente\" : 1002,  \"porcentajeBase\" : 85.0,  \"origen\" : \"Cliente Interno\",  \"nombrePerfil\" : \"Analista Senior\",  \"fechaFin\" : \"2024-02-20T18:00:00Z\",  \"prioridad\" : \"1\",  \"idEstado\" : 2,  \"descripcionPeticion\" : \"Implementación de módulo de reportes\",  \"fechaInicio\" : \"2024-01-20T08:00:00Z\",  \"idPerfil\" : 5,  \"nombreEstado\" : \"En Progreso\",  \"totalHoras\" : 120.5,  \"fechaCreacion\" : \"2024-01-15T10:30:00Z\",  \"codcia\" : \"MGT\",  \"idTipo\" : 1,  \"usuario\" : \"Juan Pérez\",  \"idPeticion\" : 1001,  \"porcentajeReal\" : 75.5,  \"codClarity\" : \"MU-2024-001234\"}, {  \"idUsuarioSolicitante\" : 1234,  \"nombreArea\" : \"Tecnología\",  \"nombreDepartamento\" : \"Desarrollo de Software\",  \"nombrePeticion\" : \"Desarrollo de nueva funcionalidad\",  \"descripcionTipoPeticion\" : \"Desarrollo\",  \"idPeticionSiguiente\" : 1002,  \"porcentajeBase\" : 85.0,  \"origen\" : \"Cliente Interno\",  \"nombrePerfil\" : \"Analista Senior\",  \"fechaFin\" : \"2024-02-20T18:00:00Z\",  \"prioridad\" : \"1\",  \"idEstado\" : 2,  \"descripcionPeticion\" : \"Implementación de módulo de reportes\",  \"fechaInicio\" : \"2024-01-20T08:00:00Z\",  \"idPerfil\" : 5,  \"nombreEstado\" : \"En Progreso\",  \"totalHoras\" : 120.5,  \"fechaCreacion\" : \"2024-01-15T10:30:00Z\",  \"codcia\" : \"MGT\",  \"idTipo\" : 1,  \"usuario\" : \"Juan Pérez\",  \"idPeticion\" : 1001,  \"porcentajeReal\" : 75.5,  \"codClarity\" : \"MU-2024-001234\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener peticiones por perfil", nickname = "getPeticionPerfil", notes = "Obtiene la lista de peticiones filtradas por perfil, proceso, estado y otros criterios opcionales", response = PycPeticionPerfil.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycPeticionPerfil.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_peticion_perfil",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycPeticionPerfil>> getPeticionPerfil(@NotNull @ApiParam(value = "ID del perfil", required = true) @Valid @RequestParam(value = "idPerfil", required = true) Integer idPerfil,@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso,@NotNull @ApiParam(value = "Número o usuario que realiza la consulta", required = true) @Valid @RequestParam(value = "numaOUsuario", required = true) String numaOUsuario,@ApiParam(value = "ID del estado (opcional)") @Valid @RequestParam(value = "idEstado", required = false) String idEstado,@ApiParam(value = "ID de la petición (opcional)") @Valid @RequestParam(value = "idPeticion", required = false) Integer idPeticion,@ApiParam(value = "ID del canal (opcional)") @Valid @RequestParam(value = "idCanal", required = false) Integer idCanal,@ApiParam(value = "ID del usuario (opcional)") @Valid @RequestParam(value = "idUsuario", required = false) Integer idUsuario,@ApiParam(value = "Indicador de substitución (opcional)") @Valid @RequestParam(value = "indSubs", required = false) String indSubs,@ApiParam(value = "ID de la oficina (opcional)") @Valid @RequestParam(value = "idOficina", required = false) Integer idOficina) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"nombreArea\" : \"OPERACIONES\",  \"noSolicitud\" : 1,  \"tipoServicio\" : \"TIPO_SERVICIO\",  \"nombreCanal\" : \"Web\",  \"nombreProceso\" : \"Gestión de Peticiones\",  \"idPeticionSiguiente\" : 1002,  \"idArea\" : 10,  \"encuUser\" : \"ENCU_USER\",  \"departamentoDir\" : \"GUATEMALA\",  \"descripcionPeticion\" : \"Solicitud para cambiar datos personales en la póliza\",  \"tipoServicioDescripcion\" : \"TIPO_SERVICIO_DESCRIPCION\",  \"encuFecha\" : \"ENCU_FECHA\",  \"nit\" : \"8944309091\",  \"gravedadDescripcion\" : \"GRAVEDAD_DESCRIPCION\",  \"idProceso\" : 3,  \"porcentajeReal\" : 0.0,  \"encuCalif\" : \"ENCU_CALIF\",  \"usuarioGraba\" : \"CARMAR\",  \"correoCliente\" : \"<EMAIL>\",  \"idUsuarioSolicitante\" : 1234,  \"iconoEstado\" : \"N\",  \"numPoliza\" : \"166295\",  \"causaDescripcion\" : \"CAUSA_DESCRIPCION\",  \"encuUsuario\" : \"ENCU_USUARIO\",  \"numReferencia\" : \"166296\",  \"direccion\" : \"33 avenida 22-30 zona5\",  \"telefonoCliente\" : \"22085997\",  \"prioriDesc\" : \"MEDIA\",  \"ftrDocs\" : \"FTR_DOCS\",  \"nombrePerfil\" : \"SOLICITANTE\",  \"fechaFin\" : \"03/06/2020\",  \"edad\" : 32,  \"campania\" : \"COTIZADOR PUBLICO\",  \"colorEstado\" : \"success\",  \"gravedad\" : \"GRAVEDAD\",  \"escolaridad\" : \"UNIVERSIDAD COMPLETA\",  \"nombreOficina\" : \"Oficina Central\",  \"usuarioSoli\" : \"CARMAR\",  \"observaciones\" : \"SE CONFIRMA CON EL CLIENTE\",  \"nextStep\" : \"warning\",  \"idTipo\" : 5,  \"usuario\" : \"CARLOS ANTONIO MARROQUIN DIAZ\",  \"perfilResponsable\" : \"PERFIL_RESPONSABLE\",  \"tipoClienteDescripcion\" : \"Descripción del tipo de cliente\",  \"areaRrhh\" : \"OPERACIONES\",  \"nombreDepartamento\" : \"SERVICIO AL CLIENTE\",  \"experienciaSeguro\" : \"N\",  \"nombrePeticion\" : \"Solicitud de cambio de datos\",  \"causa\" : \"CAUSA\",  \"fechaNacimiento\" : \"11/02/1993\",  \"nombreUsuarioSolicitante\" : \"Juan Pérez\",  \"departamentoResponsable\" : \"DEPARTAMENTO_RESPONSABLE\",  \"idResponsable\" : \"ID_RESPONSABLE\",  \"origen\" : \"MGT\",  \"prioridad\" : \"1\",  \"idCanal\" : 1,  \"idDepartamento\" : 10,  \"tituloAcademico\" : \"Cierre de persona en psicología\",  \"tituloSiguiente\" : \"hilda vasquez\",  \"nombreCliente\" : \"Juan Carlos Pérez García\",  \"fechaInicio\" : \"03/06/2020\",  \"nombreEstado\" : \"CERRADA\",  \"idPerfil\" : 1,  \"origenDescripcion\" : \"MAPFRE SEGUROS GUATEMALA\",  \"encuComent\" : \"ENCU_COMENT\",  \"perfilSolicitante\" : \"PERFIL_SOLICITANTE\",  \"trabajaActualmente\" : \"N\",  \"encuComentAdi\" : \"ENCU_COMENT_ADI\",  \"urlRedirect\" : \"https://app2.mapfre.com\",  \"idPeticion\" : 1001,  \"idSeccion\" : \"9\",  \"candidato\" : \"Carlos Ricardo Davila Mancilla\",  \"dpi\" : \"2344319670701\",  \"codClarity\" : \"0\",  \"areaResponsable\" : \"AREA_RESPONSABLE\",  \"mcaRefId\" : \"MCA_REF_ID\",  \"noPoliza\" : \"NO_POLIZA\",  \"responsable\" : \"RESPONSABLE\",  \"codciaDescripcion\" : \"MAPFRE SEGUROS GUATEMALA\",  \"idOficina\" : 1,  \"porcentajeBase\" : 16.0,  \"idFormulario\" : \"fa fa-user\",  \"codinter\" : \"CODINTER\",  \"codCliente\" : \"COD_CLIENTE\",  \"nombrePlaza\" : \"SERVICIO AL CLIENTE\",  \"tipoCliente\" : \"TIPO_CLIENTE\",  \"idEstado\" : \"16\",  \"tipoPeticion\" : \"NO RESPONDE CORREO\",  \"totalHoras\" : 0.0,  \"fechaCreacion\" : \"21/05/2025\",  \"codcia\" : \"ALTA\",  \"pretensionSalarial\" : \"4000.00\",  \"idReferencia\" : \"ID_REFERENCIA\"}, {  \"nombreArea\" : \"OPERACIONES\",  \"noSolicitud\" : 1,  \"tipoServicio\" : \"TIPO_SERVICIO\",  \"nombreCanal\" : \"Web\",  \"nombreProceso\" : \"Gestión de Peticiones\",  \"idPeticionSiguiente\" : 1002,  \"idArea\" : 10,  \"encuUser\" : \"ENCU_USER\",  \"departamentoDir\" : \"GUATEMALA\",  \"descripcionPeticion\" : \"Solicitud para cambiar datos personales en la póliza\",  \"tipoServicioDescripcion\" : \"TIPO_SERVICIO_DESCRIPCION\",  \"encuFecha\" : \"ENCU_FECHA\",  \"nit\" : \"8944309091\",  \"gravedadDescripcion\" : \"GRAVEDAD_DESCRIPCION\",  \"idProceso\" : 3,  \"porcentajeReal\" : 0.0,  \"encuCalif\" : \"ENCU_CALIF\",  \"usuarioGraba\" : \"CARMAR\",  \"correoCliente\" : \"<EMAIL>\",  \"idUsuarioSolicitante\" : 1234,  \"iconoEstado\" : \"N\",  \"numPoliza\" : \"166295\",  \"causaDescripcion\" : \"CAUSA_DESCRIPCION\",  \"encuUsuario\" : \"ENCU_USUARIO\",  \"numReferencia\" : \"166296\",  \"direccion\" : \"33 avenida 22-30 zona5\",  \"telefonoCliente\" : \"22085997\",  \"prioriDesc\" : \"MEDIA\",  \"ftrDocs\" : \"FTR_DOCS\",  \"nombrePerfil\" : \"SOLICITANTE\",  \"fechaFin\" : \"03/06/2020\",  \"edad\" : 32,  \"campania\" : \"COTIZADOR PUBLICO\",  \"colorEstado\" : \"success\",  \"gravedad\" : \"GRAVEDAD\",  \"escolaridad\" : \"UNIVERSIDAD COMPLETA\",  \"nombreOficina\" : \"Oficina Central\",  \"usuarioSoli\" : \"CARMAR\",  \"observaciones\" : \"SE CONFIRMA CON EL CLIENTE\",  \"nextStep\" : \"warning\",  \"idTipo\" : 5,  \"usuario\" : \"CARLOS ANTONIO MARROQUIN DIAZ\",  \"perfilResponsable\" : \"PERFIL_RESPONSABLE\",  \"tipoClienteDescripcion\" : \"Descripción del tipo de cliente\",  \"areaRrhh\" : \"OPERACIONES\",  \"nombreDepartamento\" : \"SERVICIO AL CLIENTE\",  \"experienciaSeguro\" : \"N\",  \"nombrePeticion\" : \"Solicitud de cambio de datos\",  \"causa\" : \"CAUSA\",  \"fechaNacimiento\" : \"11/02/1993\",  \"nombreUsuarioSolicitante\" : \"Juan Pérez\",  \"departamentoResponsable\" : \"DEPARTAMENTO_RESPONSABLE\",  \"idResponsable\" : \"ID_RESPONSABLE\",  \"origen\" : \"MGT\",  \"prioridad\" : \"1\",  \"idCanal\" : 1,  \"idDepartamento\" : 10,  \"tituloAcademico\" : \"Cierre de persona en psicología\",  \"tituloSiguiente\" : \"hilda vasquez\",  \"nombreCliente\" : \"Juan Carlos Pérez García\",  \"fechaInicio\" : \"03/06/2020\",  \"nombreEstado\" : \"CERRADA\",  \"idPerfil\" : 1,  \"origenDescripcion\" : \"MAPFRE SEGUROS GUATEMALA\",  \"encuComent\" : \"ENCU_COMENT\",  \"perfilSolicitante\" : \"PERFIL_SOLICITANTE\",  \"trabajaActualmente\" : \"N\",  \"encuComentAdi\" : \"ENCU_COMENT_ADI\",  \"urlRedirect\" : \"https://app2.mapfre.com\",  \"idPeticion\" : 1001,  \"idSeccion\" : \"9\",  \"candidato\" : \"Carlos Ricardo Davila Mancilla\",  \"dpi\" : \"2344319670701\",  \"codClarity\" : \"0\",  \"areaResponsable\" : \"AREA_RESPONSABLE\",  \"mcaRefId\" : \"MCA_REF_ID\",  \"noPoliza\" : \"NO_POLIZA\",  \"responsable\" : \"RESPONSABLE\",  \"codciaDescripcion\" : \"MAPFRE SEGUROS GUATEMALA\",  \"idOficina\" : 1,  \"porcentajeBase\" : 16.0,  \"idFormulario\" : \"fa fa-user\",  \"codinter\" : \"CODINTER\",  \"codCliente\" : \"COD_CLIENTE\",  \"nombrePlaza\" : \"SERVICIO AL CLIENTE\",  \"tipoCliente\" : \"TIPO_CLIENTE\",  \"idEstado\" : \"16\",  \"tipoPeticion\" : \"NO RESPONDE CORREO\",  \"totalHoras\" : 0.0,  \"fechaCreacion\" : \"21/05/2025\",  \"codcia\" : \"ALTA\",  \"pretensionSalarial\" : \"4000.00\",  \"idReferencia\" : \"ID_REFERENCIA\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener peticiones sin programador", nickname = "getPeticionSinProgramador", notes = "Obtiene la lista de peticiones que no tienen programador asignado", response = PycPetSinProgramador.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycPetSinProgramador.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_peticion_sin_programador",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycPetSinProgramador>> getPeticionSinProgramador() {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"nombrePeticion\" : \"Solicitud de cambio de datos\",  \"idPeticionSiguiente\" : 1002,  \"origen\" : \"CO257\",  \"prioridad\" : \"1\",  \"descripcionPeticion\" : \"Solicitud para cambiar datos personales en la póliza\",  \"fechaInicio\" : \"2025-05-21T14:20:00Z\",  \"nombreEstado\" : \"Pendiente\",  \"idPerfil\" : 1,  \"idPeticion\" : 1001,  \"porcentajeReal\" : 80.0,  \"codClarity\" : \"MU-2019-038493\",  \"usuarioGraba\" : \"admin\",  \"idUsuarioSolicitante\" : 1234,  \"porcentajeBase\" : 75.0,  \"prioriDesc\" : \"Alta\",  \"nombrePerfil\" : \"Administrador\",  \"fechaFin\" : \"2025-05-28T14:20:00Z\",  \"colorEstado\" : \"#FF5733\",  \"idEstado\" : 1,  \"usuarioSoli\" : \"jperez\",  \"tipoPeticion\" : \"Solicitud\",  \"totalHoras\" : 40.5,  \"fechaCreacion\" : \"21/05/2025\",  \"codcia\" : \"MGT\",  \"idTipo\" : 1,  \"usuario\" : \"Juan Pérez\"}, {  \"nombrePeticion\" : \"Solicitud de cambio de datos\",  \"idPeticionSiguiente\" : 1002,  \"origen\" : \"CO257\",  \"prioridad\" : \"1\",  \"descripcionPeticion\" : \"Solicitud para cambiar datos personales en la póliza\",  \"fechaInicio\" : \"2025-05-21T14:20:00Z\",  \"nombreEstado\" : \"Pendiente\",  \"idPerfil\" : 1,  \"idPeticion\" : 1001,  \"porcentajeReal\" : 80.0,  \"codClarity\" : \"MU-2019-038493\",  \"usuarioGraba\" : \"admin\",  \"idUsuarioSolicitante\" : 1234,  \"porcentajeBase\" : 75.0,  \"prioriDesc\" : \"Alta\",  \"nombrePerfil\" : \"Administrador\",  \"fechaFin\" : \"2025-05-28T14:20:00Z\",  \"colorEstado\" : \"#FF5733\",  \"idEstado\" : 1,  \"usuarioSoli\" : \"jperez\",  \"tipoPeticion\" : \"Solicitud\",  \"totalHoras\" : 40.5,  \"fechaCreacion\" : \"21/05/2025\",  \"codcia\" : \"MGT\",  \"idTipo\" : 1,  \"usuario\" : \"Juan Pérez\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener peticiones para tablero", nickname = "getPeticionesTablero", notes = "Obtiene la lista completa de peticiones con información básica para mostrar en el tablero", response = PycPeticionTab.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycPeticionTab.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_peticiones_tablero",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycPeticionTab>> getPeticionesTablero() {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"nombrePeticion\" : \"Desarrollo de nueva funcionalidad\",  \"descripcionTipoPeticion\" : \"Desarrollo de Software\",  \"primerNombre\" : \"Juan\",  \"nombreEstado\" : \"En Proceso\",  \"idPeticion\" : 1001}, {  \"nombrePeticion\" : \"Desarrollo de nueva funcionalidad\",  \"descripcionTipoPeticion\" : \"Desarrollo de Software\",  \"primerNombre\" : \"Juan\",  \"nombreEstado\" : \"En Proceso\",  \"idPeticion\" : 1001} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener procesos por tipo de petición", nickname = "getProcesosPorTipo", notes = "Obtiene la lista de procesos asociados a un tipo de petición específico", response = PycProcesoPorTipo.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycProcesoPorTipo.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_proceso_por_tipo",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycProcesoPorTipo>> getProcesosPorTipo(@NotNull @ApiParam(value = "ID del tipo de petición", required = true) @Valid @RequestParam(value = "idTipo", required = true) Integer idTipo) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"nombreProceso\" : \"Proceso de Gestión\",  \"idProceso\" : 1}, {  \"nombreProceso\" : \"Proceso de Gestión\",  \"idProceso\" : 1} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener query de dato variable", nickname = "getQueryDatVar", notes = "Obtiene la consulta asociada a un dato variable específico", response = PycGetQueryDatVar.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycGetQueryDatVar.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_query_dat_var",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<PycGetQueryDatVar> getQueryDatVar(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso,@NotNull @ApiParam(value = "ID de la sección", required = true) @Valid @RequestParam(value = "idSeccion", required = true) Integer idSeccion,@NotNull @ApiParam(value = "ID del dato variable", required = true) @Valid @RequestParam(value = "idDatoVar", required = true) Integer idDatoVar) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"query\" : \"SELECT * FROM tabla WHERE condicion = 1\"}", PycGetQueryDatVar.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener reporte de peticiones", nickname = "getReportePeticion", notes = "Obtiene el reporte de peticiones filtrado por área, departamento, solicitante, prioridad, analista y estado", response = PycReportePeti.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycReportePeti.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_reporte_peticion",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycReportePeti>> getReportePeticion(@ApiParam(value = "ID del área (opcional)") @Valid @RequestParam(value = "idArea", required = false) Integer idArea,@ApiParam(value = "ID del departamento (opcional)") @Valid @RequestParam(value = "idDepartamento", required = false) Integer idDepartamento,@ApiParam(value = "ID del usuario solicitante (opcional)") @Valid @RequestParam(value = "idSolicitante", required = false) Integer idSolicitante,@ApiParam(value = "Prioridad de la petición (opcional, usar 'null' para peticiones sin prioridad)") @Valid @RequestParam(value = "prioridad", required = false) String prioridad,@ApiParam(value = "ID del analista asignado (opcional)") @Valid @RequestParam(value = "idAnalista", required = false) Integer idAnalista,@ApiParam(value = "IDs de estados separados por coma (opcional, ej. \"1,2,3\")") @Valid @RequestParam(value = "estado", required = false) String estado) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"estado\" : \"1\",  \"nombreArea\" : \"Tecnología\",  \"nombreDepartamento\" : \"Desarrollo de Software\",  \"nombrePeticion\" : \"Solicitud de cambio de datos\",  \"fechaInicio\" : \"15/01/2024\",  \"analista\" : \"María García\",  \"observaciones\" : \"Petición en proceso de revisión\",  \"usuario\" : \"Juan Pérez\",  \"idPeticion\" : 1001,  \"fechaFin\" : \"20/02/2024\",  \"codClarity\" : \"MU-2024-001234\",  \"prioridad\" : \"Alta\"}, {  \"estado\" : \"1\",  \"nombreArea\" : \"Tecnología\",  \"nombreDepartamento\" : \"Desarrollo de Software\",  \"nombrePeticion\" : \"Solicitud de cambio de datos\",  \"fechaInicio\" : \"15/01/2024\",  \"analista\" : \"María García\",  \"observaciones\" : \"Petición en proceso de revisión\",  \"usuario\" : \"Juan Pérez\",  \"idPeticion\" : 1001,  \"fechaFin\" : \"20/02/2024\",  \"codClarity\" : \"MU-2024-001234\",  \"prioridad\" : \"Alta\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener reporte de peticiones con información de programación", nickname = "getReportePeticionProgramacion", notes = "Obtiene un reporte detallado de peticiones filtradas por año y proceso", response = PycReportePetProg.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycReportePetProg.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_reporte_peticion_programacion",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycReportePetProg>> getReportePeticionProgramacion(@NotNull @ApiParam(value = "Año de filtro para las peticiones", required = true) @Valid @RequestParam(value = "anio", required = true) Integer anio,@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"inicioDesa\" : \"20/01/2024\",  \"tipo\" : \"Desarrollo de Software\",  \"estado\" : \"En Proceso\",  \"nombreArea\" : \"Tecnología\",  \"nombreDepartamento\" : \"Desarrollo de Software\",  \"nombrePeticion\" : \"NUEVA FUNCIONALIDAD SISTEMA\",  \"diasInhabiles\" : 12.58,  \"idPeticionSiguiente\" : 1002,  \"analista\" : \"María García\",  \"diasReales\" : 31.42,  \"prioridad\" : \"Alta\",  \"descPeticion\" : \"DESARROLLO DE NUEVA FUNCIONALIDAD PARA EL SISTEMA\",  \"inicioAnalisis\" : \"16/01/2024\",  \"fechaInicio\" : \"15/01/2024\",  \"semanaTotal\" : 6.29,  \"idPeticion\" : 1001,  \"codClarity\" : \"CLR-2024-001\",  \"horaDesa\" : 120.5,  \"diasTotal\" : 44.0,  \"fechaFin\" : \"28/02/2024\",  \"finDesa\" : \"25/02/2024\",  \"horaAnalisis\" : 24.0,  \"observaciones\" : \"PETICIÓN EN PROCESO DE DESARROLLO\",  \"fechaCreacion\" : \"2024-01-15\",  \"usuario\" : \"Juan Pérez\",  \"nomPeticionSiguiente\" : \"FASE 2 DEL DESARROLLO\",  \"finAnalisis\" : \"19/01/2024\"}, {  \"inicioDesa\" : \"20/01/2024\",  \"tipo\" : \"Desarrollo de Software\",  \"estado\" : \"En Proceso\",  \"nombreArea\" : \"Tecnología\",  \"nombreDepartamento\" : \"Desarrollo de Software\",  \"nombrePeticion\" : \"NUEVA FUNCIONALIDAD SISTEMA\",  \"diasInhabiles\" : 12.58,  \"idPeticionSiguiente\" : 1002,  \"analista\" : \"María García\",  \"diasReales\" : 31.42,  \"prioridad\" : \"Alta\",  \"descPeticion\" : \"DESARROLLO DE NUEVA FUNCIONALIDAD PARA EL SISTEMA\",  \"inicioAnalisis\" : \"16/01/2024\",  \"fechaInicio\" : \"15/01/2024\",  \"semanaTotal\" : 6.29,  \"idPeticion\" : 1001,  \"codClarity\" : \"CLR-2024-001\",  \"horaDesa\" : 120.5,  \"diasTotal\" : 44.0,  \"fechaFin\" : \"28/02/2024\",  \"finDesa\" : \"25/02/2024\",  \"horaAnalisis\" : 24.0,  \"observaciones\" : \"PETICIÓN EN PROCESO DE DESARROLLO\",  \"fechaCreacion\" : \"2024-01-15\",  \"usuario\" : \"Juan Pérez\",  \"nomPeticionSiguiente\" : \"FASE 2 DEL DESARROLLO\",  \"finAnalisis\" : \"19/01/2024\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener responsables de petición", nickname = "getResponsablePeticion", notes = "Obtiene la lista de responsables asociados a una petición específica, incluyendo solicitante y usuarios asignados con sus perfiles y estados de conexión", response = PycRespPeti.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycRespPeti.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_responsable_peticion",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycRespPeti>> getResponsablePeticion(@NotNull @ApiParam(value = "ID de la petición", required = true) @Valid @RequestParam(value = "idPeticion", required = true) Integer idPeticion,@ApiParam(value = "Número o usuario que realiza la consulta (opcional)") @Valid @RequestParam(value = "numaOUsuario", required = false) String numaOUsuario) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"indicador\" : \"left\",  \"connected\" : \"on\",  \"fecha\" : \"2025-05-24 02:09:48.0\",  \"genero\" : \"M\",  \"usuario\" : \"JPEREZ\",  \"cargo\" : \"SOLICITANTE\",  \"title\" : \"INFORMACION DE LA SOLICITUD&#013;&#013;FECHA REGISTRO: 15/03/2024 10:30\",  \"nombre\" : \"Juan Pérez García\",  \"urlPerfil\" : \"/images/profiles/user_male.png\"}, {  \"indicador\" : \"left\",  \"connected\" : \"on\",  \"fecha\" : \"2025-05-24 02:09:48.0\",  \"genero\" : \"M\",  \"usuario\" : \"JPEREZ\",  \"cargo\" : \"SOLICITANTE\",  \"title\" : \"INFORMACION DE LA SOLICITUD&#013;&#013;FECHA REGISTRO: 15/03/2024 10:30\",  \"nombre\" : \"Juan Pérez García\",  \"urlPerfil\" : \"/images/profiles/user_male.png\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener resumen de actividades", nickname = "getResumenActividad", notes = "Obtiene el resumen estadístico de actividades de una petición específica por categoría, incluyendo totales, terminadas, pendientes y horas", response = PycResumenActi.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycResumenActi.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_resumen_actividad",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycResumenActi>> getResumenActividad(@NotNull @ApiParam(value = "ID de la petición", required = true) @Valid @RequestParam(value = "idPeticion", required = true) Integer idPeticion,@NotNull @ApiParam(value = "ID de la categoría del tablero", required = true) @Valid @RequestParam(value = "idCategoria", required = true) Integer idCategoria) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"terminadas\" : 8,  \"pendientes\" : 7,  \"porcentaje\" : 113,  \"totales\" : 15,  \"base\" : 120.5,  \"rea\" : 135.75}, {  \"terminadas\" : 8,  \"pendientes\" : 7,  \"porcentaje\" : 113,  \"totales\" : 15,  \"base\" : 120.5,  \"rea\" : 135.75} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Verificar opción de selección múltiple", nickname = "getSrcMultipleOption", notes = "Verifica si un perfil tiene la opción de selección múltiple", response = PycSrcMultipleOption.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycSrcMultipleOption.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_src_multiple_option",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<PycSrcMultipleOption> getSrcMultipleOption(@NotNull @ApiParam(value = "ID del perfil", required = true) @Valid @RequestParam(value = "idPerfil", required = true) Integer idPerfil) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"hasMultipleOption\" : 1}", PycSrcMultipleOption.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener subordinados por canal, oficina y supervisor", nickname = "getSubordinados", notes = "Obtiene la lista de subordinados disponibles para un supervisor específico considerando canal, oficina, indicador de login y proceso.", response = PycSubordinados.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycSubordinados.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/crm_comercial/get_subordinados",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycSubordinados>> getSubordinados(@NotNull @ApiParam(value = "ID del canal", required = true) @Valid @RequestParam(value = "canal", required = true) String canal,@NotNull @ApiParam(value = "ID de la oficina", required = true) @Valid @RequestParam(value = "oficina", required = true) String oficina,@NotNull @ApiParam(value = "ID del supervisor", required = true) @Valid @RequestParam(value = "idSupervisor", required = true) String idSupervisor,@NotNull @ApiParam(value = "Indicador de login (S/N)", required = true) @Valid @RequestParam(value = "indLogin", required = true) String indLogin,@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "proceso", required = true) String proceso) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"nsubs\" : \"S\",  \"idUsuario\" : 1234,  \"subordinado\" : \"Juan Carlos Pérez González\"}, {  \"nsubs\" : \"S\",  \"idUsuario\" : 1234,  \"subordinado\" : \"Juan Carlos Pérez González\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener años disponibles por proceso", nickname = "getTabAnios", notes = "Obtiene la lista de años distintos de las peticiones filtrados por proceso", response = PycTabAnios.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycTabAnios.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_tab_anios",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycTabAnios>> getTabAnios(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"anios\" : \"2024\"}, {  \"anios\" : \"2024\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener áreas disponibles por proceso y año", nickname = "getTabArea", notes = "Obtiene la lista de áreas distintas de las peticiones filtradas por proceso y año", response = PycTabArea.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycTabArea.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_tab_area",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycTabArea>> getTabArea(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso,@ApiParam(value = "Año de filtro (formato YYYY) (opcional)") @Valid @RequestParam(value = "anio", required = false) String anio) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"nombreArea\" : \"Tecnología\",  \"idArea\" : 1}, {  \"nombreArea\" : \"Tecnología\",  \"idArea\" : 1} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener estados disponibles por proceso, año y área", nickname = "getTabEstado", notes = "Obtiene la lista de estados distintos de las peticiones filtradas por proceso, año y área", response = PycTabEstado.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycTabEstado.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_tab_estado",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycTabEstado>> getTabEstado(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso,@ApiParam(value = "Año de filtro (formato YYYY) (opcional)") @Valid @RequestParam(value = "anio", required = false) String anio,@ApiParam(value = "ID del área (opcional)") @Valid @RequestParam(value = "idArea", required = false) Integer idArea) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idEstado\" : 1,  \"nombreEstado\" : \"Pendiente\"}, {  \"idEstado\" : 1,  \"nombreEstado\" : \"Pendiente\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener tipos de petición por proceso", nickname = "getTipoPeticion", notes = "Obtiene la lista de tipos de petición disponibles para un proceso específico", response = PycTipoPeticion.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycTipoPeticion.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_tipo_peticion",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycTipoPeticion>> getTipoPeticion(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"estado\" : \"ACT\",  \"descripcionTipoPeticion\" : \"Solicitud de Desarrollo\",  \"fechaHora\" : \"2024-01-15 10:30:00\",  \"idTipoPeticion\" : 1,  \"usuario\" : \"admin\",  \"idProceso\" : 1}, {  \"estado\" : \"ACT\",  \"descripcionTipoPeticion\" : \"Solicitud de Desarrollo\",  \"fechaHora\" : \"2024-01-15 10:30:00\",  \"idTipoPeticion\" : 1,  \"usuario\" : \"admin\",  \"idProceso\" : 1} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener usuarios por perfil y proceso", nickname = "getUsuPerfilProceso", notes = "Obtiene la lista de usuarios asociados a un perfil y proceso específicos", response = PycUsuPerfilProceso.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycUsuPerfilProceso.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_usu_perfil_proceso",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycUsuPerfilProceso>> getUsuPerfilProceso(@NotNull @ApiParam(value = "ID del perfil", required = true) @Valid @RequestParam(value = "idPerfil", required = true) Integer idPerfil,@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idUsuario\" : 1234,  \"indDefault\" : \"S\",  \"nombre\" : \"Juan Pérez\"}, {  \"idUsuario\" : 1234,  \"indDefault\" : \"S\",  \"nombre\" : \"Juan Pérez\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener procesos por usuario", nickname = "getUsuarioProcesos", notes = "Obtiene la lista de procesos asociados a un usuario específico", response = PycUsuProcesos.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycUsuProcesos.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_usuario_procesos",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycUsuProcesos>> getUsuarioProcesos(@NotNull @ApiParam(value = "Nombre de usuario", required = true) @Valid @RequestParam(value = "usuario", required = true) String usuario) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"icono\" : \"fa-tasks\",  \"estado\" : \"ACT\",  \"nombreProceso\" : \"Gestión de Peticiones\",  \"idUsuario\" : 1234,  \"usuario\" : \"jperez\",  \"nombreUsuario\" : \"Juan Pérez\",  \"idProceso\" : 1}, {  \"icono\" : \"fa-tasks\",  \"estado\" : \"ACT\",  \"nombreProceso\" : \"Gestión de Peticiones\",  \"idUsuario\" : 1234,  \"usuario\" : \"jperez\",  \"nombreUsuario\" : \"Juan Pérez\",  \"idProceso\" : 1} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener lista de usuarios", nickname = "getUsuarios", notes = "Obtiene la lista de usuarios de PYCGES", response = PycUsuario.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycUsuario.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/usuarios",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycUsuario>> getUsuarios() {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"fechaBaja\" : \"2025-12-31T00:00:00Z\",  \"estado\" : \"ACT\",  \"numaOUsuario\" : \"jperezdb\",  \"idUsuario\" : 1234,  \"genero\" : \"M\",  \"fechaCreacion\" : \"2025-05-21T14:20:00Z\",  \"nombre\" : \"Juan Pérez\",  \"email\" : \"<EMAIL>\"}, {  \"fechaBaja\" : \"2025-12-31T00:00:00Z\",  \"estado\" : \"ACT\",  \"numaOUsuario\" : \"jperezdb\",  \"idUsuario\" : 1234,  \"genero\" : \"M\",  \"fechaCreacion\" : \"2025-05-21T14:20:00Z\",  \"nombre\" : \"Juan Pérez\",  \"email\" : \"<EMAIL>\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener usuarios asignados", nickname = "getUsuariosAsig", notes = "Obtiene la lista de usuarios asignados según proceso, perfil, aplicación y estado", response = PycGetUsuariosAsig.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycGetUsuariosAsig.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_usuarios_asig",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycGetUsuariosAsig>> getUsuariosAsig(@NotNull @ApiParam(value = "ID del proceso", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso,@NotNull @ApiParam(value = "ID del perfil", required = true) @Valid @RequestParam(value = "idPerfil", required = true) Integer idPerfil,@NotNull @ApiParam(value = "ID de la aplicación", required = true) @Valid @RequestParam(value = "idAplicacion", required = true) Integer idAplicacion,@NotNull @ApiParam(value = "ID del estado", required = true) @Valid @RequestParam(value = "idEstado", required = true) Integer idEstado) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"indAdmin\" : \"S\",  \"idAplicacion\" : 2,  \"idEstado\" : 1,  \"idPerfil\" : 3,  \"idUsuario\" : 1234,  \"idProceso\" : 1,  \"nombre\" : \"Juan Carlos Pérez\",  \"indAutomatico\" : \"S\",  \"urlPerfil\" : \"https://ejemplo.com/perfil/usuario.jpg\"}, {  \"indAdmin\" : \"S\",  \"idAplicacion\" : 2,  \"idEstado\" : 1,  \"idPerfil\" : 3,  \"idUsuario\" : 1234,  \"idProceso\" : 1,  \"nombre\" : \"Juan Carlos Pérez\",  \"indAutomatico\" : \"S\",  \"urlPerfil\" : \"https://ejemplo.com/perfil/usuario.jpg\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener usuarios por perfil", nickname = "getUsuariosPerfil", notes = "Obtiene la lista de usuarios asociados a un perfil específico", response = PycUsuarioPerfil.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycUsuarioPerfil.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_usuario_perfil",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycUsuarioPerfil>> getUsuariosPerfil(@ApiParam(value = "ID del perfil") @Valid @RequestParam(value = "idPerfil", required = false) Integer idPerfil) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idPerfil\" : 1,  \"idUsuario\" : 1234,  \"indDefault\" : \"S\",  \"nombrePerfil\" : \"Administrador\",  \"multiperfil\" : \"S\",  \"nombre\" : \"Juan Pérez\"}, {  \"idPerfil\" : 1,  \"idUsuario\" : 1234,  \"indDefault\" : \"S\",  \"nombrePerfil\" : \"Administrador\",  \"multiperfil\" : \"S\",  \"nombre\" : \"Juan Pérez\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener usuarios por perfil y petición", nickname = "getUsuariosPerfilPeticion", notes = "Obtiene la lista de usuarios asociados a un perfil específico para una petición y aplicación determinada", response = PycUsuPerfilPeti.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycUsuPerfilPeti.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/get_usuarios_perfil_peticion",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycUsuPerfilPeti>> getUsuariosPerfilPeticion(@NotNull @ApiParam(value = "ID de la petición", required = true) @Valid @RequestParam(value = "idPeticion", required = true) Integer idPeticion,@NotNull @ApiParam(value = "ID del perfil", required = true) @Valid @RequestParam(value = "idPerfil", required = true) Integer idPerfil,@NotNull @ApiParam(value = "ID de la aplicación", required = true) @Valid @RequestParam(value = "idAplicacion", required = true) Integer idAplicacion) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"idPerfil\" : 5,  \"idUsuario\" : 1234,  \"genero\" : \"M\",  \"asigna\" : \"1\",  \"nombrePerfil\" : \"Analista Senior\",  \"nombre\" : \"Juan Carlos Pérez\",  \"urlPerfil\" : \"/images/user_male.png\"}, {  \"idPerfil\" : 5,  \"idUsuario\" : 1234,  \"genero\" : \"M\",  \"asigna\" : \"1\",  \"nombrePerfil\" : \"Analista Senior\",  \"nombre\" : \"Juan Carlos Pérez\",  \"urlPerfil\" : \"/images/user_male.png\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Insertar nueva actividad en una petición", nickname = "insertActividad", notes = "Crea una nueva actividad asociada a una petición específica. Automáticamente actualiza las fechas de inicio y fin de la petición basándose en las actividades asociadas (MIN fecha_inicio, MAX fecha_fin). Genera un ID único para la actividad usando la secuencia SQ_PYC_ACTIVIDAD_PETICION. ", response = PycInserActResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Actividad creada exitosamente", response = PycInserActResponse.class),
        @ApiResponse(code = 400, message = "Bad request - Datos inválidos", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Petición no encontrada", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/insert_actividad",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycInserActResponse> insertActividad(@ApiParam(value = "Datos de la actividad a insertar" ,required=true )  @Valid @RequestBody PycInserAct actividadData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"ejecutado\" : true,  \"idActividad\" : 5001,  \"mensaje\" : \"Actividad creada exitosamente\"}", PycInserActResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Insertar observación en petición", nickname = "insertObservacion", notes = "Inserta una nueva observación en una petición específica del sistema PYCGES", response = PycInsertObs.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycInsertObs.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/insert_observacion",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycInsertObs> insertObservacion(@ApiParam(value = "Datos de la observación a insertar" ,required=true )  @Valid @RequestBody PycInsertObservacion observacionData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"ejecutado\" : true,  \"mensaje\" : \"Observación insertada exitosamente\",  \"idObservacion\" : 5001}", PycInsertObs.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Insertar petición", nickname = "insertPeticion", notes = "Inserta una nueva petición en el sistema PYCGES", response = PycPeticionResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycPeticionResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/insert_peticion",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycPeticionResponse> insertPeticion(@ApiParam(value = "Datos de la petición a insertar" ,required=true )  @Valid @RequestBody PycInsertPeticion peticionData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"idPeticion\" : 0}", PycPeticionResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Insertar o actualizar usuario proceso", nickname = "insertUserProceso", notes = "Inserta o actualiza la relación entre un usuario y un proceso con su estado correspondiente", response = PycUsuarioResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycUsuarioResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/insert_user_proceso",
        produces = { "application/json" }, 
        method = RequestMethod.POST)
    default ResponseEntity<PycUsuarioResponse> insertUserProceso(@ApiParam(value = "Datos del usuario proceso a insertar/actualizar" ,required=true )  @Valid @RequestBody PycInsertUserPro userData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"idUsuario\" : 0}", PycUsuarioResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Insertar nuevo usuario", nickname = "insertUsuario", notes = "Inserta un nuevo usuario en el sistema PYCGES con todos sus datos personales", response = PycUsuarioResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycUsuarioResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/insert_usuario",
        produces = { "application/json" }, 
        method = RequestMethod.POST)
    default ResponseEntity<PycUsuarioResponse> insertUsuario(@ApiParam(value = "Datos del usuario a insertar" ,required=true )  @Valid @RequestBody PycInsertUsuario usuarioData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"idUsuario\" : 0}", PycUsuarioResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Crear o actualizar dato variable de petición", nickname = "newDatVarPeticion", notes = "Crea o actualiza un dato variable específico de una petición en el formulario correspondiente", response = PycDatVarPetResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycDatVarPetResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/new_dat_var_peticion",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycDatVarPetResponse> newDatVarPeticion(@ApiParam(value = "Datos del variable de petición a crear o actualizar" ,required=true )  @Valid @RequestBody PycNewDatVarPet datVarData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"ejecutado\" : true,  \"mensaje\" : \"Dato variable de petición guardado exitosamente\",  \"idRegistro\" : 5001}", PycDatVarPetResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener datos de variables equivalentes por ramo", nickname = "obtenerDatVarEquiv", notes = "Obtiene los datos de variables equivalentes por ramo", response = PycDatoVarEquiv.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycDatoVarEquiv.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/crm_comercial/obtener_dat_var_equiv",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycDatoVarEquiv>> obtenerDatVarEquiv(@NotNull @ApiParam(value = "Código del ramo", required = true) @Valid @RequestParam(value = "codRamo", required = true) String codRamo,@NotNull @ApiParam(value = "Código de compañía (obligatorio)", required = true) @Valid @RequestParam(value = "codCia", required = true) String codCia,@ApiParam(value = "Código de modalidad") @Valid @RequestParam(value = "codModalidad", required = false) Integer codModalidad) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"datVar\" : \"VARIABLE_001\",  \"datVarNm\" : \"NOMBRE_VARIABLE_001\",  \"codRamo\" : \"300\",  \"datVarReef\" : \"VARIABLE_REEF_001\",  \"datVarId\" : \"DAT_VAR_ID_001\",  \"codCia\" : \"2\",  \"idFormulario\" : 1,  \"idSeccion\" : 2,  \"codModalidad\" : 999}, {  \"datVar\" : \"VARIABLE_001\",  \"datVarNm\" : \"NOMBRE_VARIABLE_001\",  \"codRamo\" : \"300\",  \"datVarReef\" : \"VARIABLE_REEF_001\",  \"datVarId\" : \"DAT_VAR_ID_001\",  \"codCia\" : \"2\",  \"idFormulario\" : 1,  \"idSeccion\" : 2,  \"codModalidad\" : 999} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener información de ramo por código", nickname = "obtenerRamo", notes = "Obtiene la información de un ramo específico", response = PycRamo.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycRamo.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/crm_comercial/obtener_ramo_equiv",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycRamo>> obtenerRamo(@NotNull @ApiParam(value = "Código del ramo", required = true) @Valid @RequestParam(value = "codRamo", required = true) String codRamo,@NotNull @ApiParam(value = "Código de compañía (obligatorio)", required = true) @Valid @RequestParam(value = "codCia", required = true) String codCia,@ApiParam(value = "Código de modalidad") @Valid @RequestParam(value = "codModalidad", required = false) Integer codModalidad) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"estado\" : \"ACT\",  \"codRamo\" : \"300\",  \"descripcionRamo\" : \"Automóviles\",  \"codCia\" : \"2\",  \"idTipoPeticion\" : 1,  \"codModalidad\" : \"999\"}, {  \"estado\" : \"ACT\",  \"codRamo\" : \"300\",  \"descripcionRamo\" : \"Automóviles\",  \"codCia\" : \"2\",  \"idTipoPeticion\" : 1,  \"codModalidad\" : \"999\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Obtener perfiles de usuario por ID de usuario y proceso", nickname = "perfilesUsuario", notes = "Obtiene los perfiles asignados a un usuario", response = PycPerfilUsu.class, responseContainer = "List", authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycPerfilUsu.class, responseContainer = "List"),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/perfiles_usuario",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PycPerfilUsu>> perfilesUsuario(@NotNull @ApiParam(value = "ID del usuario (obligatorio)", required = true) @Valid @RequestParam(value = "idUsuario", required = true) Integer idUsuario,@NotNull @ApiParam(value = "ID del proceso (obligatorio)", required = true) @Valid @RequestParam(value = "idProceso", required = true) Integer idProceso) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {  \"estado\" : \"ACT\",  \"idPerfil\" : 1,  \"descripcionPerfil\" : \"Perfil con acceso completo al sistema\",  \"indDefault\" : \"S\",  \"nombrePerfil\" : \"Administrador\",  \"multiperfil\" : \"S\",  \"indConsulta\" : \"S\"}, {  \"estado\" : \"ACT\",  \"idPerfil\" : 1,  \"descripcionPerfil\" : \"Perfil con acceso completo al sistema\",  \"indDefault\" : \"S\",  \"nombrePerfil\" : \"Administrador\",  \"multiperfil\" : \"S\",  \"indConsulta\" : \"S\"} ]", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Actualizar horas reales y estado de actividad", nickname = "updateActividad", notes = "Actualiza las horas reales trabajadas en una actividad y opcionalmente marca la actividad como terminada. Si el indicador es 'S', la actividad se marca como terminada (estado 'TER') y se establece la fecha de fin real. Si el indicador es 'N', solo se actualizan las horas reales sin cambiar el estado. Automáticamente inserta una observación de la actualización usando FN_INSERT_OBSERVACION. Incluye soporte para auditoría con parámetro numaOUsuario. ", response = PycUpdActResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Actividad actualizada exitosamente", response = PycUpdActResponse.class),
        @ApiResponse(code = 400, message = "Bad request - Datos inválidos", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Actividad o petición no encontrada", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/update_actividad",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycUpdActResponse> updateActividad(@ApiParam(value = "Datos de la actividad a actualizar" ,required=true )  @Valid @RequestBody PycUpdAct actividadData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"ejecutado\" : true,  \"mensaje\" : \"Actividad actualizada exitosamente y observación registrada\",  \"idPeticion\" : 1001,  \"idObservacion\" : 2001}", PycUpdActResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Actualizar encuesta de satisfacción de una petición", nickname = "updateEncuesta", notes = "Actualiza los datos de la encuesta de satisfacción de una petición específica. ", response = PycUpdEncResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Encuesta actualizada exitosamente", response = PycUpdEncResponse.class),
        @ApiResponse(code = 400, message = "Bad request - Datos inválidos", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Petición no encontrada", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/update_encuesta",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycUpdEncResponse> updateEncuesta(@ApiParam(value = "Datos de la encuesta a actualizar" ,required=true )  @Valid @RequestBody PycUpdEnc encuestaData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"ejecutado\" : true,  \"mensaje\" : \"Encuesta de satisfacción actualizada exitosamente\",  \"idPeticion\" : \"1001\"}", PycUpdEncResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Actualizar estado de petición", nickname = "updateEstadoPeticion", notes = "Actualiza el estado de una o múltiples peticiones en el sistema PYCGES con observaciones y motivos", response = PycEstadoPeticionResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycEstadoPeticionResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/update_estado_peticion",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycEstadoPeticionResponse> updateEstadoPeticion(@ApiParam(value = "Datos para actualizar el estado de la petición" ,required=true )  @Valid @RequestBody PycUpdEstadoPet estadoData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"resultado\" : 1001}", PycEstadoPeticionResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Actualizar path y URL del perfil de usuario", nickname = "updatePathPerfil", notes = "Actualiza la URL y PATH del perfil de un usuario específico en el sistema PYCGES", response = PycPathPerfilResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycPathPerfilResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/update_path_perfil",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycPathPerfilResponse> updatePathPerfil(@ApiParam(value = "Datos para actualizar el path y URL del perfil del usuario" ,required=true )  @Valid @RequestBody PycUpdPathPerfil perfilData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"resultado\" : 1}", PycPathPerfilResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Actualizar o crear perfil de usuario", nickname = "updatePerfil", notes = "Actualiza o crea un registro de perfil para un usuario específico", response = PycUsuarioResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycUsuarioResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/update_perfil",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycUsuarioResponse> updatePerfil(@ApiParam(value = "Datos del perfil a actualizar o crear" ,required=true )  @Valid @RequestBody PycUpdPerfil perfilData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"idUsuario\" : 0}", PycUsuarioResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Actualizar una petición existente", nickname = "updatePeticion", notes = "Actualiza los datos de una petición existente. ", response = PycUpdPeticionResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Petición actualizada exitosamente", response = PycUpdPeticionResponse.class),
        @ApiResponse(code = 400, message = "Bad request - Datos inválidos", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Petición no encontrada", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/update_peticion",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.PUT)
    default ResponseEntity<PycUpdPeticionResponse> updatePeticion(@ApiParam(value = "Datos de la petición a actualizar" ,required=true )  @Valid @RequestBody PycUpdPeticion peticionData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"ejecutado\" : true,  \"mensaje\" : \"Petición actualizada exitosamente\",  \"idPeticion\" : 123}", PycUpdPeticionResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Actualizar sesión de usuario", nickname = "updateSesion", notes = "Actualiza las fechas de login/logout de un usuario específico según el estado de la sesión en el sistema PYCGES", response = PycSesionResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycSesionResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/update_sesion",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycSesionResponse> updateSesion(@ApiParam(value = "Datos para actualizar la sesión del usuario" ,required=true )  @Valid @RequestBody PycUpdSesion sesionData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"resultado\" : 1}", PycSesionResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Actualizar o crear usuario", nickname = "updateUsuario", notes = "Actualiza o crea un registro de usuario con sus datos personales y departamento", response = PycUsuarioResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Pycges", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Operación exitosa", response = PycUsuarioResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/pycges/update_usuario",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<PycUsuarioResponse> updateUsuario(@ApiParam(value = "Datos del usuario a actualizar o crear" ,required=true )  @Valid @RequestBody PycUpdUsuario usuarioData) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"idUsuario\" : 0}", PycUsuarioResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default PycgesApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

}
