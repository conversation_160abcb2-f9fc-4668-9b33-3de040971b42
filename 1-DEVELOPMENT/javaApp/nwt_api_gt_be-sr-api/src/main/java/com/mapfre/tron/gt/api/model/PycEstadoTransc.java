package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycEstadoTransc
 */
@Validated

public class PycEstadoTransc   {
  @JsonProperty("idEstadoInicial")
  private Integer idEstadoInicial = null;

  @JsonProperty("idEstado")
  private Integer idEstado = null;

  @JsonProperty("nombreEstado")
  private String nombreEstado = null;

  public PycEstadoTransc idEstadoInicial(Integer idEstadoInicial) {
    this.idEstadoInicial = idEstadoInicial;
    return this;
  }

  /**
   * Identificador del estado inicial de la transición
   * @return idEstadoInicial
  **/
  @ApiModelProperty(example = "1", value = "Identificador del estado inicial de la transición")


  public Integer getIdEstadoInicial() {
    return idEstadoInicial;
  }

  public void setIdEstadoInicial(Integer idEstadoInicial) {
    this.idEstadoInicial = idEstadoInicial;
  }

  public PycEstadoTransc idEstado(Integer idEstado) {
    this.idEstado = idEstado;
    return this;
  }

  /**
   * Identificador del estado final de la transición
   * @return idEstado
  **/
  @ApiModelProperty(example = "2", value = "Identificador del estado final de la transición")


  public Integer getIdEstado() {
    return idEstado;
  }

  public void setIdEstado(Integer idEstado) {
    this.idEstado = idEstado;
  }

  public PycEstadoTransc nombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
    return this;
  }

  /**
   * Nombre del estado final de la transición
   * @return nombreEstado
  **/
  @ApiModelProperty(example = "En Proceso", value = "Nombre del estado final de la transición")


  public String getNombreEstado() {
    return nombreEstado;
  }

  public void setNombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycEstadoTransc pycEstadoTransc = (PycEstadoTransc) o;
    return Objects.equals(this.idEstadoInicial, pycEstadoTransc.idEstadoInicial) &&
        Objects.equals(this.idEstado, pycEstadoTransc.idEstado) &&
        Objects.equals(this.nombreEstado, pycEstadoTransc.nombreEstado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idEstadoInicial, idEstado, nombreEstado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycEstadoTransc {\n");
    
    sb.append("    idEstadoInicial: ").append(toIndentedString(idEstadoInicial)).append("\n");
    sb.append("    idEstado: ").append(toIndentedString(idEstado)).append("\n");
    sb.append("    nombreEstado: ").append(toIndentedString(nombreEstado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

