package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycProcesoPorTipo
 */
@Validated

public class PycProcesoPorTipo   {
  @JsonProperty("idProceso")
  private Integer idProceso = null;

  @JsonProperty("nombreProceso")
  private String nombreProceso = null;

  public PycProcesoPorTipo idProceso(Integer idProceso) {
    this.idProceso = idProceso;
    return this;
  }

  /**
   * Identificador del proceso
   * @return idProceso
  **/
  @ApiModelProperty(example = "1", value = "Identificador del proceso")


  public Integer getIdProceso() {
    return idProceso;
  }

  public void setIdProceso(Integer idProceso) {
    this.idProceso = idProceso;
  }

  public PycProcesoPorTipo nombreProceso(String nombreProceso) {
    this.nombreProceso = nombreProceso;
    return this;
  }

  /**
   * Nombre del proceso
   * @return nombreProceso
  **/
  @ApiModelProperty(example = "Proceso de Gestión", value = "Nombre del proceso")


  public String getNombreProceso() {
    return nombreProceso;
  }

  public void setNombreProceso(String nombreProceso) {
    this.nombreProceso = nombreProceso;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycProcesoPorTipo pycProcesoPorTipo = (PycProcesoPorTipo) o;
    return Objects.equals(this.idProceso, pycProcesoPorTipo.idProceso) &&
        Objects.equals(this.nombreProceso, pycProcesoPorTipo.nombreProceso);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idProceso, nombreProceso);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycProcesoPorTipo {\n");
    
    sb.append("    idProceso: ").append(toIndentedString(idProceso)).append("\n");
    sb.append("    nombreProceso: ").append(toIndentedString(nombreProceso)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

