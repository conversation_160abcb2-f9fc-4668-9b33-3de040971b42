package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycGetQueryDatVar
 */
@Validated

public class PycGetQueryDatVar   {
  @JsonProperty("query")
  private String query = null;

  public PycGetQueryDatVar query(String query) {
    this.query = query;
    return this;
  }

  /**
   * Consulta SQL asociada al dato variable
   * @return query
  **/
  @ApiModelProperty(example = "SELECT * FROM tabla WHERE condicion = 1", value = "Consulta SQL asociada al dato variable")


  public String getQuery() {
    return query;
  }

  public void setQuery(String query) {
    this.query = query;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycGetQueryDatVar pycGetQueryDatVar = (PycGetQueryDatVar) o;
    return Objects.equals(this.query, pycGetQueryDatVar.query);
  }

  @Override
  public int hashCode() {
    return Objects.hash(query);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycGetQueryDatVar {\n");
    
    sb.append("    query: ").append(toIndentedString(query)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

