package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * RamoSiniestro
 */
@Validated

public class RamoSiniestro   {
  @JsonProperty("ramo")
  private Integer ramo = null;

  public RamoSiniestro ramo(Integer ramo) {
    this.ramo = ramo;
    return this;
  }

  /**
   * Código de ramo del siniestro
   * @return ramo
  **/
  @ApiModelProperty(example = "300", value = "Código de ramo del siniestro")


  public Integer getRamo() {
    return ramo;
  }

  public void setRamo(Integer ramo) {
    this.ramo = ramo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RamoSiniestro ramoSiniestro = (RamoSiniestro) o;
    return Objects.equals(this.ramo, ramoSiniestro.ramo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ramo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RamoSiniestro {\n");
    
    sb.append("    ramo: ").append(toIndentedString(ramo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

