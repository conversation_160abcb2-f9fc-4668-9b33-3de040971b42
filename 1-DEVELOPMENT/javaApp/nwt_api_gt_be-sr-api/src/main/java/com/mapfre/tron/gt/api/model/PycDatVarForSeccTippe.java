package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycDatVarForSeccTippe
 */
@Validated

public class PycDatVarForSeccTippe   {
  @JsonProperty("idProceso")
  private Integer idProceso = null;

  @JsonProperty("idFormulario")
  private Integer idFormulario = null;

  @JsonProperty("idSeccion")
  private Integer idSeccion = null;

  @JsonProperty("nombreFormulario")
  private String nombreFormulario = null;

  @JsonProperty("nombreSeccion")
  private String nombreSeccion = null;

  @JsonProperty("iconoSeccion")
  private String iconoSeccion = null;

  @JsonProperty("ordenSeccion")
  private Integer ordenSeccion = null;

  @JsonProperty("idDatoVariable")
  private Integer idDatoVariable = null;

  @JsonProperty("nombreDatoVariable")
  private String nombreDatoVariable = null;

  @JsonProperty("descripcionDatoVariable")
  private String descripcionDatoVariable = null;

  @JsonProperty("tipoDato")
  private String tipoDato = null;

  @JsonProperty("valorDefecto")
  private String valorDefecto = null;

  @JsonProperty("valorPeticion")
  private String valorPeticion = null;

  @JsonProperty("obligatorio")
  private String obligatorio = null;

  @JsonProperty("orden")
  private Integer orden = null;

  @JsonProperty("longitudMaxima")
  private Integer longitudMaxima = null;

  @JsonProperty("longitudMinima")
  private Integer longitudMinima = null;

  @JsonProperty("patronValidacion")
  private String patronValidacion = null;

  @JsonProperty("mensajeError")
  private String mensajeError = null;

  @JsonProperty("tipoControl")
  private String tipoControl = null;

  @JsonProperty("propiedadesControl")
  private String propiedadesControl = null;

  @JsonProperty("classField")
  private String classField = null;

  @JsonProperty("classForm")
  private String classForm = null;

  @JsonProperty("javascript")
  private String javascript = null;

  @JsonProperty("icono")
  private String icono = null;

  @JsonProperty("placeholder")
  private String placeholder = null;

  @JsonProperty("indVisible")
  private String indVisible = null;

  @JsonProperty("dependeDe")
  private Integer dependeDe = null;

  @JsonProperty("valorDependencia")
  private String valorDependencia = null;

  @JsonProperty("editable")
  private String editable = null;

  public PycDatVarForSeccTippe idProceso(Integer idProceso) {
    this.idProceso = idProceso;
    return this;
  }

  /**
   * Identificador del proceso
   * @return idProceso
  **/
  @ApiModelProperty(example = "1", value = "Identificador del proceso")


  public Integer getIdProceso() {
    return idProceso;
  }

  public void setIdProceso(Integer idProceso) {
    this.idProceso = idProceso;
  }

  public PycDatVarForSeccTippe idFormulario(Integer idFormulario) {
    this.idFormulario = idFormulario;
    return this;
  }

  /**
   * Identificador del formulario
   * @return idFormulario
  **/
  @ApiModelProperty(example = "1", value = "Identificador del formulario")


  public Integer getIdFormulario() {
    return idFormulario;
  }

  public void setIdFormulario(Integer idFormulario) {
    this.idFormulario = idFormulario;
  }

  public PycDatVarForSeccTippe idSeccion(Integer idSeccion) {
    this.idSeccion = idSeccion;
    return this;
  }

  /**
   * Identificador de la sección
   * @return idSeccion
  **/
  @ApiModelProperty(example = "1", value = "Identificador de la sección")


  public Integer getIdSeccion() {
    return idSeccion;
  }

  public void setIdSeccion(Integer idSeccion) {
    this.idSeccion = idSeccion;
  }

  public PycDatVarForSeccTippe nombreFormulario(String nombreFormulario) {
    this.nombreFormulario = nombreFormulario;
    return this;
  }

  /**
   * Nombre del formulario
   * @return nombreFormulario
  **/
  @ApiModelProperty(example = "FORMULARIO SOLICITUD COTIZACION", value = "Nombre del formulario")


  public String getNombreFormulario() {
    return nombreFormulario;
  }

  public void setNombreFormulario(String nombreFormulario) {
    this.nombreFormulario = nombreFormulario;
  }

  public PycDatVarForSeccTippe nombreSeccion(String nombreSeccion) {
    this.nombreSeccion = nombreSeccion;
    return this;
  }

  /**
   * Nombre de la sección
   * @return nombreSeccion
  **/
  @ApiModelProperty(example = "DATOS PERSONALES", value = "Nombre de la sección")


  public String getNombreSeccion() {
    return nombreSeccion;
  }

  public void setNombreSeccion(String nombreSeccion) {
    this.nombreSeccion = nombreSeccion;
  }

  public PycDatVarForSeccTippe iconoSeccion(String iconoSeccion) {
    this.iconoSeccion = iconoSeccion;
    return this;
  }

  /**
   * Icono de la sección
   * @return iconoSeccion
  **/
  @ApiModelProperty(example = "fa fa-user", value = "Icono de la sección")


  public String getIconoSeccion() {
    return iconoSeccion;
  }

  public void setIconoSeccion(String iconoSeccion) {
    this.iconoSeccion = iconoSeccion;
  }

  public PycDatVarForSeccTippe ordenSeccion(Integer ordenSeccion) {
    this.ordenSeccion = ordenSeccion;
    return this;
  }

  /**
   * Orden de la sección
   * @return ordenSeccion
  **/
  @ApiModelProperty(example = "1", value = "Orden de la sección")


  public Integer getOrdenSeccion() {
    return ordenSeccion;
  }

  public void setOrdenSeccion(Integer ordenSeccion) {
    this.ordenSeccion = ordenSeccion;
  }

  public PycDatVarForSeccTippe idDatoVariable(Integer idDatoVariable) {
    this.idDatoVariable = idDatoVariable;
    return this;
  }

  /**
   * Identificador del dato variable
   * @return idDatoVariable
  **/
  @ApiModelProperty(example = "10", value = "Identificador del dato variable")


  public Integer getIdDatoVariable() {
    return idDatoVariable;
  }

  public void setIdDatoVariable(Integer idDatoVariable) {
    this.idDatoVariable = idDatoVariable;
  }

  public PycDatVarForSeccTippe nombreDatoVariable(String nombreDatoVariable) {
    this.nombreDatoVariable = nombreDatoVariable;
    return this;
  }

  /**
   * Nombre del dato variable
   * @return nombreDatoVariable
  **/
  @ApiModelProperty(example = "NOMBRE_SOLICITANTE", value = "Nombre del dato variable")


  public String getNombreDatoVariable() {
    return nombreDatoVariable;
  }

  public void setNombreDatoVariable(String nombreDatoVariable) {
    this.nombreDatoVariable = nombreDatoVariable;
  }

  public PycDatVarForSeccTippe descripcionDatoVariable(String descripcionDatoVariable) {
    this.descripcionDatoVariable = descripcionDatoVariable;
    return this;
  }

  /**
   * Descripción del dato variable
   * @return descripcionDatoVariable
  **/
  @ApiModelProperty(example = "Nombre completo del solicitante", value = "Descripción del dato variable")


  public String getDescripcionDatoVariable() {
    return descripcionDatoVariable;
  }

  public void setDescripcionDatoVariable(String descripcionDatoVariable) {
    this.descripcionDatoVariable = descripcionDatoVariable;
  }

  public PycDatVarForSeccTippe tipoDato(String tipoDato) {
    this.tipoDato = tipoDato;
    return this;
  }

  /**
   * Tipo de dato (TEXT, NUMBER, DATE, etc.)
   * @return tipoDato
  **/
  @ApiModelProperty(example = "TEXT", value = "Tipo de dato (TEXT, NUMBER, DATE, etc.)")


  public String getTipoDato() {
    return tipoDato;
  }

  public void setTipoDato(String tipoDato) {
    this.tipoDato = tipoDato;
  }

  public PycDatVarForSeccTippe valorDefecto(String valorDefecto) {
    this.valorDefecto = valorDefecto;
    return this;
  }

  /**
   * Valor por defecto del campo
   * @return valorDefecto
  **/
  @ApiModelProperty(example = "", value = "Valor por defecto del campo")


  public String getValorDefecto() {
    return valorDefecto;
  }

  public void setValorDefecto(String valorDefecto) {
    this.valorDefecto = valorDefecto;
  }

  public PycDatVarForSeccTippe valorPeticion(String valorPeticion) {
    this.valorPeticion = valorPeticion;
    return this;
  }

  /**
   * Valor específico de la petición (si existe)
   * @return valorPeticion
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Valor específico de la petición (si existe)")


  public String getValorPeticion() {
    return valorPeticion;
  }

  public void setValorPeticion(String valorPeticion) {
    this.valorPeticion = valorPeticion;
  }

  public PycDatVarForSeccTippe obligatorio(String obligatorio) {
    this.obligatorio = obligatorio;
    return this;
  }

  /**
   * Indicador si el campo es obligatorio (S/N)
   * @return obligatorio
  **/
  @ApiModelProperty(example = "S", value = "Indicador si el campo es obligatorio (S/N)")


  public String getObligatorio() {
    return obligatorio;
  }

  public void setObligatorio(String obligatorio) {
    this.obligatorio = obligatorio;
  }

  public PycDatVarForSeccTippe orden(Integer orden) {
    this.orden = orden;
    return this;
  }

  /**
   * Orden de presentación del campo
   * @return orden
  **/
  @ApiModelProperty(example = "1", value = "Orden de presentación del campo")


  public Integer getOrden() {
    return orden;
  }

  public void setOrden(Integer orden) {
    this.orden = orden;
  }

  public PycDatVarForSeccTippe longitudMaxima(Integer longitudMaxima) {
    this.longitudMaxima = longitudMaxima;
    return this;
  }

  /**
   * Longitud máxima permitida
   * @return longitudMaxima
  **/
  @ApiModelProperty(example = "100", value = "Longitud máxima permitida")


  public Integer getLongitudMaxima() {
    return longitudMaxima;
  }

  public void setLongitudMaxima(Integer longitudMaxima) {
    this.longitudMaxima = longitudMaxima;
  }

  public PycDatVarForSeccTippe longitudMinima(Integer longitudMinima) {
    this.longitudMinima = longitudMinima;
    return this;
  }

  /**
   * Longitud mínima requerida
   * @return longitudMinima
  **/
  @ApiModelProperty(example = "1", value = "Longitud mínima requerida")


  public Integer getLongitudMinima() {
    return longitudMinima;
  }

  public void setLongitudMinima(Integer longitudMinima) {
    this.longitudMinima = longitudMinima;
  }

  public PycDatVarForSeccTippe patronValidacion(String patronValidacion) {
    this.patronValidacion = patronValidacion;
    return this;
  }

  /**
   * Patrón de validación (regex)
   * @return patronValidacion
  **/
  @ApiModelProperty(example = "^[A-Za-z\\s]+$", value = "Patrón de validación (regex)")


  public String getPatronValidacion() {
    return patronValidacion;
  }

  public void setPatronValidacion(String patronValidacion) {
    this.patronValidacion = patronValidacion;
  }

  public PycDatVarForSeccTippe mensajeError(String mensajeError) {
    this.mensajeError = mensajeError;
    return this;
  }

  /**
   * Mensaje de error personalizado
   * @return mensajeError
  **/
  @ApiModelProperty(example = "Solo se permiten letras y espacios", value = "Mensaje de error personalizado")


  public String getMensajeError() {
    return mensajeError;
  }

  public void setMensajeError(String mensajeError) {
    this.mensajeError = mensajeError;
  }

  public PycDatVarForSeccTippe tipoControl(String tipoControl) {
    this.tipoControl = tipoControl;
    return this;
  }

  /**
   * Tipo de control HTML (INPUT, SELECT, TEXTAREA, etc.)
   * @return tipoControl
  **/
  @ApiModelProperty(example = "INPUT", value = "Tipo de control HTML (INPUT, SELECT, TEXTAREA, etc.)")


  public String getTipoControl() {
    return tipoControl;
  }

  public void setTipoControl(String tipoControl) {
    this.tipoControl = tipoControl;
  }

  public PycDatVarForSeccTippe propiedadesControl(String propiedadesControl) {
    this.propiedadesControl = propiedadesControl;
    return this;
  }

  /**
   * Propiedades adicionales del control HTML
   * @return propiedadesControl
  **/
  @ApiModelProperty(example = "placeholder='Ingrese su nombre completo'", value = "Propiedades adicionales del control HTML")


  public String getPropiedadesControl() {
    return propiedadesControl;
  }

  public void setPropiedadesControl(String propiedadesControl) {
    this.propiedadesControl = propiedadesControl;
  }

  public PycDatVarForSeccTippe classField(String classField) {
    this.classField = classField;
    return this;
  }

  /**
   * Clase CSS del campo
   * @return classField
  **/
  @ApiModelProperty(example = "form-control", value = "Clase CSS del campo")


  public String getClassField() {
    return classField;
  }

  public void setClassField(String classField) {
    this.classField = classField;
  }

  public PycDatVarForSeccTippe classForm(String classForm) {
    this.classForm = classForm;
    return this;
  }

  /**
   * Clase CSS del formulario
   * @return classForm
  **/
  @ApiModelProperty(example = "form-group-sm col-md-6", value = "Clase CSS del formulario")


  public String getClassForm() {
    return classForm;
  }

  public void setClassForm(String classForm) {
    this.classForm = classForm;
  }

  public PycDatVarForSeccTippe javascript(String javascript) {
    this.javascript = javascript;
    return this;
  }

  /**
   * Código JavaScript asociado al campo
   * @return javascript
  **/
  @ApiModelProperty(example = "onchange='validarCampo()'", value = "Código JavaScript asociado al campo")


  public String getJavascript() {
    return javascript;
  }

  public void setJavascript(String javascript) {
    this.javascript = javascript;
  }

  public PycDatVarForSeccTippe icono(String icono) {
    this.icono = icono;
    return this;
  }

  /**
   * Icono del campo
   * @return icono
  **/
  @ApiModelProperty(example = "fa fa-user", value = "Icono del campo")


  public String getIcono() {
    return icono;
  }

  public void setIcono(String icono) {
    this.icono = icono;
  }

  public PycDatVarForSeccTippe placeholder(String placeholder) {
    this.placeholder = placeholder;
    return this;
  }

  /**
   * Texto de placeholder del campo
   * @return placeholder
  **/
  @ApiModelProperty(example = "Ingrese su nombre completo", value = "Texto de placeholder del campo")


  public String getPlaceholder() {
    return placeholder;
  }

  public void setPlaceholder(String placeholder) {
    this.placeholder = placeholder;
  }

  public PycDatVarForSeccTippe indVisible(String indVisible) {
    this.indVisible = indVisible;
    return this;
  }

  /**
   * Indicador si el campo es visible (S/N)
   * @return indVisible
  **/
  @ApiModelProperty(example = "S", value = "Indicador si el campo es visible (S/N)")


  public String getIndVisible() {
    return indVisible;
  }

  public void setIndVisible(String indVisible) {
    this.indVisible = indVisible;
  }

  public PycDatVarForSeccTippe dependeDe(Integer dependeDe) {
    this.dependeDe = dependeDe;
    return this;
  }

  /**
   * ID del dato variable del cual depende
   * @return dependeDe
  **/
  @ApiModelProperty(value = "ID del dato variable del cual depende")


  public Integer getDependeDe() {
    return dependeDe;
  }

  public void setDependeDe(Integer dependeDe) {
    this.dependeDe = dependeDe;
  }

  public PycDatVarForSeccTippe valorDependencia(String valorDependencia) {
    this.valorDependencia = valorDependencia;
    return this;
  }

  /**
   * Valor que debe tener la dependencia para mostrar este campo
   * @return valorDependencia
  **/
  @ApiModelProperty(value = "Valor que debe tener la dependencia para mostrar este campo")


  public String getValorDependencia() {
    return valorDependencia;
  }

  public void setValorDependencia(String valorDependencia) {
    this.valorDependencia = valorDependencia;
  }

  public PycDatVarForSeccTippe editable(String editable) {
    this.editable = editable;
    return this;
  }

  /**
   * Indicador si el control es editable (S/N)
   * @return editable
  **/
  @ApiModelProperty(example = "S", value = "Indicador si el control es editable (S/N)")


  public String getEditable() {
    return editable;
  }

  public void setEditable(String editable) {
    this.editable = editable;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycDatVarForSeccTippe pycDatVarForSeccTippe = (PycDatVarForSeccTippe) o;
    return Objects.equals(this.idProceso, pycDatVarForSeccTippe.idProceso) &&
        Objects.equals(this.idFormulario, pycDatVarForSeccTippe.idFormulario) &&
        Objects.equals(this.idSeccion, pycDatVarForSeccTippe.idSeccion) &&
        Objects.equals(this.nombreFormulario, pycDatVarForSeccTippe.nombreFormulario) &&
        Objects.equals(this.nombreSeccion, pycDatVarForSeccTippe.nombreSeccion) &&
        Objects.equals(this.iconoSeccion, pycDatVarForSeccTippe.iconoSeccion) &&
        Objects.equals(this.ordenSeccion, pycDatVarForSeccTippe.ordenSeccion) &&
        Objects.equals(this.idDatoVariable, pycDatVarForSeccTippe.idDatoVariable) &&
        Objects.equals(this.nombreDatoVariable, pycDatVarForSeccTippe.nombreDatoVariable) &&
        Objects.equals(this.descripcionDatoVariable, pycDatVarForSeccTippe.descripcionDatoVariable) &&
        Objects.equals(this.tipoDato, pycDatVarForSeccTippe.tipoDato) &&
        Objects.equals(this.valorDefecto, pycDatVarForSeccTippe.valorDefecto) &&
        Objects.equals(this.valorPeticion, pycDatVarForSeccTippe.valorPeticion) &&
        Objects.equals(this.obligatorio, pycDatVarForSeccTippe.obligatorio) &&
        Objects.equals(this.orden, pycDatVarForSeccTippe.orden) &&
        Objects.equals(this.longitudMaxima, pycDatVarForSeccTippe.longitudMaxima) &&
        Objects.equals(this.longitudMinima, pycDatVarForSeccTippe.longitudMinima) &&
        Objects.equals(this.patronValidacion, pycDatVarForSeccTippe.patronValidacion) &&
        Objects.equals(this.mensajeError, pycDatVarForSeccTippe.mensajeError) &&
        Objects.equals(this.tipoControl, pycDatVarForSeccTippe.tipoControl) &&
        Objects.equals(this.propiedadesControl, pycDatVarForSeccTippe.propiedadesControl) &&
        Objects.equals(this.classField, pycDatVarForSeccTippe.classField) &&
        Objects.equals(this.classForm, pycDatVarForSeccTippe.classForm) &&
        Objects.equals(this.javascript, pycDatVarForSeccTippe.javascript) &&
        Objects.equals(this.icono, pycDatVarForSeccTippe.icono) &&
        Objects.equals(this.placeholder, pycDatVarForSeccTippe.placeholder) &&
        Objects.equals(this.indVisible, pycDatVarForSeccTippe.indVisible) &&
        Objects.equals(this.dependeDe, pycDatVarForSeccTippe.dependeDe) &&
        Objects.equals(this.valorDependencia, pycDatVarForSeccTippe.valorDependencia) &&
        Objects.equals(this.editable, pycDatVarForSeccTippe.editable);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idProceso, idFormulario, idSeccion, nombreFormulario, nombreSeccion, iconoSeccion, ordenSeccion, idDatoVariable, nombreDatoVariable, descripcionDatoVariable, tipoDato, valorDefecto, valorPeticion, obligatorio, orden, longitudMaxima, longitudMinima, patronValidacion, mensajeError, tipoControl, propiedadesControl, classField, classForm, javascript, icono, placeholder, indVisible, dependeDe, valorDependencia, editable);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycDatVarForSeccTippe {\n");
    
    sb.append("    idProceso: ").append(toIndentedString(idProceso)).append("\n");
    sb.append("    idFormulario: ").append(toIndentedString(idFormulario)).append("\n");
    sb.append("    idSeccion: ").append(toIndentedString(idSeccion)).append("\n");
    sb.append("    nombreFormulario: ").append(toIndentedString(nombreFormulario)).append("\n");
    sb.append("    nombreSeccion: ").append(toIndentedString(nombreSeccion)).append("\n");
    sb.append("    iconoSeccion: ").append(toIndentedString(iconoSeccion)).append("\n");
    sb.append("    ordenSeccion: ").append(toIndentedString(ordenSeccion)).append("\n");
    sb.append("    idDatoVariable: ").append(toIndentedString(idDatoVariable)).append("\n");
    sb.append("    nombreDatoVariable: ").append(toIndentedString(nombreDatoVariable)).append("\n");
    sb.append("    descripcionDatoVariable: ").append(toIndentedString(descripcionDatoVariable)).append("\n");
    sb.append("    tipoDato: ").append(toIndentedString(tipoDato)).append("\n");
    sb.append("    valorDefecto: ").append(toIndentedString(valorDefecto)).append("\n");
    sb.append("    valorPeticion: ").append(toIndentedString(valorPeticion)).append("\n");
    sb.append("    obligatorio: ").append(toIndentedString(obligatorio)).append("\n");
    sb.append("    orden: ").append(toIndentedString(orden)).append("\n");
    sb.append("    longitudMaxima: ").append(toIndentedString(longitudMaxima)).append("\n");
    sb.append("    longitudMinima: ").append(toIndentedString(longitudMinima)).append("\n");
    sb.append("    patronValidacion: ").append(toIndentedString(patronValidacion)).append("\n");
    sb.append("    mensajeError: ").append(toIndentedString(mensajeError)).append("\n");
    sb.append("    tipoControl: ").append(toIndentedString(tipoControl)).append("\n");
    sb.append("    propiedadesControl: ").append(toIndentedString(propiedadesControl)).append("\n");
    sb.append("    classField: ").append(toIndentedString(classField)).append("\n");
    sb.append("    classForm: ").append(toIndentedString(classForm)).append("\n");
    sb.append("    javascript: ").append(toIndentedString(javascript)).append("\n");
    sb.append("    icono: ").append(toIndentedString(icono)).append("\n");
    sb.append("    placeholder: ").append(toIndentedString(placeholder)).append("\n");
    sb.append("    indVisible: ").append(toIndentedString(indVisible)).append("\n");
    sb.append("    dependeDe: ").append(toIndentedString(dependeDe)).append("\n");
    sb.append("    valorDependencia: ").append(toIndentedString(valorDependencia)).append("\n");
    sb.append("    editable: ").append(toIndentedString(editable)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

