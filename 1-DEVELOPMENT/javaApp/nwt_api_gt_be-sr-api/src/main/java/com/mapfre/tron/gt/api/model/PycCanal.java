package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycCanal
 */
@Validated

public class PycCanal   {
  @JsonProperty("codigo")
  private Integer codigo = null;

  @JsonProperty("canal")
  private String canal = null;

  public PycCanal codigo(Integer codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Código identificador del canal
   * @return codigo
  **/
  @ApiModelProperty(example = "1", value = "Código identificador del canal")


  public Integer getCodigo() {
    return codigo;
  }

  public void setCodigo(Integer codigo) {
    this.codigo = codigo;
  }

  public PycCanal canal(String canal) {
    this.canal = canal;
    return this;
  }

  /**
   * Nombre del canal
   * @return canal
  **/
  @ApiModelProperty(example = "Canal Web", value = "Nombre del canal")


  public String getCanal() {
    return canal;
  }

  public void setCanal(String canal) {
    this.canal = canal;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycCanal pycCanal = (PycCanal) o;
    return Objects.equals(this.codigo, pycCanal.codigo) &&
        Objects.equals(this.canal, pycCanal.canal);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codigo, canal);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycCanal {\n");
    
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("    canal: ").append(toIndentedString(canal)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

