package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycEstadoUsu
 */
@Validated

public class PycEstadoUsu   {
  @JsonProperty("numaOUsuario")
  private String numaOUsuario = null;

  @JsonProperty("estado")
  private String estado = null;

  public PycEstadoUsu numaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
    return this;
  }

  /**
   * Nombre o número de usuario
   * @return numaOUsuario
  **/
  @ApiModelProperty(example = "jperez", value = "Nombre o número de usuario")


  public String getNumaOUsuario() {
    return numaOUsuario;
  }

  public void setNumaOUsuario(String numaOUsuario) {
    this.numaOUsuario = numaOUsuario;
  }

  public PycEstadoUsu estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado del usuario
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", value = "Estado del usuario")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycEstadoUsu pycEstadoUsu = (PycEstadoUsu) o;
    return Objects.equals(this.numaOUsuario, pycEstadoUsu.numaOUsuario) &&
        Objects.equals(this.estado, pycEstadoUsu.estado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(numaOUsuario, estado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycEstadoUsu {\n");
    
    sb.append("    numaOUsuario: ").append(toIndentedString(numaOUsuario)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

