package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CajeroUsuarioResponseCajeros
 */
@Validated

public class CajeroUsuarioResponseCajeros   {
  @JsonProperty("sistema")
  private String sistema = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("codigo")
  private String codigo = null;

  public CajeroUsuarioResponseCajeros sistema(String sistema) {
    this.sistema = sistema;
    return this;
  }

  /**
   * Código del sistema
   * @return sistema
  **/
  @ApiModelProperty(example = "ACSEL", value = "Código del sistema")


  public String getSistema() {
    return sistema;
  }

  public void setSistema(String sistema) {
    this.sistema = sistema;
  }

  public CajeroUsuarioResponseCajeros usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Usuario del cajero
   * @return usuario
  **/
  @ApiModelProperty(example = "cajero01", value = "Usuario del cajero")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public CajeroUsuarioResponseCajeros codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Código del cajero
   * @return codigo
  **/
  @ApiModelProperty(example = "CAJ001", value = "Código del cajero")


  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CajeroUsuarioResponseCajeros cajeroUsuarioResponseCajeros = (CajeroUsuarioResponseCajeros) o;
    return Objects.equals(this.sistema, cajeroUsuarioResponseCajeros.sistema) &&
        Objects.equals(this.usuario, cajeroUsuarioResponseCajeros.usuario) &&
        Objects.equals(this.codigo, cajeroUsuarioResponseCajeros.codigo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(sistema, usuario, codigo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CajeroUsuarioResponseCajeros {\n");
    
    sb.append("    sistema: ").append(toIndentedString(sistema)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

