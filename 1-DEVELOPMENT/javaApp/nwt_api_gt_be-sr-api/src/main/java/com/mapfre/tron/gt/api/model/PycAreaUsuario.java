package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycAreaUsuario
 */
@Validated

public class PycAreaUsuario   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("usuario")
  private String usuario = null;

  public PycAreaUsuario idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Identificador del usuario
   * @return idUsuario
  **/
  @ApiModelProperty(example = "1234", value = "Identificador del usuario")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycAreaUsuario usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Nombre completo del usuario (primer nombre y primer apellido)
   * @return usuario
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre completo del usuario (primer nombre y primer apellido)")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycAreaUsuario pycAreaUsuario = (PycAreaUsuario) o;
    return Objects.equals(this.idUsuario, pycAreaUsuario.idUsuario) &&
        Objects.equals(this.usuario, pycAreaUsuario.usuario);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, usuario);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycAreaUsuario {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

