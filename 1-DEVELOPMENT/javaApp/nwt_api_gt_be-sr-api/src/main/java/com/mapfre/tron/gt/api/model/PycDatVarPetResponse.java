package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycDatVarPetResponse
 */
@Validated

public class PycDatVarPetResponse   {
  @JsonProperty("idRegistro")
  private Integer idRegistro = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  @JsonProperty("ejecutado")
  private Boolean ejecutado = null;

  public PycDatVarPetResponse idRegistro(Integer idRegistro) {
    this.idRegistro = idRegistro;
    return this;
  }

  /**
   * ID del registro creado o actualizado
   * @return idRegistro
  **/
  @ApiModelProperty(example = "5001", value = "ID del registro creado o actualizado")


  public Integer getIdRegistro() {
    return idRegistro;
  }

  public void setIdRegistro(Integer idRegistro) {
    this.idRegistro = idRegistro;
  }

  public PycDatVarPetResponse mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje de confirmación de la operación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Dato variable de petición guardado exitosamente", value = "Mensaje de confirmación de la operación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }

  public PycDatVarPetResponse ejecutado(Boolean ejecutado) {
    this.ejecutado = ejecutado;
    return this;
  }

  /**
   * Indica si la operación se ejecutó correctamente
   * @return ejecutado
  **/
  @ApiModelProperty(example = "true", value = "Indica si la operación se ejecutó correctamente")


  public Boolean isEjecutado() {
    return ejecutado;
  }

  public void setEjecutado(Boolean ejecutado) {
    this.ejecutado = ejecutado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycDatVarPetResponse pycDatVarPetResponse = (PycDatVarPetResponse) o;
    return Objects.equals(this.idRegistro, pycDatVarPetResponse.idRegistro) &&
        Objects.equals(this.mensaje, pycDatVarPetResponse.mensaje) &&
        Objects.equals(this.ejecutado, pycDatVarPetResponse.ejecutado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idRegistro, mensaje, ejecutado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycDatVarPetResponse {\n");
    
    sb.append("    idRegistro: ").append(toIndentedString(idRegistro)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("    ejecutado: ").append(toIndentedString(ejecutado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

