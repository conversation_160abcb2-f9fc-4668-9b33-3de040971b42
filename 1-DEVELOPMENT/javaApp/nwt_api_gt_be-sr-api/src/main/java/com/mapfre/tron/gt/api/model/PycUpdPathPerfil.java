package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUpdPathPerfil
 */
@Validated

public class PycUpdPathPerfil   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("urlPerfil")
  private String urlPerfil = null;

  @JsonProperty("pathPerfil")
  private String pathPerfil = null;

  public PycUpdPathPerfil idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * ID del usuario al que se le actualizará el perfil
   * @return idUsuario
  **/
  @ApiModelProperty(example = "123", required = true, value = "ID del usuario al que se le actualizará el perfil")
  @NotNull


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycUpdPathPerfil urlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
    return this;
  }

  /**
   * Nueva URL del perfil del usuario
   * @return urlPerfil
  **/
  @ApiModelProperty(example = "https://app.mapfre.com/perfil/usuario123", required = true, value = "Nueva URL del perfil del usuario")
  @NotNull


  public String getUrlPerfil() {
    return urlPerfil;
  }

  public void setUrlPerfil(String urlPerfil) {
    this.urlPerfil = urlPerfil;
  }

  public PycUpdPathPerfil pathPerfil(String pathPerfil) {
    this.pathPerfil = pathPerfil;
    return this;
  }

  /**
   * Nuevo PATH del perfil del usuario
   * @return pathPerfil
  **/
  @ApiModelProperty(example = "/app/perfil/usuario123", required = true, value = "Nuevo PATH del perfil del usuario")
  @NotNull


  public String getPathPerfil() {
    return pathPerfil;
  }

  public void setPathPerfil(String pathPerfil) {
    this.pathPerfil = pathPerfil;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUpdPathPerfil pycUpdPathPerfil = (PycUpdPathPerfil) o;
    return Objects.equals(this.idUsuario, pycUpdPathPerfil.idUsuario) &&
        Objects.equals(this.urlPerfil, pycUpdPathPerfil.urlPerfil) &&
        Objects.equals(this.pathPerfil, pycUpdPathPerfil.pathPerfil);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, urlPerfil, pathPerfil);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUpdPathPerfil {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    urlPerfil: ").append(toIndentedString(urlPerfil)).append("\n");
    sb.append("    pathPerfil: ").append(toIndentedString(pathPerfil)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

