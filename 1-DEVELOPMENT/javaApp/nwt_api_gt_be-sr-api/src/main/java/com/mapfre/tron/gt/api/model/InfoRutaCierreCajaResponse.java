package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * InfoRutaCierreCajaResponse
 */
@Validated

public class InfoRutaCierreCajaResponse   {
  @JsonProperty("ruta")
  private Integer ruta = null;

  @JsonProperty("fecha")
  private String fecha = null;

  @JsonProperty("cantidad")
  private Double cantidad = null;

  @JsonProperty("cantidadQ")
  private Double cantidadQ = null;

  @JsonProperty("cantidadD")
  private Double cantidadD = null;

  public InfoRutaCierreCajaResponse ruta(Integer ruta) {
    this.ruta = ruta;
    return this;
  }

  /**
   * Id  de la ruta
   * @return ruta
  **/
  @ApiModelProperty(example = "4", value = "Id  de la ruta")


  public Integer getRuta() {
    return ruta;
  }

  public void setRuta(Integer ruta) {
    this.ruta = ruta;
  }

  public InfoRutaCierreCajaResponse fecha(String fecha) {
    this.fecha = fecha;
    return this;
  }

  /**
   * Fecha
   * @return fecha
  **/
  @ApiModelProperty(example = "16-12-2020 03:52 PM", value = "Fecha")


  public String getFecha() {
    return fecha;
  }

  public void setFecha(String fecha) {
    this.fecha = fecha;
  }

  public InfoRutaCierreCajaResponse cantidad(Double cantidad) {
    this.cantidad = cantidad;
    return this;
  }

  /**
   * Cantidad
   * @return cantidad
  **/
  @ApiModelProperty(example = "45960.33", value = "Cantidad")


  public Double getCantidad() {
    return cantidad;
  }

  public void setCantidad(Double cantidad) {
    this.cantidad = cantidad;
  }

  public InfoRutaCierreCajaResponse cantidadQ(Double cantidadQ) {
    this.cantidadQ = cantidadQ;
    return this;
  }

  /**
   * CantidadQ
   * @return cantidadQ
  **/
  @ApiModelProperty(example = "45960.33", value = "CantidadQ")


  public Double getCantidadQ() {
    return cantidadQ;
  }

  public void setCantidadQ(Double cantidadQ) {
    this.cantidadQ = cantidadQ;
  }

  public InfoRutaCierreCajaResponse cantidadD(Double cantidadD) {
    this.cantidadD = cantidadD;
    return this;
  }

  /**
   * CantidadD
   * @return cantidadD
  **/
  @ApiModelProperty(example = "45960.33", value = "CantidadD")


  public Double getCantidadD() {
    return cantidadD;
  }

  public void setCantidadD(Double cantidadD) {
    this.cantidadD = cantidadD;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InfoRutaCierreCajaResponse infoRutaCierreCajaResponse = (InfoRutaCierreCajaResponse) o;
    return Objects.equals(this.ruta, infoRutaCierreCajaResponse.ruta) &&
        Objects.equals(this.fecha, infoRutaCierreCajaResponse.fecha) &&
        Objects.equals(this.cantidad, infoRutaCierreCajaResponse.cantidad) &&
        Objects.equals(this.cantidadQ, infoRutaCierreCajaResponse.cantidadQ) &&
        Objects.equals(this.cantidadD, infoRutaCierreCajaResponse.cantidadD);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ruta, fecha, cantidad, cantidadQ, cantidadD);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InfoRutaCierreCajaResponse {\n");
    
    sb.append("    ruta: ").append(toIndentedString(ruta)).append("\n");
    sb.append("    fecha: ").append(toIndentedString(fecha)).append("\n");
    sb.append("    cantidad: ").append(toIndentedString(cantidad)).append("\n");
    sb.append("    cantidadQ: ").append(toIndentedString(cantidadQ)).append("\n");
    sb.append("    cantidadD: ").append(toIndentedString(cantidadD)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

