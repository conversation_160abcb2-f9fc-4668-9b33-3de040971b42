package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * LlenarDetalleCierreCajaResponse
 */
@Validated

public class LlenarDetalleCierreCajaResponse   {
  @JsonProperty("ruta")
  private Integer ruta = null;

  @JsonProperty("tipoPago")
  private String tipoPago = null;

  @JsonProperty("cantidad")
  private Double cantidad = null;

  @JsonProperty("moneda")
  private String moneda = null;

  public LlenarDetalleCierreCajaResponse ruta(Integer ruta) {
    this.ruta = ruta;
    return this;
  }

  /**
   * Id  de la ruta
   * @return ruta
  **/
  @ApiModelProperty(example = "4", value = "Id  de la ruta")


  public Integer getRuta() {
    return ruta;
  }

  public void setRuta(Integer ruta) {
    this.ruta = ruta;
  }

  public LlenarDetalleCierreCajaResponse tipoPago(String tipoPago) {
    this.tipoPago = tipoPago;
    return this;
  }

  /**
   * tipo de pago
   * @return tipoPago
  **/
  @ApiModelProperty(example = "EFECTIVO", value = "tipo de pago")


  public String getTipoPago() {
    return tipoPago;
  }

  public void setTipoPago(String tipoPago) {
    this.tipoPago = tipoPago;
  }

  public LlenarDetalleCierreCajaResponse cantidad(Double cantidad) {
    this.cantidad = cantidad;
    return this;
  }

  /**
   * Cantidad
   * @return cantidad
  **/
  @ApiModelProperty(example = "45960.33", value = "Cantidad")


  public Double getCantidad() {
    return cantidad;
  }

  public void setCantidad(Double cantidad) {
    this.cantidad = cantidad;
  }

  public LlenarDetalleCierreCajaResponse moneda(String moneda) {
    this.moneda = moneda;
    return this;
  }

  /**
   * Moneda
   * @return moneda
  **/
  @ApiModelProperty(example = "Q", value = "Moneda")


  public String getMoneda() {
    return moneda;
  }

  public void setMoneda(String moneda) {
    this.moneda = moneda;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LlenarDetalleCierreCajaResponse llenarDetalleCierreCajaResponse = (LlenarDetalleCierreCajaResponse) o;
    return Objects.equals(this.ruta, llenarDetalleCierreCajaResponse.ruta) &&
        Objects.equals(this.tipoPago, llenarDetalleCierreCajaResponse.tipoPago) &&
        Objects.equals(this.cantidad, llenarDetalleCierreCajaResponse.cantidad) &&
        Objects.equals(this.moneda, llenarDetalleCierreCajaResponse.moneda);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ruta, tipoPago, cantidad, moneda);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LlenarDetalleCierreCajaResponse {\n");
    
    sb.append("    ruta: ").append(toIndentedString(ruta)).append("\n");
    sb.append("    tipoPago: ").append(toIndentedString(tipoPago)).append("\n");
    sb.append("    cantidad: ").append(toIndentedString(cantidad)).append("\n");
    sb.append("    moneda: ").append(toIndentedString(moneda)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

