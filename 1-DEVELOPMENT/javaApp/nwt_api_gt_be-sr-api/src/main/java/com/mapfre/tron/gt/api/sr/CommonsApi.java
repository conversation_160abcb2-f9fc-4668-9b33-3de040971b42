/**
 * NOTE: This class is auto generated by the swagger code generator program (2.4.14).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.mapfre.tron.gt.api.sr;

import com.mapfre.tron.gt.api.model.CargaArchivoRequest;
import com.mapfre.tron.gt.api.model.CargaArchivoResponse;
import com.mapfre.tron.gt.api.model.Error;
import com.mapfre.tron.gt.api.model.Message;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Api(value = "commons", description = "the commons API")
@RequestMapping(value = "/newtron/api")
public interface CommonsApi {

    Logger log = LoggerFactory.getLogger(CommonsApi.class);

    default Optional<ObjectMapper> getObjectMapper() {
        return Optional.empty();
    }

    default Optional<HttpServletRequest> getRequest() {
        return Optional.empty();
    }

    default Optional<String> getAcceptHeader() {
        return getRequest().map(r -> r.getHeader("Accept"));
    }

    @ApiOperation(value = "Endpoint actualizacion tasa de cambio", nickname = "actualizacionDeTasaDeCambio", notes = "", response = Message.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Commons", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "La tasa de cambio se ha actualizado correctamente", response = Message.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class) })
    @RequestMapping(value = "/commons/tasa_cambio",
        produces = { "application/json" }, 
        method = RequestMethod.POST)
    default ResponseEntity<Message> actualizacionDeTasaDeCambio() {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"code\" : \"000\",  \"title\" : \"Operation successfully completed\",  \"message\" : \"Description of the operation performed\",  \"type\" : \"success\"}", Message.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CommonsApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }


    @ApiOperation(value = "Endpoint para carga de archivos via SOAP", nickname = "cargaDeArchivos", notes = "", response = CargaArchivoResponse.class, authorizations = {
        @Authorization(value = "basicAuth")
    }, tags={ "Commons", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Archivos cargados correctamente", response = CargaArchivoResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/commons/carga_archivos",
        produces = { "application/json" }, 
        consumes = { "application/json" },
        method = RequestMethod.POST)
    default ResponseEntity<CargaArchivoResponse> cargaDeArchivos(@ApiParam(value = "Datos para la carga de archivos" ,required=true )  @Valid @RequestBody CargaArchivoRequest request) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"codigo\" : \"200\",  \"archivos\" : [ {    \"estado\" : \"SUCCESS\",    \"ruta\" : \"/doc_pycges_tecnicos/archivo_123.pdf\",    \"error\" : \"Error al subir archivo\",    \"nombre\" : \"documento.pdf\"  }, {    \"estado\" : \"SUCCESS\",    \"ruta\" : \"/doc_pycges_tecnicos/archivo_123.pdf\",    \"error\" : \"Error al subir archivo\",    \"nombre\" : \"documento.pdf\"  } ],  \"mensaje\" : \"Archivos cargados exitosamente\"}", CargaArchivoResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default CommonsApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

}
