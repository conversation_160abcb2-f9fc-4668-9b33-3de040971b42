package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycActPeticion
 */
@Validated

public class PycActPeticion   {
  @JsonProperty("idActividad")
  private Integer idActividad = null;

  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("nombreActividad")
  private String nombreActividad = null;

  @JsonProperty("descripcionActividad")
  private String descripcionActividad = null;

  @JsonProperty("horasBase")
  private Double horasBase = null;

  @JsonProperty("horasReal")
  private Double horasReal = null;

  @JsonProperty("horasPendientes")
  private Double horasPendientes = null;

  @JsonProperty("fechaInicio")
  private String fechaInicio = null;

  @JsonProperty("fechaFin")
  private String fechaFin = null;

  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("idCategoriaTablero")
  private Integer idCategoriaTablero = null;

  @JsonProperty("nombre")
  private String nombre = null;

  @JsonProperty("asignadoA")
  private String asignadoA = null;

  @JsonProperty("perfilAsignado")
  private String perfilAsignado = null;

  public PycActPeticion idActividad(Integer idActividad) {
    this.idActividad = idActividad;
    return this;
  }

  /**
   * Identificador de la actividad
   * @return idActividad
  **/
  @ApiModelProperty(example = "1", value = "Identificador de la actividad")


  public Integer getIdActividad() {
    return idActividad;
  }

  public void setIdActividad(Integer idActividad) {
    this.idActividad = idActividad;
  }

  public PycActPeticion idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "123", value = "Identificador de la petición")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycActPeticion nombreActividad(String nombreActividad) {
    this.nombreActividad = nombreActividad;
    return this;
  }

  /**
   * Nombre de la actividad
   * @return nombreActividad
  **/
  @ApiModelProperty(example = "Análisis de requerimientos", value = "Nombre de la actividad")


  public String getNombreActividad() {
    return nombreActividad;
  }

  public void setNombreActividad(String nombreActividad) {
    this.nombreActividad = nombreActividad;
  }

  public PycActPeticion descripcionActividad(String descripcionActividad) {
    this.descripcionActividad = descripcionActividad;
    return this;
  }

  /**
   * Descripción detallada de la actividad
   * @return descripcionActividad
  **/
  @ApiModelProperty(example = "Análisis detallado de los requerimientos del cliente", value = "Descripción detallada de la actividad")


  public String getDescripcionActividad() {
    return descripcionActividad;
  }

  public void setDescripcionActividad(String descripcionActividad) {
    this.descripcionActividad = descripcionActividad;
  }

  public PycActPeticion horasBase(Double horasBase) {
    this.horasBase = horasBase;
    return this;
  }

  /**
   * Horas base estimadas para la actividad
   * @return horasBase
  **/
  @ApiModelProperty(example = "40.0", value = "Horas base estimadas para la actividad")


  public Double getHorasBase() {
    return horasBase;
  }

  public void setHorasBase(Double horasBase) {
    this.horasBase = horasBase;
  }

  public PycActPeticion horasReal(Double horasReal) {
    this.horasReal = horasReal;
    return this;
  }

  /**
   * Horas reales trabajadas en la actividad
   * @return horasReal
  **/
  @ApiModelProperty(example = "35.5", value = "Horas reales trabajadas en la actividad")


  public Double getHorasReal() {
    return horasReal;
  }

  public void setHorasReal(Double horasReal) {
    this.horasReal = horasReal;
  }

  public PycActPeticion horasPendientes(Double horasPendientes) {
    this.horasPendientes = horasPendientes;
    return this;
  }

  /**
   * Horas pendientes por trabajar (horasBase - horasReal)
   * @return horasPendientes
  **/
  @ApiModelProperty(example = "4.5", value = "Horas pendientes por trabajar (horasBase - horasReal)")


  public Double getHorasPendientes() {
    return horasPendientes;
  }

  public void setHorasPendientes(Double horasPendientes) {
    this.horasPendientes = horasPendientes;
  }

  public PycActPeticion fechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
    return this;
  }

  /**
   * Fecha de inicio de la actividad (formato DD/MM/YYYY)
   * @return fechaInicio
  **/
  @ApiModelProperty(example = "15/01/2025", value = "Fecha de inicio de la actividad (formato DD/MM/YYYY)")


  public String getFechaInicio() {
    return fechaInicio;
  }

  public void setFechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
  }

  public PycActPeticion fechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
    return this;
  }

  /**
   * Fecha de fin de la actividad (formato DD/MM/YYYY)
   * @return fechaFin
  **/
  @ApiModelProperty(example = "25/01/2025", value = "Fecha de fin de la actividad (formato DD/MM/YYYY)")


  public String getFechaFin() {
    return fechaFin;
  }

  public void setFechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
  }

  public PycActPeticion idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Identificador del usuario responsable
   * @return idUsuario
  **/
  @ApiModelProperty(example = "456", value = "Identificador del usuario responsable")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycActPeticion estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado actual de la actividad
   * @return estado
  **/
  @ApiModelProperty(example = "EN_PROGRESO", value = "Estado actual de la actividad")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycActPeticion idCategoriaTablero(Integer idCategoriaTablero) {
    this.idCategoriaTablero = idCategoriaTablero;
    return this;
  }

  /**
   * Identificador de la categoría del tablero
   * @return idCategoriaTablero
  **/
  @ApiModelProperty(example = "2", value = "Identificador de la categoría del tablero")


  public Integer getIdCategoriaTablero() {
    return idCategoriaTablero;
  }

  public void setIdCategoriaTablero(Integer idCategoriaTablero) {
    this.idCategoriaTablero = idCategoriaTablero;
  }

  public PycActPeticion nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre de la categoría del tablero
   * @return nombre
  **/
  @ApiModelProperty(example = "Desarrollo", value = "Nombre de la categoría del tablero")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public PycActPeticion asignadoA(String asignadoA) {
    this.asignadoA = asignadoA;
    return this;
  }

  /**
   * Nombre completo del usuario asignado a la actividad
   * @return asignadoA
  **/
  @ApiModelProperty(example = "Juan Carlos Pérez", value = "Nombre completo del usuario asignado a la actividad")


  public String getAsignadoA() {
    return asignadoA;
  }

  public void setAsignadoA(String asignadoA) {
    this.asignadoA = asignadoA;
  }

  public PycActPeticion perfilAsignado(String perfilAsignado) {
    this.perfilAsignado = perfilAsignado;
    return this;
  }

  /**
   * URL del perfil del usuario asignado
   * @return perfilAsignado
  **/
  @ApiModelProperty(example = "/images/profiles/user_male.png", value = "URL del perfil del usuario asignado")


  public String getPerfilAsignado() {
    return perfilAsignado;
  }

  public void setPerfilAsignado(String perfilAsignado) {
    this.perfilAsignado = perfilAsignado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycActPeticion pycActPeticion = (PycActPeticion) o;
    return Objects.equals(this.idActividad, pycActPeticion.idActividad) &&
        Objects.equals(this.idPeticion, pycActPeticion.idPeticion) &&
        Objects.equals(this.nombreActividad, pycActPeticion.nombreActividad) &&
        Objects.equals(this.descripcionActividad, pycActPeticion.descripcionActividad) &&
        Objects.equals(this.horasBase, pycActPeticion.horasBase) &&
        Objects.equals(this.horasReal, pycActPeticion.horasReal) &&
        Objects.equals(this.horasPendientes, pycActPeticion.horasPendientes) &&
        Objects.equals(this.fechaInicio, pycActPeticion.fechaInicio) &&
        Objects.equals(this.fechaFin, pycActPeticion.fechaFin) &&
        Objects.equals(this.idUsuario, pycActPeticion.idUsuario) &&
        Objects.equals(this.estado, pycActPeticion.estado) &&
        Objects.equals(this.idCategoriaTablero, pycActPeticion.idCategoriaTablero) &&
        Objects.equals(this.nombre, pycActPeticion.nombre) &&
        Objects.equals(this.asignadoA, pycActPeticion.asignadoA) &&
        Objects.equals(this.perfilAsignado, pycActPeticion.perfilAsignado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idActividad, idPeticion, nombreActividad, descripcionActividad, horasBase, horasReal, horasPendientes, fechaInicio, fechaFin, idUsuario, estado, idCategoriaTablero, nombre, asignadoA, perfilAsignado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycActPeticion {\n");
    
    sb.append("    idActividad: ").append(toIndentedString(idActividad)).append("\n");
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    nombreActividad: ").append(toIndentedString(nombreActividad)).append("\n");
    sb.append("    descripcionActividad: ").append(toIndentedString(descripcionActividad)).append("\n");
    sb.append("    horasBase: ").append(toIndentedString(horasBase)).append("\n");
    sb.append("    horasReal: ").append(toIndentedString(horasReal)).append("\n");
    sb.append("    horasPendientes: ").append(toIndentedString(horasPendientes)).append("\n");
    sb.append("    fechaInicio: ").append(toIndentedString(fechaInicio)).append("\n");
    sb.append("    fechaFin: ").append(toIndentedString(fechaFin)).append("\n");
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    idCategoriaTablero: ").append(toIndentedString(idCategoriaTablero)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    asignadoA: ").append(toIndentedString(asignadoA)).append("\n");
    sb.append("    perfilAsignado: ").append(toIndentedString(perfilAsignado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

