package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * TasaCambio
 */
@Validated

public class TasaCambio   {
  @JsonProperty("code")
  private String code = null;

  @JsonProperty("tasaCambio")
  private Integer tasaCambio = null;

  public TasaCambio code(String code) {
    this.code = code;
    return this;
  }

  /**
   * A code that represents the result of the operation.
   * @return code
  **/
  @ApiModelProperty(example = "200", value = "A code that represents the result of the operation.")


  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public TasaCambio tasaCambio(Integer tasaCambio) {
    this.tasaCambio = tasaCambio;
    return this;
  }

  /**
   * Monto equivalente de tasa cambio
   * @return tasaCambio
  **/
  @ApiModelProperty(example = "7.75", value = "Monto equivalente de tasa cambio")


  public Integer getTasaCambio() {
    return tasaCambio;
  }

  public void setTasaCambio(Integer tasaCambio) {
    this.tasaCambio = tasaCambio;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TasaCambio tasaCambio = (TasaCambio) o;
    return Objects.equals(this.code, tasaCambio.code) &&
        Objects.equals(this.tasaCambio, tasaCambio.tasaCambio);
  }

  @Override
  public int hashCode() {
    return Objects.hash(code, tasaCambio);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TasaCambio {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    tasaCambio: ").append(toIndentedString(tasaCambio)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

