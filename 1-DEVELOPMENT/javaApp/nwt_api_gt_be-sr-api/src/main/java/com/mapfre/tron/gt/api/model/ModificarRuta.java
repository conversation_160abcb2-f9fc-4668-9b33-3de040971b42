package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ModificarRuta
 */
@Validated

public class ModificarRuta   {
  @JsonProperty("idRutaCreada")
  private Integer idRutaCreada = null;

  @JsonProperty("codigo")
  private String codigo = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  public ModificarRuta idRutaCreada(Integer idRutaCreada) {
    this.idRutaCreada = idRutaCreada;
    return this;
  }

  /**
   * Id de ruta modificada
   * @return idRutaCreada
  **/
  @ApiModelProperty(example = "1", value = "Id de ruta modificada")


  public Integer getIdRutaCreada() {
    return idRutaCreada;
  }

  public void setIdRutaCreada(Integer idRutaCreada) {
    this.idRutaCreada = idRutaCreada;
  }

  public ModificarRuta codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Código de resultado de la operación
   * @return codigo
  **/
  @ApiModelProperty(example = "200", value = "Código de resultado de la operación")


  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public ModificarRuta mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje descriptivo del resultado de la operación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Operación exitosa", value = "Mensaje descriptivo del resultado de la operación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ModificarRuta modificarRuta = (ModificarRuta) o;
    return Objects.equals(this.idRutaCreada, modificarRuta.idRutaCreada) &&
        Objects.equals(this.codigo, modificarRuta.codigo) &&
        Objects.equals(this.mensaje, modificarRuta.mensaje);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idRutaCreada, codigo, mensaje);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ModificarRuta {\n");
    
    sb.append("    idRutaCreada: ").append(toIndentedString(idRutaCreada)).append("\n");
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

