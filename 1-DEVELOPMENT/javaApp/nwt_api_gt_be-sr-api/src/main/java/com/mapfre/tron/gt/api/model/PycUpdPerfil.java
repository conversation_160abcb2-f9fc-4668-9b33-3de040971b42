package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUpdPerfil
 */
@Validated

public class PycUpdPerfil   {
  @JsonProperty("idPerfil")
  private Integer idPerfil = null;

  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("indDefault")
  private String indDefault = null;

  @JsonProperty("multiperfil")
  private String multiperfil = null;

  @JsonProperty("estado")
  private String estado = null;

  public PycUpdPerfil idPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
    return this;
  }

  /**
   * Identificador del perfil
   * @return idPerfil
  **/
  @ApiModelProperty(example = "1", required = true, value = "Identificador del perfil")
  @NotNull


  public Integer getIdPerfil() {
    return idPerfil;
  }

  public void setIdPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
  }

  public PycUpdPerfil idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Identificador del usuario
   * @return idUsuario
  **/
  @ApiModelProperty(example = "1234", required = true, value = "Identificador del usuario")
  @NotNull


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycUpdPerfil indDefault(String indDefault) {
    this.indDefault = indDefault;
    return this;
  }

  /**
   * Indicador si es el perfil por defecto
   * @return indDefault
  **/
  @ApiModelProperty(example = "S", value = "Indicador si es el perfil por defecto")


  public String getIndDefault() {
    return indDefault;
  }

  public void setIndDefault(String indDefault) {
    this.indDefault = indDefault;
  }

  public PycUpdPerfil multiperfil(String multiperfil) {
    this.multiperfil = multiperfil;
    return this;
  }

  /**
   * Indicador si el usuario puede tener múltiples perfiles
   * @return multiperfil
  **/
  @ApiModelProperty(example = "S", value = "Indicador si el usuario puede tener múltiples perfiles")


  public String getMultiperfil() {
    return multiperfil;
  }

  public void setMultiperfil(String multiperfil) {
    this.multiperfil = multiperfil;
  }

  public PycUpdPerfil estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado del perfil para el usuario
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", value = "Estado del perfil para el usuario")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUpdPerfil pycUpdPerfil = (PycUpdPerfil) o;
    return Objects.equals(this.idPerfil, pycUpdPerfil.idPerfil) &&
        Objects.equals(this.idUsuario, pycUpdPerfil.idUsuario) &&
        Objects.equals(this.indDefault, pycUpdPerfil.indDefault) &&
        Objects.equals(this.multiperfil, pycUpdPerfil.multiperfil) &&
        Objects.equals(this.estado, pycUpdPerfil.estado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPerfil, idUsuario, indDefault, multiperfil, estado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUpdPerfil {\n");
    
    sb.append("    idPerfil: ").append(toIndentedString(idPerfil)).append("\n");
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    indDefault: ").append(toIndentedString(indDefault)).append("\n");
    sb.append("    multiperfil: ").append(toIndentedString(multiperfil)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

