package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycEstadoPeticionResponse
 */
@Validated

public class PycEstadoPeticionResponse   {
  @JsonProperty("resultado")
  private Integer resultado = null;

  public PycEstadoPeticionResponse resultado(Integer resultado) {
    this.resultado = resultado;
    return this;
  }

  /**
   * Resultado de la operación de actualización de estado (ID de la petición procesada)
   * @return resultado
  **/
  @ApiModelProperty(example = "1001", value = "Resultado de la operación de actualización de estado (ID de la petición procesada)")


  public Integer getResultado() {
    return resultado;
  }

  public void setResultado(Integer resultado) {
    this.resultado = resultado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycEstadoPeticionResponse pycEstadoPeticionResponse = (PycEstadoPeticionResponse) o;
    return Objects.equals(this.resultado, pycEstadoPeticionResponse.resultado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(resultado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycEstadoPeticionResponse {\n");
    
    sb.append("    resultado: ").append(toIndentedString(resultado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

