package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.mapfre.tron.gt.api.model.AccionesPorRolesResponseAcciones;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * AccionesPorRolesResponse
 */
@Validated

public class AccionesPorRolesResponse   {
  @JsonProperty("codigo")
  private String codigo = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  @JsonProperty("acciones")
  @Valid
  private List<AccionesPorRolesResponseAcciones> acciones = null;

  public AccionesPorRolesResponse codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Código de resultado de la operación
   * @return codigo
  **/
  @ApiModelProperty(example = "200", value = "Código de resultado de la operación")


  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public AccionesPorRolesResponse mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje descriptivo del resultado de la operación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Acciones obtenidas exitosamente", value = "Mensaje descriptivo del resultado de la operación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }

  public AccionesPorRolesResponse acciones(List<AccionesPorRolesResponseAcciones> acciones) {
    this.acciones = acciones;
    return this;
  }

  public AccionesPorRolesResponse addAccionesItem(AccionesPorRolesResponseAcciones accionesItem) {
    if (this.acciones == null) {
      this.acciones = new ArrayList<>();
    }
    this.acciones.add(accionesItem);
    return this;
  }

  /**
   * Lista de acciones disponibles para los roles
   * @return acciones
  **/
  @ApiModelProperty(value = "Lista de acciones disponibles para los roles")

  @Valid

  public List<AccionesPorRolesResponseAcciones> getAcciones() {
    return acciones;
  }

  public void setAcciones(List<AccionesPorRolesResponseAcciones> acciones) {
    this.acciones = acciones;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AccionesPorRolesResponse accionesPorRolesResponse = (AccionesPorRolesResponse) o;
    return Objects.equals(this.codigo, accionesPorRolesResponse.codigo) &&
        Objects.equals(this.mensaje, accionesPorRolesResponse.mensaje) &&
        Objects.equals(this.acciones, accionesPorRolesResponse.acciones);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codigo, mensaje, acciones);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AccionesPorRolesResponse {\n");
    
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("    acciones: ").append(toIndentedString(acciones)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

