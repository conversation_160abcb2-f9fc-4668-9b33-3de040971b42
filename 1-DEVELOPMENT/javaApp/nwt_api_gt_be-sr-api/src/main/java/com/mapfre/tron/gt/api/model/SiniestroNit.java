package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * SiniestroNit
 */
@Validated

public class SiniestroNit   {
  @JsonProperty("siniestro")
  private String siniestro = null;

  @JsonProperty("poliza")
  private String poliza = null;

  public SiniestroNit siniestro(String siniestro) {
    this.siniestro = siniestro;
    return this;
  }

  /**
   * Número de siniestro
   * @return siniestro
  **/
  @ApiModelProperty(example = "300252001000007", value = "Número de siniestro")


  public String getSiniestro() {
    return siniestro;
  }

  public void setSiniestro(String siniestro) {
    this.siniestro = siniestro;
  }

  public SiniestroNit poliza(String poliza) {
    this.poliza = poliza;
    return this;
  }

  /**
   * Número de póliza
   * @return poliza
  **/
  @ApiModelProperty(example = "0230025018359", value = "Número de póliza")


  public String getPoliza() {
    return poliza;
  }

  public void setPoliza(String poliza) {
    this.poliza = poliza;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SiniestroNit siniestroNit = (SiniestroNit) o;
    return Objects.equals(this.siniestro, siniestroNit.siniestro) &&
        Objects.equals(this.poliza, siniestroNit.poliza);
  }

  @Override
  public int hashCode() {
    return Objects.hash(siniestro, poliza);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SiniestroNit {\n");
    
    sb.append("    siniestro: ").append(toIndentedString(siniestro)).append("\n");
    sb.append("    poliza: ").append(toIndentedString(poliza)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

