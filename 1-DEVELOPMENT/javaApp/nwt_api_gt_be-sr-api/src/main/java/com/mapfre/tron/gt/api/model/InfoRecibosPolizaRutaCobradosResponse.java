package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * InfoRecibosPolizaRutaCobradosResponse
 */
@Validated

public class InfoRecibosPolizaRutaCobradosResponse   {
  @JsonProperty("idRuta")
  private Integer idRuta = null;

  @JsonProperty("codPol")
  private String codPol = null;

  @JsonProperty("numPol")
  private String numPol = null;

  @JsonProperty("certificado")
  private String certificado = null;

  @JsonProperty("numeroRequerimiento")
  private String numeroRequerimiento = null;

  @JsonProperty("moneda")
  private String moneda = null;

  @JsonProperty("totalRequerimiento")
  private String totalRequerimiento = null;

  @JsonProperty("numeroCuota")
  private String numeroCuota = null;

  @JsonProperty("sistema")
  private String sistema = null;

  @JsonProperty("vencimientoRequerimiento")
  private String vencimientoRequerimiento = null;

  @JsonProperty("nombrePagador")
  private String nombrePagador = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("nomEstado")
  private String nomEstado = null;

  @JsonProperty("idPol")
  private String idPol = null;

  @JsonProperty("esAviso")
  private String esAviso = null;

  public InfoRecibosPolizaRutaCobradosResponse idRuta(Integer idRuta) {
    this.idRuta = idRuta;
    return this;
  }

  /**
   * Id  de la ruta
   * @return idRuta
  **/
  @ApiModelProperty(example = "4", value = "Id  de la ruta")


  public Integer getIdRuta() {
    return idRuta;
  }

  public void setIdRuta(Integer idRuta) {
    this.idRuta = idRuta;
  }

  public InfoRecibosPolizaRutaCobradosResponse codPol(String codPol) {
    this.codPol = codPol;
    return this;
  }

  /**
   * Codigo de la poliza
   * @return codPol
  **/
  @ApiModelProperty(example = "AUOL", value = "Codigo de la poliza")


  public String getCodPol() {
    return codPol;
  }

  public void setCodPol(String codPol) {
    this.codPol = codPol;
  }

  public InfoRecibosPolizaRutaCobradosResponse numPol(String numPol) {
    this.numPol = numPol;
    return this;
  }

  /**
   * Numero de poliza
   * @return numPol
  **/
  @ApiModelProperty(example = "17083", value = "Numero de poliza")


  public String getNumPol() {
    return numPol;
  }

  public void setNumPol(String numPol) {
    this.numPol = numPol;
  }

  public InfoRecibosPolizaRutaCobradosResponse certificado(String certificado) {
    this.certificado = certificado;
    return this;
  }

  /**
   * Certificado
   * @return certificado
  **/
  @ApiModelProperty(example = "1", value = "Certificado")


  public String getCertificado() {
    return certificado;
  }

  public void setCertificado(String certificado) {
    this.certificado = certificado;
  }

  public InfoRecibosPolizaRutaCobradosResponse numeroRequerimiento(String numeroRequerimiento) {
    this.numeroRequerimiento = numeroRequerimiento;
    return this;
  }

  /**
   * Numero requerimiento
   * @return numeroRequerimiento
  **/
  @ApiModelProperty(example = "94644", value = "Numero requerimiento")


  public String getNumeroRequerimiento() {
    return numeroRequerimiento;
  }

  public void setNumeroRequerimiento(String numeroRequerimiento) {
    this.numeroRequerimiento = numeroRequerimiento;
  }

  public InfoRecibosPolizaRutaCobradosResponse moneda(String moneda) {
    this.moneda = moneda;
    return this;
  }

  /**
   * Moneda
   * @return moneda
  **/
  @ApiModelProperty(example = "Q", value = "Moneda")


  public String getMoneda() {
    return moneda;
  }

  public void setMoneda(String moneda) {
    this.moneda = moneda;
  }

  public InfoRecibosPolizaRutaCobradosResponse totalRequerimiento(String totalRequerimiento) {
    this.totalRequerimiento = totalRequerimiento;
    return this;
  }

  /**
   * total
   * @return totalRequerimiento
  **/
  @ApiModelProperty(example = "579.76", value = "total")


  public String getTotalRequerimiento() {
    return totalRequerimiento;
  }

  public void setTotalRequerimiento(String totalRequerimiento) {
    this.totalRequerimiento = totalRequerimiento;
  }

  public InfoRecibosPolizaRutaCobradosResponse numeroCuota(String numeroCuota) {
    this.numeroCuota = numeroCuota;
    return this;
  }

  /**
   * Numero cuota
   * @return numeroCuota
  **/
  @ApiModelProperty(example = "5", value = "Numero cuota")


  public String getNumeroCuota() {
    return numeroCuota;
  }

  public void setNumeroCuota(String numeroCuota) {
    this.numeroCuota = numeroCuota;
  }

  public InfoRecibosPolizaRutaCobradosResponse sistema(String sistema) {
    this.sistema = sistema;
    return this;
  }

  /**
   * sistema
   * @return sistema
  **/
  @ApiModelProperty(example = "T", value = "sistema")


  public String getSistema() {
    return sistema;
  }

  public void setSistema(String sistema) {
    this.sistema = sistema;
  }

  public InfoRecibosPolizaRutaCobradosResponse vencimientoRequerimiento(String vencimientoRequerimiento) {
    this.vencimientoRequerimiento = vencimientoRequerimiento;
    return this;
  }

  /**
   * fecha vencimiento requerimiento
   * @return vencimientoRequerimiento
  **/
  @ApiModelProperty(example = "25/12/2020", value = "fecha vencimiento requerimiento")


  public String getVencimientoRequerimiento() {
    return vencimientoRequerimiento;
  }

  public void setVencimientoRequerimiento(String vencimientoRequerimiento) {
    this.vencimientoRequerimiento = vencimientoRequerimiento;
  }

  public InfoRecibosPolizaRutaCobradosResponse nombrePagador(String nombrePagador) {
    this.nombrePagador = nombrePagador;
    return this;
  }

  /**
   * Nombre pagador
   * @return nombrePagador
  **/
  @ApiModelProperty(example = "S. A.", value = "Nombre pagador")


  public String getNombrePagador() {
    return nombrePagador;
  }

  public void setNombrePagador(String nombrePagador) {
    this.nombrePagador = nombrePagador;
  }

  public InfoRecibosPolizaRutaCobradosResponse estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * codigo estado
   * @return estado
  **/
  @ApiModelProperty(example = "COB", value = "codigo estado")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public InfoRecibosPolizaRutaCobradosResponse nomEstado(String nomEstado) {
    this.nomEstado = nomEstado;
    return this;
  }

  /**
   * Nombre del estado
   * @return nomEstado
  **/
  @ApiModelProperty(example = "COBRADO", value = "Nombre del estado")


  public String getNomEstado() {
    return nomEstado;
  }

  public void setNomEstado(String nomEstado) {
    this.nomEstado = nomEstado;
  }

  public InfoRecibosPolizaRutaCobradosResponse idPol(String idPol) {
    this.idPol = idPol;
    return this;
  }

  /**
   * Id de polilza
   * @return idPol
  **/
  @ApiModelProperty(example = "1594513", value = "Id de polilza")


  public String getIdPol() {
    return idPol;
  }

  public void setIdPol(String idPol) {
    this.idPol = idPol;
  }

  public InfoRecibosPolizaRutaCobradosResponse esAviso(String esAviso) {
    this.esAviso = esAviso;
    return this;
  }

  /**
   * Indicador si es aviso
   * @return esAviso
  **/
  @ApiModelProperty(example = "N", value = "Indicador si es aviso")


  public String getEsAviso() {
    return esAviso;
  }

  public void setEsAviso(String esAviso) {
    this.esAviso = esAviso;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InfoRecibosPolizaRutaCobradosResponse infoRecibosPolizaRutaCobradosResponse = (InfoRecibosPolizaRutaCobradosResponse) o;
    return Objects.equals(this.idRuta, infoRecibosPolizaRutaCobradosResponse.idRuta) &&
        Objects.equals(this.codPol, infoRecibosPolizaRutaCobradosResponse.codPol) &&
        Objects.equals(this.numPol, infoRecibosPolizaRutaCobradosResponse.numPol) &&
        Objects.equals(this.certificado, infoRecibosPolizaRutaCobradosResponse.certificado) &&
        Objects.equals(this.numeroRequerimiento, infoRecibosPolizaRutaCobradosResponse.numeroRequerimiento) &&
        Objects.equals(this.moneda, infoRecibosPolizaRutaCobradosResponse.moneda) &&
        Objects.equals(this.totalRequerimiento, infoRecibosPolizaRutaCobradosResponse.totalRequerimiento) &&
        Objects.equals(this.numeroCuota, infoRecibosPolizaRutaCobradosResponse.numeroCuota) &&
        Objects.equals(this.sistema, infoRecibosPolizaRutaCobradosResponse.sistema) &&
        Objects.equals(this.vencimientoRequerimiento, infoRecibosPolizaRutaCobradosResponse.vencimientoRequerimiento) &&
        Objects.equals(this.nombrePagador, infoRecibosPolizaRutaCobradosResponse.nombrePagador) &&
        Objects.equals(this.estado, infoRecibosPolizaRutaCobradosResponse.estado) &&
        Objects.equals(this.nomEstado, infoRecibosPolizaRutaCobradosResponse.nomEstado) &&
        Objects.equals(this.idPol, infoRecibosPolizaRutaCobradosResponse.idPol) &&
        Objects.equals(this.esAviso, infoRecibosPolizaRutaCobradosResponse.esAviso);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idRuta, codPol, numPol, certificado, numeroRequerimiento, moneda, totalRequerimiento, numeroCuota, sistema, vencimientoRequerimiento, nombrePagador, estado, nomEstado, idPol, esAviso);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InfoRecibosPolizaRutaCobradosResponse {\n");
    
    sb.append("    idRuta: ").append(toIndentedString(idRuta)).append("\n");
    sb.append("    codPol: ").append(toIndentedString(codPol)).append("\n");
    sb.append("    numPol: ").append(toIndentedString(numPol)).append("\n");
    sb.append("    certificado: ").append(toIndentedString(certificado)).append("\n");
    sb.append("    numeroRequerimiento: ").append(toIndentedString(numeroRequerimiento)).append("\n");
    sb.append("    moneda: ").append(toIndentedString(moneda)).append("\n");
    sb.append("    totalRequerimiento: ").append(toIndentedString(totalRequerimiento)).append("\n");
    sb.append("    numeroCuota: ").append(toIndentedString(numeroCuota)).append("\n");
    sb.append("    sistema: ").append(toIndentedString(sistema)).append("\n");
    sb.append("    vencimientoRequerimiento: ").append(toIndentedString(vencimientoRequerimiento)).append("\n");
    sb.append("    nombrePagador: ").append(toIndentedString(nombrePagador)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    nomEstado: ").append(toIndentedString(nomEstado)).append("\n");
    sb.append("    idPol: ").append(toIndentedString(idPol)).append("\n");
    sb.append("    esAviso: ").append(toIndentedString(esAviso)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

