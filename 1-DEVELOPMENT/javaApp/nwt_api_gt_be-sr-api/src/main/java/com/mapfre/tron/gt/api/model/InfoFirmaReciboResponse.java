package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * InfoFirmaReciboResponse
 */
@Validated

public class InfoFirmaReciboResponse   {
  @JsonProperty("idDetalle")
  private Integer idDetalle = null;

  @JsonProperty("idRuta")
  private Integer idRuta = null;

  @JsonProperty("idEstado")
  private Integer idEstado = null;

  @JsonProperty("idePol")
  private String idePol = null;

  @JsonProperty("codPol")
  private String codPol = null;

  @JsonProperty("numPol")
  private String numPol = null;

  @JsonProperty("numCert")
  private String numCert = null;

  @JsonProperty("recibo")
  private String recibo = null;

  @JsonProperty("moneda")
  private String moneda = null;

  @JsonProperty("total")
  private String total = null;

  @JsonProperty("cuota")
  private String cuota = null;

  @JsonProperty("orden")
  private Integer orden = null;

  @JsonProperty("sistema")
  private String sistema = null;

  @JsonProperty("asegurado")
  private String asegurado = null;

  @JsonProperty("direccion")
  private String direccion = null;

  @JsonProperty("comentario")
  private String comentario = null;

  @JsonProperty("dirCobLatitud")
  private String dirCobLatitud = null;

  @JsonProperty("dirCobLongitud")
  private String dirCobLongitud = null;

  @JsonProperty("fechaReciVencimiento")
  private String fechaReciVencimiento = null;

  @JsonProperty("fechaReciCobro")
  private String fechaReciCobro = null;

  @JsonProperty("fechaCrea")
  private String fechaCrea = null;

  @JsonProperty("fechaModi")
  private String fechaModi = null;

  @JsonProperty("idUsuarioCobra")
  private Integer idUsuarioCobra = null;

  @JsonProperty("fechaRecordatorio")
  private String fechaRecordatorio = null;

  @JsonProperty("imgFirma")
  private String imgFirma = null;

  @JsonProperty("correo")
  private String correo = null;

  public InfoFirmaReciboResponse idDetalle(Integer idDetalle) {
    this.idDetalle = idDetalle;
    return this;
  }

  /**
   * Id  del detalle
   * @return idDetalle
  **/
  @ApiModelProperty(example = "4", value = "Id  del detalle")


  public Integer getIdDetalle() {
    return idDetalle;
  }

  public void setIdDetalle(Integer idDetalle) {
    this.idDetalle = idDetalle;
  }

  public InfoFirmaReciboResponse idRuta(Integer idRuta) {
    this.idRuta = idRuta;
    return this;
  }

  /**
   * Id  de ruta
   * @return idRuta
  **/
  @ApiModelProperty(value = "Id  de ruta")


  public Integer getIdRuta() {
    return idRuta;
  }

  public void setIdRuta(Integer idRuta) {
    this.idRuta = idRuta;
  }

  public InfoFirmaReciboResponse idEstado(Integer idEstado) {
    this.idEstado = idEstado;
    return this;
  }

  /**
   * Id  de estado
   * @return idEstado
  **/
  @ApiModelProperty(example = "4", value = "Id  de estado")


  public Integer getIdEstado() {
    return idEstado;
  }

  public void setIdEstado(Integer idEstado) {
    this.idEstado = idEstado;
  }

  public InfoFirmaReciboResponse idePol(String idePol) {
    this.idePol = idePol;
    return this;
  }

  /**
   * Codigo de la poliza
   * @return idePol
  **/
  @ApiModelProperty(example = "AUOL", value = "Codigo de la poliza")


  public String getIdePol() {
    return idePol;
  }

  public void setIdePol(String idePol) {
    this.idePol = idePol;
  }

  public InfoFirmaReciboResponse codPol(String codPol) {
    this.codPol = codPol;
    return this;
  }

  /**
   * Codigo de poliza
   * @return codPol
  **/
  @ApiModelProperty(example = "SC", value = "Codigo de poliza")


  public String getCodPol() {
    return codPol;
  }

  public void setCodPol(String codPol) {
    this.codPol = codPol;
  }

  public InfoFirmaReciboResponse numPol(String numPol) {
    this.numPol = numPol;
    return this;
  }

  /**
   * Numero de poliza
   * @return numPol
  **/
  @ApiModelProperty(example = "79628", value = "Numero de poliza")


  public String getNumPol() {
    return numPol;
  }

  public void setNumPol(String numPol) {
    this.numPol = numPol;
  }

  public InfoFirmaReciboResponse numCert(String numCert) {
    this.numCert = numCert;
    return this;
  }

  /**
   * Numero de certificado
   * @return numCert
  **/
  @ApiModelProperty(example = "1", value = "Numero de certificado")


  public String getNumCert() {
    return numCert;
  }

  public void setNumCert(String numCert) {
    this.numCert = numCert;
  }

  public InfoFirmaReciboResponse recibo(String recibo) {
    this.recibo = recibo;
    return this;
  }

  /**
   * Numero de recibo
   * @return recibo
  **/
  @ApiModelProperty(example = "443500", value = "Numero de recibo")


  public String getRecibo() {
    return recibo;
  }

  public void setRecibo(String recibo) {
    this.recibo = recibo;
  }

  public InfoFirmaReciboResponse moneda(String moneda) {
    this.moneda = moneda;
    return this;
  }

  /**
   * moneda
   * @return moneda
  **/
  @ApiModelProperty(example = "Q", value = "moneda")


  public String getMoneda() {
    return moneda;
  }

  public void setMoneda(String moneda) {
    this.moneda = moneda;
  }

  public InfoFirmaReciboResponse total(String total) {
    this.total = total;
    return this;
  }

  /**
   * Total
   * @return total
  **/
  @ApiModelProperty(example = "4337.09", value = "Total")


  public String getTotal() {
    return total;
  }

  public void setTotal(String total) {
    this.total = total;
  }

  public InfoFirmaReciboResponse cuota(String cuota) {
    this.cuota = cuota;
    return this;
  }

  /**
   * cuota
   * @return cuota
  **/
  @ApiModelProperty(example = "0/1", value = "cuota")


  public String getCuota() {
    return cuota;
  }

  public void setCuota(String cuota) {
    this.cuota = cuota;
  }

  public InfoFirmaReciboResponse orden(Integer orden) {
    this.orden = orden;
    return this;
  }

  /**
   * orden
   * @return orden
  **/
  @ApiModelProperty(example = "1", value = "orden")


  public Integer getOrden() {
    return orden;
  }

  public void setOrden(Integer orden) {
    this.orden = orden;
  }

  public InfoFirmaReciboResponse sistema(String sistema) {
    this.sistema = sistema;
    return this;
  }

  /**
   * Sistema
   * @return sistema
  **/
  @ApiModelProperty(example = "A", value = "Sistema")


  public String getSistema() {
    return sistema;
  }

  public void setSistema(String sistema) {
    this.sistema = sistema;
  }

  public InfoFirmaReciboResponse asegurado(String asegurado) {
    this.asegurado = asegurado;
    return this;
  }

  /**
   * Nombre asegurado
   * @return asegurado
  **/
  @ApiModelProperty(example = "Grupo H", value = "Nombre asegurado")


  public String getAsegurado() {
    return asegurado;
  }

  public void setAsegurado(String asegurado) {
    this.asegurado = asegurado;
  }

  public InfoFirmaReciboResponse direccion(String direccion) {
    this.direccion = direccion;
    return this;
  }

  /**
   * Direccion
   * @return direccion
  **/
  @ApiModelProperty(example = "ciudad de Guatemala", value = "Direccion")


  public String getDireccion() {
    return direccion;
  }

  public void setDireccion(String direccion) {
    this.direccion = direccion;
  }

  public InfoFirmaReciboResponse comentario(String comentario) {
    this.comentario = comentario;
    return this;
  }

  /**
   * Comentario
   * @return comentario
  **/
  @ApiModelProperty(example = "comentario", value = "Comentario")


  public String getComentario() {
    return comentario;
  }

  public void setComentario(String comentario) {
    this.comentario = comentario;
  }

  public InfoFirmaReciboResponse dirCobLatitud(String dirCobLatitud) {
    this.dirCobLatitud = dirCobLatitud;
    return this;
  }

  /**
   * latitud
   * @return dirCobLatitud
  **/
  @ApiModelProperty(example = "99993", value = "latitud")


  public String getDirCobLatitud() {
    return dirCobLatitud;
  }

  public void setDirCobLatitud(String dirCobLatitud) {
    this.dirCobLatitud = dirCobLatitud;
  }

  public InfoFirmaReciboResponse dirCobLongitud(String dirCobLongitud) {
    this.dirCobLongitud = dirCobLongitud;
    return this;
  }

  /**
   * longitud
   * @return dirCobLongitud
  **/
  @ApiModelProperty(example = "12", value = "longitud")


  public String getDirCobLongitud() {
    return dirCobLongitud;
  }

  public void setDirCobLongitud(String dirCobLongitud) {
    this.dirCobLongitud = dirCobLongitud;
  }

  public InfoFirmaReciboResponse fechaReciVencimiento(String fechaReciVencimiento) {
    this.fechaReciVencimiento = fechaReciVencimiento;
    return this;
  }

  /**
   * fecha vencimiento
   * @return fechaReciVencimiento
  **/
  @ApiModelProperty(example = "2020-11-30 00:00:00", value = "fecha vencimiento")


  public String getFechaReciVencimiento() {
    return fechaReciVencimiento;
  }

  public void setFechaReciVencimiento(String fechaReciVencimiento) {
    this.fechaReciVencimiento = fechaReciVencimiento;
  }

  public InfoFirmaReciboResponse fechaReciCobro(String fechaReciCobro) {
    this.fechaReciCobro = fechaReciCobro;
    return this;
  }

  /**
   * fecha cobro
   * @return fechaReciCobro
  **/
  @ApiModelProperty(example = "2020-11-30 00:00:00", value = "fecha cobro")


  public String getFechaReciCobro() {
    return fechaReciCobro;
  }

  public void setFechaReciCobro(String fechaReciCobro) {
    this.fechaReciCobro = fechaReciCobro;
  }

  public InfoFirmaReciboResponse fechaCrea(String fechaCrea) {
    this.fechaCrea = fechaCrea;
    return this;
  }

  /**
   * fecha crea
   * @return fechaCrea
  **/
  @ApiModelProperty(example = "2020-12-22 15:42:45", value = "fecha crea")


  public String getFechaCrea() {
    return fechaCrea;
  }

  public void setFechaCrea(String fechaCrea) {
    this.fechaCrea = fechaCrea;
  }

  public InfoFirmaReciboResponse fechaModi(String fechaModi) {
    this.fechaModi = fechaModi;
    return this;
  }

  /**
   * fecha modifica
   * @return fechaModi
  **/
  @ApiModelProperty(example = "2020-11-30 00:00:00", value = "fecha modifica")


  public String getFechaModi() {
    return fechaModi;
  }

  public void setFechaModi(String fechaModi) {
    this.fechaModi = fechaModi;
  }

  public InfoFirmaReciboResponse idUsuarioCobra(Integer idUsuarioCobra) {
    this.idUsuarioCobra = idUsuarioCobra;
    return this;
  }

  /**
   * Id usuario cobra
   * @return idUsuarioCobra
  **/
  @ApiModelProperty(example = "1", value = "Id usuario cobra")


  public Integer getIdUsuarioCobra() {
    return idUsuarioCobra;
  }

  public void setIdUsuarioCobra(Integer idUsuarioCobra) {
    this.idUsuarioCobra = idUsuarioCobra;
  }

  public InfoFirmaReciboResponse fechaRecordatorio(String fechaRecordatorio) {
    this.fechaRecordatorio = fechaRecordatorio;
    return this;
  }

  /**
   * Fecha recordatorio
   * @return fechaRecordatorio
  **/
  @ApiModelProperty(example = "2020-11-30 00:00:00", value = "Fecha recordatorio")


  public String getFechaRecordatorio() {
    return fechaRecordatorio;
  }

  public void setFechaRecordatorio(String fechaRecordatorio) {
    this.fechaRecordatorio = fechaRecordatorio;
  }

  public InfoFirmaReciboResponse imgFirma(String imgFirma) {
    this.imgFirma = imgFirma;
    return this;
  }

  /**
   * Imagen firma base 64
   * @return imgFirma
  **/
  @ApiModelProperty(example = "iVBORw0KGgoAAAANSUhEUgAAAA...", value = "Imagen firma base 64")


  public String getImgFirma() {
    return imgFirma;
  }

  public void setImgFirma(String imgFirma) {
    this.imgFirma = imgFirma;
  }

  public InfoFirmaReciboResponse correo(String correo) {
    this.correo = correo;
    return this;
  }

  /**
   * Direccion de correo
   * @return correo
  **/
  @ApiModelProperty(example = "<EMAIL>", value = "Direccion de correo")


  public String getCorreo() {
    return correo;
  }

  public void setCorreo(String correo) {
    this.correo = correo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InfoFirmaReciboResponse infoFirmaReciboResponse = (InfoFirmaReciboResponse) o;
    return Objects.equals(this.idDetalle, infoFirmaReciboResponse.idDetalle) &&
        Objects.equals(this.idRuta, infoFirmaReciboResponse.idRuta) &&
        Objects.equals(this.idEstado, infoFirmaReciboResponse.idEstado) &&
        Objects.equals(this.idePol, infoFirmaReciboResponse.idePol) &&
        Objects.equals(this.codPol, infoFirmaReciboResponse.codPol) &&
        Objects.equals(this.numPol, infoFirmaReciboResponse.numPol) &&
        Objects.equals(this.numCert, infoFirmaReciboResponse.numCert) &&
        Objects.equals(this.recibo, infoFirmaReciboResponse.recibo) &&
        Objects.equals(this.moneda, infoFirmaReciboResponse.moneda) &&
        Objects.equals(this.total, infoFirmaReciboResponse.total) &&
        Objects.equals(this.cuota, infoFirmaReciboResponse.cuota) &&
        Objects.equals(this.orden, infoFirmaReciboResponse.orden) &&
        Objects.equals(this.sistema, infoFirmaReciboResponse.sistema) &&
        Objects.equals(this.asegurado, infoFirmaReciboResponse.asegurado) &&
        Objects.equals(this.direccion, infoFirmaReciboResponse.direccion) &&
        Objects.equals(this.comentario, infoFirmaReciboResponse.comentario) &&
        Objects.equals(this.dirCobLatitud, infoFirmaReciboResponse.dirCobLatitud) &&
        Objects.equals(this.dirCobLongitud, infoFirmaReciboResponse.dirCobLongitud) &&
        Objects.equals(this.fechaReciVencimiento, infoFirmaReciboResponse.fechaReciVencimiento) &&
        Objects.equals(this.fechaReciCobro, infoFirmaReciboResponse.fechaReciCobro) &&
        Objects.equals(this.fechaCrea, infoFirmaReciboResponse.fechaCrea) &&
        Objects.equals(this.fechaModi, infoFirmaReciboResponse.fechaModi) &&
        Objects.equals(this.idUsuarioCobra, infoFirmaReciboResponse.idUsuarioCobra) &&
        Objects.equals(this.fechaRecordatorio, infoFirmaReciboResponse.fechaRecordatorio) &&
        Objects.equals(this.imgFirma, infoFirmaReciboResponse.imgFirma) &&
        Objects.equals(this.correo, infoFirmaReciboResponse.correo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idDetalle, idRuta, idEstado, idePol, codPol, numPol, numCert, recibo, moneda, total, cuota, orden, sistema, asegurado, direccion, comentario, dirCobLatitud, dirCobLongitud, fechaReciVencimiento, fechaReciCobro, fechaCrea, fechaModi, idUsuarioCobra, fechaRecordatorio, imgFirma, correo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InfoFirmaReciboResponse {\n");
    
    sb.append("    idDetalle: ").append(toIndentedString(idDetalle)).append("\n");
    sb.append("    idRuta: ").append(toIndentedString(idRuta)).append("\n");
    sb.append("    idEstado: ").append(toIndentedString(idEstado)).append("\n");
    sb.append("    idePol: ").append(toIndentedString(idePol)).append("\n");
    sb.append("    codPol: ").append(toIndentedString(codPol)).append("\n");
    sb.append("    numPol: ").append(toIndentedString(numPol)).append("\n");
    sb.append("    numCert: ").append(toIndentedString(numCert)).append("\n");
    sb.append("    recibo: ").append(toIndentedString(recibo)).append("\n");
    sb.append("    moneda: ").append(toIndentedString(moneda)).append("\n");
    sb.append("    total: ").append(toIndentedString(total)).append("\n");
    sb.append("    cuota: ").append(toIndentedString(cuota)).append("\n");
    sb.append("    orden: ").append(toIndentedString(orden)).append("\n");
    sb.append("    sistema: ").append(toIndentedString(sistema)).append("\n");
    sb.append("    asegurado: ").append(toIndentedString(asegurado)).append("\n");
    sb.append("    direccion: ").append(toIndentedString(direccion)).append("\n");
    sb.append("    comentario: ").append(toIndentedString(comentario)).append("\n");
    sb.append("    dirCobLatitud: ").append(toIndentedString(dirCobLatitud)).append("\n");
    sb.append("    dirCobLongitud: ").append(toIndentedString(dirCobLongitud)).append("\n");
    sb.append("    fechaReciVencimiento: ").append(toIndentedString(fechaReciVencimiento)).append("\n");
    sb.append("    fechaReciCobro: ").append(toIndentedString(fechaReciCobro)).append("\n");
    sb.append("    fechaCrea: ").append(toIndentedString(fechaCrea)).append("\n");
    sb.append("    fechaModi: ").append(toIndentedString(fechaModi)).append("\n");
    sb.append("    idUsuarioCobra: ").append(toIndentedString(idUsuarioCobra)).append("\n");
    sb.append("    fechaRecordatorio: ").append(toIndentedString(fechaRecordatorio)).append("\n");
    sb.append("    imgFirma: ").append(toIndentedString(imgFirma)).append("\n");
    sb.append("    correo: ").append(toIndentedString(correo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

