package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * InfoRutaResponse
 */
@Validated

public class InfoRutaResponse   {
  @JsonProperty("idRuta")
  private Integer idRuta = null;

  @JsonProperty("cobrador")
  private String cobrador = null;

  @JsonProperty("cobradorImg")
  private String cobradorImg = null;

  @JsonProperty("fecha")
  private String fecha = null;

  @JsonProperty("estadoCod")
  private String estadoCod = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("total")
  private String total = null;

  public InfoRutaResponse idRuta(Integer idRuta) {
    this.idRuta = idRuta;
    return this;
  }

  /**
   * Id  de la ruta
   * @return idRuta
  **/
  @ApiModelProperty(example = "4", value = "Id  de la ruta")


  public Integer getIdRuta() {
    return idRuta;
  }

  public void setIdRuta(Integer idRuta) {
    this.idRuta = idRuta;
  }

  public InfoRutaResponse cobrador(String cobrador) {
    this.cobrador = cobrador;
    return this;
  }

  /**
   * Nombre cobrador
   * @return cobrador
  **/
  @ApiModelProperty(example = "JUAN", value = "Nombre cobrador")


  public String getCobrador() {
    return cobrador;
  }

  public void setCobrador(String cobrador) {
    this.cobrador = cobrador;
  }

  public InfoRutaResponse cobradorImg(String cobradorImg) {
    this.cobradorImg = cobradorImg;
    return this;
  }

  /**
   * Imagen cobrador
   * @return cobradorImg
  **/
  @ApiModelProperty(example = "JUAN.png", value = "Imagen cobrador")


  public String getCobradorImg() {
    return cobradorImg;
  }

  public void setCobradorImg(String cobradorImg) {
    this.cobradorImg = cobradorImg;
  }

  public InfoRutaResponse fecha(String fecha) {
    this.fecha = fecha;
    return this;
  }

  /**
   * Fecha
   * @return fecha
  **/
  @ApiModelProperty(example = "16-12-2020 03:52 PM", value = "Fecha")


  public String getFecha() {
    return fecha;
  }

  public void setFecha(String fecha) {
    this.fecha = fecha;
  }

  public InfoRutaResponse estadoCod(String estadoCod) {
    this.estadoCod = estadoCod;
    return this;
  }

  /**
   * codigo de estado
   * @return estadoCod
  **/
  @ApiModelProperty(example = "PEN", value = "codigo de estado")


  public String getEstadoCod() {
    return estadoCod;
  }

  public void setEstadoCod(String estadoCod) {
    this.estadoCod = estadoCod;
  }

  public InfoRutaResponse estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Nombre estado
   * @return estado
  **/
  @ApiModelProperty(example = "PENDIENTE", value = "Nombre estado")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public InfoRutaResponse total(String total) {
    this.total = total;
    return this;
  }

  /**
   * total
   * @return total
  **/
  @ApiModelProperty(example = "45960.33", value = "total")


  public String getTotal() {
    return total;
  }

  public void setTotal(String total) {
    this.total = total;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InfoRutaResponse infoRutaResponse = (InfoRutaResponse) o;
    return Objects.equals(this.idRuta, infoRutaResponse.idRuta) &&
        Objects.equals(this.cobrador, infoRutaResponse.cobrador) &&
        Objects.equals(this.cobradorImg, infoRutaResponse.cobradorImg) &&
        Objects.equals(this.fecha, infoRutaResponse.fecha) &&
        Objects.equals(this.estadoCod, infoRutaResponse.estadoCod) &&
        Objects.equals(this.estado, infoRutaResponse.estado) &&
        Objects.equals(this.total, infoRutaResponse.total);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idRuta, cobrador, cobradorImg, fecha, estadoCod, estado, total);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InfoRutaResponse {\n");
    
    sb.append("    idRuta: ").append(toIndentedString(idRuta)).append("\n");
    sb.append("    cobrador: ").append(toIndentedString(cobrador)).append("\n");
    sb.append("    cobradorImg: ").append(toIndentedString(cobradorImg)).append("\n");
    sb.append("    fecha: ").append(toIndentedString(fecha)).append("\n");
    sb.append("    estadoCod: ").append(toIndentedString(estadoCod)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    total: ").append(toIndentedString(total)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

