package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUsuProcesos
 */
@Validated

public class PycUsuProcesos   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("nombreUsuario")
  private String nombreUsuario = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("idProceso")
  private Integer idProceso = null;

  @JsonProperty("nombreProceso")
  private String nombreProceso = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("icono")
  private String icono = null;

  public PycUsuProcesos idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Identificador del usuario
   * @return idUsuario
  **/
  @ApiModelProperty(example = "1234", value = "Identificador del usuario")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycUsuProcesos nombreUsuario(String nombreUsuario) {
    this.nombreUsuario = nombreUsuario;
    return this;
  }

  /**
   * Nombre completo del usuario
   * @return nombreUsuario
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre completo del usuario")


  public String getNombreUsuario() {
    return nombreUsuario;
  }

  public void setNombreUsuario(String nombreUsuario) {
    this.nombreUsuario = nombreUsuario;
  }

  public PycUsuProcesos usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Nombre de usuario en la base de datos
   * @return usuario
  **/
  @ApiModelProperty(example = "jperez", value = "Nombre de usuario en la base de datos")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public PycUsuProcesos idProceso(Integer idProceso) {
    this.idProceso = idProceso;
    return this;
  }

  /**
   * Identificador del proceso
   * @return idProceso
  **/
  @ApiModelProperty(example = "1", value = "Identificador del proceso")


  public Integer getIdProceso() {
    return idProceso;
  }

  public void setIdProceso(Integer idProceso) {
    this.idProceso = idProceso;
  }

  public PycUsuProcesos nombreProceso(String nombreProceso) {
    this.nombreProceso = nombreProceso;
    return this;
  }

  /**
   * Nombre del proceso
   * @return nombreProceso
  **/
  @ApiModelProperty(example = "Gestión de Peticiones", value = "Nombre del proceso")


  public String getNombreProceso() {
    return nombreProceso;
  }

  public void setNombreProceso(String nombreProceso) {
    this.nombreProceso = nombreProceso;
  }

  public PycUsuProcesos estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado del proceso para el usuario
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", value = "Estado del proceso para el usuario")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycUsuProcesos icono(String icono) {
    this.icono = icono;
    return this;
  }

  /**
   * Icono asociado al proceso
   * @return icono
  **/
  @ApiModelProperty(example = "fa-tasks", value = "Icono asociado al proceso")


  public String getIcono() {
    return icono;
  }

  public void setIcono(String icono) {
    this.icono = icono;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUsuProcesos pycUsuProcesos = (PycUsuProcesos) o;
    return Objects.equals(this.idUsuario, pycUsuProcesos.idUsuario) &&
        Objects.equals(this.nombreUsuario, pycUsuProcesos.nombreUsuario) &&
        Objects.equals(this.usuario, pycUsuProcesos.usuario) &&
        Objects.equals(this.idProceso, pycUsuProcesos.idProceso) &&
        Objects.equals(this.nombreProceso, pycUsuProcesos.nombreProceso) &&
        Objects.equals(this.estado, pycUsuProcesos.estado) &&
        Objects.equals(this.icono, pycUsuProcesos.icono);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, nombreUsuario, usuario, idProceso, nombreProceso, estado, icono);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUsuProcesos {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    nombreUsuario: ").append(toIndentedString(nombreUsuario)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    idProceso: ").append(toIndentedString(idProceso)).append("\n");
    sb.append("    nombreProceso: ").append(toIndentedString(nombreProceso)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    icono: ").append(toIndentedString(icono)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

