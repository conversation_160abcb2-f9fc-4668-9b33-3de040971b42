package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycTipoPeticion
 */
@Validated

public class PycTipoPeticion   {
  @JsonProperty("idTipoPeticion")
  private Integer idTipoPeticion = null;

  @JsonProperty("descripcionTipoPeticion")
  private String descripcionTipoPeticion = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("fechaHora")
  private String fechaHora = null;

  @JsonProperty("idProceso")
  private Integer idProceso = null;

  public PycTipoPeticion idTipoPeticion(Integer idTipoPeticion) {
    this.idTipoPeticion = idTipoPeticion;
    return this;
  }

  /**
   * Identificador único del tipo de petición
   * @return idTipoPeticion
  **/
  @ApiModelProperty(example = "1", value = "Identificador único del tipo de petición")


  public Integer getIdTipoPeticion() {
    return idTipoPeticion;
  }

  public void setIdTipoPeticion(Integer idTipoPeticion) {
    this.idTipoPeticion = idTipoPeticion;
  }

  public PycTipoPeticion descripcionTipoPeticion(String descripcionTipoPeticion) {
    this.descripcionTipoPeticion = descripcionTipoPeticion;
    return this;
  }

  /**
   * Descripción del tipo de petición
   * @return descripcionTipoPeticion
  **/
  @ApiModelProperty(example = "Solicitud de Desarrollo", value = "Descripción del tipo de petición")


  public String getDescripcionTipoPeticion() {
    return descripcionTipoPeticion;
  }

  public void setDescripcionTipoPeticion(String descripcionTipoPeticion) {
    this.descripcionTipoPeticion = descripcionTipoPeticion;
  }

  public PycTipoPeticion estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado del tipo de petición
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", value = "Estado del tipo de petición")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycTipoPeticion usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Usuario que creó o modificó el tipo de petición
   * @return usuario
  **/
  @ApiModelProperty(example = "admin", value = "Usuario que creó o modificó el tipo de petición")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public PycTipoPeticion fechaHora(String fechaHora) {
    this.fechaHora = fechaHora;
    return this;
  }

  /**
   * Fecha y hora de creación o modificación
   * @return fechaHora
  **/
  @ApiModelProperty(example = "2024-01-15 10:30:00", value = "Fecha y hora de creación o modificación")


  public String getFechaHora() {
    return fechaHora;
  }

  public void setFechaHora(String fechaHora) {
    this.fechaHora = fechaHora;
  }

  public PycTipoPeticion idProceso(Integer idProceso) {
    this.idProceso = idProceso;
    return this;
  }

  /**
   * Identificador del proceso asociado
   * @return idProceso
  **/
  @ApiModelProperty(example = "1", value = "Identificador del proceso asociado")


  public Integer getIdProceso() {
    return idProceso;
  }

  public void setIdProceso(Integer idProceso) {
    this.idProceso = idProceso;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycTipoPeticion pycTipoPeticion = (PycTipoPeticion) o;
    return Objects.equals(this.idTipoPeticion, pycTipoPeticion.idTipoPeticion) &&
        Objects.equals(this.descripcionTipoPeticion, pycTipoPeticion.descripcionTipoPeticion) &&
        Objects.equals(this.estado, pycTipoPeticion.estado) &&
        Objects.equals(this.usuario, pycTipoPeticion.usuario) &&
        Objects.equals(this.fechaHora, pycTipoPeticion.fechaHora) &&
        Objects.equals(this.idProceso, pycTipoPeticion.idProceso);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idTipoPeticion, descripcionTipoPeticion, estado, usuario, fechaHora, idProceso);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycTipoPeticion {\n");
    
    sb.append("    idTipoPeticion: ").append(toIndentedString(idTipoPeticion)).append("\n");
    sb.append("    descripcionTipoPeticion: ").append(toIndentedString(descripcionTipoPeticion)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    fechaHora: ").append(toIndentedString(fechaHora)).append("\n");
    sb.append("    idProceso: ").append(toIndentedString(idProceso)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

