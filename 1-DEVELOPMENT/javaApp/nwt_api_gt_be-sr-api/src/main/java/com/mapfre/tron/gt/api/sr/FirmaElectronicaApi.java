package com.mapfre.tron.gt.api.sr;

import java.util.List;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mapfre.tron.gt.api.model.PLE_MedioPagoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_DepositoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_IdentificadorMedioPago;
import com.mapfre.tron.gt.api.model.PLE_DetallePlanilla;
import com.mapfre.tron.gt.api.model.PLE_CuentaBanco;
import com.mapfre.tron.gt.api.model.PLE_TotalPagoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_MedioPago;
import com.mapfre.tron.gt.api.model.PLE_TipoPlanilla;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;

@Api(value = "firmaElectronica", description = "the firmaElectronica API")
@RequestMapping(value = "/newtron/api")
public interface FirmaElectronicaApi {

    Logger log = LoggerFactory.getLogger(FirmaElectronicaApi.class);

    default Optional<ObjectMapper> getObjectMapper() {
        return Optional.empty();
    }

    default Optional<HttpServletRequest> getRequest() {
        return Optional.empty();
    }

    default Optional<String> getAcceptHeader() {
        return getRequest().map(r -> r.getHeader("Accept"));
    }

    @RequestMapping(value = "/firmaElectronica/buscarMediosPagoPlanilla",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PLE_MedioPagoPlanilla>> buscarMediosPagoPlanilla(@NotNull @ApiParam(value = "ID de la planilla", required = true) @Valid @RequestParam(value = "idPlanilla", required = true) Integer idPlanilla) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {\n  \"idplanilla\" : 0,\n  \"requerimientos\" : \"requerimientos\",\n  \"idmedio_pago\" : 6,\n  \"medio_pago\" : \"medio_pago\",\n  \"codmoneda\" : \"codmoneda\",\n  \"monto\" : 1.46581298050294517310021547018550336360931396484375,\n  \"fecha_cheque\" : \"2000-01-23\",\n  \"fecha_deposito\" : \"2000-01-23\",\n  \"codbanco\" : \"codbanco\",\n  \"nombre_banco\" : \"nombre_banco\",\n  \"documento\" : \"documento\",\n  \"comentario\" : \"comentario\",\n  \"identificador\" : \"identificador\",\n  \"estado_mediopago\" : \"estado_mediopago\",\n  \"correlativo\" : 5,\n  \"grupo\" : \"grupo\",\n  \"enviar_mensajero\" : \"enviar_mensajero\",\n  \"cuenta_codigo\" : \"cuenta_codigo\",\n  \"cuenta_numero\" : \"cuenta_numero\",\n  \"cobro_anticipado\" : \"cobro_anticipado\"\n} ]\", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (Exception e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default FirmaElectronicaApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @RequestMapping(value = "/firmaElectronica/buscarDepositosPlanilla",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PLE_DepositoPlanilla>> buscarDepositosPlanilla(@NotNull @ApiParam(value = "ID de la planilla", required = true) @Valid @RequestParam(value = "idPlanilla", required = true) Integer idPlanilla) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {\n  \"id\" : 0,\n  \"idplanilla\" : 6,\n  \"codmedio\" : \"codmedio\",\n  \"medio_deposito\" : \"medio_deposito\",\n  \"monto\" : 1.46581298050294517310021547018550336360931396484375,\n  \"documento\" : \"documento\",\n  \"fecha_deposito\" : \"2000-01-23\",\n  \"codbanco\" : \"codbanco\",\n  \"nombre_banco\" : \"nombre_banco\",\n  \"descarga\" : \"descarga\",\n  \"registro\" : \"2000-01-23T04:56:07.000+00:00\"\n} ]\", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (Exception e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default FirmaElectronicaApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @RequestMapping(value = "/firmaElectronica/buscarIdentificadorMedioPago",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PLE_IdentificadorMedioPago>> buscarIdentificadorMedioPago(@NotNull @ApiParam(value = "Sistema (A/T)", required = true) @Valid @RequestParam(value = "sistema", required = true) String sistema,@NotNull @ApiParam(value = "Código de moneda", required = true) @Valid @RequestParam(value = "moneda", required = true) String moneda,@NotNull @ApiParam(value = "Medio de pago", required = true) @Valid @RequestParam(value = "medio", required = true) String medio,@NotNull @ApiParam(value = "Tipo (N/I)", required = true) @Valid @RequestParam(value = "tipo", required = true) String tipo) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {\n  \"sistema\" : \"sistema\",\n  \"idsistema\" : 0,\n  \"medio\" : \"medio\",\n  \"idmedio\" : 6,\n  \"identificador\" : \"identificador\",\n  \"moneda\" : \"moneda\"\n} ]\", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (Exception e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default FirmaElectronicaApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @RequestMapping(value = "/firmaElectronica/buscarDetallePlanilla",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PLE_DetallePlanilla>> buscarDetallePlanilla(@NotNull @ApiParam(value = "ID de la planilla", required = true) @Valid @RequestParam(value = "idPlanilla", required = true) Integer idPlanilla) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {\n  \"idplanilla_detalle\" : 0,\n  \"poliza\" : \"poliza\",\n  \"requerimiento\" : \"requerimiento\",\n  \"factura\" : \"factura\",\n  \"nombre_pagador\" : \"nombre_pagador\",\n  \"nit_pagador\" : \"nit_pagador\",\n  \"no_cuota\" : \"no_cuota\",\n  \"tipo_moneda\" : \"tipo_moneda\",\n  \"prima\" : 6.02745618307040320615897144307382404804229736328125,\n  \"tipo_pago\" : \"tipo_pago\",\n  \"comentario\" : \"comentario\",\n  \"estado_poliza\" : \"estado_poliza\",\n  \"sistema\" : \"sistema\",\n  \"fecha_vig\" : \"2000-01-23\",\n  \"estado_cobro\" : \"estado_cobro\",\n  \"esaviso\" : \"esaviso\"\n} ]\", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (Exception e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default FirmaElectronicaApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @RequestMapping(value = "/firmaElectronica/getCuentasBancos",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PLE_CuentaBanco>> getCuentasBancos(@NotNull @ApiParam(value = "Código de moneda", required = true) @Valid @RequestParam(value = "moneda", required = true) String moneda,@NotNull @ApiParam(value = "Código de entidad", required = true) @Valid @RequestParam(value = "entidad", required = true) String entidad) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {\n  \"codigo\" : \"codigo\",\n  \"cuenta\" : \"cuenta\",\n  \"tronCode\" : \"tronCode\",\n  \"acselCode\" : \"acselCode\",\n  \"nombre\" : \"nombre\"\n} ]\", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (Exception e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default FirmaElectronicaApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @RequestMapping(value = "/firmaElectronica/obtenerTotalPagosPlanilla",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PLE_TotalPagoPlanilla>> obtenerTotalPagosPlanilla(@NotNull @ApiParam(value = "ID de la planilla", required = true) @Valid @RequestParam(value = "idPlanilla", required = true) Integer idPlanilla) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {\n  \"medio_pago\" : \"medio_pago\",\n  \"tipo_moneda\" : \"tipo_moneda\",\n  \"monto\" : 0.80082819046101150206595775671303272247314453125,\n  \"documento\" : \"documento\",\n  \"banco\" : \"banco\",\n  \"nombre_banco\" : \"nombre_banco\",\n  \"fecha_cheque\" : \"2000-01-23\",\n  \"fecha_deposito\" : \"2000-01-23\",\n  \"estado\" : \"estado\",\n  \"procesado\" : 6.02745618307040320615897144307382404804229736328125,\n  \"no_procesado\" : 1.46581298050294517310021547018550336360931396484375\n} ]\", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (Exception e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default FirmaElectronicaApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @RequestMapping(value = "/firmaElectronica/obtenerMedioPago",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PLE_MedioPago>> obtenerMedioPago() {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {\n  \"codigo\" : 0,\n  \"descripcion\" : \"descripcion\"\n} ]\", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (Exception e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default FirmaElectronicaApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @RequestMapping(value = "/firmaElectronica/obtenerTipoPlanilla",
        produces = { "application/json" }, 
        method = RequestMethod.GET)
    default ResponseEntity<List<PLE_TipoPlanilla>> obtenerTipoPlanilla() {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("[ {\n  \"codigo\" : 0,\n  \"descripcion\" : \"descripcion\"\n} ]\", List.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (Exception e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default FirmaElectronicaApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

}
