package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycPerfil
 */
@Validated

public class PycPerfil   {
  @JsonProperty("idPerfil")
  private Integer idPerfil = null;

  @JsonProperty("nombrePerfil")
  private String nombrePerfil = null;

  @JsonProperty("descripcionPerfil")
  private String descripcionPerfil = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("fechaHora")
  private String fechaHora = null;

  public PycPerfil idPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
    return this;
  }

  /**
   * Identificador del perfil
   * @return idPerfil
  **/
  @ApiModelProperty(example = "1", value = "Identificador del perfil")


  public Integer getIdPerfil() {
    return idPerfil;
  }

  public void setIdPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
  }

  public PycPerfil nombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
    return this;
  }

  /**
   * Nombre del perfil
   * @return nombrePerfil
  **/
  @ApiModelProperty(example = "Administrador", value = "Nombre del perfil")


  public String getNombrePerfil() {
    return nombrePerfil;
  }

  public void setNombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
  }

  public PycPerfil descripcionPerfil(String descripcionPerfil) {
    this.descripcionPerfil = descripcionPerfil;
    return this;
  }

  /**
   * Descripción del perfil
   * @return descripcionPerfil
  **/
  @ApiModelProperty(example = "Perfil con acceso completo al sistema", value = "Descripción del perfil")


  public String getDescripcionPerfil() {
    return descripcionPerfil;
  }

  public void setDescripcionPerfil(String descripcionPerfil) {
    this.descripcionPerfil = descripcionPerfil;
  }

  public PycPerfil usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Usuario que creó o modificó el perfil
   * @return usuario
  **/
  @ApiModelProperty(example = "jperez", value = "Usuario que creó o modificó el perfil")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public PycPerfil fechaHora(String fechaHora) {
    this.fechaHora = fechaHora;
    return this;
  }

  /**
   * Fecha y hora de creación o última modificación
   * @return fechaHora
  **/
  @ApiModelProperty(example = "2025-05-21T14:20:00Z", value = "Fecha y hora de creación o última modificación")


  public String getFechaHora() {
    return fechaHora;
  }

  public void setFechaHora(String fechaHora) {
    this.fechaHora = fechaHora;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycPerfil pycPerfil = (PycPerfil) o;
    return Objects.equals(this.idPerfil, pycPerfil.idPerfil) &&
        Objects.equals(this.nombrePerfil, pycPerfil.nombrePerfil) &&
        Objects.equals(this.descripcionPerfil, pycPerfil.descripcionPerfil) &&
        Objects.equals(this.usuario, pycPerfil.usuario) &&
        Objects.equals(this.fechaHora, pycPerfil.fechaHora);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPerfil, nombrePerfil, descripcionPerfil, usuario, fechaHora);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycPerfil {\n");
    
    sb.append("    idPerfil: ").append(toIndentedString(idPerfil)).append("\n");
    sb.append("    nombrePerfil: ").append(toIndentedString(nombrePerfil)).append("\n");
    sb.append("    descripcionPerfil: ").append(toIndentedString(descripcionPerfil)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    fechaHora: ").append(toIndentedString(fechaHora)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

