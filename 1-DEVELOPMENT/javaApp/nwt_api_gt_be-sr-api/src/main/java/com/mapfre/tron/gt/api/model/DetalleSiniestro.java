package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * DetalleSiniestro
 */
@Validated

public class DetalleSiniestro   {
  @JsonProperty("numOrden")
  private Integer numOrden = null;

  @JsonProperty("codEtapa")
  private String codEtapa = null;

  @JsonProperty("descripcion")
  private String descripcion = null;

  @JsonProperty("fecha")
  private String fecha = null;

  @JsonProperty("etapa")
  private String etapa = null;

  @JsonProperty("imagen")
  private String imagen = null;

  @JsonProperty("subTracking")
  private String subTracking = null;

  public DetalleSiniestro numOrden(Integer numOrden) {
    this.numOrden = numOrden;
    return this;
  }

  /**
   * Número de orden
   * @return numOrden
  **/
  @ApiModelProperty(example = "1", value = "Número de orden")


  public Integer getNumOrden() {
    return numOrden;
  }

  public void setNumOrden(Integer numOrden) {
    this.numOrden = numOrden;
  }

  public DetalleSiniestro codEtapa(String codEtapa) {
    this.codEtapa = codEtapa;
    return this;
  }

  /**
   * Código de etapa del siniestro
   * @return codEtapa
  **/
  @ApiModelProperty(example = "GRABACION DEL SINIESTRO", value = "Código de etapa del siniestro")


  public String getCodEtapa() {
    return codEtapa;
  }

  public void setCodEtapa(String codEtapa) {
    this.codEtapa = codEtapa;
  }

  public DetalleSiniestro descripcion(String descripcion) {
    this.descripcion = descripcion;
    return this;
  }

  /**
   * Descripción detallada de la etapa
   * @return descripcion
  **/
  @ApiModelProperty(example = "Tu siniestro ha sido aperturado", value = "Descripción detallada de la etapa")


  public String getDescripcion() {
    return descripcion;
  }

  public void setDescripcion(String descripcion) {
    this.descripcion = descripcion;
  }

  public DetalleSiniestro fecha(String fecha) {
    this.fecha = fecha;
    return this;
  }

  /**
   * Fecha de la etapa
   * @return fecha
  **/
  @ApiModelProperty(example = "05/05/2025 21:11:20", value = "Fecha de la etapa")


  public String getFecha() {
    return fecha;
  }

  public void setFecha(String fecha) {
    this.fecha = fecha;
  }

  public DetalleSiniestro etapa(String etapa) {
    this.etapa = etapa;
    return this;
  }

  /**
   * Nombre de la etapa
   * @return etapa
  **/
  @ApiModelProperty(example = "Apertura Siniestro", value = "Nombre de la etapa")


  public String getEtapa() {
    return etapa;
  }

  public void setEtapa(String etapa) {
    this.etapa = etapa;
  }

  public DetalleSiniestro imagen(String imagen) {
    this.imagen = imagen;
    return this;
  }

  /**
   * URL de la imagen asociada a la etapa
   * @return imagen
  **/
  @ApiModelProperty(example = "https://app2.mapfre.com.gt/CDN/images/mapfre/tracking/reclamos/autos/apertura.png", value = "URL de la imagen asociada a la etapa")


  public String getImagen() {
    return imagen;
  }

  public void setImagen(String imagen) {
    this.imagen = imagen;
  }

  public DetalleSiniestro subTracking(String subTracking) {
    this.subTracking = subTracking;
    return this;
  }

  /**
   * Indicador de sub-tracking
   * @return subTracking
  **/
  @ApiModelProperty(example = "N", value = "Indicador de sub-tracking")


  public String getSubTracking() {
    return subTracking;
  }

  public void setSubTracking(String subTracking) {
    this.subTracking = subTracking;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DetalleSiniestro detalleSiniestro = (DetalleSiniestro) o;
    return Objects.equals(this.numOrden, detalleSiniestro.numOrden) &&
        Objects.equals(this.codEtapa, detalleSiniestro.codEtapa) &&
        Objects.equals(this.descripcion, detalleSiniestro.descripcion) &&
        Objects.equals(this.fecha, detalleSiniestro.fecha) &&
        Objects.equals(this.etapa, detalleSiniestro.etapa) &&
        Objects.equals(this.imagen, detalleSiniestro.imagen) &&
        Objects.equals(this.subTracking, detalleSiniestro.subTracking);
  }

  @Override
  public int hashCode() {
    return Objects.hash(numOrden, codEtapa, descripcion, fecha, etapa, imagen, subTracking);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DetalleSiniestro {\n");
    
    sb.append("    numOrden: ").append(toIndentedString(numOrden)).append("\n");
    sb.append("    codEtapa: ").append(toIndentedString(codEtapa)).append("\n");
    sb.append("    descripcion: ").append(toIndentedString(descripcion)).append("\n");
    sb.append("    fecha: ").append(toIndentedString(fecha)).append("\n");
    sb.append("    etapa: ").append(toIndentedString(etapa)).append("\n");
    sb.append("    imagen: ").append(toIndentedString(imagen)).append("\n");
    sb.append("    subTracking: ").append(toIndentedString(subTracking)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

