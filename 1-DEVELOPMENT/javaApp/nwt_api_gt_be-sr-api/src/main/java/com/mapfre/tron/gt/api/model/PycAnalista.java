package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycAnalista
 */
@Validated

public class PycAnalista   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("usuario")
  private String usuario = null;

  public PycAnalista idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Identificador del usuario analista
   * @return idUsuario
  **/
  @ApiModelProperty(example = "1234", value = "Identificador del usuario analista")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycAnalista usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Nombre completo del analista (primer nombre y primer apellido)
   * @return usuario
  **/
  @ApiModelProperty(example = "María García", value = "Nombre completo del analista (primer nombre y primer apellido)")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycAnalista pycAnalista = (PycAnalista) o;
    return Objects.equals(this.idUsuario, pycAnalista.idUsuario) &&
        Objects.equals(this.usuario, pycAnalista.usuario);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, usuario);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycAnalista {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

