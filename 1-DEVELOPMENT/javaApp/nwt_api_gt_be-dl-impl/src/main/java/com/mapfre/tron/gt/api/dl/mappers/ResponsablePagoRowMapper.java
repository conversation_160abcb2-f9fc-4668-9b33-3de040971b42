package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.ResponsablePagoResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos ResponsablePagoResponse.
 */
@Slf4j
public class ResponsablePagoRowMapper implements RowMapper<ResponsablePagoResponse> {

    @Override
    public ResponsablePagoResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto ResponsablePagoResponse", rowNum);
        
        ResponsablePagoResponse response = new ResponsablePagoResponse();
        
        try {
            response.setCodCia(rs.getInt("COD_CIA"));
            response.setCodActTercero(rs.getInt("COD_ACT_TERCERO"));
            response.setTipDocum(rs.getString("TIP_DOCUM"));
            response.setCodDocum(rs.getString("COD_DOCUM"));
            response.setNombreCompleto(rs.getString("NOMBRE_COMPLETO"));
            response.setDirecCobro(rs.getString("DIREC_COBRO"));
            response.setEmail(rs.getString("EMAIL"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
