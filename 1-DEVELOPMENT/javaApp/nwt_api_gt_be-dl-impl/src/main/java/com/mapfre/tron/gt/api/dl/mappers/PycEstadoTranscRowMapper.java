package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycEstadoTransc;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycEstadoTransc.
 * 
 * La función FN_CAMBIO_ESTADO retorna los siguientes campos:
 * - ID_ESTADO_INICIAL: Identificador del estado inicial de la transición
 * - ID_ESTADO: Identificador del estado final de la transición
 * - NOMBRE_ESTADO: Nombre del estado final de la transición
 */
@Slf4j
public class PycEstadoTranscRowMapper implements RowMapper<PycEstadoTransc> {

    @Override
    public PycEstadoTransc mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycEstadoTransc", rowNum);
        
        PycEstadoTransc estadoTransc = new PycEstadoTransc();
        
        try {
            // Mapear ID_ESTADO_INICIAL
            estadoTransc.setIdEstadoInicial(rs.getInt("ID_ESTADO_INICIAL"));
            
            // Mapear ID_ESTADO (estado final)
            estadoTransc.setIdEstado(rs.getInt("ID_ESTADO"));
            
            // Mapear NOMBRE_ESTADO (nombre del estado final)
            estadoTransc.setNombreEstado(rs.getString("NOMBRE_ESTADO"));
            
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return estadoTransc;
    }
}
