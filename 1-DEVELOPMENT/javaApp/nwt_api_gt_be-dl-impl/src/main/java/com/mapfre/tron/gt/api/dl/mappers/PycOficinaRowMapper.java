package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycOficina;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycOficina.
 */
@Slf4j
public class PycOficinaRowMapper implements RowMapper<PycOficina> {

    @Override
    public PycOficina mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycOficina", rowNum);
        
        PycOficina oficina = new PycOficina();
        
        try {
            oficina.setIdOficina(rs.getInt("ID_OFICINA"));
            oficina.setNombre(rs.getString("NOMBRE"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return oficina;
    }
}
