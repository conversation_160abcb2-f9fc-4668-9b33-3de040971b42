package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycUsuPerfilProceso;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycUsuPerfilProceso.
 */
@Slf4j
public class PycUsuPerfilProcesoRowMapper implements RowMapper<PycUsuPerfilProceso> {

    @Override
    public PycUsuPerfilProceso mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycUsuPerfilProceso", rowNum);
        
        PycUsuPerfilProceso usuPerfilProceso = new PycUsuPerfilProceso();
        
        try {
            usuPerfilProceso.setIdUsuario(rs.getInt("ID_USUARIO"));
            usuPerfilProceso.setNombre(rs.getString("NOMBRE"));
            usuPerfilProceso.setIndDefault(rs.getString("IND_DEFAULT"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return usuPerfilProceso;
    }
}
