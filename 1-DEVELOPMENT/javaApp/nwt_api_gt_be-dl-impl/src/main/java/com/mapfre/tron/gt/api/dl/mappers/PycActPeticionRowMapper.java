package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycActPeticion;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycActPeticion.
 * 
 * La función FN_ACT_PETICION retorna los siguientes campos:
 * - ID_ACTIVIDAD: Identificador de la actividad
 * - ID_PETICION: Identificador de la petición
 * - NOMBRE_ACTIVIDAD: Nombre de la actividad
 * - DESCRIPCION_ACTIVIDAD: Descripción detallada de la actividad
 * - HORAS_BASE: Horas base estimadas para la actividad
 * - HORAS_REAL: Horas reales trabajadas en la actividad
 * - HORAS_PENDIENTES: Horas pendientes por trabajar (calculado: HORAS_BASE - HORAS_REAL)
 * - FECHA_INICIO: Fecha de inicio de la actividad (formato DD/MM/YYYY)
 * - FECHA_FIN: Fecha de fin de la actividad (formato DD/MM/YYYY)
 * - ID_USUARIO: Identificador del usuario responsable
 * - ESTADO: Estado actual de la actividad
 * - ID_CATEGORIA_TABLERO: Identificador de la categoría del tablero
 * - NOMBRE: Nombre de la categoría del tablero (desde PYC_CATEGORIA_TABLERO)
 * - ASIGNADO_A: Nombre completo del usuario asignado (PRIMER_NOMBRE + PRIMER_APELLIDO)
 * - PERFIL_ASIGNADO: URL del perfil del usuario asignado
 */
@Slf4j
public class PycActPeticionRowMapper implements RowMapper<PycActPeticion> {

    @Override
    public PycActPeticion mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycActPeticion", rowNum);
        
        PycActPeticion actPeticion = new PycActPeticion();
        
        try {
            // Información básica de la actividad
            actPeticion.setIdActividad(rs.getInt("ID_ACTIVIDAD"));
            actPeticion.setIdPeticion(rs.getInt("ID_PETICION"));
            actPeticion.setNombreActividad(rs.getString("NOMBRE_ACTIVIDAD"));
            actPeticion.setDescripcionActividad(rs.getString("DESCRIPCION_ACTIVIDAD"));
            
            // Información de horas
            actPeticion.setHorasBase(rs.getDouble("HORAS_BASE"));
            actPeticion.setHorasReal(rs.getDouble("HORAS_REAL"));
            actPeticion.setHorasPendientes(rs.getDouble("HORAS_PENDIENTES"));
            
            // Fechas formateadas (ya vienen formateadas desde la función Oracle como DD/MM/YYYY)
            actPeticion.setFechaInicio(rs.getString("FECHA_INICIO"));
            actPeticion.setFechaFin(rs.getString("FECHA_FIN"));
            
            // Información del usuario y estado
            actPeticion.setIdUsuario(rs.getInt("ID_USUARIO"));
            actPeticion.setEstado(rs.getString("ESTADO"));
            
            // Información de categoría del tablero
            actPeticion.setIdCategoriaTablero(rs.getInt("ID_CATEGORIA_TABLERO"));
            actPeticion.setNombre(rs.getString("NOMBRE"));
            
            // Información del usuario asignado
            actPeticion.setAsignadoA(rs.getString("ASIGNADO_A"));
            actPeticion.setPerfilAsignado(rs.getString("PERFIL_ASIGNADO"));
            
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return actPeticion;
    }
}
