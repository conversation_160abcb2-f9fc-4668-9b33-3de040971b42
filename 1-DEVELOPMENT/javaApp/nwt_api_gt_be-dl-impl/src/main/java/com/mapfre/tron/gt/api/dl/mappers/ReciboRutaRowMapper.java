package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.AutenticarUsuarioResponseUsuario;
import com.mapfre.tron.gt.api.model.UpdateReciboRutaResponse;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class ReciboRutaRowMapper implements RowMapper<UpdateReciboRutaResponse> {
    @Override
    public UpdateReciboRutaResponse mapRow(ResultSet resultSet, int i) throws SQLException {

        UpdateReciboRutaResponse rutaResponse = new UpdateReciboRutaResponse();

        rutaResponse.setCorreos(resultSet.getString("correos"));
        return null;
    }
}
