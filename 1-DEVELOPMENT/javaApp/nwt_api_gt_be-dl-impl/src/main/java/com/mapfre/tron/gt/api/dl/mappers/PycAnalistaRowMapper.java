package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycAnalista;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycAnalista.
 */
@Slf4j
public class PycAnalistaRowMapper implements RowMapper<PycAnalista> {

    @Override
    public PycAnalista mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycAnalista", rowNum);
        
        PycAnalista analista = new PycAnalista();
        
        try {
            analista.setIdUsuario(rs.getInt("ID_USUARIO"));
            analista.setUsuario(rs.getString("USUARIO"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return analista;
    }
}
