package com.mapfre.tron.gt.api.dl;

import java.sql.Types;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.SqlOutParameter;
import org.springframework.jdbc.core.SqlParameter;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.jdbc.core.simple.SimpleJdbcCall;
import org.springframework.stereotype.Repository;

import com.mapfre.tron.gt.api.commons.DatabaseConstants;
import com.mapfre.tron.gt.api.dl.mappers.MedioPagoPlanillaRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.DepositoPlanillaRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.IdentificadorMedioPagoRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.DetallePlanillaRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.CuentaBancoRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.TotalPagoPlanillaRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.MedioPagoRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.TipoPlanillaRowMapper;
import com.mapfre.tron.gt.api.model.PLE_MedioPagoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_DepositoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_IdentificadorMedioPago;
import com.mapfre.tron.gt.api.model.PLE_DetallePlanilla;
import com.mapfre.tron.gt.api.model.PLE_CuentaBanco;
import com.mapfre.tron.gt.api.model.PLE_TotalPagoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_MedioPago;
import com.mapfre.tron.gt.api.model.PLE_TipoPlanilla;

import lombok.extern.slf4j.Slf4j;

@Repository
@Slf4j
public class DlFirmaElectronicaImpl implements IDlFirmaElectronica {
	 
    @Autowired
    @Qualifier("jdbcTemplate")
    protected JdbcTemplate jdbcTemplate;

    private SimpleJdbcCall prepareJdbcCall(String procedureName, SqlParameter... parameters) {
        return new SimpleJdbcCall(jdbcTemplate)
                .withSchemaName(DatabaseConstants.SCHEMA_NAME)
                .withCatalogName(DatabaseConstants.CATALOG_PLANILLA_ELECTRONICA_NAME)
                .withProcedureName(procedureName)
                .declareParameters(parameters);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PLE_MedioPagoPlanilla> buscarMediosPagoPlanilla(Integer idPlanilla) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, "buscarMediosPagoPlanilla"));
         
        SimpleJdbcCall call = prepareJdbcCall(DatabaseConstants.PROC_BUSCAR_MEDIOS_PAGO_PLANILLA,
                new SqlOutParameter(DatabaseConstants.PARAM_OUT_CURSOR, Types.REF_CURSOR, new MedioPagoPlanillaRowMapper()),
                new SqlParameter(DatabaseConstants.PARAM_IN_ID_PLANILLA, Types.INTEGER)
        );

        SqlParameterSource inParams = new MapSqlParameterSource()
                .addValue(DatabaseConstants.PARAM_IN_ID_PLANILLA, idPlanilla);

        log.info(String.format(DatabaseConstants.LOG_LAUNCH_DESC, DatabaseConstants.SCHEMA_NAME,
                DatabaseConstants.CATALOG_PLANILLA_ELECTRONICA_NAME, DatabaseConstants.PROC_BUSCAR_MEDIOS_PAGO_PLANILLA));

        Map<String, Object> result = call.execute(inParams);
        return (List<PLE_MedioPagoPlanilla>) result.get(DatabaseConstants.PARAM_OUT_CURSOR);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PLE_DepositoPlanilla> buscarDepositosPlanilla(Integer idPlanilla) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, "buscarDepositosPlanilla"));
         
        SimpleJdbcCall call = prepareJdbcCall(DatabaseConstants.PROC_BUSCAR_DEPOSITOS_PLANILLA,
                new SqlOutParameter(DatabaseConstants.PARAM_OUT_CURSOR, Types.REF_CURSOR, new DepositoPlanillaRowMapper()),
                new SqlParameter(DatabaseConstants.PARAM_IN_ID_PLANILLA, Types.INTEGER)
        );

        SqlParameterSource inParams = new MapSqlParameterSource()
                .addValue(DatabaseConstants.PARAM_IN_ID_PLANILLA, idPlanilla);

        log.info(String.format(DatabaseConstants.LOG_LAUNCH_DESC, DatabaseConstants.SCHEMA_NAME,
                DatabaseConstants.CATALOG_PLANILLA_ELECTRONICA_NAME, DatabaseConstants.PROC_BUSCAR_DEPOSITOS_PLANILLA));

        Map<String, Object> result = call.execute(inParams);
        return (List<PLE_DepositoPlanilla>) result.get(DatabaseConstants.PARAM_OUT_CURSOR);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PLE_IdentificadorMedioPago> buscarIdentificadorMedioPago(String sistema, String moneda, String medio, String tipo) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, "buscarIdentificadorMedioPago"));
         
        SimpleJdbcCall call = prepareJdbcCall(DatabaseConstants.PROC_BUSCAR_IDENTIFICADOR_MEDIO_PAGO,
                new SqlOutParameter(DatabaseConstants.PARAM_OUT_CURSOR, Types.REF_CURSOR, new IdentificadorMedioPagoRowMapper()),
                new SqlParameter(DatabaseConstants.PARAM_P_SISTEMA, Types.VARCHAR),
                new SqlParameter(DatabaseConstants.PARAM_P_MONEDA, Types.VARCHAR),
                new SqlParameter(DatabaseConstants.PARAM_P_MEDIO, Types.VARCHAR),
                new SqlParameter(DatabaseConstants.PARAM_P_TIPO, Types.VARCHAR)
        );

        SqlParameterSource inParams = new MapSqlParameterSource()
                .addValue(DatabaseConstants.PARAM_P_SISTEMA, sistema)
                .addValue(DatabaseConstants.PARAM_P_MONEDA, moneda)
                .addValue(DatabaseConstants.PARAM_P_MEDIO, medio)
                .addValue(DatabaseConstants.PARAM_P_TIPO, tipo);

        log.info(String.format(DatabaseConstants.LOG_LAUNCH_DESC, DatabaseConstants.SCHEMA_NAME,
                DatabaseConstants.CATALOG_PLANILLA_ELECTRONICA_NAME, DatabaseConstants.PROC_BUSCAR_IDENTIFICADOR_MEDIO_PAGO));

        Map<String, Object> result = call.execute(inParams);
        return (List<PLE_IdentificadorMedioPago>) result.get(DatabaseConstants.PARAM_OUT_CURSOR);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PLE_DetallePlanilla> buscarDetallePlanilla(Integer idPlanilla) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, "buscarDetallePlanilla"));
         
        SimpleJdbcCall call = prepareJdbcCall(DatabaseConstants.PROC_BUSCAR_DETALLE_PLANILLA,
                new SqlOutParameter(DatabaseConstants.PARAM_OUT_CURSOR, Types.REF_CURSOR, new DetallePlanillaRowMapper()),
                new SqlParameter(DatabaseConstants.PARAM_IN_ID_PLANILLA, Types.INTEGER)
        );

        SqlParameterSource inParams = new MapSqlParameterSource()
                .addValue(DatabaseConstants.PARAM_IN_ID_PLANILLA, idPlanilla);

        log.info(String.format(DatabaseConstants.LOG_LAUNCH_DESC, DatabaseConstants.SCHEMA_NAME,
                DatabaseConstants.CATALOG_PLANILLA_ELECTRONICA_NAME, DatabaseConstants.PROC_BUSCAR_DETALLE_PLANILLA));

        Map<String, Object> result = call.execute(inParams);
        return (List<PLE_DetallePlanilla>) result.get(DatabaseConstants.PARAM_OUT_CURSOR);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PLE_CuentaBanco> getCuentasBancos(String moneda, String entidad) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, "getCuentasBancos"));
         
        SimpleJdbcCall call = prepareJdbcCall(DatabaseConstants.PROC_GET_CUENTAS_BANCOS,
                new SqlOutParameter(DatabaseConstants.PARAM_OUT_CURSOR, Types.REF_CURSOR, new CuentaBancoRowMapper()),
                new SqlParameter(DatabaseConstants.PARAM_P_MONEDA, Types.VARCHAR),
                new SqlParameter(DatabaseConstants.PARAM_P_ENTIDAD, Types.VARCHAR)
        );

        SqlParameterSource inParams = new MapSqlParameterSource()
                .addValue(DatabaseConstants.PARAM_P_MONEDA, moneda)
                .addValue(DatabaseConstants.PARAM_P_ENTIDAD, entidad);

        log.info(String.format(DatabaseConstants.LOG_LAUNCH_DESC, DatabaseConstants.SCHEMA_NAME,
                DatabaseConstants.CATALOG_PLANILLA_ELECTRONICA_NAME, DatabaseConstants.PROC_GET_CUENTAS_BANCOS));

        Map<String, Object> result = call.execute(inParams);
        return (List<PLE_CuentaBanco>) result.get(DatabaseConstants.PARAM_OUT_CURSOR);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PLE_TotalPagoPlanilla> obtenerTotalPagosPlanilla(Integer idPlanilla) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, "obtenerTotalPagosPlanilla"));
         
        SimpleJdbcCall call = prepareJdbcCall(DatabaseConstants.PROC_OBTENER_TOTAL_PAGOS_PLANILLA,
                new SqlOutParameter(DatabaseConstants.PARAM_OUT_CURSOR, Types.REF_CURSOR, new TotalPagoPlanillaRowMapper()),
                new SqlParameter(DatabaseConstants.PARAM_IN_PLANILLA, Types.INTEGER)
        );

        SqlParameterSource inParams = new MapSqlParameterSource()
                .addValue(DatabaseConstants.PARAM_IN_PLANILLA, idPlanilla);

        log.info(String.format(DatabaseConstants.LOG_LAUNCH_DESC, DatabaseConstants.SCHEMA_NAME,
                DatabaseConstants.CATALOG_PLANILLA_ELECTRONICA_NAME, DatabaseConstants.PROC_OBTENER_TOTAL_PAGOS_PLANILLA));

        Map<String, Object> result = call.execute(inParams);
        return (List<PLE_TotalPagoPlanilla>) result.get(DatabaseConstants.PARAM_OUT_CURSOR);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PLE_MedioPago> obtenerMedioPago() {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, "obtenerMedioPago"));
         
        SimpleJdbcCall call = prepareJdbcCall(DatabaseConstants.PROC_OBTENER_MEDIO_PAGO,
                new SqlOutParameter(DatabaseConstants.PARAM_OUT_CURSOR, Types.REF_CURSOR, new MedioPagoRowMapper())
        );

        SqlParameterSource inParams = new MapSqlParameterSource();

        log.info(String.format(DatabaseConstants.LOG_LAUNCH_DESC, DatabaseConstants.SCHEMA_NAME,
                DatabaseConstants.CATALOG_PLANILLA_ELECTRONICA_NAME, DatabaseConstants.PROC_OBTENER_MEDIO_PAGO));

        Map<String, Object> result = call.execute(inParams);
        return (List<PLE_MedioPago>) result.get(DatabaseConstants.PARAM_OUT_CURSOR);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PLE_TipoPlanilla> obtenerTipoPlanilla() {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, "obtenerTipoPlanilla"));
         
        SimpleJdbcCall call = prepareJdbcCall(DatabaseConstants.PROC_OBTENER_TIPO_PLANILLA,
                new SqlOutParameter(DatabaseConstants.PARAM_OUT_CURSOR, Types.REF_CURSOR, new TipoPlanillaRowMapper())
        );

        SqlParameterSource inParams = new MapSqlParameterSource();

        log.info(String.format(DatabaseConstants.LOG_LAUNCH_DESC, DatabaseConstants.SCHEMA_NAME,
                DatabaseConstants.CATALOG_PLANILLA_ELECTRONICA_NAME, DatabaseConstants.PROC_OBTENER_TIPO_PLANILLA));

        Map<String, Object> result = call.execute(inParams);
        return (List<PLE_TipoPlanilla>) result.get(DatabaseConstants.PARAM_OUT_CURSOR);
    }

}
