package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.AutenticarUsuarioResponseUsuario;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class AutenticarUsuarioRowMapper implements RowMapper<AutenticarUsuarioResponseUsuario> {

    @Override
    public AutenticarUsuarioResponseUsuario mapRow(ResultSet rs, int rowNum) throws SQLException {
        AutenticarUsuarioResponseUsuario usuario = new AutenticarUsuarioResponseUsuario();
        
        usuario.setIdUsuario(rs.getInt("id_usuario"));
        usuario.setIdTipo(rs.getInt("id_tipo"));
        usuario.setNombres(rs.getString("nombres"));
        usuario.setApellidos(rs.getString("apellidos"));
        usuario.setNombreUnicoUsuario(rs.getString("nombre_unico_usuario"));
        usuario.setCobrador(rs.getString("cobrador"));
        usuario.setImgPerfil(rs.getString("img_perfil"));
        usuario.setEstado(rs.getString("estado"));
        usuario.setTipo(rs.getString("tipo"));
        usuario.setCodigo(rs.getString("codigo"));
        usuario.setEsInterno(rs.getInt("usuario_interno"));

        return usuario;
    }
}
