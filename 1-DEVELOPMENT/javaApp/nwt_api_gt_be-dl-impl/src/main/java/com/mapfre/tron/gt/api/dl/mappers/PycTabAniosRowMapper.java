package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycTabAnios;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycTabAnios.
 */
@Slf4j
public class PycTabAniosRowMapper implements RowMapper<PycTabAnios> {

    @Override
    public PycTabAnios mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycTabAnios", rowNum);
        
        PycTabAnios tabAnios = new PycTabAnios();
        
        try {
            tabAnios.setAnios(rs.getString("ANIOS"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return tabAnios;
    }
}
