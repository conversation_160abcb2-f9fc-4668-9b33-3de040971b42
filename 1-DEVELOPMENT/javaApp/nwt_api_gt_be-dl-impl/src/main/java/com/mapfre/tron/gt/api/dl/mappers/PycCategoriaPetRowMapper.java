package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycCategoriaPet;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycCategoriaPet.
 */
@Slf4j
public class PycCategoriaPetRowMapper implements RowMapper<PycCategoriaPet> {

    @Override
    public PycCategoriaPet mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycCategoriaPet", rowNum);
        
        PycCategoriaPet categoriaPet = new PycCategoriaPet();
        
        try {
            categoriaPet.setIdPeticion(rs.getInt("ID_PETICION"));
            categoriaPet.setIdCategoriaTablero(rs.getInt("ID_CATEGORIA_TABLERO"));
            categoriaPet.setNombre(rs.getString("NOMBRE"));
            categoriaPet.setTotal(rs.getInt("TOTAL"));
            categoriaPet.setTerminadas(rs.getInt("TERMINADAS"));
            categoriaPet.setPendientes(rs.getInt("PENDIENTES"));
            categoriaPet.setHorasBase(rs.getDouble("HORAS_BASE"));
            categoriaPet.setHorasReal(rs.getDouble("HORAS_REAL"));
            categoriaPet.setPorcentaje(rs.getDouble("PORCENTAJE"));
            categoriaPet.setFechaInicio(rs.getString("FECHA_INICIO"));
            categoriaPet.setFechaFin(rs.getString("FECHA_FIN"));
            categoriaPet.setFechaRealTerminado(rs.getString("FECHA_REAL_TERMINADO"));
            categoriaPet.setFechaRealPendiente(rs.getString("FECHA_REAL_PENDIENTE"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return categoriaPet;
    }
}
