package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycDatVarForSeccTippe;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycDatVarForSeccTippe.
 *
 * La función FN_GET_DAT_VAR_FORM_SECC_TIPPE retorna todas las columnas disponibles:
 * - ID_FORMULARIO, NOMBRE_FORMULARIO, ID_SECCION, NOMBRE_SECCION, ICONO_SECCION, ID_DATO
 * - DESCRIPCION, ORDEN_SECCION, VAL_DEFAULT, ORDEN, TIPO, TIPO_HTML, CLASS, CLASS_FORM
 * - JAVASCRIPT, ICONO, PLACEHOLDER, VAL_MIN, VAL_MAX, ID_DEPENDENCIA
 * - IND_VISIBLE, IND_OBLIGATORIO, IND_EDITABLE
 */
@Slf4j
public class PycDatVarForSeccTippeRowMapper implements RowMapper<PycDatVarForSeccTippe> {

    @Override
    public PycDatVarForSeccTippe mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycDatVarForSeccTippe", rowNum);
        
        PycDatVarForSeccTippe datVarForSeccTippe = new PycDatVarForSeccTippe();
        
        try {
            // Información de contexto - mapear desde las columnas reales disponibles
            datVarForSeccTippe.setIdProceso(null); // No está en el resultado
            datVarForSeccTippe.setIdFormulario(rs.getInt("ID_FORMULARIO"));
            datVarForSeccTippe.setIdSeccion(rs.getInt("ID_SECCION"));
            datVarForSeccTippe.setIdDatoVariable(rs.getInt("ID_DATO"));

            // Información del formulario y sección - mapear desde las columnas disponibles
            datVarForSeccTippe.setNombreFormulario(rs.getString("NOMBRE_FORMULARIO"));
            datVarForSeccTippe.setNombreSeccion(rs.getString("NOMBRE_SECCION"));
            datVarForSeccTippe.setIconoSeccion(rs.getString("ICONO_SECCION"));
            datVarForSeccTippe.setOrdenSeccion(rs.getInt("ORDEN_SECCION"));

            // Información del dato variable - mapear desde las columnas reales
            datVarForSeccTippe.setNombreDatoVariable(rs.getString("DESCRIPCION")); // Usar DESCRIPCION como nombre
            datVarForSeccTippe.setDescripcionDatoVariable(rs.getString("DESCRIPCION"));
            datVarForSeccTippe.setTipoDato(rs.getString("TIPO"));

            // Valores - mapear desde las columnas reales
            datVarForSeccTippe.setValorDefecto(rs.getString("VAL_DEFAULT"));
            datVarForSeccTippe.setValorPeticion(null); // No está disponible en esta función

            // Configuración - mapear desde las columnas reales
            datVarForSeccTippe.setObligatorio(rs.getString("IND_OBLIGATORIO"));
            datVarForSeccTippe.setOrden(rs.getInt("ORDEN"));

            // Longitudes - mapear desde las columnas reales (pueden ser NULL)
            String valMaxStr = rs.getString("VAL_MAX");
            if (valMaxStr != null && !valMaxStr.trim().isEmpty()) {
                try {
                    datVarForSeccTippe.setLongitudMaxima(Integer.parseInt(valMaxStr));
                } catch (NumberFormatException e) {
                    datVarForSeccTippe.setLongitudMaxima(null);
                }
            } else {
                datVarForSeccTippe.setLongitudMaxima(null);
            }

            String valMinStr = rs.getString("VAL_MIN");
            if (valMinStr != null && !valMinStr.trim().isEmpty()) {
                try {
                    datVarForSeccTippe.setLongitudMinima(Integer.parseInt(valMinStr));
                } catch (NumberFormatException e) {
                    datVarForSeccTippe.setLongitudMinima(null);
                }
            } else {
                datVarForSeccTippe.setLongitudMinima(null);
            }

            // Validaciones - no están disponibles en esta función
            datVarForSeccTippe.setPatronValidacion(null);
            datVarForSeccTippe.setMensajeError(null);

            // Control HTML - mapear desde las columnas reales disponibles
            datVarForSeccTippe.setTipoControl(rs.getString("TIPO_HTML"));

            // Mapear cada campo individual de control HTML a su propio campo en el modelo
            datVarForSeccTippe.setClassField(rs.getString("CLASS"));
            datVarForSeccTippe.setClassForm(rs.getString("CLASS_FORM"));
            datVarForSeccTippe.setJavascript(rs.getString("JAVASCRIPT"));
            datVarForSeccTippe.setIcono(rs.getString("ICONO"));
            datVarForSeccTippe.setPlaceholder(rs.getString("PLACEHOLDER"));
            datVarForSeccTippe.setIndVisible(rs.getString("IND_VISIBLE"));
            datVarForSeccTippe.setEditable(rs.getString("IND_EDITABLE"));

            // Dejar propiedadesControl como null ya que ahora tenemos campos separados
            datVarForSeccTippe.setPropiedadesControl(null);

            // Dependencias - mapear desde las columnas reales (pueden ser NULL)
            Integer dependeDe = rs.getObject("ID_DEPENDENCIA", Integer.class);
            datVarForSeccTippe.setDependeDe(dependeDe);
            datVarForSeccTippe.setValorDependencia(null); // No está disponible en esta función

        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return datVarForSeccTippe;
    }
}
