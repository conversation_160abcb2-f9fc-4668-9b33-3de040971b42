package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycAreaUsuario;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycAreaUsuario.
 */
@Slf4j
public class PycAreaUsuarioRowMapper implements RowMapper<PycAreaUsuario> {

    @Override
    public PycAreaUsuario mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycAreaUsuario", rowNum);
        
        PycAreaUsuario areaUsuario = new PycAreaUsuario();
        
        try {
            areaUsuario.setIdUsuario(rs.getInt("ID_USUARIO"));
            areaUsuario.setUsuario(rs.getString("USUARIO"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return areaUsuario;
    }
}
