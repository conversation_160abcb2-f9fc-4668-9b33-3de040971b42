package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.SiniestroNit;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper para convertir resultados de la base de datos a objetos SiniestroNit.
 */
public class SiniestroNitRowMapper implements RowMapper<SiniestroNit> {

    @Override
    public SiniestroNit mapRow(ResultSet rs, int rowNum) throws SQLException {
        SiniestroNit siniestroNit = new SiniestroNit();
        
        siniestroNit.setSiniestro(rs.getString("SINIESTRO"));
        siniestroNit.setPoliza(rs.getString("POLIZA"));
        
        return siniestroNit;
    }
}
