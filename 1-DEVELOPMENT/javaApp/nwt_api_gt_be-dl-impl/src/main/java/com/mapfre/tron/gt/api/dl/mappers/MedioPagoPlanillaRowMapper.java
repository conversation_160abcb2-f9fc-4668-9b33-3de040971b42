package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PLE_MedioPagoPlanilla;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PLE_MedioPagoPlanilla.
 */
@Slf4j
public class MedioPagoPlanillaRowMapper implements RowMapper<PLE_MedioPagoPlanilla> {

    @Override
    public PLE_MedioPagoPlanilla mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PLE_MedioPagoPlanilla", rowNum);
        
        PLE_MedioPagoPlanilla response = new PLE_MedioPagoPlanilla();
        
        try {
            response.setIdplanilla(rs.getInt("idplanilla"));
            response.setRequerimientos(rs.getString("requerimientos"));
            response.setIdmedioPago(rs.getInt("idmedio_pago"));
            response.setMedioPago(rs.getString("medio_pago"));
            response.setCodmoneda(rs.getString("codmoneda"));
            response.setMonto(rs.getBigDecimal("monto"));
            response.setFechaCheque(rs.getDate("fecha_cheque"));
            response.setFechaDeposito(rs.getDate("fecha_deposito"));
            response.setCodbanco(rs.getString("codbanco"));
            response.setNombreBanco(rs.getString("nombre_banco"));
            response.setDocumento(rs.getString("documento"));
            response.setComentario(rs.getString("comentario"));
            response.setIdentificador(rs.getString("identificador"));
            response.setEstadoMediopago(rs.getString("estado_mediopago"));
            response.setCorrelativo(rs.getInt("correlativo"));
            response.setGrupo(rs.getString("grupo"));
            response.setEnviarMensajero(rs.getString("enviar_mensajero"));
            response.setCuentaCodigo(rs.getString("cuenta_codigo"));
            response.setCuentaNumero(rs.getString("cuenta_numero"));
            response.setCobroAnticipado(rs.getString("cobro_anticipado"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
