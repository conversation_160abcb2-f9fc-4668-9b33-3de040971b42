package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycCatAct;

import lombok.extern.slf4j.Slf4j;

/**
 * RowMapper para mapear resultados de la función FN_OBTENER_CATEGORIA_ACT a objetos PycCatAct.
 * 
 * Esta clase mapea los resultados de la consulta de categorías activas del tablero
 * asociadas a un proceso específico.
 */
@Slf4j
public class PycCatActRowMapper implements RowMapper<PycCatAct> {

    @Override
    public PycCatAct mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} del ResultSet para PycCatAct", rowNum);
        
        PycCatAct catAct = new PycCatAct();
        
        try {
            // Mapear campos de la función FN_OBTENER_CATEGORIA_ACT
            catAct.setIdCategoria(rs.getInt("ID_CATEGORIA"));
            catAct.setNombre(rs.getString("NOMBRE"));
            
            log.debug("PycCatAct mapeado exitosamente: ID_CATEGORIA={}, NOMBRE={}", 
                     catAct.getIdCategoria(), catAct.getNombre());
            
        } catch (SQLException e) {
            log.error("Error al mapear PycCatAct en la fila {}: {}", rowNum, e.getMessage(), e);
            throw e;
        }
        
        return catAct;
    }
}
