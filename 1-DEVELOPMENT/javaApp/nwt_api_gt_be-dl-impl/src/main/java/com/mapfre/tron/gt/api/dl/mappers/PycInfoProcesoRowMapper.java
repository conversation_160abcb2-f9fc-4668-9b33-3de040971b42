package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

import org.springframework.jdbc.core.RowMapper;
import static com.mapfre.tron.gt.api.sr.CacheApi.log;
import com.mapfre.tron.gt.api.model.PycInfoProceso;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycInfoProceso.
 */
@Slf4j
public class PycInfoProcesoRowMapper implements RowMapper<PycInfoProceso> {

    @Override
    public PycInfoProceso mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycInfoProceso", rowNum);

        PycInfoProceso infoProceso = new PycInfoProceso();

        try {
            // Campos obligatorios (NOT NULL)
            infoProceso.setIdProceso(rs.getInt("ID_PROCESO"));
            infoProceso.setNombreProceso(rs.getString("NOMBRE_PROCESO"));
            infoProceso.setEstado(rs.getString("ESTADO"));
            infoProceso.setUsuario(rs.getString("USUARIO"));

            // Manejo de fecha (NOT NULL)
            Timestamp fechaHora = rs.getTimestamp("FECHA_HORA");
            if (fechaHora != null) {
                infoProceso.setFechaHora(fechaHora.toString());
            }

            // Campo opcional
            infoProceso.setIcono(rs.getString("ICONO"));

            // Campo obligatorio
            infoProceso.setUtilizaFiltros(rs.getString("UTLIZA_FILTOS"));

            // Campos opcionales (pueden ser null)
            Integer columInicio = rs.getObject("COLUM_INICIO", Integer.class);
            infoProceso.setColumInicio(columInicio);

            Integer columFin = rs.getObject("COLUM_FIN", Integer.class);
            infoProceso.setColumFin(columFin);

            // Campos obligatorios (NOT NULL)
            infoProceso.setMcaRefId(rs.getString("MCA_REF_ID"));
            infoProceso.setMcaUpdSol(rs.getString("MCA_UPD_SOL"));
            infoProceso.setMcaAsigEstCom(rs.getString("MCA_ASIG_EST_COM"));
            infoProceso.setMcaInhSecAct(rs.getString("MCA_INH_SEC_ACT"));
            infoProceso.setMcaInhSecObs(rs.getString("MCA_INH_SEC_OBS"));
            infoProceso.setMcaInhSecInv(rs.getString("MCA_INH_SEC_INV"));
            infoProceso.setDirFtpWeb(rs.getString("DIR_FTP_WEB"));

            // Campos CLOB (pueden ser null)
            infoProceso.setSqlSelect(rs.getString("SQL_SELECT"));
            infoProceso.setSqlTable(rs.getString("SQL_TABLE"));
            infoProceso.setSqlWhere(rs.getString("SQL_WHERE"));
            infoProceso.setDatatableHtml(rs.getString("DATATABLE_HTML"));
            infoProceso.setDatatableJs(rs.getString("DATATABLE_JS"));

            // Campo obligatorio
            infoProceso.setMcaIgnorInter(rs.getString("MCA_IGNOR_INTER"));

            // Campos CLOB (pueden ser null)
            infoProceso.setDatatableDef(rs.getString("DATATABLE_DEF"));
            infoProceso.setExtraHtml(rs.getString("EXTRA_HTML"));

            // Campo opcional
            infoProceso.setEmailCc(rs.getString("EMAIL_CC"));

            // Campos obligatorios
            infoProceso.setMcaCommentOblig(rs.getString("MCA_COMMENT_OBLIG"));
            infoProceso.setMcaSecuIdReferencia(rs.getString("MCA_SECU_ID_REFERENCIA"));
            infoProceso.setMcaAsigAutom(rs.getString("MCA_ASIG_AUTOM"));
            infoProceso.setMcaCambioSol(rs.getString("MCA_CAMBIO_SOL"));

        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }

        return infoProceso;
    }
}
