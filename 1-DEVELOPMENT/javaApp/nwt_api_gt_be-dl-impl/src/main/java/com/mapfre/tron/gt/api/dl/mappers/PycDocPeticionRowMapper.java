package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

import org.springframework.jdbc.core.RowMapper;
import static com.mapfre.tron.gt.api.sr.CacheApi.log;
import com.mapfre.tron.gt.api.model.PycDocPeticion;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycDocPeticion.
 * 
 * La función FN_DOCUMENTO_PETICION retorna los siguientes campos:
 * - ID_PETICION: Identificador único de la petición
 * - ID_DOCUMENTO: Identificador único del documento
 * - NOMBRE_DOCUMENTO: Nombre del documento con secuencia si aplica
 * - DESCRIPCION_DOCUMENTO: Descripción del documento
 * - LOCALIZACION: Ubicación o ruta del archivo del documento
 * - ID_USUARIO: Identificador del usuario que subió el documento
 * - USUARIO_BASE_DATOS: Usuario de base de datos que realizó la operación
 * - FECHA_RECEPCION: Fecha y hora de recepción formateada (dd/MM/yyyy HH24:MI)
 * - USUARIO: Nombre completo del usuario que subió el documento
 * - FTP_WEB: Indicador de ubicación FTP o Web del documento
 * - FECHA: Fecha de recepción en formato timestamp
 * - VERSION: Versión del documento
 */
@Slf4j
public class PycDocPeticionRowMapper implements RowMapper<PycDocPeticion> {

    @Override
    public PycDocPeticion mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycDocPeticion", rowNum);
        
        PycDocPeticion docPeticion = new PycDocPeticion();
        
        try {
            // Mapear ID_PETICION
            docPeticion.setIdPeticion(rs.getInt("ID_PETICION"));
            
            // Mapear ID_DOCUMENTO
            docPeticion.setIdDocumento(rs.getInt("ID_DOCUMENTO"));
            
            // Mapear NOMBRE_DOCUMENTO (con lógica de secuencia)
            docPeticion.setNombreDocumento(rs.getString("NOMBRE_DOCUMENTO"));
            
            // Mapear DESCRIPCION_DOCUMENTO
            docPeticion.setDescripcionDocumento(rs.getString("DESCRIPCION_DOCUMENTO"));
            
            // Mapear LOCALIZACION (puede ser NULL para documentos disponibles)
            docPeticion.setLocalizacion(rs.getString("LOCALIZACION"));
            
            // Mapear ID_USUARIO (puede ser NULL para documentos disponibles)
            Integer idUsuario = rs.getInt("ID_USUARIO");
            if (rs.wasNull()) {
                docPeticion.setIdUsuario(null);
            } else {
                docPeticion.setIdUsuario(idUsuario);
            }
            
            // Mapear USUARIO_BASE_DATOS (puede ser NULL)
            docPeticion.setUsuarioBaseDatos(rs.getString("USUARIO_BASE_DATOS"));
            
            // Mapear FECHA_RECEPCION (formateada, puede ser NULL)
            docPeticion.setFechaRecepcion(rs.getString("FECHA_RECEPCION"));
            
            // Mapear USUARIO (nombre completo, puede ser NULL)
            docPeticion.setUsuario(rs.getString("USUARIO"));
            
            // Mapear FTP_WEB (puede ser NULL)
            docPeticion.setFtpWeb(rs.getString("FTP_WEB"));

            // Mapear FECHA (timestamp, puede ser NULL) - siguiendo el patrón de otros mappers
            Timestamp fechaTimestamp = rs.getTimestamp("FECHA");
            if (fechaTimestamp != null) {
                docPeticion.setFecha(fechaTimestamp.toString());
            } else {
                docPeticion.setFecha(null);
            }

            // Mapear VERSION (puede ser NULL)
            docPeticion.setVersion(rs.getString("VERSION"));
            
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return docPeticion;
    }
}
