package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PLE_DetallePlanilla;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PLE_DetallePlanilla.
 */
@Slf4j
public class DetallePlanillaRowMapper implements RowMapper<PLE_DetallePlanilla> {

    @Override
    public PLE_DetallePlanilla mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PLE_DetallePlanilla", rowNum);
        
        PLE_DetallePlanilla response = new PLE_DetallePlanilla();
        
        try {
            response.setIdplanillaDetalle(rs.getInt("idplanilla_detalle"));
            response.setPoliza(rs.getString("poliza"));
            response.setRequerimiento(rs.getString("requerimiento"));
            response.setFactura(rs.getString("factura"));
            response.setNombrePagador(rs.getString("nombre_pagador"));
            response.setNitPagador(rs.getString("nit_pagador"));
            response.setNoCuota(rs.getString("no_cuota"));
            response.setTipoMoneda(rs.getString("tipo_moneda"));
            response.setPrima(rs.getBigDecimal("prima"));
            response.setTipoPago(rs.getString("tipo_pago"));
            response.setComentario(rs.getString("comentario"));
            response.setEstadoPoliza(rs.getString("estado_poliza"));
            response.setSistema(rs.getString("sistema"));
            response.setFechaVig(rs.getDate("fecha_vig"));
            response.setEstadoCobro(rs.getString("estado_cobro"));
            response.setEsaviso(rs.getString("esaviso"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
