package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycReportePetProg;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycReportePetProg.
 */
@Slf4j
public class PycReportePetProgRowMapper implements RowMapper<PycReportePetProg> {

    @Override
    public PycReportePetProg mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycReportePetProg", rowNum);
        
        PycReportePetProg reportePetProg = new PycReportePetProg();
        
        try {
            reportePetProg.setIdPeticion(rs.getInt("ID_PETICION"));
            reportePetProg.setTipo(rs.getString("TIPO"));
            reportePetProg.setNombrePeticion(rs.getString("NOMBRE_PETICION"));
            reportePetProg.setDescPeticion(rs.getString("DESC_PETICION"));
            reportePetProg.setFechaCreacion(rs.getString("FECHA_CREACION"));
            reportePetProg.setEstado(rs.getString("ESTADO"));
            reportePetProg.setFechaInicio(rs.getString("FECHA_INICIO"));
            reportePetProg.setFechaFin(rs.getString("FECHA_FIN"));
            reportePetProg.setInicioDesa(rs.getString("INICIO_DESA"));
            reportePetProg.setFinDesa(rs.getString("FIN_DESA"));
            reportePetProg.setHoraDesa(rs.getDouble("HORA_DESA"));
            reportePetProg.setInicioAnalisis(rs.getString("INICIO_ANALISIS"));
            reportePetProg.setFinAnalisis(rs.getString("FIN_ANALISIS"));
            reportePetProg.setHoraAnalisis(rs.getDouble("HORA_ANALISIS"));
            reportePetProg.setPrioridad(rs.getString("PRIORIDAD"));
            reportePetProg.setUsuario(rs.getString("USUARIO"));
            reportePetProg.setAnalista(rs.getString("ANALISTA"));
            reportePetProg.setCodClarity(rs.getString("COD_CLARITY"));
            reportePetProg.setNombreArea(rs.getString("NOMBRE_AREA"));
            reportePetProg.setNombreDepartamento(rs.getString("NOMBRE_DEPARTAMENTO"));
            reportePetProg.setObservaciones(rs.getString("OBSERVACIONES"));
            reportePetProg.setIdPeticionSiguiente(rs.getInt("ID_PETICION_SIGUIENTE"));
            reportePetProg.setNomPeticionSiguiente(rs.getString("NOM_PETICION_SIGUIENTE"));
            reportePetProg.setDiasTotal(rs.getDouble("DIAS_TOTAL"));
            reportePetProg.setSemanaTotal(rs.getDouble("SEMANA_TOTAL"));
            reportePetProg.setDiasInhabiles(rs.getDouble("DIAS_INHABILDES"));
            reportePetProg.setDiasReales(rs.getDouble("DIAS_REALES"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return reportePetProg;
    }
}
