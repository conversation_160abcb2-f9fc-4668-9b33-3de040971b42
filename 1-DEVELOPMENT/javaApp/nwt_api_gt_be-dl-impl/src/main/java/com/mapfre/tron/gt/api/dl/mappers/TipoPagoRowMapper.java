package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.TipoPagoResponse;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper para convertir resultados de la base de datos a objetos TipoPagoResponse.
 */
public class TipoPagoRowMapper implements RowMapper<TipoPagoResponse> {

    @Override
    public TipoPagoResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        TipoPagoResponse response = new TipoPagoResponse();

        // Mapear los campos según la definición en TipoPagoResponse
        response.setCodigo(rs.getString("CODIGO"));
        response.setNombre(rs.getString("NOMBRE"));

        return response;
    }
}