package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycPetAvance;
import static com.mapfre.tron.gt.api.sr.CacheApi.log;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycPetAvance.
 */
@Slf4j
public class PycPetAvanceRowMapper implements RowMapper<PycPetAvance> {

    @Override
    public PycPetAvance mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycPetAvance", rowNum);

        PycPetAvance petAvance = new PycPetAvance();

        try {
            // Información de la petición principal
            petAvance.setIdPeticion(rs.getInt("ID_PETICION"));
            petAvance.setNombrePeticion(rs.getString("NOMBRE_PETICION"));
            petAvance.setFechaInicio(rs.getString("FECHA_INICIO"));
            petAvance.setFechaFin(rs.getString("FECHA_FIN"));
            petAvance.setNombreEstado(rs.getString("NOMBRE_ESTADO"));
            
            // Información de la petición siguiente
            petAvance.setIdPeticionSiguiente(rs.getInt("ID_PETICION_SIGUIENTE"));
            petAvance.setNombrePeticionSiguiente(rs.getString("NOMBRE_PETICION_SIGUIENTE"));
            petAvance.setFechaInicioSiguiente(rs.getString("FECHA_INICIO_SIGUIENTE"));
            petAvance.setFechaFinSiguiente(rs.getString("FECHA_FIN_SIGUIENTE"));
            
            // Información de observaciones
            petAvance.setIdObservacion(rs.getInt("ID_OBSERVACION"));
            petAvance.setObservacion(rs.getString("OBSERVACION"));
            petAvance.setFechaHoraObservacion(rs.getString("FECHA_HORA_OBSERVACION"));
            
            // Información del usuario
            petAvance.setNombreUsuario(rs.getString("NOMBRE_USUARIO"));
            petAvance.setGeneroUsuario(rs.getString("GENERO_USUARIO"));
            petAvance.setIndicadorUsuario(rs.getString("INDICADOR_USUARIO"));
            petAvance.setUrlPerfil(rs.getString("URL_PERFIL"));
            
            // Información adicional
            petAvance.setTipoPeticion(rs.getString("TIPO_PETICION"));
            petAvance.setPrioridad(rs.getString("PRIORIDAD"));
            petAvance.setSolicitante(rs.getString("SOLICITANTE"));

        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }

        return petAvance;
    }
}
