package com.mapfre.tron.gt.api.dl;

import java.sql.Types;
import java.util.Date;
import java.util.Map;

import com.mapfre.tron.gt.api.commons.CurrencyType;
import com.mapfre.tron.gt.api.commons.DatabaseConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataRetrievalFailureException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.SqlParameter;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.jdbc.core.simple.SimpleJdbcCall;
import org.springframework.stereotype.Repository;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;

@Repository
@Slf4j
public class DlCommonsImpl implements IDlCommons {

    @Autowired
    @Qualifier("jdbcTemplate")
    protected JdbcTemplate jdbcTemplate;

    private SimpleJdbcCall prepareJdbcCall(String procedureName, SqlParameter... parameters) {
        return new SimpleJdbcCall(jdbcTemplate)
                .withSchemaName(DatabaseConstants.SCHEMA_NAME)
                .withProcedureName(procedureName)
                .declareParameters(parameters);
    }

    @Override
    public boolean actualizacionFechaEquivalente(Date fecha) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, DatabaseConstants.PROC_ACTUALIZAR_FECHA_EQUIVALENTE));

        try {
            SimpleJdbcCall call = prepareJdbcCall(DatabaseConstants.PROC_ACTUALIZAR_FECHA_EQUIVALENTE,
                    new SqlParameter(DatabaseConstants.PARAM_COD_CIA, Types.INTEGER),
                    new SqlParameter(DatabaseConstants.PARAM_FECHA, Types.DATE)
            );

            SqlParameterSource inParams = new MapSqlParameterSource()
                    .addValue(DatabaseConstants.PARAM_COD_CIA, DatabaseConstants.COD_CIA)
                    .addValue(DatabaseConstants.PARAM_FECHA, fecha);

            log.info(String.format(DatabaseConstants.LOG_LAUNCH_DESC,
                    DatabaseConstants.SCHEMA_NAME,
                    "-",
                    DatabaseConstants.PROC_ACTUALIZAR_FECHA_EQUIVALENTE));

            call.execute(inParams);
            return true;

        } catch (Exception ex) {
            log.error("Error ejecutando actualizacionFechaEquivalente", ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.PROC_ACTUALIZAR_FECHA_EQUIVALENTE, ex);

        }
    }

    @Override
    public boolean actualizacionDeTasaDeCambio(Date fecha, double tasaCambio) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, DatabaseConstants.PROC_ACTUALIZAR_TASA_CAMBIO));

        try {
            SimpleJdbcCall call = prepareJdbcCall(DatabaseConstants.PROC_ACTUALIZAR_TASA_CAMBIO,
                    new SqlParameter(DatabaseConstants.PARAM_COD_CIA, Types.INTEGER),
                    new SqlParameter(DatabaseConstants.PARAM_FECHA, Types.DATE),
                    new SqlParameter(DatabaseConstants.PARAM_TASA_CAMBIO, Types.NUMERIC),
                    new SqlParameter(DatabaseConstants.PARAM_COD_MON, Types.INTEGER)
            );

            SqlParameterSource inParams = new MapSqlParameterSource()
                    .addValue(DatabaseConstants.PARAM_COD_CIA, DatabaseConstants.COD_CIA)
                    .addValue(DatabaseConstants.PARAM_FECHA, fecha)
                    .addValue(DatabaseConstants.PARAM_TASA_CAMBIO, tasaCambio)
                    .addValue(DatabaseConstants.PARAM_COD_MON, CurrencyType.COD_MON_DOLAR);

            log.info(String.format(DatabaseConstants.LOG_LAUNCH_DESC,
                    DatabaseConstants.SCHEMA_NAME,
                    "-",
                    DatabaseConstants.PROC_ACTUALIZAR_TASA_CAMBIO));

            call.execute(inParams);
            return true;

        } catch (Exception ex) {
            log.error("Error ejecutando actualizacionDeTasaDeCambio", ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.PROC_ACTUALIZAR_TASA_CAMBIO, ex);
        }
    }

}
