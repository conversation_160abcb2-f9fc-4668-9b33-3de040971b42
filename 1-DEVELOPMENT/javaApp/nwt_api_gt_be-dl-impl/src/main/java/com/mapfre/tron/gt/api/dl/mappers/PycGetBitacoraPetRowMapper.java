package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycGetBitacoraPet;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycGetBitacoraPet.
 * 
 * La función FN_OBTENER_BITACORA_PETICION retorna los siguientes campos:
 * - ID_PETICION: ID de la petición
 * - COD_PERFIL_INICIAL, COD_PERFIL_FINAL: Códigos de perfiles inicial y final
 * - COD_ESTADO_INICIAL, COD_ESTADO_FINAL: Códigos de estados inicial y final
 * - PERFIL_INICIAL, PERFIL_FINAL: Nombres de perfiles inicial y final
 * - ESTADO_INICIAL, ESTADO_FINAL: Nombres de estados inicial y final
 * - RESPONSABLES: Lista de responsables asignados
 * - USUARIO: Usuario que realizó el cambio
 * - FEC_INI, FEC_FIN: Fechas de inicio y fin formateadas (dd/MM/yyyy HH24:MI)
 * - ANIO, MES, DIA, HORA, MINUTO, SEGUNDO: Componentes de tiempo transcurrido
 * - TITULO: Título del evento ("CAMBIO DE ESTADO")
 * - TIEMPO: Descripción del tiempo transcurrido
 * - ICONO: Icono del estado
 * - CONTENIDO: Contenido HTML formateado con detalles completos
 * - PERFIL_USUARIO: URL del perfil del usuario
 */
@Slf4j
public class PycGetBitacoraPetRowMapper implements RowMapper<PycGetBitacoraPet> {

    @Override
    public PycGetBitacoraPet mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycGetBitacoraPet", rowNum);
        
        PycGetBitacoraPet bitacoraPet = new PycGetBitacoraPet();
        
        try {
            // Información básica de la petición
            bitacoraPet.setIdPeticion(rs.getInt("ID_PETICION"));
            
            // Códigos de perfil y estado
            bitacoraPet.setCodPerfilInicial(rs.getInt("COD_PERFIL_INICIAL"));
            bitacoraPet.setCodPerfilFinal(rs.getInt("COD_PERFIL_FINAL"));
            bitacoraPet.setCodEstadoInicial(rs.getInt("COD_ESTADO_INICIAL"));
            bitacoraPet.setCodEstadoFinal(rs.getInt("COD_ESTADO_FINAL"));
            
            // Nombres descriptivos de perfil y estado
            bitacoraPet.setPerfilInicial(rs.getString("PERFIL_INICIAL"));
            bitacoraPet.setPerfilFinal(rs.getString("PERFIL_FINAL"));
            bitacoraPet.setEstadoInicial(rs.getString("ESTADO_INICIAL"));
            bitacoraPet.setEstadoFinal(rs.getString("ESTADO_FINAL"));
            
            // Información de usuarios y responsables
            bitacoraPet.setResponsables(rs.getString("RESPONSABLES"));
            bitacoraPet.setUsuario(rs.getString("USUARIO"));
            bitacoraPet.setPerfilUsuario(rs.getString("PERFIL_USUARIO"));
            
            // Fechas formateadas (ya vienen formateadas desde la función Oracle)
            bitacoraPet.setFecIni(rs.getString("FEC_INI"));
            bitacoraPet.setFecFin(rs.getString("FEC_FIN"));
            
            // Componentes de tiempo transcurrido
            bitacoraPet.setAnio(rs.getInt("ANIO"));
            bitacoraPet.setMes(rs.getInt("MES"));
            bitacoraPet.setDia(rs.getInt("DIA"));
            bitacoraPet.setHora(rs.getInt("HORA"));
            bitacoraPet.setMinuto(rs.getInt("MINUTO"));
            bitacoraPet.setSegundo(rs.getInt("SEGUNDO"));
            
            // Información de presentación
            bitacoraPet.setTitulo(rs.getString("TITULO"));
            bitacoraPet.setTiempo(rs.getString("TIEMPO"));
            bitacoraPet.setIcono(rs.getString("ICONO"));
            
            // Contenido HTML formateado (campo principal con toda la información)
            bitacoraPet.setContenido(rs.getString("CONTENIDO"));
            
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return bitacoraPet;
    }
}
