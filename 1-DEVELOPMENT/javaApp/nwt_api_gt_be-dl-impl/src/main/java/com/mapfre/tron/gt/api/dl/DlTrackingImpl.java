package com.mapfre.tron.gt.api.dl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.mapfre.tron.gt.api.commons.DatabaseConstants;
import com.mapfre.tron.gt.api.dl.mappers.DetalleSiniestroRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.EncuestaSiniestroRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.EtapaSiniestroRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.SiniestroNitRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.SiniestroRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.SiniestroVehiculoRowMapper;
import com.mapfre.tron.gt.api.model.DetalleSiniestro;
import com.mapfre.tron.gt.api.model.EncuestaSiniestro;
import com.mapfre.tron.gt.api.model.EtapaSiniestro;
import com.mapfre.tron.gt.api.model.Siniestro;
import com.mapfre.tron.gt.api.model.SiniestroNit;
import com.mapfre.tron.gt.api.model.SiniestroVehiculo;
import com.mapfre.tron.gt.api.model.UrlSiniestro;

import lombok.extern.slf4j.Slf4j;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;

@Repository
@Slf4j
public class DlTrackingImpl implements IDlTracking {

    @Autowired
    private DataSource dataSource;

    @Override
    public List<Siniestro> getSiniestros(Integer codRamo, Integer codInter, String numPoliza,
                                         String numSini, LocalDate fechaInicio, LocalDate fechaFin) {
        log.info("Ejecutando función getSiniestros con codRamo={}, codInter={}, numPoliza={}, numSini={}, fechaInicio={}, fechaFin={}",
                codRamo, codInter, numPoliza, numSini, fechaInicio, fechaFin);

        // Usar exactamente la misma sintaxis que en la clase que sí funciona
        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.DC_K_UTILS_WEB_MGT + "." +
                DatabaseConstants.F_SINIESTROS +
                "(?, ?, ?, ?, ?, ?) AS siniestros FROM dual";

        List<Siniestro> result = new ArrayList<>();
        SiniestroRowMapper mapper = new SiniestroRowMapper();
        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {
            // Asegurarse de que los parámetros se establecen correctamente
            stmt.setInt(1, codRamo);
            // Para el resto de los parámetros, si son null, establecerlos explícitamente como null
            if (codInter != null) {
                stmt.setInt(2, codInter);
            } else {
                stmt.setNull(2, java.sql.Types.INTEGER);
            }

            if (numPoliza != null && !numPoliza.trim().isEmpty()) {
                stmt.setString(3, numPoliza);
            } else {
                stmt.setNull(3, java.sql.Types.VARCHAR);
            }

            if (numSini != null && !numSini.trim().isEmpty()) {
                stmt.setString(4, numSini);
            } else {
                stmt.setNull(4, java.sql.Types.VARCHAR);
            }

            if (fechaInicio != null) {
                stmt.setDate(5, Date.valueOf(fechaInicio));
            } else {
                stmt.setNull(5, java.sql.Types.DATE);
            }

            if (fechaFin != null) {
                stmt.setDate(6, Date.valueOf(fechaFin));
            } else {
                stmt.setNull(6, java.sql.Types.DATE);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("siniestros");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }
            log.info("Función ejecutada correctamente. Siniestros encontrados: {}", result.size());
            return result;
        } catch (SQLException e) {
            log.error("Error al ejecutar la función getSiniestros: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los siniestros", e);
        }
    }

    @Override
    public Integer getRamoSiniestro(String numSiniestro) {
        log.info("Ejecutando función getRamoSiniestro con numSiniestro={}", numSiniestro);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.DC_K_UTILS_WEB_MGT + "." +
                DatabaseConstants.F_SIN_RAMO +
                "(?) AS RAMO FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            stmt.setString(1, numSiniestro);

            if (stmt.execute()) {
                try (ResultSet rs = stmt.getResultSet()) {
                    if (rs.next()) {
                        Integer ramo = rs.getInt("RAMO");
                        if (rs.wasNull()) {
                            return null;
                        }
                        log.info("Función ejecutada correctamente. Ramo encontrado: {}", ramo);
                        return ramo;
                    }
                }
            }

            log.info("No se encontró ramo para el siniestro: {}", numSiniestro);
            return null;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getRamoSiniestro: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el ramo del siniestro", e);
        }
    }

    @Override
    public List<EtapaSiniestro> getTrackingEtapaSiniestro(String numSiniestro, Integer codRamo) {
        log.info("Ejecutando función getTrackingEtapaSiniestro con numSiniestro={}, codRamo={}", numSiniestro, codRamo);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.DC_K_UTILS_WEB_MGT + "." +
                          DatabaseConstants.F_ETAPA_SINIESTRO +
                          "(?, ?) AS etapas FROM dual";

        List<EtapaSiniestro> result = new ArrayList<>();
        EtapaSiniestroRowMapper mapper = new EtapaSiniestroRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            stmt.setString(1, numSiniestro);
            stmt.setInt(2, codRamo);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("etapas");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Etapas encontradas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getTrackingEtapaSiniestro: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las etapas del siniestro", e);
        }
    }

    @Override
    public List<SiniestroNit> getSiniestroByDoc(String numSiniestro, String numDoc, String tipoDoc) {
        log.info("Ejecutando función getSiniestroByDoc con numSiniestro={}, numDoc={}, tipoDoc={}", numSiniestro, numDoc, tipoDoc);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.DC_K_UTILS_WEB_MGT + "." +
                          DatabaseConstants.F_GET_SNTROS +
                          "(?, ?, ?) AS siniestros FROM dual";

        List<SiniestroNit> result = new ArrayList<>();
        SiniestroNitRowMapper mapper = new SiniestroNitRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            stmt.setString(1, numSiniestro);
            stmt.setString(2, numDoc);
            stmt.setString(3, tipoDoc);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("siniestros");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Siniestros encontrados: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getSiniestroByDoc: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los siniestros por documento", e);
        }
    }

    @Override
    public UrlSiniestro getUrlSiniestro(String url, String numSiniestro, String tipoCliente) {
        log.info("Ejecutando función getUrlSiniestro con url={}, numSiniestro={}, tipoCliente={}", url, numSiniestro, tipoCliente);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.DC_K_UTILS_WEB_MGT + "." +
                          DatabaseConstants.F_SIN_CRYPT +
                          "(?, ?, ?) AS url FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            stmt.setString(1, url);
            stmt.setString(2, numSiniestro);
            stmt.setString(3, tipoCliente);

            if (stmt.execute()) {
                try (ResultSet rs = stmt.getResultSet()) {
                    if (rs.next()) {
                        String urlEncriptada = rs.getString("url");
                        UrlSiniestro urlSiniestro = new UrlSiniestro();
                        urlSiniestro.setUrl(urlEncriptada);

                        log.info("Función ejecutada correctamente. URL encriptada: {}", urlEncriptada);
                        return urlSiniestro;
                    }
                }
            }

            log.info("No se pudo generar la URL encriptada");
            return null;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getUrlSiniestro: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la URL encriptada", e);
        }
    }

    @Override
    public List<DetalleSiniestro> getTrackingDetalleSiniestro(String numSiniestro, Integer codRamo) {
        log.info("Ejecutando función getTrackingDetalleSiniestro con numSiniestro={}, codRamo={}", numSiniestro, codRamo);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.DC_K_UTILS_WEB_MGT + "." +
                          DatabaseConstants.F_DESC_SINIESTROS +
                          "(?, ?) AS detalle FROM dual";

        List<DetalleSiniestro> result = new ArrayList<>();
        DetalleSiniestroRowMapper mapper = new DetalleSiniestroRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            stmt.setString(1, numSiniestro);
            stmt.setInt(2, codRamo);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("detalle");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Detalles encontrados: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getTrackingDetalleSiniestro: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el detalle del siniestro", e);
        }
    }

    @Override
    public List<EncuestaSiniestro> getEncuestaSiniestro() {
        log.info("Ejecutando función getEncuestaSiniestro");

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.DC_K_UTILS_WEB_MGT + "." +
                          DatabaseConstants.F_GET_ENCUESTA +
                          "() AS encuesta FROM dual";

        List<EncuestaSiniestro> result = new ArrayList<>();
        EncuestaSiniestroRowMapper mapper = new EncuestaSiniestroRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("encuesta");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Encuestas encontradas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getEncuestaSiniestro: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la encuesta de siniestro", e);
        }
    }

    @Override
    public String saveEncuesta(String numSiniestro, Integer idEncuesta, String archXml) {
        log.info("Ejecutando función saveEncuesta con numSiniestro={}, idEncuesta={}, archXml={}", numSiniestro, idEncuesta, archXml);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.DC_K_UTILS_WEB_MGT + "." +
                          DatabaseConstants.F_SAVE_ENCUESTAS +
                          "(?, ?, ?) AS result FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            stmt.setString(1, numSiniestro);
            stmt.setInt(2, idEncuesta);
            stmt.setString(3, archXml);

            if (stmt.execute()) {
                try (ResultSet rs = stmt.getResultSet()) {
                    if (rs.next()) {
                        String resultado = rs.getString("result");
                        log.info("Función ejecutada correctamente. Resultado: {}", resultado);
                        return resultado;
                    }
                }
            }

            log.info("No se pudo guardar la encuesta");
            return "ERROR";

        } catch (SQLException e) {
            log.error("Error al ejecutar la función saveEncuesta: {}", e.getMessage(), e);
            throw new RuntimeException("Error al guardar la encuesta de siniestro", e);
        }
    }

    @Override
    public String validaIngresoEncuesta(String numSiniestro) {
        log.info("Ejecutando función validaIngresoEncuesta con numSiniestro={}", numSiniestro);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.DC_K_UTILS_WEB_MGT + "." +
                          DatabaseConstants.F_VALID_SINI +
                          "(?) AS result FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            stmt.setString(1, numSiniestro);

            if (stmt.execute()) {
                try (ResultSet rs = stmt.getResultSet()) {
                    if (rs.next()) {
                        String resultado = rs.getString("result");
                        log.info("Función ejecutada correctamente. Resultado: {}", resultado);
                        return resultado;
                    }
                }
            }

            log.info("No se pudo validar el ingreso de encuesta");
            return "ERROR";

        } catch (SQLException e) {
            log.error("Error al ejecutar la función validaIngresoEncuesta: {}", e.getMessage(), e);
            throw new RuntimeException("Error al validar el ingreso de encuesta", e);
        }
    }

    @Override
    public List<SiniestroVehiculo> getSiniestroVehiculo(String numSiniestro, Integer codCia, String numPoliza, String codFase, Integer codRamo) {
        log.info("Ejecutando función getSiniestroVehiculo con numSiniestro={}, codCia={}, numPoliza={}, codFase={}, codRamo={}",
                 numSiniestro, codCia, numPoliza, codFase, codRamo);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.TS_K_MOD_PERITACION_MGT + "." +
                          DatabaseConstants.F_SUB_TRACKING_ETAPA +
                          "(?, ?, ?, ?, ?) AS siniestro FROM dual";

        List<SiniestroVehiculo> result = new ArrayList<>();
        SiniestroVehiculoRowMapper mapper = new SiniestroVehiculoRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            stmt.setString(1, numSiniestro);
            stmt.setInt(2, codCia);
            stmt.setString(3, numPoliza);
            stmt.setString(4, codFase);
            stmt.setInt(5, codRamo);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("siniestro");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Siniestros de vehículo encontrados: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getSiniestroVehiculo: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la información de siniestro de vehículo", e);
        }
    }
}