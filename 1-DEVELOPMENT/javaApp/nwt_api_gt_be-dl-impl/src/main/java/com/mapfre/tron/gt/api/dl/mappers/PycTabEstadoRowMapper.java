package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycTabEstado;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycTabEstado.
 */
@Slf4j
public class PycTabEstadoRowMapper implements RowMapper<PycTabEstado> {

    @Override
    public PycTabEstado mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycTabEstado", rowNum);
        
        PycTabEstado tabEstado = new PycTabEstado();
        
        try {
            tabEstado.setIdEstado(rs.getInt("ID_ESTADO"));
            tabEstado.setNombreEstado(rs.getString("NOMBRE_ESTADO"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return tabEstado;
    }
}
