package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.DetalleSiniestro;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper para convertir resultados de la base de datos a objetos DetalleSiniestro.
 */
public class DetalleSiniestroRowMapper implements RowMapper<DetalleSiniestro> {

    @Override
    public DetalleSiniestro mapRow(ResultSet rs, int rowNum) throws SQLException {
        DetalleSiniestro detalle = new DetalleSiniestro();
        
        detalle.setNumOrden(rs.getInt("NUM_ORDEN"));
        detalle.setCodEtapa(rs.getString("COD_ETAPA"));
        detalle.setDescripcion(rs.getString("DESCRIPCION"));
        detalle.setFecha(rs.getString("FECHA"));
        detalle.setEtapa(rs.getString("ETAPA"));
        detalle.setImagen(rs.getString("IMAGEN"));
        detalle.setSubTracking(rs.getString("SUB_TRACKING"));
        
        return detalle;
    }
}
