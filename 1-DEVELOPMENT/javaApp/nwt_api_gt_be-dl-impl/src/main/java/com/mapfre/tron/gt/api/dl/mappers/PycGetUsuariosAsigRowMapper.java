package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycGetUsuariosAsig;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycGetUsuariosAsig.
 */
@Slf4j
public class PycGetUsuariosAsigRowMapper implements RowMapper<PycGetUsuariosAsig> {

    @Override
    public PycGetUsuariosAsig mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycGetUsuariosAsig", rowNum);
        
        PycGetUsuariosAsig usuarioAsig = new PycGetUsuariosAsig();
        
        try {
            usuarioAsig.setIdUsuario(rs.getInt("ID_USUARIO"));
            usuarioAsig.setIdProceso(rs.getInt("ID_PROCESO"));
            usuarioAsig.setNombre(rs.getString("NOMBRE"));
            
            // Estos campos pueden ser nulos si no hay asignación automática
            if (rs.getObject("ID_APLICACION") != null) {
                usuarioAsig.setIdAplicacion(rs.getInt("ID_APLICACION"));
            }
            if (rs.getObject("ID_PERFIL") != null) {
                usuarioAsig.setIdPerfil(rs.getInt("ID_PERFIL"));
            }
            if (rs.getObject("ID_ESTADO") != null) {
                usuarioAsig.setIdEstado(rs.getInt("ID_ESTADO"));
            }
            
            usuarioAsig.setIndAutomatico(rs.getString("IND_AUTOMATICO"));
            usuarioAsig.setIndAdmin(rs.getString("IND_ADMIN"));
            usuarioAsig.setUrlPerfil(rs.getString("URL_PERFIL"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return usuarioAsig;
    }
}
