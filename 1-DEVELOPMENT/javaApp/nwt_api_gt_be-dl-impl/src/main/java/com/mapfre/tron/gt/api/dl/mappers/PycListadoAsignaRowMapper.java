package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycListadoAsigna;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycListadoAsigna.
 */
@Slf4j
public class PycListadoAsignaRowMapper implements RowMapper<PycListadoAsigna> {

    @Override
    public PycListadoAsigna mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycListadoAsigna", rowNum);
        
        PycListadoAsigna listadoAsigna = new PycListadoAsigna();
        
        try {
            listadoAsigna.setIdUsuario(rs.getInt("ID_USUARIO"));
            listadoAsigna.setNombre(rs.getString("NOMBRE"));
            listadoAsigna.setIdPerfil(rs.getInt("ID_PERFIL"));
            listadoAsigna.setNombrePerfil(rs.getString("NOMBRE_PERFIL"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return listadoAsigna;
    }
}
