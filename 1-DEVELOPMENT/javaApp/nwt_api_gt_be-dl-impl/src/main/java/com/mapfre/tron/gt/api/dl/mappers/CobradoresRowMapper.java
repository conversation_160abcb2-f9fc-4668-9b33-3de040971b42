package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.CobradoresResponse;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper para convertir resultados de la base de datos a objetos CobradoresResponse.
 */
public class CobradoresRowMapper implements RowMapper<CobradoresResponse> {

    @Override
    public CobradoresResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        CobradoresResponse response = new CobradoresResponse();

        // Mapear los campos según la definición en CobradoresResponse
        Integer idUsuario = rs.getObject("ID_USUARIO", Integer.class);
        if (idUsuario != null && !rs.wasNull()) {
            response.setIdUsuario(idUsuario);
        }

        response.setNombre(rs.getString("NOMBRE"));

        return response;
    }
}