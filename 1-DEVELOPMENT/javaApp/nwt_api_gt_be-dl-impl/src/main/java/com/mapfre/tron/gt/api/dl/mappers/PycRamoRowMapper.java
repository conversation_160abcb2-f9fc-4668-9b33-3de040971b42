package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycRamo;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycRamo.
 */
@Slf4j
public class PycRamoRowMapper implements RowMapper<PycRamo> {

    @Override
    public PycRamo mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycRamo", rowNum);
        
        PycRamo ramo = new PycRamo();
        
        try {
            ramo.setCodCia(rs.getString("COD_CIA"));
            ramo.setCodModalidad(rs.getString("COD_MODALIDAD"));
            ramo.setCodRamo(rs.getString("COD_RAMO"));
            ramo.setIdTipoPeticion(rs.getInt("ID_TIPO_PETICION"));
            ramo.setDescripcionRamo(rs.getString("DESCRIPCION_RAMO"));
            ramo.setEstado(rs.getString("ESTADO"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return ramo;
    }
}
