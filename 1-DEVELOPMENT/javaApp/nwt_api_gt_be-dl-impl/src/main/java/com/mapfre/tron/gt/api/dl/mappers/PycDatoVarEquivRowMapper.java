package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycDatoVarEquiv;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycDatoVarEquiv.
 */
@Slf4j
public class PycDatoVarEquivRowMapper implements RowMapper<PycDatoVarEquiv> {

    @Override
    public PycDatoVarEquiv mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycDatoVarEquiv", rowNum);

        PycDatoVarEquiv datoVarEquiv = new PycDatoVarEquiv();

        try {
            datoVarEquiv.setCodCia(rs.getString("COD_CIA"));
            datoVarEquiv.setCodModalidad(rs.getInt("COD_MODALIDAD"));
            datoVarEquiv.setCodRamo(rs.getString("COD_RAMO"));
            datoVarEquiv.setIdFormulario(rs.getInt("ID_FORMULARIO"));
            datoVarEquiv.setIdSeccion(rs.getInt("ID_SECCION"));
            datoVarEquiv.setDatVarId(rs.getString("DAT_VAR_ID"));
            datoVarEquiv.setDatVar(rs.getString("DAT_VAR"));
            datoVarEquiv.setDatVarNm(rs.getString("DAT_VAR_NM"));
            datoVarEquiv.setDatVarReef(rs.getString("DAT_VAR_REEF"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }

        return datoVarEquiv;
    }
}
