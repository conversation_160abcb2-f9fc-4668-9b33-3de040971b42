package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.EncuestaSiniestro;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper para convertir resultados de la base de datos a objetos EncuestaSiniestro.
 */
public class EncuestaSiniestroRowMapper implements RowMapper<EncuestaSiniestro> {

    @Override
    public EncuestaSiniestro mapRow(ResultSet rs, int rowNum) throws SQLException {
        EncuestaSiniestro encuesta = new EncuestaSiniestro();
        
        encuesta.setIdEncuesta(rs.getInt("id_encuesta"));
        encuesta.setEncuesta(rs.getString("encuesta"));
        encuesta.setDescripcion(rs.getString("descripcion"));
        encuesta.setIdPregunta(rs.getInt("id_pregunta"));
        encuesta.setPregunta(rs.getString("pregunta"));
        encuesta.setIdRespuesta(rs.getInt("id_respuesta"));
        encuesta.setRespuesta(rs.getString("respuesta"));
        
        return encuesta;
    }
}
