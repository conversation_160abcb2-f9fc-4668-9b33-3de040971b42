package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.SiniestroVehiculo;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper para convertir resultados de la base de datos a objetos SiniestroVehiculo.
 */
public class SiniestroVehiculoRowMapper implements RowMapper<SiniestroVehiculo> {

    @Override
    public SiniestroVehiculo mapRow(ResultSet rs, int rowNum) throws SQLException {
        SiniestroVehiculo siniestro = new SiniestroVehiculo();
        
        siniestro.setCodEstado(rs.getString("cod_estado"));
        siniestro.setNomEstado(rs.getString("nom_estado"));
        siniestro.setJsonArchivos(rs.getString("json_archivos"));
        siniestro.setFechaActualizacion(rs.getString("fec_actu"));
        siniestro.setImagen(rs.getString("clb_imagen"));
        
        return siniestro;
    }
}
