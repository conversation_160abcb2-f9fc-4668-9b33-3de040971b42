package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.ModRutaPolizaFacturasResponse;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper para convertir resultados de la base de datos a objetos ModRutaPolizaFacturasResponse.
 */
public class ModRutaPolizaFacturasRowMapper implements RowMapper<ModRutaPolizaFacturasResponse> {

    @Override
    public ModRutaPolizaFacturasResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        ModRutaPolizaFacturasResponse response = new ModRutaPolizaFacturasResponse();

        response.setPolizaCodigo(rs.getString("POLIZA_CODIGO"));
        response.setPolizaNumero(rs.getString("POLIZA_NUMERO"));
        response.setCertificado(rs.getString("CERTIFICADO"));
        response.setAsegurado(rs.getString("ASEGURADO"));
        response.setDireccion(rs.getString("DIRECCION"));
        response.setEstadoCod(rs.getString("ESTADO_COD"));
        response.setEstado(rs.getString("ESTADO"));
        response.setRuta(rs.getInt("RUTA"));
        response.setIdePol(rs.getString("IDEPOL"));

        return response;
    }
}