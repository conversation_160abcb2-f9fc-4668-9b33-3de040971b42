package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.BusquedaAsegurado;

public class BuscarAseguradoRowMapper implements RowMapper<BusquedaAsegurado> {

	@Override
	public BusquedaAsegurado mapRow(ResultSet rs, int rowNum) throws SQLException {
		BusquedaAsegurado res = new BusquedaAsegurado();

		res.setNombre(rs.getString("NOM_COMPLETO"));
		res.setTipDocum(rs.getString("TIP_DOCUM"));
		res.setCodDocum(rs.getString("COD_DOCUM"));

		return res;
	}

}