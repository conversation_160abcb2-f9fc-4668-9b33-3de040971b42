package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycEstadoPerfil;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycEstadoPerfil.
 */
@Slf4j
public class PycEstadoPerfilRowMapper implements RowMapper<PycEstadoPerfil> {

    @Override
    public PycEstadoPerfil mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycEstadoPerfil", rowNum);
        
        PycEstadoPerfil estadoPerfil = new PycEstadoPerfil();
        
        try {
            estadoPerfil.setIdEstado(rs.getInt("ID_ESTADO"));
            estadoPerfil.setNombreEstado(rs.getString("NOMBRE_ESTADO"));
            estadoPerfil.setCodigoEstado(rs.getString("CODIGO_ESTADO"));
            
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return estadoPerfil;
    }
}
