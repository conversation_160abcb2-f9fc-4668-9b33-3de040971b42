package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycGetPerfiles;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycGetPerfiles.
 * 
 * La función FN_GET_PERFIL retorna los siguientes campos:
 * - NOMBRE_PROCESO: Nombre del proceso
 * - ID_PERFIL: Identificador único del perfil
 * - NOMBRE_PERFIL: Nombre del perfil
 */
@Slf4j
public class PycGetPerfilesRowMapper implements RowMapper<PycGetPerfiles> {

    @Override
    public PycGetPerfiles mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycGetPerfiles", rowNum);
        
        PycGetPerfiles perfiles = new PycGetPerfiles();
        
        try {
            // Mapear NOMBRE_PROCESO
            perfiles.setNombreProceso(rs.getString("NOMBRE_PROCESO"));
            
            // Mapear ID_PERFIL
            perfiles.setIdPerfil(rs.getInt("ID_PERFIL"));
            
            // Mapear NOMBRE_PERFIL
            perfiles.setNombrePerfil(rs.getString("NOMBRE_PERFIL"));
            
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return perfiles;
    }
}
