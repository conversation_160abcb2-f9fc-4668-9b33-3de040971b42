package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycPetAvanceSig;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycPetAvanceSig.
 */
@Slf4j
public class PycPetAvanceSigRowMapper implements RowMapper<PycPetAvanceSig> {

    @Override
    public PycPetAvanceSig mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycPetAvanceSig", rowNum);
        
        PycPetAvanceSig petAvanceSig = new PycPetAvanceSig();
        
        try {
            // Información de la petición principal
            petAvanceSig.setIdPeticion(rs.getInt("ID_PETICION"));
            petAvanceSig.setNombrePeticion(rs.getString("NOMBRE_PETICION"));
            petAvanceSig.setFechaInicio(rs.getString("FECHA_INICIO"));
            petAvanceSig.setFechaFin(rs.getString("FECHA_FIN"));
            petAvanceSig.setNombreEstado(rs.getString("NOMBRE_ESTADO"));
            
            // Información de la petición siguiente
            petAvanceSig.setIdPeticionSiguiente(rs.getInt("ID_PETICION_SIGUIENTE"));
            petAvanceSig.setNombrePeticionSiguiente(rs.getString("NOMBRE_PETICION_SIGUIENTE"));
            petAvanceSig.setFechaInicioSiguiente(rs.getString("FECHA_INICIO_SIGUIENTE"));
            petAvanceSig.setFechaFinSiguiente(rs.getString("FECHA_FIN_SIGUIENTE"));
            petAvanceSig.setNombreEstadoSiguiente(rs.getString("NOMBRE_ESTADO_SIGUIENTE"));
            
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return petAvanceSig;
    }
}
