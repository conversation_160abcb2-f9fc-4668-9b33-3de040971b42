package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycUsuarioPerfil;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycUsuarioPerfil.
 */
@Slf4j
public class PycUsuarioPerfilRowMapper implements RowMapper<PycUsuarioPerfil> {

    @Override
    public PycUsuarioPerfil mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycUsuarioPerfil", rowNum);
        
        PycUsuarioPerfil usuarioPerfil = new PycUsuarioPerfil();
        
        try {
            usuarioPerfil.setNombre(rs.getString("NOMBRE"));
            usuarioPerfil.setNombrePerfil(rs.getString("NOMBRE_PERFIL"));
            usuarioPerfil.setIdUsuario(rs.getInt("ID_USUARIO"));
            usuarioPerfil.setIdPerfil(rs.getInt("ID_PERFIL"));
            usuarioPerfil.setMultiperfil(rs.getString("MULTIPERFIL"));
            usuarioPerfil.setIndDefault(rs.getString("IND_DEFAULT"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return usuarioPerfil;
    }
}
