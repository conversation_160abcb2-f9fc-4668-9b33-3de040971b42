package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycPerfilUsu;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycPerfilUsu.
 */
@Slf4j
public class PycPerfilUsuRowMapper implements RowMapper<PycPerfilUsu> {

    @Override
    public PycPerfilUsu mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycPerfilUsu", rowNum);
        
        PycPerfilUsu perfilUsu = new PycPerfilUsu();
        
        try {
            perfilUsu.setIdPerfil(rs.getInt("ID_PERFIL"));
            perfilUsu.setNombrePerfil(rs.getString("NOMBRE_PERFIL"));
            perfilUsu.setDescripcionPerfil(rs.getString("DESCRIPCION_PERFIL"));
            perfilUsu.setIndDefault(rs.getString("IND_DEFAULT"));
            perfilUsu.setMultiperfil(rs.getString("MULTIPERFIL"));
            perfilUsu.setEstado(rs.getString("ESTADO"));
            perfilUsu.setIndConsulta(rs.getString("IND_CONSULTA"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return perfilUsu;
    }
}
