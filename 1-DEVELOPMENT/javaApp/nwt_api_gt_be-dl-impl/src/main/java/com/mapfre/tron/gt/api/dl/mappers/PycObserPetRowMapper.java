package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycObserPet;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycObserPet.
 */
@Slf4j
public class PycObserPetRowMapper implements RowMapper<PycObserPet> {

    @Override
    public PycObserPet mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycObserPet", rowNum);
        
        PycObserPet obserPet = new PycObserPet();
        
        try {
            obserPet.setPublico(rs.getString("PUBLICO"));
            obserPet.setUrlPerfil(rs.getString("URL_PERFIL"));
            obserPet.setIdPeticion(rs.getInt("ID_PETICION"));
            obserPet.setNombre(rs.getString("NOMBRE"));
            obserPet.setNombrePeticion(rs.getString("NOMBRE_PETICION"));
            obserPet.setIdObservacion(rs.getInt("ID_OBSERVACION"));
            obserPet.setObservacion(rs.getString("OBSERVACION"));
            obserPet.setFechaHora(rs.getString("FECHA_HORA"));
            obserPet.setNombreArea(rs.getString("NOMBRE_AREA"));
            obserPet.setNombreDepartamento(rs.getString("NOMBRE_DEPARTAMENTO"));
            obserPet.setEstado(rs.getString("ESTADO"));
            obserPet.setGenero(rs.getString("GENERO"));
            obserPet.setUsuario(rs.getString("USUARIO"));
            obserPet.setIndicador(rs.getString("INDICADOR"));
            obserPet.setConnected(rs.getString("CONNECTED"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return obserPet;
    }
}
