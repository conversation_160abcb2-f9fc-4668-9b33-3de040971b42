package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycAplicacion;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycAplicacion.
 */
@Slf4j
public class PycAplicacionRowMapper implements RowMapper<PycAplicacion> {

    @Override
    public PycAplicacion mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycAplicacion", rowNum);
        
        PycAplicacion aplicacion = new PycAplicacion();
        
        try {
            aplicacion.setIdAplicacion(rs.getInt("ID_APLICACION"));
            aplicacion.setNombreAplicacion(rs.getString("NOMBRE_APLICACION"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return aplicacion;
    }
}
