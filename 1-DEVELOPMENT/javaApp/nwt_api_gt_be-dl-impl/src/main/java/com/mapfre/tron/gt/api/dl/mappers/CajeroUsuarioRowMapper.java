package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.CajeroUsuarioResponseCajeros;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class CajeroUsuarioRowMapper implements RowMapper<CajeroUsuarioResponseCajeros> {

    @Override
    public CajeroUsuarioResponseCajeros mapRow(ResultSet rs, int rowNum) throws SQLException {
        CajeroUsuarioResponseCajeros cajero = new CajeroUsuarioResponseCajeros();
        
        cajero.setSistema(rs.getString("sistema"));
        cajero.setUsuario(rs.getString("usuario"));
        cajero.setCodigo(rs.getString("codigo"));
        
        return cajero;
    }
}
