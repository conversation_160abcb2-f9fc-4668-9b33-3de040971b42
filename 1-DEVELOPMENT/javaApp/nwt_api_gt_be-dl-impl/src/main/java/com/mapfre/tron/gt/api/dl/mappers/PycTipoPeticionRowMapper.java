package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycTipoPeticion;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycTipoPeticion.
 * 
 * La función FN_TIPO_PETICION retorna los siguientes campos:
 * - ID_TIPO_PETICION: Identificador único del tipo de petición
 * - DESCRIPCION_TIPO_PETICION: Descripción del tipo de petición
 * - ESTADO: Estado del tipo de petición
 * - USUARIO: Usuario que creó o modificó el tipo de petición
 * - FECHA_HORA: Fecha y hora de creación o modificación
 * - ID_PROCESO: Identificador del proceso asociado
 */
@Slf4j
public class PycTipoPeticionRowMapper implements RowMapper<PycTipoPeticion> {

    @Override
    public PycTipoPeticion mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycTipoPeticion", rowNum);
        
        PycTipoPeticion tipoPeticion = new PycTipoPeticion();
        
        try {
            // Mapear ID_TIPO_PETICION
            tipoPeticion.setIdTipoPeticion(rs.getInt("ID_TIPO_PETICION"));
            
            // Mapear DESCRIPCION_TIPO_PETICION
            tipoPeticion.setDescripcionTipoPeticion(rs.getString("DESCRIPCION_TIPO_PETICION"));
            
            // Mapear ESTADO
            tipoPeticion.setEstado(rs.getString("ESTADO"));
            
            // Mapear USUARIO
            tipoPeticion.setUsuario(rs.getString("USUARIO"));
            
            // Mapear FECHA_HORA
            tipoPeticion.setFechaHora(rs.getString("FECHA_HORA"));
            
            // Mapear ID_PROCESO
            tipoPeticion.setIdProceso(rs.getInt("ID_PROCESO"));
            
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return tipoPeticion;
    }
}
