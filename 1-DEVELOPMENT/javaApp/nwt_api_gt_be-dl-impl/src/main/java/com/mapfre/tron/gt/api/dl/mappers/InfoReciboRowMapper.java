package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.InfoReciboResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos InfoReciboResponse.
 */
@Slf4j
public class InfoReciboRowMapper implements RowMapper<InfoReciboResponse> {

    @Override
    public InfoReciboResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto InfoReciboResponse", rowNum);
        
        InfoReciboResponse response = new InfoReciboResponse();
        
        try {
            response.setCodCia(rs.getInt("COD_CIA"));
            response.setNumPoliza(rs.getString("NUM_POLIZA"));
            response.setNumRecibo(rs.getInt("NUM_RECIBO"));
            response.setCodMon(rs.getInt("COD_MON"));
            response.setImpRecibo(rs.getDouble("IMP_RECIBO"));
            response.setTipSituacion(rs.getString("TIP_SITUACION"));
            response.setFecVctoRecibo(rs.getString("FEC_VCTO_RECIBO"));
            response.setFecSituacion(rs.getString("FEC_SITUACION"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
