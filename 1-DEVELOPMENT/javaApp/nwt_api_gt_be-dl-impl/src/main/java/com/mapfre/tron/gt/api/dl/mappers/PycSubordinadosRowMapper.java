package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycSubordinados;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycSubordinados.
 */
@Slf4j
public class PycSubordinadosRowMapper implements RowMapper<PycSubordinados> {

    @Override
    public PycSubordinados mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycSubordinados", rowNum);
        
        PycSubordinados subordinados = new PycSubordinados();
        
        try {
            subordinados.setIdUsuario(rs.getInt("ID_USUARIO"));
            subordinados.setSubordinado(rs.getString("SUBORDINADO"));
            subordinados.setNsubs(rs.getString("NSUBS"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return subordinados;
    }
}
