package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycDocNoObli;

import lombok.extern.slf4j.Slf4j;

/**
 * RowMapper para mapear los resultados de la función FN_DOC_NOOBLIGATORIOS
 * a objetos PycDocNoObli.
 */
@Slf4j
public class PycDocNoObliRowMapper implements RowMapper<PycDocNoObli> {

    @Override
    public PycDocNoObli mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} del resultado de FN_DOC_NOOBLIGATORIOS", rowNum);

        PycDocNoObli docNoObli = new PycDocNoObli();

        try {
            docNoObli.setIdTipoPeticion(rs.getInt("ID_TIPO_PETICION"));
        } catch (SQLException e) {
            log.debug("Campo ID_TIPO_PETICION no presente en el resultado");
        }

        try {
            docNoObli.setIdDocumento(rs.getInt("ID_DOCUMENTO"));
        } catch (SQLException e) {
            log.debug("Campo ID_DOCUMENTO no presente en el resultado");
        }

        try {
            docNoObli.setNombreDocumento(rs.getString("NOMBRE_DOCUMENTO"));
        } catch (SQLException e) {
            log.debug("Campo NOMBRE_DOCUMENTO no presente en el resultado");
        }

        try {
            docNoObli.setDescripcionDocumento(rs.getString("DESCRIPCION_DOCUMENTO"));
        } catch (SQLException e) {
            log.debug("Campo DESCRIPCION_DOCUMENTO no presente en el resultado");
        }

        log.debug("Documento no obligatorio mapeado: ID_TIPO_PETICION={}, ID_DOCUMENTO={}, NOMBRE_DOCUMENTO={}",
                docNoObli.getIdTipoPeticion(), docNoObli.getIdDocumento(), docNoObli.getNombreDocumento());

        return docNoObli;
    }
}
