package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.ObtenerRolesUsuarioResponseRoles;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class ObtenerRolesUsuarioRowMapper implements RowMapper<ObtenerRolesUsuarioResponseRoles> {

    @Override
    public ObtenerRolesUsuarioResponseRoles mapRow(ResultSet rs, int rowNum) throws SQLException {
        ObtenerRolesUsuarioResponseRoles rol = new ObtenerRolesUsuarioResponseRoles();
        
        rol.setIdRol(rs.getInt("id_rol"));
        rol.setNombre(rs.getString("nombre"));
        
        return rol;
    }
}
