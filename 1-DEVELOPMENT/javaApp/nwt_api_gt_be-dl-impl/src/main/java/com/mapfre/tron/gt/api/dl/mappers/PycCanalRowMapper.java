package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycCanal;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycCanal.
 */
@Slf4j
public class PycCanalRowMapper implements RowMapper<PycCanal> {

    @Override
    public PycCanal mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycCanal", rowNum);
        
        PycCanal canal = new PycCanal();
        
        try {
            canal.setCodigo(rs.getInt("CODIGO"));
            canal.setCanal(rs.getString("CANAL"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return canal;
    }
}
