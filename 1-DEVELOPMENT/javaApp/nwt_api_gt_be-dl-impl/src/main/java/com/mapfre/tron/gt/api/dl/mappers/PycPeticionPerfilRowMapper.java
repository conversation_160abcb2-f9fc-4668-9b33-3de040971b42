package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycPeticionPerfil;
import static com.mapfre.tron.gt.api.sr.CacheApi.log;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycPeticionPerfil.
 */
@Slf4j
public class PycPeticionPerfilRowMapper implements RowMapper<PycPeticionPerfil> {

    @Override
    public PycPeticionPerfil mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycPeticionPerfil", rowNum);

        PycPeticionPerfil peticionPerfil = new PycPeticionPerfil();

        try {
            // Campos principales de la petición (siempre presentes según la función Oracle)
            peticionPerfil.setIdProceso(rs.getInt("ID_PROCESO"));
            peticionPerfil.setIdPeticion(rs.getInt("ID_PETICION"));
            peticionPerfil.setTipoPeticion(rs.getString("TIPO_PETICION"));
            peticionPerfil.setNombrePeticion(rs.getString("NOMBRE_PETICION"));
            peticionPerfil.setDescripcionPeticion(rs.getString("DESCRIPCION_PETICION"));
            peticionPerfil.setFechaCreacion(rs.getString("FECHA_CREACION"));
            peticionPerfil.setIdTipo(rs.getInt("ID_TIPO"));
            peticionPerfil.setIdUsuarioSolicitante(rs.getInt("ID_USUARIO_SOLICITANTE"));
            peticionPerfil.setUsuario(rs.getString("USUARIO"));
            peticionPerfil.setFechaInicio(rs.getString("FECHA_INICIO"));
            peticionPerfil.setFechaFin(rs.getString("FECHA_FIN"));
            peticionPerfil.setTotalHoras(rs.getDouble("TOTAL_HORAS"));
            peticionPerfil.setPorcentajeBase(rs.getDouble("PORCENTAJE_BASE"));
            peticionPerfil.setPorcentajeReal(rs.getDouble("PORCENTAJE_REAL"));
            peticionPerfil.setIdEstado(rs.getString("ID_ESTADO"));
            peticionPerfil.setNombreEstado(rs.getString("NOMBRE_ESTADO"));
            peticionPerfil.setColorEstado(rs.getString("COLOR_ESTADO"));
            peticionPerfil.setIdPerfil(rs.getInt("ID_PERFIL"));
            peticionPerfil.setNombrePerfil(rs.getString("NOMBRE_PERFIL"));
            peticionPerfil.setUsuarioGraba(rs.getString("USUARIO_GRABA"));
            peticionPerfil.setUsuarioSoli(rs.getString("USUARIO_SOLI"));
            peticionPerfil.setOrigen(rs.getString("ORIGEN"));
            peticionPerfil.setCodClarity(rs.getString("COD_CLARITY"));
            peticionPerfil.setCodcia(rs.getString("CODCIA"));
            peticionPerfil.setPrioridad(rs.getString("PRIORIDAD"));
            peticionPerfil.setPrioriDesc(rs.getString("PRIORI_DESC"));
            peticionPerfil.setIdPeticionSiguiente(rs.getInt("ID_PETICION_SIGUIENTE"));
            peticionPerfil.setTituloSiguiente(rs.getString("TITULO_SIGUIENTE"));
            peticionPerfil.setNombreCliente(rs.getString("NOMBRE_CLIENTE"));
            peticionPerfil.setTelefonoCliente(rs.getString("TELEFONO_CLIENTE"));
            peticionPerfil.setCorreoCliente(rs.getString("CORREO_CLIENTE"));
            peticionPerfil.setOrigenDescripcion(rs.getString("ORIGEN_DESCRIPCION"));
            peticionPerfil.setCodciaDescripcion(rs.getString("CODCIA_DESCRIPCION"));
            peticionPerfil.setTipoCliente(rs.getString("TIPO_CLIENTE"));
            peticionPerfil.setTipoClienteDescripcion(rs.getString("TIPO_CLIENTE_DESCRIPCION"));
            peticionPerfil.setCodCliente(rs.getString("COD_CLIENTE"));
            peticionPerfil.setNoPoliza(rs.getString("NO_POLIZA"));
            peticionPerfil.setTipoServicio(rs.getString("TIPO_SERVICIO"));
            peticionPerfil.setTipoServicioDescripcion(rs.getString("TIPO_SERVICIO_DESCRIPCION"));
            peticionPerfil.setCausa(rs.getString("CAUSA"));
            peticionPerfil.setCausaDescripcion(rs.getString("CAUSA_DESCRIPCION"));
            peticionPerfil.setGravedad(rs.getString("GRAVEDAD"));
            peticionPerfil.setGravedadDescripcion(rs.getString("GRAVEDAD_DESCRIPCION"));

            // Nota: En la función Oracle es ENCU_CALIFI (no ENCU_CALIF)
            peticionPerfil.setEncuCalif(rs.getString("ENCU_CALIFI"));
            peticionPerfil.setEncuComent(rs.getString("ENCU_COMENT"));
            peticionPerfil.setEncuComentAdi(rs.getString("ENCU_COMENT_ADI"));
            peticionPerfil.setEncuUser(rs.getString("ENCU_USER"));
            peticionPerfil.setEncuUsuario(rs.getString("ENCU_USUARIO"));
            peticionPerfil.setEncuFecha(rs.getString("ENCU_FECHA"));
            peticionPerfil.setObservaciones(rs.getString("OBSERVACIONES"));
            peticionPerfil.setIdArea(rs.getInt("ID_AREA"));
            peticionPerfil.setNombreArea(rs.getString("NOMBRE_AREA"));
            peticionPerfil.setIdDepartamento(rs.getInt("ID_DEPARTAMENTO"));
            peticionPerfil.setNombreDepartamento(rs.getString("NOMBRE_DEPARTAMENTO"));
            peticionPerfil.setIdReferencia(rs.getString("ID_REFERENCIA"));
            peticionPerfil.setPerfilSolicitante(rs.getString("PERFIL_SOLICITANTE"));
            peticionPerfil.setUrlRedirect(rs.getString("URL_REDIRECT"));
            peticionPerfil.setIdResponsable(rs.getString("ID_RESPONSABLE"));
            peticionPerfil.setResponsable(rs.getString("RESPONSABLE"));
            peticionPerfil.setPerfilResponsable(rs.getString("PERFIL_RESPONSABLE"));
            peticionPerfil.setAreaResponsable(rs.getString("AREA_RESPONSABLE"));
            peticionPerfil.setDepartamentoResponsable(rs.getString("DEPARTAMENTO_RESPONSABLE"));
            peticionPerfil.setMcaRefId(rs.getString("MCA_REF_ID"));
            peticionPerfil.setCodinter(rs.getString("CODINTER"));

            // Nota: En la función Oracle es FTP_DOCS (no FTR_DOCS)
            peticionPerfil.setFtrDocs(rs.getString("FTP_DOCS"));
            peticionPerfil.setIconoEstado(rs.getString("ICONO_ESTADO"));

            // Campos adicionales identificados en las imágenes
            try {
                peticionPerfil.setIdFormulario(rs.getString("ID_FORMULARIO"));
            } catch (SQLException e) {
                log.debug("Campo ID_FORMULARIO no presente en el resultado");
            }

            try {
                peticionPerfil.setIdSeccion(rs.getString("ID_SECCION"));
            } catch (SQLException e) {
                log.debug("Campo ID_SECCION no presente en el resultado");
            }

            try {
                peticionPerfil.setNumPoliza(rs.getString("NUM_POLIZA"));
            } catch (SQLException e) {
                log.debug("Campo NUM_POLIZA no presente en el resultado");
            }

            try {
                peticionPerfil.setNumReferencia(rs.getString("NUM_REFERENCIA"));
            } catch (SQLException e) {
                log.debug("Campo NUM_REFERENCIA no presente en el resultado");
            }

            try {
                peticionPerfil.setCampania(rs.getString("CAMPANIA"));
            } catch (SQLException e) {
                log.debug("Campo CAMPANIA no presente en el resultado");
            }

            try {
                peticionPerfil.setNextStep(rs.getString("NEXT_STEP"));
            } catch (SQLException e) {
                log.debug("Campo NEXT_STEP no presente en el resultado");
            }

            // Campos de compatibilidad (alias)
            peticionPerfil.setNombreUsuarioSolicitante(rs.getString("USUARIO"));
            peticionPerfil.setNombreCanal(rs.getString("ORIGEN_DESCRIPCION"));
            peticionPerfil.setNombreOficina(rs.getString("CODCIA_DESCRIPCION"));

            // Campos opcionales que pueden no estar presentes en todos los casos
            try {
                // Información del canal (puede no estar presente)
                peticionPerfil.setIdCanal(rs.getInt("ID_CANAL"));
            } catch (SQLException e) {
                // Campo no presente, continuar
                log.debug("Campo ID_CANAL no presente en el resultado");
            }

            try {
                // Información de la oficina (puede no estar presente)
                peticionPerfil.setIdOficina(rs.getInt("ID_OFICINA"));
            } catch (SQLException e) {
                // Campo no presente, continuar
                log.debug("Campo ID_OFICINA no presente en el resultado");
            }

            // Campos adicionales identificados en las imágenes para RRHH
            try {
                peticionPerfil.setCandidato(rs.getString("CANDIDATO"));
            } catch (SQLException e) {
                log.debug("Campo CANDIDATO no presente en el resultado");
            }

            try {
                peticionPerfil.setFechaNacimiento(rs.getString("FECHA_NACIMIENTO"));
            } catch (SQLException e) {
                log.debug("Campo FECHA_NACIMIENTO no presente en el resultado");
            }

            try {
                peticionPerfil.setEdad(rs.getInt("EDAD"));
            } catch (SQLException e) {
                log.debug("Campo EDAD no presente en el resultado");
            }

            try {
                peticionPerfil.setNombrePlaza(rs.getString("NOMBRE_PLAZA"));
            } catch (SQLException e) {
                log.debug("Campo NOMBRE_PLAZA no presente en el resultado");
            }

            try {
                peticionPerfil.setDpi(rs.getString("DPI"));
            } catch (SQLException e) {
                log.debug("Campo DPI no presente en el resultado");
            }

            try {
                peticionPerfil.setNit(rs.getString("NIT"));
            } catch (SQLException e) {
                log.debug("Campo NIT no presente en el resultado");
            }

            try {
                peticionPerfil.setEscolaridad(rs.getString("ESCOLARIDAD"));
            } catch (SQLException e) {
                log.debug("Campo ESCOLARIDAD no presente en el resultado");
            }

            try {
                peticionPerfil.setTituloAcademico(rs.getString("TITULO_ACADEMICO"));
            } catch (SQLException e) {
                log.debug("Campo TITULO_ACADEMICO no presente en el resultado");
            }

            try {
                peticionPerfil.setDireccion(rs.getString("DIRECCION"));
            } catch (SQLException e) {
                log.debug("Campo DIRECCION no presente en el resultado");
            }

            try {
                peticionPerfil.setDepartamentoDir(rs.getString("DEPARTAMENTO_DIR"));
            } catch (SQLException e) {
                log.debug("Campo DEPARTAMENTO_DIR no presente en el resultado");
            }

            try {
                peticionPerfil.setTrabajaActualmente(rs.getString("TRABAJA_ACTUALMENTE"));
            } catch (SQLException e) {
                log.debug("Campo TRABAJA_ACTUALMENTE no presente en el resultado");
            }

            try {
                peticionPerfil.setExperienciaSeguro(rs.getString("EXPERIENCIA_SEGURO"));
            } catch (SQLException e) {
                log.debug("Campo EXPERIENCIA_SEGURO no presente en el resultado");
            }

            try {
                peticionPerfil.setPretensionSalarial(rs.getString("PRETENSION_SALARIAL"));
            } catch (SQLException e) {
                log.debug("Campo PRETENSION_SALARIAL no presente en el resultado");
            }

            try {
                peticionPerfil.setAreaRrhh(rs.getString("AREA"));
            } catch (SQLException e) {
                log.debug("Campo AREA no presente en el resultado");
            }

            try {
                peticionPerfil.setNoSolicitud(rs.getInt("NO_SOLICITUD"));
            } catch (SQLException e) {
                log.debug("Campo NO_SOLICITUD no presente en el resultado");
            }

        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }

        return peticionPerfil;
    }
}
