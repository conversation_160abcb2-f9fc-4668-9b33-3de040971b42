package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycDocXPeticion;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycDocXPeticion.
 *
 * La función FN_DOC_X_PETICION retorna los siguientes campos:
 * - OBLIGATORIO: Indicador si el documento es obligatorio (S/N)
 * - ID_DOCUMENTO: Identificador único del documento
 * - NOMBRE_DOCUMENTO: Nombre del documento
 * - DESCRIPCION_DOCUMENTO: Descripción del documento
 * - ESTADO: Estado del documento
 * - USUARIO: Usuario que creó o modificó el documento
 * - FECHA_HORA: Fecha y hora de creación o modificación
 */
@Slf4j
public class PycDocXPeticionRowMapper implements RowMapper<PycDocXPeticion> {

    @Override
    public PycDocXPeticion mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycDocXPeticion", rowNum);
        
        PycDocXPeticion docXPeticion = new PycDocXPeticion();
        
        try {
            // Mapear OBLIGATORIO (de la tabla PYC_DOCUMENTO_TIPO_PETICION)
            docXPeticion.setObligatorio(rs.getString("OBLIGATORIO"));

            // Mapear ID_DOCUMENTO (de la tabla PYC_DOCUMENTO)
            docXPeticion.setIdDocumento(rs.getInt("ID_DOCUMENTO"));

            // Mapear NOMBRE_DOCUMENTO
            docXPeticion.setNombreDocumento(rs.getString("NOMBRE_DOCUMENTO"));

            // Mapear DESCRIPCION_DOCUMENTO
            docXPeticion.setDescripcionDocumento(rs.getString("DESCRIPCION_DOCUMENTO"));

            // Mapear ESTADO
            docXPeticion.setEstado(rs.getString("ESTADO"));

            // Mapear USUARIO
            docXPeticion.setUsuario(rs.getString("USUARIO"));

            // Mapear FECHA_HORA - siguiendo el patrón de otros mappers
            Timestamp fechaHora = rs.getTimestamp("FECHA_HORA");
            if (fechaHora != null) {
                docXPeticion.setFechaHora(fechaHora.toString());
            } else {
                docXPeticion.setFechaHora(null);
            }

            // Los siguientes campos no están en el resultado de la función Oracle
            // Se establecen como null ya que no están disponibles en esta consulta
            docXPeticion.setMultiArchivos(null);
            docXPeticion.setExtension(null);
            docXPeticion.setTamanoMaximo(null);

        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return docXPeticion;
    }
}
