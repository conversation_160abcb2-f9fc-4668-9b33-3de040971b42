package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycPetSinProgramador;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycPetSinProgramador.
 */
@Slf4j
public class PycPetSinProgramadorRowMapper implements RowMapper<PycPetSinProgramador> {

    @Override
    public PycPetSinProgramador mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycPetSinProgramador", rowNum);
        
        PycPetSinProgramador petSinProgramador = new PycPetSinProgramador();
        
        try {
            petSinProgramador.setIdPeticion(rs.getInt("ID_PETICION"));
            petSinProgramador.setTipoPeticion(rs.getString("TIPO_PETICION"));
            petSinProgramador.setNombrePeticion(rs.getString("NOMBRE_PETICION"));
            petSinProgramador.setDescripcionPeticion(rs.getString("DESCRIPCION_PETICION"));
            petSinProgramador.setFechaCreacion(rs.getString("FECHA_CREACION"));
            petSinProgramador.setIdTipo(rs.getInt("ID_TIPO"));
            petSinProgramador.setIdUsuarioSolicitante(rs.getInt("ID_USUARIO_SOLICITANTE"));
            petSinProgramador.setUsuario(rs.getString("USUARIO"));
            petSinProgramador.setFechaInicio(rs.getString("FECHA_INICIO"));
            petSinProgramador.setFechaFin(rs.getString("FECHA_FIN"));
            petSinProgramador.setTotalHoras(rs.getDouble("TOTAL_HORAS"));
            petSinProgramador.setPorcentajeBase(rs.getDouble("PORCENTAJE_BASE"));
            petSinProgramador.setPorcentajeReal(rs.getDouble("PORCENTAJE_REAL"));
            petSinProgramador.setIdEstado(rs.getInt("ID_ESTADO"));
            petSinProgramador.setNombreEstado(rs.getString("NOMBRE_ESTADO"));
            petSinProgramador.setColorEstado(rs.getString("COLOR_ESTADO"));
            petSinProgramador.setIdPerfil(rs.getInt("ID_PERFIL"));
            petSinProgramador.setNombrePerfil(rs.getString("NOMBRE_PERFIL"));
            petSinProgramador.setUsuarioGraba(rs.getString("USUARIO_GRABA"));
            petSinProgramador.setUsuarioSoli(rs.getString("USUARIO_SOLI"));
            petSinProgramador.setOrigen(rs.getString("ORIGEN"));
            petSinProgramador.setCodClarity(rs.getString("COD_CLARITY"));
            petSinProgramador.setCodcia(rs.getString("CODCIA"));
            petSinProgramador.setPrioridad(rs.getString("PRIORIDAD"));
            petSinProgramador.setPrioriDesc(rs.getString("PRIORI_DESC"));
            petSinProgramador.setIdPeticionSiguiente(rs.getInt("ID_PETICION_SIGUIENTE"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return petSinProgramador;
    }
}
