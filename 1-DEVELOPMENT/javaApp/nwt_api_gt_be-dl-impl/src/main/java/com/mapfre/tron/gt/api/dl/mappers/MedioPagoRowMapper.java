package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PLE_MedioPago;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PLE_MedioPago.
 */
@Slf4j
public class MedioPagoRowMapper implements RowMapper<PLE_MedioPago> {

    @Override
    public PLE_MedioPago mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PLE_MedioPago", rowNum);
        
        PLE_MedioPago response = new PLE_MedioPago();
        
        try {
            response.setCodigo(rs.getInt("codigo"));
            response.setDescripcion(rs.getString("descripcion"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
