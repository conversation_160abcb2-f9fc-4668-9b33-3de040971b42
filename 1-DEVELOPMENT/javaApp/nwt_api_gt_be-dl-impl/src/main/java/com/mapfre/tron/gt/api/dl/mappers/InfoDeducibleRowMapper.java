package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.InfoDeducibleResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos InfoDeducibleResponse.
 */
@Slf4j
public class InfoDeducibleRowMapper implements RowMapper<InfoDeducibleResponse> {

    @Override
    public InfoDeducibleResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto InfoDeducibleResponse", rowNum);
        
        InfoDeducibleResponse response = new InfoDeducibleResponse();
        
        try {
            response.setNumLiq(rs.getString("NUM_LIQ"));
            response.setNumSini(rs.getString("NUM_SINI"));
            response.setNumExp(rs.getInt("NUM_EXP"));
            response.setNumPoliza(rs.getString("NUM_POLIZA"));
            response.setCodNivel3(rs.getString("COD_NIVEL3"));
            response.setNomNivel3(rs.getString("NOM_NIVEL3"));
            response.setCodRamo(rs.getInt("COD_RAMO"));
            response.setCodSector(rs.getInt("COD_SECTOR"));
            response.setNomSector(rs.getString("NOM_SECTOR"));
            response.setCodTercero(rs.getString("COD_TERCERO"));
            response.setNomTercero(rs.getString("NOM_TERCERO"));
            response.setCodActTercero(rs.getInt("COD_ACT_TERCERO"));
            response.setNomActTercero(rs.getString("NOM_ACT_TERCERO"));
            response.setTipDocum(rs.getString("TIP_DOCUM"));
            response.setCodDocum(rs.getString("COD_DOCUM"));
            response.setObs(rs.getString("OBS"));
            response.setFecLiq(rs.getString("FEC_LIQ"));
            response.setFecEstPago(rs.getString("FEC_EST_PAGO"));
            response.setFecPago(rs.getString("FEC_PAGO"));
            response.setCodMonLiq(rs.getInt("COD_MON_LIQ"));
            response.setCodMonLiqIso(rs.getString("COD_MON_LIQ_ISO"));
            response.setNumDecimales(rs.getInt("NUM_DECIMALES"));
            response.setCodMonPago(rs.getInt("COD_MON_PAGO"));
            response.setCodMonPagoIso(rs.getString("COD_MON_PAGO_ISO"));
            response.setImpLiqNeto(rs.getDouble("IMP_LIQ_NETO"));
            response.setImpIva(rs.getDouble("IMP_IVA"));
            response.setImpLiq(rs.getDouble("IMP_LIQ"));
            response.setValCambio(rs.getDouble("VAL_CAMBIO"));
            response.setTipDocto(rs.getString("TIP_DOCTO"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
