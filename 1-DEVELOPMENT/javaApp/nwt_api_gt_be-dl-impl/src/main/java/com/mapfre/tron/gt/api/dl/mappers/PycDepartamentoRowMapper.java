package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycDepartamento;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycDepartamento.
 */
@Slf4j
public class PycDepartamentoRowMapper implements RowMapper<PycDepartamento> {

    @Override
    public PycDepartamento mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycDepartamento", rowNum);
        
        PycDepartamento departamento = new PycDepartamento();
        
        try {
            departamento.setIdDepartamento(rs.getInt("ID_DEPARTAMENTO"));
            departamento.setNombreDepartamento(rs.getString("NOMBRE_DEPARTAMENTO"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return departamento;
    }
}
