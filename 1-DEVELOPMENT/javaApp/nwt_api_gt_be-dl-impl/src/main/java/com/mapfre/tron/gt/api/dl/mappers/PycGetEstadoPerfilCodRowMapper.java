package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycGetEstadoPerfilCod;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycGetEstadoPerfilCod.
 * 
 * La función FN_GET_ESTADO_PERFIL retorna los siguientes campos:
 * - ID_PERFIL: Identificador único del perfil
 * - NOMBRE_PERFIL: Nombre del perfil
 * - ID_ESTADO: Identificador único del estado
 * - NOMBRE_ESTADO: Nombre del estado
 */
@Slf4j
public class PycGetEstadoPerfilCodRowMapper implements RowMapper<PycGetEstadoPerfilCod> {

    @Override
    public PycGetEstadoPerfilCod mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycGetEstadoPerfilCod", rowNum);
        
        PycGetEstadoPerfilCod estadoPerfil = new PycGetEstadoPerfilCod();
        
        try {
            // Mapear ID_PERFIL
            estadoPerfil.setIdPerfil(rs.getInt("ID_PERFIL"));
            
            // Mapear NOMBRE_PERFIL
            estadoPerfil.setNombrePerfil(rs.getString("NOMBRE_PERFIL"));
            
            // Mapear ID_ESTADO
            estadoPerfil.setIdEstado(rs.getInt("ID_ESTADO"));
            
            // Mapear NOMBRE_ESTADO
            estadoPerfil.setNombreEstado(rs.getString("NOMBRE_ESTADO"));
            
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return estadoPerfil;
    }
}
