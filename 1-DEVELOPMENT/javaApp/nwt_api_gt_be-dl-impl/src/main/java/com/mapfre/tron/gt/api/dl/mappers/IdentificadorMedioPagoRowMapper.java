package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PLE_IdentificadorMedioPago;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PLE_IdentificadorMedioPago.
 */
@Slf4j
public class IdentificadorMedioPagoRowMapper implements RowMapper<PLE_IdentificadorMedioPago> {

    @Override
    public PLE_IdentificadorMedioPago mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PLE_IdentificadorMedioPago", rowNum);
        
        PLE_IdentificadorMedioPago response = new PLE_IdentificadorMedioPago();
        
        try {
            response.setSistema(rs.getString("sistema"));
            response.setIdsistema(rs.getInt("idsistema"));
            response.setMedio(rs.getString("medio"));
            response.setIdmedio(rs.getInt("idmedio"));
            response.setIdentificador(rs.getString("identificador"));
            response.setMoneda(rs.getString("moneda"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
