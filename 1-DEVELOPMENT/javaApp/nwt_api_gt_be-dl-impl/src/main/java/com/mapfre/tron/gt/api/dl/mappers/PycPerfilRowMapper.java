package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycPerfil;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycPerfil.
 */
@Slf4j
public class PycPerfilRowMapper implements RowMapper<PycPerfil> {

    @Override
    public PycPerfil mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycPerfil", rowNum);
        
        PycPerfil perfil = new PycPerfil();
        
        try {
            perfil.setIdPerfil(rs.getInt("ID_PERFIL"));
            perfil.setNombrePerfil(rs.getString("NOMBRE_PERFIL"));
            perfil.setDescripcionPerfil(rs.getString("DESCRIPCION_PERFIL"));
            perfil.setUsuario(rs.getString("USUARIO"));
            perfil.setFechaHora(rs.getString("FECHA_HORA"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return perfil;
    }
}
