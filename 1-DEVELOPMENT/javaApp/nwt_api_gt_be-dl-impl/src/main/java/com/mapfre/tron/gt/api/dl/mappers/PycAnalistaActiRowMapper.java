package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycAnalistaActi;

/**
 * RowMapper para mapear los resultados de la función FN_ANALISTA_ACTIVIDAD a objetos PycAnalistaActi.
 * 
 * La función retorna los siguientes campos:
 * - ID_USUARIO: ID único del usuario analista
 * - NOMBRE: Nombre completo del analista (concatenación de PRIMER_NOMBRE + ' ' + PRIMER_APELLIDO)
 * - URL_PERFIL: URL de la imagen de perfil o imagen por defecto según género
 * - ESTADO_USUARIO: Estado del usuario analista
 * - ASIGNA: Indicador de asignación a la actividad ('0'=No asignado, '1'=Asignado)
 */
public class PycAnalistaActiRowMapper implements RowMapper<PycAnalistaActi> {

    @Override
    public PycAnalistaActi mapRow(ResultSet rs, int rowNum) throws SQLException {
        PycAnalistaActi analistaActi = new PycAnalistaActi();
        
        // Mapear ID_USUARIO
        analistaActi.setIdUsuario(rs.getInt("ID_USUARIO"));
        
        // Mapear NOMBRE (concatenación ya realizada en la función Oracle)
        analistaActi.setNombre(rs.getString("NOMBRE"));
        
        // Mapear URL_PERFIL (con lógica NVL y DECODE ya aplicada en la función Oracle)
        analistaActi.setUrlPerfil(rs.getString("URL_PERFIL"));
        
        // Mapear ESTADO_USUARIO
        analistaActi.setEstadoUsuario(rs.getString("ESTADO_USUARIO"));
        
        // Mapear ASIGNA (DECODE ya aplicado en la función Oracle, retorna '0' o '1')
        analistaActi.setAsigna(rs.getString("ASIGNA"));
        
        return analistaActi;
    }
}
