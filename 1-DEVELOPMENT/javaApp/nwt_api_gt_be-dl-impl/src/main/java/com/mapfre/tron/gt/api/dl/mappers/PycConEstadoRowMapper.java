package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycConEstado;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycConEstado.
 */
@Slf4j
public class PycConEstadoRowMapper implements RowMapper<PycConEstado> {

    @Override
    public PycConEstado mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycConEstado", rowNum);
        
        PycConEstado conEstado = new PycConEstado();
        
        try {
            conEstado.setIdEstado(rs.getInt("ID_ESTADO"));
            conEstado.setNombreEstado(rs.getString("NOMBRE_ESTADO"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return conEstado;
    }
}
