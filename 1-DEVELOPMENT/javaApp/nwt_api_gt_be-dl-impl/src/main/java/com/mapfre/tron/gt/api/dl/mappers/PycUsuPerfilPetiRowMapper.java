package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycUsuPerfilPeti;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycUsuPerfilPeti.
 */
@Slf4j
public class PycUsuPerfilPetiRowMapper implements RowMapper<PycUsuPerfilPeti> {

    @Override
    public PycUsuPerfilPeti mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycUsuPerfilPeti", rowNum);
        
        PycUsuPerfilPeti usuPerfilPeti = new PycUsuPerfilPeti();
        
        try {
            usuPerfilPeti.setIdUsuario(rs.getInt("ID_USUARIO"));
            usuPerfilPeti.setNombre(rs.getString("NOMBRE"));
            usuPerfilPeti.setGenero(rs.getString("GENERO"));
            usuPerfilPeti.setUrlPerfil(rs.getString("URL_PERFIL"));
            usuPerfilPeti.setIdPerfil(rs.getInt("ID_PERFIL"));
            usuPerfilPeti.setNombrePerfil(rs.getString("NOMBRE_PERFIL"));
            usuPerfilPeti.setAsigna(rs.getString("ASIGNA"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return usuPerfilPeti;
    }
}
