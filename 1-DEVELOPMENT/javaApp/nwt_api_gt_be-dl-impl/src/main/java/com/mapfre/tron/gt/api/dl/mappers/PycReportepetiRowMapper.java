package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycReportePeti;
import static com.mapfre.tron.gt.api.sr.CacheApi.log;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycReportePeti.
 */
@Slf4j
public class PycReportepetiRowMapper implements RowMapper<PycReportePeti> {

    @Override
    public PycReportePeti mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycReportePeti", rowNum);

        PycReportePeti reportePeti = new PycReportePeti();

        try {
            reportePeti.setIdPeticion(rs.getInt("ID_PETICION"));
            reportePeti.setNombrePeticion(rs.getString("NOMBRE_PETICION"));
            reportePeti.setEstado(rs.getString("ESTADO"));
            
            // Mapear fechas como String (siguiendo el patrón de otros mappers)
            reportePeti.setFechaInicio(rs.getString("FECHA_INICIO"));
            reportePeti.setFechaFin(rs.getString("FECHA_FIN"));
            
            reportePeti.setPrioridad(rs.getString("PRIORIDAD"));
            reportePeti.setUsuario(rs.getString("USUARIO"));
            reportePeti.setAnalista(rs.getString("ANALISTA"));
            reportePeti.setCodClarity(rs.getString("COD_CLARITY"));
            reportePeti.setNombreArea(rs.getString("NOMBRE_AREA"));
            reportePeti.setNombreDepartamento(rs.getString("NOMBRE_DEPARTAMENTO"));
            reportePeti.setObservaciones(rs.getString("OBSERVACIONES"));

        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }

        return reportePeti;
    }
}
