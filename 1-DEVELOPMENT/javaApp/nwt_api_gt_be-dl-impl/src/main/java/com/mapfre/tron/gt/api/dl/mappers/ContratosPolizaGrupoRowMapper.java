package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.ContratosPolizaGrupoResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos ContratosPolizaGrupoResponse.
 */
@Slf4j
public class ContratosPolizaGrupoRowMapper implements RowMapper<ContratosPolizaGrupoResponse> {

    @Override
    public ContratosPolizaGrupoResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto ContratosPolizaGrupoResponse", rowNum);
        
        ContratosPolizaGrupoResponse response = new ContratosPolizaGrupoResponse();
        
        try {
            response.setCodCia(rs.getInt("COD_CIA"));
            response.setNumPoliza(rs.getString("NUM_POLIZA"));
            response.setNumContrato(rs.getInt("NUM_CONTRATO"));
            response.setTipPoliza(rs.getString("TIP_POLIZA"));
            response.setMcaRiesgos(rs.getString("MCA_RIESGOS"));
            response.setFecVctoPoliza(rs.getString("FEC_VCTO_POLIZA"));
            response.setCantAviso(rs.getInt("CANT_AVISO"));
            response.setCantPoliza(rs.getInt("CANT_POLIZA"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
