package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycGetDatVarFormSec;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycGetDatVarFormSec.
 */
@Slf4j
public class PycGetDatVarFormSecRowMapper implements RowMapper<PycGetDatVarFormSec> {

    @Override
    public PycGetDatVarFormSec mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycGetDatVarFormSec", rowNum);
        
        PycGetDatVarFormSec datVarFormSec = new PycGetDatVarFormSec();
        
        try {
            // Información del formulario
            datVarFormSec.setIdFormulario(rs.getInt("ID_FORMULARIO"));
            datVarFormSec.setNombreFormulario(rs.getString("NOMBRE_FORMULARIO"));
            
            // Información de la sección
            datVarFormSec.setIdSeccion(rs.getInt("ID_SECCION"));
            datVarFormSec.setNombreSeccion(rs.getString("NOMBRE_SECCION"));
            datVarFormSec.setIconoSeccion(rs.getString("ICONO_SECCION"));
            datVarFormSec.setOrdenSeccion(rs.getInt("ORDEN_SECCION"));
            
            // Información del dato variable
            datVarFormSec.setIdDato(rs.getInt("ID_DATO"));
            datVarFormSec.setDescripcion(rs.getString("DESCRIPCION"));
            datVarFormSec.setValDefault(rs.getString("VAL_DEFAULT"));
            datVarFormSec.setOrden(rs.getInt("ORDEN"));
            
            // Tipo de dato
            datVarFormSec.setTipo(rs.getString("TIPO"));
            datVarFormSec.setTipoHtml(rs.getString("TIPO_HTML"));
            
            // Propiedades del control
            datVarFormSec.setPropertyClass(rs.getString("CLASS"));
            datVarFormSec.setClassForm(rs.getString("CLASS_FORM"));
            datVarFormSec.setJavascript(rs.getString("JAVASCRIPT"));
            datVarFormSec.setIcono(rs.getString("ICONO"));
            datVarFormSec.setPlaceholder(rs.getString("PLACEHOLDER"));
            
            // Validaciones
            datVarFormSec.setValMin(rs.getString("VAL_MIN"));
            datVarFormSec.setValMax(rs.getString("VAL_MAX"));
            
            // Dependencias y configuración
            Integer idDependencia = rs.getObject("ID_DEPENDENCIA", Integer.class);
            datVarFormSec.setIdDependencia(idDependencia);
            
            datVarFormSec.setIndVisible(rs.getString("IND_VISIBLE"));
            datVarFormSec.setIndObligatorio(rs.getString("IND_OBLIGATORIO"));
            datVarFormSec.setIndEditable(rs.getString("IND_EDITABLE"));
            
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return datVarFormSec;
    }
}
