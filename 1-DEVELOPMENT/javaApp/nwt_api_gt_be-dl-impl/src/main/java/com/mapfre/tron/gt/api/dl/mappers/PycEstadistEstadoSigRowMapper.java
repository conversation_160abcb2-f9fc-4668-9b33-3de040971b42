package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycEstadistEstadoSig;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycEstadistEstadoSig.
 */
@Slf4j
public class PycEstadistEstadoSigRowMapper implements RowMapper<PycEstadistEstadoSig> {

    @Override
    public PycEstadistEstadoSig mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycEstadistEstadoSig", rowNum);
        
        PycEstadistEstadoSig estadistEstadoSig = new PycEstadistEstadoSig();
        
        try {
            estadistEstadoSig.setNombreEstado(rs.getString("NOMBRE_ESTADO"));
            estadistEstadoSig.setIdEstado(rs.getInt("ID_ESTADO"));
            estadistEstadoSig.setColorEstado(rs.getString("COLOR_ESTADO"));
            estadistEstadoSig.setCantidadEstado(rs.getInt("CANTIDAD_ESTADO"));
            estadistEstadoSig.setTotalEstado(rs.getInt("TOTAL_ESTADO"));
            estadistEstadoSig.setPorcentajeEstado(rs.getDouble("PORCENTAJE_ESTADO"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return estadistEstadoSig;
    }
}
