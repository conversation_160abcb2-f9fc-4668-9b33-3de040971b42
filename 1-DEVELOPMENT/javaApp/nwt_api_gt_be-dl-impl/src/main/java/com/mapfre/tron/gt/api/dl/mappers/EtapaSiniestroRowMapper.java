package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.EtapaSiniestro;
import org.springframework.jdbc.core.RowMapper;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper para convertir resultados de la base de datos a objetos EtapaSiniestro.
 */
public class EtapaSiniestroRowMapper implements RowMapper<EtapaSiniestro> {

    @Override
    public EtapaSiniestro mapRow(ResultSet rs, int rowNum) throws SQLException {
        EtapaSiniestro etapa = new EtapaSiniestro();
        
        etapa.setNumOrden(rs.getInt("NUM_ORDEN"));
        etapa.setCodigoFase(rs.getString("CODIGO_FASE"));
        etapa.setCodCia(rs.getInt("COD_CIA"));
        etapa.setCodRamo(rs.getInt("COD_RAMO"));
        etapa.setNumPoliza(rs.getString("NUM_POLIZA"));
        etapa.setNumSini(rs.getString("NUM_SINI"));
        
        BigDecimal deducible = rs.getBigDecimal("DEDUCIBLE");
        if (!rs.wasNull()) {
            etapa.setDeducible(deducible.doubleValue());
        }
        
        etapa.setEtapa(rs.getString("ETAPA"));
        
        String cuenta = rs.getString("CUENTA");
        if (!rs.wasNull()) {
            etapa.setCuenta(cuenta);
        }
        
        String cheque = rs.getString("CHEQUE");
        if (!rs.wasNull()) {
            etapa.setCheque(cheque);
        }
        
        BigDecimal monto = rs.getBigDecimal("MONTO");
        if (!rs.wasNull()) {
            etapa.setMonto(monto.doubleValue());
        }
        
        etapa.setFecha(rs.getString("FECHA"));
        
        String perito = rs.getString("PERITO");
        if (!rs.wasNull()) {
            etapa.setPerito(perito);
        }
        
        String taller = rs.getString("TALLER");
        if (!rs.wasNull()) {
            etapa.setTaller(taller);
        }
        
        return etapa;
    }
}
