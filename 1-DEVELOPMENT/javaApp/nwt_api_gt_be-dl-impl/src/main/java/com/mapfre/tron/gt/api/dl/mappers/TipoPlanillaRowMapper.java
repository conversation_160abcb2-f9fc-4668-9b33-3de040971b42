package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PLE_TipoPlanilla;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PLE_TipoPlanilla.
 */
@Slf4j
public class TipoPlanillaRowMapper implements RowMapper<PLE_TipoPlanilla> {

    @Override
    public PLE_TipoPlanilla mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PLE_TipoPlanilla", rowNum);
        
        PLE_TipoPlanilla response = new PLE_TipoPlanilla();
        
        try {
            response.setCodigo(rs.getInt("codigo"));
            response.setDescripcion(rs.getString("descripcion"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
