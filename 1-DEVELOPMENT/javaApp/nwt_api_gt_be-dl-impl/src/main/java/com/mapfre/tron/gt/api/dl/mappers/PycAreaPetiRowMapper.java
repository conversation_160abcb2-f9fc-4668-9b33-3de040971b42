package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycAreaPeti;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycAreaPeti.
 */
@Slf4j
public class PycAreaPetiRowMapper implements RowMapper<PycAreaPeti> {

    @Override
    public PycAreaPeti mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycAreaPeti", rowNum);
        
        PycAreaPeti areaPeti = new PycAreaPeti();
        
        try {
            areaPeti.setIdArea(rs.getInt("ID_AREA"));
            areaPeti.setNombreArea(rs.getString("NOMBRE_AREA"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return areaPeti;
    }
}
