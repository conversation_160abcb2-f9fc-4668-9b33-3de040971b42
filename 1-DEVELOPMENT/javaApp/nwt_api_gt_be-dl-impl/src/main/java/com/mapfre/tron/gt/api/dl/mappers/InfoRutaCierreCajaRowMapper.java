package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.InfoRutaCierreCajaResponse;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper para convertir resultados de la base de datos a objetos InfoRutaCierreCajaResponse.
 */
public class InfoRutaCierreCajaRowMapper implements RowMapper<InfoRutaCierreCajaResponse> {

    @Override
    public InfoRutaCierreCajaResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        InfoRutaCierreCajaResponse response = new InfoRutaCierreCajaResponse();

        // Mapear los campos según la definición en InfoRutaCierreCajaResponse
        response.setRuta(rs.getInt("ruta"));
        response.setFecha(rs.getString("fecha"));
        response.setCantidad(rs.getDouble("cantidad"));
        response.setCantidadQ(rs.getDouble("cantidadQ"));
        response.setCantidadD(rs.getDouble("cantidadD"));

        return response;
    }
}