package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycOpcionUsu;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycOpcionUsu.
 */
@Slf4j
public class PycOpcionUsuRowMapper implements RowMapper<PycOpcionUsu> {

    @Override
    public PycOpcionUsu mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycOpcionUsu", rowNum);
        
        PycOpcionUsu opcionUsu = new PycOpcionUsu();
        
        try {
            opcionUsu.setCodigoMenu(rs.getInt("CODIGOMENU"));
            opcionUsu.setCodigoMenuPadre(rs.getInt("CODIGOMENUPADRE"));
            opcionUsu.setTitulo(rs.getString("TITULO"));
            opcionUsu.setLogo(rs.getString("LOGO"));
            opcionUsu.setRuta(rs.getString("RUTA"));
            opcionUsu.setFunciones(rs.getString("FUNCIONES"));
            opcionUsu.setEstado(rs.getString("ESTADO"));
            opcionUsu.setLevel(rs.getInt("LEVEL"));
            opcionUsu.setOrden(rs.getInt("ORDEN"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return opcionUsu;
    }
}
