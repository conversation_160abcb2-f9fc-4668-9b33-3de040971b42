package com.mapfre.tron.gt.api.dl.mappers;
import com.mapfre.tron.gt.api.model.InfoFirmaReciboResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;
import java.sql.Blob;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
/**
 * Mapper para convertir resultados de la base de datos a objetos InfoFirmaReciboResponse.
 */
public class InfoFirmaReciboRowMapper implements RowMapper<InfoFirmaReciboResponse> {
    private static final Logger log = LoggerFactory.getLogger(InfoFirmaReciboRowMapper.class);
    @Override
    public InfoFirmaReciboResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        InfoFirmaReciboResponse response = new InfoFirmaReciboResponse();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // Mapear los campos según la definición en InfoFirmaReciboResponse
        response.setIdDetalle(rs.getInt("ID_DETALLE"));
        if (rs.wasNull()) {
            response.setIdDetalle(null);
        }
        response.setIdRuta(rs.getInt("ID_RUTA"));
        if (rs.wasNull()) {
            response.setIdRuta(null);
        }
        response.setIdEstado(rs.getInt("ID_ESTADO"));
        if (rs.wasNull()) {
            response.setIdEstado(null);
        }
        response.setIdePol(rs.getString("IDEPOL"));
        response.setCodPol(rs.getString("COD_POL"));
        response.setNumPol(rs.getString("NUM_POL"));
        response.setNumCert(rs.getString("NUM_CERT"));
        response.setRecibo(rs.getString("RECIBO"));
        response.setMoneda(rs.getString("MONEDA"));
        response.setTotal(rs.getString("TOTAL"));
        response.setCuota(rs.getString("CUOTA"));
        // Manejar el campo orden - puede ser nulo
        Integer orden = rs.getObject("ORDEN", Integer.class);
        if (orden != null) {
            response.setOrden(orden);
        }
        response.setSistema(rs.getString("SISTEMA"));
        response.setAsegurado(rs.getString("ASEGURADO"));
        response.setDireccion(rs.getString("DIRECCION"));
        response.setComentario(rs.getString("COMENTARIO"));
        response.setDirCobLatitud(rs.getString("DIR_COB_LATITUD"));
        response.setDirCobLongitud(rs.getString("DIR_COB_LONGITUD"));
        // Manejar fechas - convertir a String con formato yyyy-MM-dd
        Date fechaReciVencimiento = rs.getDate("FECHA_RECI_VENCIMIENTO");
        if (fechaReciVencimiento != null && !rs.wasNull()) {
            response.setFechaReciVencimiento(dateFormat.format(fechaReciVencimiento));
        }
        Date fechaReciCobro = rs.getDate("FECHA_RECI_COBRO");
        if (fechaReciCobro != null && !rs.wasNull()) {
            response.setFechaReciCobro(dateFormat.format(fechaReciCobro));
        }
        Date fechaCrea = rs.getDate("FECHA_CREA");
        if (fechaCrea != null && !rs.wasNull()) {
            response.setFechaCrea(dateFormat.format(fechaCrea));
        }
        Date fechaModi = rs.getDate("FECHA_MODI");
        if (fechaModi != null && !rs.wasNull()) {
            response.setFechaModi(dateFormat.format(fechaModi));
        }
        // Manejar el campo idUsuarioCobra - puede ser nulo
        Integer idUsuarioCobra = rs.getObject("ID_USUARIO_COBRA", Integer.class);
        if (idUsuarioCobra != null && !rs.wasNull()) {
            response.setIdUsuarioCobra(idUsuarioCobra);
        }
        Date fechaRecordatorio = rs.getDate("FECHA_RECORDATORIO");
        if (fechaRecordatorio != null && !rs.wasNull()) {
            response.setFechaRecordatorio(dateFormat.format(fechaRecordatorio));
        }
        // Manejar el BLOB de la firma - puede ser nulo
        Blob imgFirmaBlob = rs.getBlob("IMG_FIRMA");
        if (imgFirmaBlob != null && !rs.wasNull()) {
            try {
                int blobLength = (int) imgFirmaBlob.length();
                if (blobLength > 0) {
                    byte[] imgFirmaBytes = imgFirmaBlob.getBytes(1, blobLength);
                    // Convertir a Base64 String
                    String imgFirmaBase64 = Base64.getEncoder().encodeToString(imgFirmaBytes);
                    // Almacenar la cadena Base64 directamente como String
                    response.setImgFirma(imgFirmaBase64);
                }
            } catch (Exception e) {
                // En caso de error al leer el BLOB, registrar el error pero continuar
                log.error("Error al leer el BLOB de firma: {}", e.getMessage(), e);
            } finally {
                try {
                    imgFirmaBlob.free();
                } catch (SQLException e) {
                    log.warn("Error al liberar recursos del BLOB: {}", e.getMessage());
                }
            }
        }
        response.setCorreo(rs.getString("CORREO"));
        return response;
    }
}