package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.RutaLocalizacionResponse;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;



/**
 * Mapper para convertir resultados de la base de datos a objetos RutaLocalizacionResponse.
 */
public class RutaLocalizacionRowMapper implements RowMapper<RutaLocalizacionResponse> {


    @Override
    public RutaLocalizacionResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        RutaLocalizacionResponse response = new RutaLocalizacionResponse();

        // Mapear los campos según la definición en RutaLocalizacionResponse
        response.setRuta(rs.getInt("ruta"));

        // Usar la fecha tal como viene de la base de datos (ya formateada)
        response.setFecha(rs.getString("fecha"));

        response.setCantidad(rs.getDouble("cantidad"));
        response.setCantidadQ(rs.getDouble("cantidadQ"));
        response.setCantidadD(rs.getDouble("cantidadD"));
        response.setCantidadCobros(rs.getInt("CantidadCobros"));

        return response;
    }
}
