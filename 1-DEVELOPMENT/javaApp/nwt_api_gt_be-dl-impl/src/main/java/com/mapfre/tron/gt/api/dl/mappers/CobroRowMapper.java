package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.Cobro;
import com.mapfre.tron.gt.api.model.Entidad;
import org.springframework.jdbc.core.RowMapper;

import javax.swing.tree.TreePath;
import java.sql.ResultSet;
import java.sql.SQLException;

public class CobroRowMapper implements RowMapper<Cobro>{

    @Override
    public Cobro mapRow(ResultSet resultSet, int i) throws SQLException {
        Cobro cobro = new Cobro();
        cobro.setCodigo(resultSet.getString("codigo"));
        cobro.setDescripcion(resultSet.getString("descripcion"));

        return cobro;
    }
}
