package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycProcesoPorTipo;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycProcesoPorTipo.
 */
@Slf4j
public class PycProcesoPorTipoRowMapper implements RowMapper<PycProcesoPorTipo> {

    @Override
    public PycProcesoPorTipo mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycProcesoPorTipo", rowNum);
        
        PycProcesoPorTipo procesoPorTipo = new PycProcesoPorTipo();
        
        try {
            procesoPorTipo.setIdProceso(rs.getInt("ID_PROCESO"));
            procesoPorTipo.setNombreProceso(rs.getString("NOMBRE_PROCESO"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return procesoPorTipo;
    }
}
