package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.AvisoPagoPolizaGrupoResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos AvisoPagoPolizaGrupoResponse.
 */
@Slf4j
public class AvisoPagoPolizaGrupoRowMapper implements RowMapper<AvisoPagoPolizaGrupoResponse> {

    @Override
    public AvisoPagoPolizaGrupoResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto AvisoPagoPolizaGrupoResponse", rowNum);
        
        AvisoPagoPolizaGrupoResponse response = new AvisoPagoPolizaGrupoResponse();
        
        try {
            response.setCodActTercero(rs.getInt("COD_ACT_TERCERO"));
            response.setCodAgt(rs.getString("COD_AGT"));
            response.setCodCia(rs.getInt("COD_CIA"));
            response.setCodDocum(rs.getString("COD_DOCUM"));
            response.setCodDocumPago(rs.getString("COD_DOCUM_PAGO"));
            response.setCodGestor(rs.getInt("COD_GESTOR"));
            response.setCodMon(rs.getInt("COD_MON"));
            response.setFecMvto(rs.getString("FEC_MVTO"));
            response.setFecVcto(rs.getString("FEC_VCTO"));
            response.setImpDocum(rs.getDouble("IMP_DOCUM"));
            response.setNumContrato(rs.getInt("NUM_CONTRATO"));
            response.setNumMvto(rs.getInt("NUM_MVTO"));
            response.setNumPoliza(rs.getString("NUM_POLIZA"));
            response.setNumPolizaCliente(rs.getString("NUM_POLIZA_CLIENTE"));
            response.setNumPolizaGrupo(rs.getString("NUM_POLIZA_GRUPO"));
            response.setTipDocum(rs.getString("TIP_DOCUM"));
            response.setTipDocumPago(rs.getString("TIP_DOCUM_PAGO"));
            response.setTipEstado(rs.getString("TIP_ESTADO"));
            response.setTipGestor(rs.getString("TIP_GESTOR"));
            response.setValCambio(rs.getDouble("VAL_CAMBIO"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
