package com.mapfre.tron.gt.api.dl;

import java.sql.Types;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.SqlOutParameter;
import org.springframework.jdbc.core.SqlParameter;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.jdbc.core.simple.SimpleJdbcCall;
import org.springframework.stereotype.Repository;

import com.mapfre.tron.gt.api.commons.DatabaseConstants;
import com.mapfre.tron.gt.api.dl.mappers.BuscarAseguradoRowMapper;
import com.mapfre.tron.gt.api.model.BusquedaAsegurado;

import lombok.extern.slf4j.Slf4j;

@Repository
@Slf4j
public class DlOficinaDigitalImpl implements IDlOficinaDigital {
	 
    @Autowired
    @Qualifier("jdbcTemplate")
    protected JdbcTemplate jdbcTemplate;

    private SimpleJdbcCall prepareJdbcCall(String procedureName, SqlParameter... parameters) {
            return new SimpleJdbcCall(jdbcTemplate)
                            .withSchemaName(DatabaseConstants.SCHEMA_NAME)
                            .withCatalogName(DatabaseConstants.CATALOG_OFICINA_DIGITAL_NAME)
                            .withProcedureName(procedureName)
                            .declareParameters(parameters);
    }

	@SuppressWarnings("unchecked")
	@Override
	public List<BusquedaAsegurado> busquedaAsegurados(Integer codCia, String nombreAseg, Integer codAgente) {
		log.info(String.format(DatabaseConstants.LOG_PROC_CALL, "getBuscarAsegurado"));
		 
        SimpleJdbcCall call = prepareJdbcCall(DatabaseConstants.PROC_DATOS_BUSQ_ASEGURADOS,
                        new SqlOutParameter(DatabaseConstants.PARAM_REG_BUSQ_ASEGURADOS, Types.REF_CURSOR, new BuscarAseguradoRowMapper()),
                        new SqlParameter(DatabaseConstants.PARAM_NAME_AUX, Types.VARCHAR),
                        new SqlParameter(DatabaseConstants.PARAM_COD_AGENTE, Types.VARCHAR),
                        new SqlParameter(DatabaseConstants.PARAM_COD_CIA, Types.INTEGER)
        );

        String codigo = codAgente != null ? codAgente.toString() : null;

        SqlParameterSource inParams = new MapSqlParameterSource()
                        .addValue(DatabaseConstants.PARAM_NAME_AUX, nombreAseg.replace(" ", "%").toUpperCase())
                        .addValue(DatabaseConstants.PARAM_COD_AGENTE, codigo)
                        .addValue(DatabaseConstants.PARAM_COD_CIA, codCia);

        log.info(String.format(DatabaseConstants.LOG_LAUNCH_DESC, DatabaseConstants.SCHEMA_NAME,
                        DatabaseConstants.CATALOG_OFICINA_DIGITAL_NAME, DatabaseConstants.PROC_DATOS_BUSQ_ASEGURADOS));

        Map<String, Object> result = call.execute(inParams);
        return (List<BusquedaAsegurado>) result.get(DatabaseConstants.PARAM_REG_BUSQ_ASEGURADOS);
	}

	@Override
	public List<BusquedaAsegurado> busquedaAseguradosQuery(Integer codCia, String nombreAseg, Integer codAgente) {
		String query = new StringBuilder()
				.append("SELECT distinct pe.nom_completo, pe.tip_docum, pe.cod_docum ")
				.append("FROM v1001390_trn pe ")
				.append("INNER JOIN a2000030 a30 ")
				.append("on pe.COD_DOCUM = a30.cod_docum and pe.TIP_DOCUM = a30.tip_docum and pe.COD_ACT_TERCERO = 1 ")
				.append("WHERE a30.cod_cia = ? ")
				.append("AND pe.cod_act_tercero = 1 ")
				.append("AND pe.NOM_TERCERO||pe.NOM2_TERCERO||pe.APE1_TERCERO||pe.APE2_TERCERO like '%'||?||'%' ")
				.append("AND a30.cod_agt = NVL(?, a30.cod_agt) ")
				.append("AND a30.num_spto IN ")
				.append("    (SELECT num_spto ")
				.append("FROM a2000030 b ")
				.append("    WHERE b.cod_cia = a30.cod_cia ")
				.append("AND b.cod_ramo = a30.cod_ramo ")
				.append("AND b.num_poliza = a30.num_poliza ")
				.append("AND b.mca_provisional = 'N' ")
				.append("AND b.mca_spto_anulado = 'N') ")
				.toString();
		
		return jdbcTemplate.query(query, new Object[] {codCia, nombreAseg, codAgente}, new BuscarAseguradoRowMapper());
	}

}
