package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycResumenActi;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycResumenActi.
 */
@Slf4j
public class PycResumenActiRowMapper implements RowMapper<PycResumenActi> {

    @Override
    public PycResumenActi mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycResumenActi", rowNum);
        
        PycResumenActi resumenActi = new PycResumenActi();
        
        try {
            resumenActi.setTotales(rs.getInt("TOTALES"));
            resumenActi.setTerminadas(rs.getInt("TERMINADAS"));
            resumenActi.setPendientes(rs.getInt("PENDIENTES"));
            resumenActi.setBase(rs.getDouble("BASE"));
            resumenActi.setRea(rs.getDouble("REA"));
            resumenActi.setPorcentaje(rs.getInt("PORCENTAJE"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return resumenActi;
    }
}
