package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.ValidaEstadoDeducibleResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos ValidaEstadoDeducibleResponse.
 */
@Slf4j
public class ValidaEstadoDeducibleRowMapper implements RowMapper<ValidaEstadoDeducibleResponse> {

    @Override
    public ValidaEstadoDeducibleResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto ValidaEstadoDeducibleResponse", rowNum);
        
        ValidaEstadoDeducibleResponse response = new ValidaEstadoDeducibleResponse();
        
        try {
            response.setResp(rs.getString("resp"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
