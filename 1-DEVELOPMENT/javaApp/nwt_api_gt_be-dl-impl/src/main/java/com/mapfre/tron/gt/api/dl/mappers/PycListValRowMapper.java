package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycListVal;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycListVal.
 */
@Slf4j
public class PycListValRowMapper implements RowMapper<PycListVal> {

    @Override
    public PycListVal mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycListVal", rowNum);
        
        PycListVal listVal = new PycListVal();
        
        try {
            listVal.setValor(rs.getString("VALOR"));
            listVal.setDescripcion(rs.getString("DESCRIPCION"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return listVal;
    }
}
