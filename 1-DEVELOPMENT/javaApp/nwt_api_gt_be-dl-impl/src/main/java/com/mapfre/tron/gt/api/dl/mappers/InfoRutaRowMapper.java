package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.InfoRutaResponse;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper para convertir resultados de la base de datos a objetos InfoRutaResponse.
 */
public class InfoRutaRowMapper implements RowMapper<InfoRutaResponse> {

    @Override
    public InfoRutaResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        InfoRutaResponse response = new InfoRutaResponse();

        // Mapear los campos según la definición en InfoRutaResponse
        response.setIdRuta(rs.getInt("ID_RUTA"));
        response.setCobrador(rs.getString("COBRADOR"));
        response.setCobradorImg(rs.getString("COBRADOR_IMG"));
        response.setFecha(rs.getString("FECHA"));
        response.setEstadoCod(rs.getString("ESTADO_COD"));
        response.setEstado(rs.getString("ESTADO"));
        response.setTotal(rs.getString("TOTAL"));

        return response;
    }
}