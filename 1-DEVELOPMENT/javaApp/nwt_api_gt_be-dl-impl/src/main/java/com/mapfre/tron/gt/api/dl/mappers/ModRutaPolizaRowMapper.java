package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.ModRutaPolizaResponse;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper para convertir resultados de la base de datos a objetos ModRutaPolizaResponse.
 */
public class ModRutaPolizaRowMapper implements RowMapper<ModRutaPolizaResponse> {

    @Override
    public ModRutaPolizaResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        ModRutaPolizaResponse response = new ModRutaPolizaResponse();

        // Mapear los campos según la definición en ModRutaPolizaResponse
        response.setPolizaCodigo(rs.getString("POLIZA_CODIGO"));
        response.setPolizaNumero(rs.getString("POLIZA_NUMERO"));
        response.setCertificado(rs.getString("CERTIFICADO"));
        response.setAsegurado(rs.getString("ASEGURADO"));
        response.setDireccion(rs.getString("DIRECCION"));
        response.setEstadoCod(rs.getString("ESTADO_COD"));
        response.setEstado(rs.getString("ESTADO"));
        response.setRuta(rs.getInt("RUTA"));
        response.setIdePol(rs.getString("IDEPOL"));

        return response;
    }
}