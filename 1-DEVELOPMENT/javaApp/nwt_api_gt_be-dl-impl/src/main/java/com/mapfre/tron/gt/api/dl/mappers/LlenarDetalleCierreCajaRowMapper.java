package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.LlenarDetalleCierreCajaResponse;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper para convertir resultados de la base de datos a objetos LlenarDetalleCierreCajaResponse.
 */
public class LlenarDetalleCierreCajaRowMapper implements RowMapper<LlenarDetalleCierreCajaResponse> {

    @Override
    public LlenarDetalleCierreCajaResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        LlenarDetalleCierreCajaResponse response = new LlenarDetalleCierreCajaResponse();

        // Mapear los campos según la definición en LlenarDetalleCierreCajaResponse
        response.setRuta(rs.getInt("ruta"));
        response.setTipoPago(rs.getString("tipoPago"));
        response.setCantidad(rs.getDouble("cantidad"));
        response.setMoneda(rs.getString("moneda"));

        return response;
    }
}