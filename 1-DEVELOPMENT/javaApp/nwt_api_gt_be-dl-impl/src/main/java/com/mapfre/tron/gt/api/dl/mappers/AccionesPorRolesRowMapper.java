package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.AccionesPorRolesResponseAcciones;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class AccionesPorRolesRowMapper implements RowMapper<AccionesPorRolesResponseAcciones> {

    @Override
    public AccionesPorRolesResponseAcciones mapRow(ResultSet rs, int rowNum) throws SQLException {
        AccionesPorRolesResponseAcciones accion = new AccionesPorRolesResponseAcciones();
        
        accion.setIdAccion(rs.getInt("id_accion"));
        accion.setNombre(rs.getString("nombre"));
        
        return accion;
    }
}
