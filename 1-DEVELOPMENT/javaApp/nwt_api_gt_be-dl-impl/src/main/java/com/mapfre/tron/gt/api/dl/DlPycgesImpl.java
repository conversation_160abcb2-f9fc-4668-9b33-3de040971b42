package com.mapfre.tron.gt.api.dl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

import oracle.jdbc.OracleTypes;

import javax.sql.DataSource;
import javax.sql.rowset.serial.SerialClob;

import com.mapfre.tron.gt.api.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.mapfre.tron.gt.api.commons.DatabaseConstants;
import com.mapfre.tron.gt.api.dl.mappers.PycAplicacionRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycAnalistaActiRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycAnalistaRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycConEstadoRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycAreaPetiRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycAreaUsuarioRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycDepartamentoRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycPrioridadRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycTabAniosRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycTabAreaRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycTabEstadoRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycUsuPerfilPetiRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycPetiFiltroRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycReportepetiRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycGetUsuariosAsigRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycListadoAsignaRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycPerfilRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycPeticionPerfilRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycPetSinProgramadorRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycProcesoPorTipoRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycUsuarioPerfilRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycUsuarioRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycUsuPerfilProcesoRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycEstadoUsuRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycUsuProcesosRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycObserPetRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycPetAvanceRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycPetAvanceSigRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycResumenActiRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycListValRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycOficinaRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycCanalRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycSubordinadosRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycInfoUsuarioRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycInfoProcesoRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycControlProcRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycEstadoPerfilRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycRamoRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycDatoVarEquivRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycPerfilUsuRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycCategoriaPetRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycPeticionTabRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycOpcionUsuRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycReportePetProgRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycEstadisticaEstadoRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycEstadistEstadoSigRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycGetDatVarFormSecRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycGetBitacoraPetRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycTipoPeticionRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycEstadoTranscRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycDocPeticionRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycDocXPeticionRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycDatVarForSeccTippeRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycGetPerfilesRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycGetEstadoPerfilCodRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycRespPetiRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycActPeticionRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycDocNoObliRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.PycCatActRowMapper;

import lombok.extern.slf4j.Slf4j;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;

/**
 * Implementación de la interfaz IDlPycges para operaciones de Pycges en la capa de acceso a datos.
 */
@Repository
@Slf4j
public class DlPycgesImpl implements IDlPycges {

    @Autowired
    private DataSource dataSource;

    /**
     * Limpia el XML eliminando espacios y saltos de línea entre etiquetas,
     * pero preservando el contenido dentro de las etiquetas.
     * También maneja contenido CDATA extrayendo el XML interno.
     *
     * Ejemplos de transformación:
     * - "<INFO_PETICION> </INFO_PETICION>" -> "<INFO_PETICION></INFO_PETICION>"
     * - "<TAG>\n  <SUBTAG>valor</SUBTAG>\n</TAG>" -> "<TAG><SUBTAG>valor</SUBTAG></TAG>"
     * - "<![CDATA[<INFO_PETICION>          </INFO_PETICION>]]>" -> "<INFO_PETICION></INFO_PETICION>"
     *
     * @param xmlText El texto XML a limpiar
     * @return El XML limpio sin espacios entre etiquetas
     */
    private String cleanXmlText(String xmlText) {
        if (xmlText == null || xmlText.trim().isEmpty()) {
            return xmlText;
        }

        try {
            String cleanedXml = xmlText.trim();

            // Paso 1: Detectar y extraer contenido CDATA
            if (cleanedXml.startsWith("<![CDATA[") && cleanedXml.endsWith("]]>")) {
                log.debug("Detectado contenido CDATA, extrayendo XML interno");
                // Extraer el contenido entre <![CDATA[ y ]]>
                cleanedXml = cleanedXml.substring(9, cleanedXml.length() - 3);
                log.debug("XML extraído de CDATA: {}", cleanedXml);
            }

            // Paso 2: Eliminar espacios y saltos de línea entre etiquetas de cierre y apertura
            cleanedXml = cleanedXml.replaceAll(">\\s+<", "><");

            // Paso 3: Eliminar espacios dentro de etiquetas vacías o que solo contienen espacios
            cleanedXml = cleanedXml.replaceAll("<([^>]+)>\\s+</\\1>", "<$1></$1>");

            // Paso 4: Eliminar espacios al inicio y final del XML completo
            cleanedXml = cleanedXml.trim();

            // Paso 5: Normalizar espacios múltiples dentro del contenido de las etiquetas (pero no eliminarlos completamente)
            // Solo normaliza espacios múltiples consecutivos a uno solo
            cleanedXml = cleanedXml.replaceAll("(>[^<]*?)\\s{2,}([^<]*?<)", "$1 $2");

            log.debug("XML original length: {}, XML limpio length: {}", xmlText.length(), cleanedXml.length());
            if (log.isDebugEnabled()) {
                log.debug("XML original: {}", xmlText.replaceAll("\\s", " "));
                log.debug("XML limpio: {}", cleanedXml);
            }

            return cleanedXml;

        } catch (Exception e) {
            log.warn("Error al limpiar XML, usando original: {}", e.getMessage());
            return xmlText;
        }
    }

    @Override
    public List<PycUsuario> getUsuarios() {
        log.info("Ejecutando función getUsuarios");

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_USUARIO +
                "() AS usuarios FROM dual";

        List<PycUsuario> result = new ArrayList<>();
        PycUsuarioRowMapper mapper = new PycUsuarioRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("usuarios");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Usuarios obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getUsuarios: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la información de los usuarios", e);
        }
    }

    @Override
    public List<PycAplicacion> getAplicaciones(Integer idProceso) {
        log.info("Ejecutando función getAplicaciones con idProceso={}", idProceso);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_GET_APLICACION +
                "(?) AS aplicaciones FROM dual";

        List<PycAplicacion> result = new ArrayList<>();
        PycAplicacionRowMapper mapper = new PycAplicacionRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idProceso
            stmt.setInt(1, idProceso);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("aplicaciones");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Aplicaciones obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getAplicaciones: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la información de las aplicaciones", e);
        }
    }

    @Override
    public List<PycInfoProceso> getInfoProceso(Integer idProceso) {
        log.info("Ejecutando función getInfoProceso con idProceso={}", idProceso);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_GET_INFO_PROCESO +
                "(?) AS info_proceso FROM dual";

        List<PycInfoProceso> result = new ArrayList<>();
        PycInfoProcesoRowMapper mapper = new PycInfoProcesoRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idProceso
            stmt.setInt(1, idProceso);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("info_proceso");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Información de proceso obtenida: {} registros", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getInfoProceso: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la información del proceso", e);
        }
    }

    @Override
    public List<PycControlProc> getControlesProceso(Integer idProceso, String tipoControl) {
        log.info("Ejecutando función getControlesProceso con idProceso={}, tipoControl={}", idProceso, tipoControl);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_OBTENER_CONTROLES_PRO +
                "(?, ?) AS controles_proceso FROM dual";

        List<PycControlProc> result = new ArrayList<>();
        PycControlProcRowMapper mapper = new PycControlProcRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros
            stmt.setInt(1, idProceso);
            stmt.setString(2, tipoControl);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("controles_proceso");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Controles de proceso obtenidos: {} registros", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getControlesProceso: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los controles del proceso", e);
        }
    }

    @Override
    public List<PycEstadoPerfil> getEstadoPerfil(Integer idPerfil, Integer idProceso) {
        log.info("Ejecutando función getEstadoPerfil con idPerfil={}, idProceso={}", idPerfil, idProceso);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_OBTENER_ESTADO_PERFIL +
                "(?, ?) AS estado_perfil FROM dual";

        List<PycEstadoPerfil> result = new ArrayList<>();
        PycEstadoPerfilRowMapper mapper = new PycEstadoPerfilRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros
            stmt.setInt(1, idPerfil);
            stmt.setInt(2, idProceso);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("estado_perfil");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Estados de perfil obtenidos: {} registros", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getEstadoPerfil: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los estados del perfil", e);
        }
    }

    @Override
    public List<PycPerfil> getPerfiles() {
        log.info("Ejecutando función getPerfiles");

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_PERFIL +
                "() AS perfiles FROM dual";

        List<PycPerfil> result = new ArrayList<>();
        PycPerfilRowMapper mapper = new PycPerfilRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("perfiles");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Perfiles obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getPerfiles: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la información de los perfiles", e);
        }
    }

    @Override
    public List<PycUsuarioPerfil> getUsuariosPerfil(Integer idPerfil) {
        log.info("Ejecutando función getUsuariosPerfil con idPerfil={}", idPerfil);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_USUARIO_PERFIL +
                "(?) AS usuarios_perfil FROM dual";

        List<PycUsuarioPerfil> result = new ArrayList<>();
        PycUsuarioPerfilRowMapper mapper = new PycUsuarioPerfilRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idPerfil (puede ser null)
            if (idPerfil != null) {
                stmt.setInt(1, idPerfil);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("usuarios_perfil");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Usuarios-perfil obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getUsuariosPerfil: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la información de los usuarios y sus perfiles", e);
        }
    }

    @Override
    public List<PycListadoAsigna> getListadoAsigna(Integer idPerfil) {
        log.info("Ejecutando función getListadoAsigna con idPerfil={}", idPerfil);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_LISTADO_ASIGNA +
                "(?) AS listado_asigna FROM dual";

        List<PycListadoAsigna> result = new ArrayList<>();
        PycListadoAsignaRowMapper mapper = new PycListadoAsignaRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idPerfil
            stmt.setInt(1, idPerfil);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("listado_asigna");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Listado de asignaciones obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getListadoAsigna: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el listado de asignaciones por perfil", e);
        }
    }

    @Override
    public List<PycProcesoPorTipo> getProcesosPorTipo(Integer idTipo) {
        log.info("Ejecutando función getProcesosPorTipo con idTipo={}", idTipo);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_PROCESO_POR_TIPO +
                "(?) AS procesos_por_tipo FROM dual";

        List<PycProcesoPorTipo> result = new ArrayList<>();
        PycProcesoPorTipoRowMapper mapper = new PycProcesoPorTipoRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idTipo
            stmt.setInt(1, idTipo);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("procesos_por_tipo");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Procesos por tipo obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getProcesosPorTipo: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los procesos por tipo de petición", e);
        }
    }

    @Override
    public PycGetQueryDatVar getQueryDatVar(Integer idProceso, Integer idSeccion, Integer idDatoVar) {
        log.info("Ejecutando función getQueryDatVar con idProceso={}, idSeccion={}, idDatoVar={}",
                idProceso, idSeccion, idDatoVar);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_GET_QUERY_DAT_VAR +
                "(?, ?, ?) AS query FROM dual";

        PycGetQueryDatVar result = new PycGetQueryDatVar();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros
            stmt.setInt(1, idProceso);
            stmt.setInt(2, idSeccion);
            stmt.setInt(3, idDatoVar);

            if (stmt.execute()) {
                try (ResultSet rs = stmt.getResultSet()) {
                    if (rs.next()) {
                        String query = rs.getString("query");
                        result.setQuery(query);
                    }
                }
            }

            log.info("Función ejecutada correctamente. Query obtenida: {}", result.getQuery());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getQueryDatVar: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la consulta del dato variable", e);
        }
    }

    @Override
    public PycSrcMultipleOption getSrcMultipleOption(Integer idPerfil) {
        log.info("Ejecutando función getSrcMultipleOption con idPerfil={}", idPerfil);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_SRC_MULTIPLE_OPTION +
                "(?) AS has_multiple_option FROM dual";

        PycSrcMultipleOption result = new PycSrcMultipleOption();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idPerfil
            stmt.setInt(1, idPerfil);

            if (stmt.execute()) {
                try (ResultSet rs = stmt.getResultSet()) {
                    if (rs.next()) {
                        Integer hasMultipleOption = rs.getInt("has_multiple_option");
                        result.setHasMultipleOption(hasMultipleOption);
                    }
                }
            }

            log.info("Función ejecutada correctamente. Resultado: {}", result.getHasMultipleOption());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getSrcMultipleOption: {}", e.getMessage(), e);
            throw new RuntimeException("Error al verificar la opción de selección múltiple", e);
        }
    }

    @Override
    public List<PycGetUsuariosAsig> getUsuariosAsig(Integer idProceso, Integer idPerfil, Integer idAplicacion, Integer idEstado) {
        log.info("Ejecutando función getUsuariosAsig con idProceso={}, idPerfil={}, idAplicacion={}, idEstado={}",
                idProceso, idPerfil, idAplicacion, idEstado);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_GET_USUARIOS_ASIG +
                "(?, ?, ?, ?) AS usuarios_asig FROM dual";

        List<PycGetUsuariosAsig> result = new ArrayList<>();
        PycGetUsuariosAsigRowMapper mapper = new PycGetUsuariosAsigRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros
            stmt.setInt(1, idProceso);
            stmt.setInt(2, idPerfil);
            stmt.setInt(3, idAplicacion);
            stmt.setInt(4, idEstado);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("usuarios_asig");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Usuarios asignados obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getUsuariosAsig: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los usuarios asignados", e);
        }
    }

    @Override
    public List<PycUsuPerfilProceso> getUsuPerfilProceso(Integer idPerfil, Integer idProceso) {
        log.info("Ejecutando función getUsuPerfilProceso con idPerfil={}, idProceso={}", idPerfil, idProceso);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                "FN_USU_PERFIL_PROCESO" +
                "(?, ?) AS usu_perfil_proceso FROM dual";

        List<PycUsuPerfilProceso> result = new ArrayList<>();
        PycUsuPerfilProcesoRowMapper mapper = new PycUsuPerfilProcesoRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros
            stmt.setInt(1, idPerfil);
            stmt.setInt(2, idProceso);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("usu_perfil_proceso");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Usuarios por perfil y proceso obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getUsuPerfilProceso: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los usuarios por perfil y proceso", e);
        }
    }

    @Override
    public List<PycUsuProcesos> getUsuarioProcesos(String usuario) {
        log.info("Ejecutando función getUsuarioProcesos con usuario={}", usuario);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_AUTH_MGT + "." +
                DatabaseConstants.F_USUARIO_PROCESOS +
                "(?) AS usuario_procesos FROM dual";

        List<PycUsuProcesos> result = new ArrayList<>();
        PycUsuProcesosRowMapper mapper = new PycUsuProcesosRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros
            stmt.setString(1, usuario);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("usuario_procesos");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Procesos del usuario obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getUsuarioProcesos: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los procesos del usuario", e);
        }
    }

    @Override
    public Integer updatePerfil(PycUpdPerfil perfilData) {
        log.info("Ejecutando función updatePerfil con idPerfil={}, idUsuario={}", perfilData.getIdPerfil(), perfilData.getIdUsuario());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_MOD_PERFIL +
                "(?, ?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de retorno
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada
            stmt.setInt(2, perfilData.getIdPerfil());
            stmt.setString(3, perfilData.getIndDefault());
            stmt.setString(4, perfilData.getMultiperfil());
            stmt.setInt(5, perfilData.getIdUsuario());
            stmt.setString(6, perfilData.getEstado());

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer idUsuario = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID Usuario actualizado: {}", idUsuario);
            return idUsuario;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función updatePerfil: {}", e.getMessage(), e);
            throw new RuntimeException("Error al actualizar el perfil", e);
        }
    }

    @Override
    public Integer updateUsuario(PycUpdUsuario usuarioData) {
        log.info("Ejecutando función updateUsuario con primerNombre={}, numaOUsuario={}",
                usuarioData.getPrimerNombre(), usuarioData.getNumaOUsuario());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_MOD_USUARIO +
                "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de retorno
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada
            stmt.setString(2, usuarioData.getPrimerNombre());
            stmt.setString(3, usuarioData.getSegundoNombre());
            stmt.setString(4, usuarioData.getPrimerApellido());
            stmt.setString(5, usuarioData.getSegundoApellido());
            stmt.setString(6, usuarioData.getNumaOUsuario());
            stmt.setString(7, usuarioData.getEstado());
            stmt.setString(8, usuarioData.getEmail());
            stmt.setString(9, usuarioData.getGenero());
            stmt.setObject(10, usuarioData.getIdArea(), Types.INTEGER);
            stmt.setObject(11, usuarioData.getIdDepartamento(), Types.INTEGER);

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer idUsuario = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID Usuario actualizado: {}", idUsuario);
            return idUsuario;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función updateUsuario: {}", e.getMessage(), e);
            throw new RuntimeException("Error al actualizar el usuario", e);
        }
    }

    @Override
    public Integer creaSolicitud(PycCreaSolicitud solicitudData) {
        log.info("Ejecutando función creaSolicitud con plataforma={}, tipoSeguro={}, cliente={} {}",
                solicitudData.getPlataforma(), solicitudData.getTipoSeguro(),
                solicitudData.getNombres(), solicitudData.getApellidos());

        // Modificar la consulta SQL para usar la función XMLTYPE de Oracle directamente
        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_COMERCIAL_MGT + "." +
                DatabaseConstants.P_CREA_SOLICITUD +
                "(?, ?, ?, ?, ?, ?, XMLTYPE(?), ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de salida
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada
            stmt.setString(2, solicitudData.getPlataforma());
            stmt.setString(3, solicitudData.getTipoSeguro());
            stmt.setString(4, solicitudData.getNombres());
            stmt.setString(5, solicitudData.getApellidos());
            stmt.setString(6, solicitudData.getTelefono());
            stmt.setString(7, solicitudData.getEmail());

            // Establecer el XML como string o null, limpiando espacios entre etiquetas
            if (solicitudData.getXmlText() != null && !solicitudData.getXmlText().isEmpty()) {
                String cleanedXml = cleanXmlText(solicitudData.getXmlText());
                stmt.setString(8, cleanedXml);
                log.debug("XML limpiado para creaSolicitud: {}", cleanedXml);
            } else {
                stmt.setString(8, "<INFO_PETICION></INFO_PETICION>");
            }
            // El último parámetro es opcional
            if (solicitudData.getCodinter() != null && !solicitudData.getCodinter().isEmpty()) {
                stmt.setString(9, solicitudData.getCodinter());
            } else {
                stmt.setNull(9, Types.VARCHAR);
            }

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer idSolicitud = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID Solicitud creada: {}", idSolicitud);
            return idSolicitud;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función creaSolicitud: {}", e.getMessage(), e);
            throw new RuntimeException("Error al crear la solicitud", e);
        }
    }

    @Override
    public Integer insertPeticion(PycInsertPeticion peticionData) {
        log.info("Ejecutando función insertPeticion con nombre={}, descripcion={}, idUsuarioSolicitante={}",
                peticionData.getNombrePeticion(), peticionData.getDescripcionPeticion(),
                peticionData.getIdUsuarioSolicitante());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_INSERT_PETICION +
                "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, XMLTYPE(?), ?,?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de salida
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada obligatorios
            stmt.setString(2, peticionData.getNombrePeticion());
            stmt.setString(3, peticionData.getDescripcionPeticion());
            stmt.setInt(4, peticionData.getIdUsuarioSolicitante());
            stmt.setInt(5, peticionData.getIdPerfil());
            stmt.setInt(6, peticionData.getIdTipo());
            stmt.setInt(7, peticionData.getIdProceso());
            stmt.setString(8, peticionData.getCodcia());

            // Establecer los parámetros opcionales
            if (peticionData.getNombreCliente() != null && !peticionData.getNombreCliente().isEmpty()) {
                stmt.setString(9, peticionData.getNombreCliente());
            } else {
                stmt.setNull(9, Types.VARCHAR);
            }

            if (peticionData.getTelefonoCliente() != null && !peticionData.getTelefonoCliente().isEmpty()) {
                stmt.setString(10, peticionData.getTelefonoCliente());
            } else {
                stmt.setNull(10, Types.VARCHAR);
            }

            if (peticionData.getCorreoCliente() != null && !peticionData.getCorreoCliente().isEmpty()) {
                stmt.setString(11, peticionData.getCorreoCliente());
            } else {
                stmt.setNull(11, Types.VARCHAR);
            }

            if (peticionData.getOrigen() != null && !peticionData.getOrigen().isEmpty()) {
                stmt.setString(12, peticionData.getOrigen());
            } else {
                stmt.setNull(12, Types.VARCHAR);
            }

            if (peticionData.getCodClarity() != null && !peticionData.getCodClarity().isEmpty()) {
                stmt.setString(13, peticionData.getCodClarity());
            } else {
                stmt.setNull(13, Types.VARCHAR);
            }

            if (peticionData.getPrioridad() != null && !peticionData.getPrioridad().isEmpty()) {
                stmt.setString(14, peticionData.getPrioridad());
            } else {
                stmt.setNull(14, Types.VARCHAR);
            }

            if (peticionData.getTipoCliente() != null && !peticionData.getTipoCliente().isEmpty()) {
                stmt.setString(15, peticionData.getTipoCliente());
            } else {
                stmt.setNull(15, Types.VARCHAR);
            }

            if (peticionData.getCodCliente() != null && !peticionData.getCodCliente().isEmpty()) {
                stmt.setString(16, peticionData.getCodCliente());
            } else {
                stmt.setNull(16, Types.VARCHAR);
            }

            if (peticionData.getNoPoliza() != null && !peticionData.getNoPoliza().isEmpty()) {
                stmt.setString(17, peticionData.getNoPoliza());
            } else {
                stmt.setNull(17, Types.VARCHAR);
            }

            if (peticionData.getTipServicio() != null && !peticionData.getTipServicio().isEmpty()) {
                stmt.setString(18, peticionData.getTipServicio());
            } else {
                stmt.setNull(18, Types.VARCHAR);
            }

            if (peticionData.getCausa() != null && !peticionData.getCausa().isEmpty()) {
                stmt.setString(19, peticionData.getCausa());
            } else {
                stmt.setNull(19, Types.VARCHAR);
            }

            if (peticionData.getGravedad() != null && !peticionData.getGravedad().isEmpty()) {
                stmt.setString(20, peticionData.getGravedad());
            } else {
                stmt.setNull(20, Types.VARCHAR);
            }

            if (peticionData.getIdReferencia() != null && !peticionData.getIdReferencia().isEmpty()) {
                stmt.setString(21, peticionData.getIdReferencia());
            } else {
                stmt.setNull(21, Types.VARCHAR);
            }

            if (peticionData.getCodinter() != null && !peticionData.getCodinter().isEmpty()) {
                stmt.setString(22, peticionData.getCodinter());
            } else {
                stmt.setNull(22, Types.VARCHAR);
            }

            // Establecer el XML como string o null, limpiando espacios entre etiquetas
            if (peticionData.getXmlText() != null && !peticionData.getXmlText().isEmpty()) {
                String cleanedXml = cleanXmlText(peticionData.getXmlText());
                stmt.setString(23, cleanedXml);
                log.debug("XML limpiado para insertPeticion: {}", cleanedXml);
            } else {
               stmt.setString(23, "<INFO_PETICION></INFO_PETICION>");
            }

            // P_USUARIO_REG como parámetro
            if (peticionData.getUsuarioReg() != null && !peticionData.getUsuarioReg().isEmpty()) {
                stmt.setString(24, peticionData.getUsuarioReg());
            }else {
                stmt.setNull(24, Types.VARCHAR);
            }

            // El parámetro asigAuto es boolean para Oracle (parámetro 25)  siivas a probar agrega el ? arriba
//            if (peticionData.isAsigAuto() != null) {
//                stmt.setBoolean(25, peticionData.isAsigAuto());
//            } else {
//                stmt.setBoolean(25, true); // Valor por defecto
//            }

            if (peticionData.isAsigAuto() != null) {
                stmt.setInt(25, peticionData.isAsigAuto() ? 1 : 0); // 1 para TRUE, 0 para FALSE
            } else {
                stmt.setInt(25, 1); // Valor por defecto si es null
            }

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer idPeticion = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID Petición creada: {}", idPeticion);
            return idPeticion;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función insertPeticion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al insertar la petición", e);
        }
    }

    @Override
    public List<PycPeticionPerfil> getPeticionPerfil(Integer idPerfil, Integer idProceso, String idEstado,
                                                     Integer idPeticion, Integer idCanal, Integer idUsuario,
                                                     String indSubs, Integer idOficina, String numaOUsuario) {
        log.info("Ejecutando función getPeticionPerfil con idPerfil={}, idProceso={}, idEstado={}, idPeticion={}, idCanal={}, idUsuario={}, indSubs={}, idOficina={}, numaOUsuario={}",
                idPerfil, idProceso, idEstado, idPeticion, idCanal, idUsuario, indSubs, idOficina, numaOUsuario);

        String sqlCall = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_PETICION_PERFIL +
                "(?, ?, ?, ?, ?, ?, ?, ?, ?) }";

        List<PycPeticionPerfil> result = new ArrayList<>();
        PycPeticionPerfilRowMapper mapper = new PycPeticionPerfilRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlCall)) {

            // Registrar el parámetro de salida (sys_refcursor)
            stmt.registerOutParameter(1, OracleTypes.CURSOR);

            // Establecer los parámetros de entrada requeridos
            stmt.setInt(2, idPerfil);
            stmt.setInt(3, idProceso);

            // Establecer el parámetro idEstado (ahora opcional)
            if (idEstado != null && !idEstado.trim().isEmpty()) {
                stmt.setString(4, idEstado);
            } else {
                stmt.setNull(4, Types.VARCHAR);
            }

            // Establecer los parámetros opcionales
            if (idPeticion != null) {
                stmt.setInt(5, idPeticion);
            } else {
                stmt.setNull(5, Types.INTEGER);
            }

            if (idCanal != null) {
                stmt.setInt(6, idCanal);
            } else {
                stmt.setNull(6, Types.INTEGER);
            }

            if (idUsuario != null) {
                stmt.setInt(7, idUsuario);
            } else {
                stmt.setNull(7, Types.INTEGER);
            }

            if (indSubs != null && !indSubs.isEmpty()) {
                stmt.setString(8, indSubs);
            } else {
                stmt.setNull(8, Types.VARCHAR);
            }

            if (idOficina != null) {
                stmt.setInt(9, idOficina);
            } else {
                stmt.setNull(9, Types.INTEGER);
            }

            // Establecer el parámetro numaOUsuario (requerido)
            stmt.setString(10, numaOUsuario);

            // Ejecutar la función
            stmt.execute();

            // Obtener el cursor de resultado
            try (ResultSet rs = (ResultSet) stmt.getObject(1)) {
                int rowNum = 0;
                while (rs.next()) {
                    result.add(mapper.mapRow(rs, rowNum++));
                }
            }

            log.info("Función ejecutada correctamente. Peticiones obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getPeticionPerfil: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las peticiones por perfil", e);
        }
    }

    @Override
    public List<PycPetSinProgramador> getPeticionSinProgramador() {
        log.info("Ejecutando función getPeticionSinProgramador");

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_PETICION_SIN_PROGRAMADOR +
                "() AS peticiones_sin_programador FROM dual";

        List<PycPetSinProgramador> result = new ArrayList<>();
        PycPetSinProgramadorRowMapper mapper = new PycPetSinProgramadorRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("peticiones_sin_programador");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Peticiones sin programador obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getPeticionSinProgramador: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las peticiones sin programador", e);
        }
    }

    @Override
    public List<PycAreaPeti> getConArea() {
        log.info("Ejecutando función getConArea");

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_CON_AREA +
                "() AS areas_con_peticiones FROM dual";

        List<PycAreaPeti> result = new ArrayList<>();
        PycAreaPetiRowMapper mapper = new PycAreaPetiRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("areas_con_peticiones");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Áreas con peticiones obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getConArea: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las áreas con peticiones", e);
        }
    }

    @Override
    public List<PycDepartamento> getConDepartamento(Integer idArea) {
        log.info("Ejecutando función getConDepartamento con idArea={}", idArea);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_CON_DEPARTAMENTO +
                "(?) AS departamentos FROM dual";

        List<PycDepartamento> result = new ArrayList<>();
        PycDepartamentoRowMapper mapper = new PycDepartamentoRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idArea (puede ser null)
            if (idArea != null) {
                stmt.setInt(1, idArea);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("departamentos");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Departamentos obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getConDepartamento: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los departamentos", e);
        }
    }

    @Override
    public List<PycAreaUsuario> getConSolicitante(Integer idArea, Integer idDepartamento) {
        log.info("Ejecutando función getConSolicitante con idArea={}, idDepartamento={}", idArea, idDepartamento);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_CON_SOLICITANTE +
                "(?, ?) AS usuarios_solicitantes FROM dual";

        List<PycAreaUsuario> result = new ArrayList<>();
        PycAreaUsuarioRowMapper mapper = new PycAreaUsuarioRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idArea (puede ser null)
            if (idArea != null) {
                stmt.setInt(1, idArea);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            // Establecer el parámetro idDepartamento (puede ser null)
            if (idDepartamento != null) {
                stmt.setInt(2, idDepartamento);
            } else {
                stmt.setNull(2, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("usuarios_solicitantes");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Usuarios solicitantes obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getConSolicitante: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los usuarios solicitantes", e);
        }
    }

    @Override
    public List<PycAnalista> getConAnalista(Integer idSolicitante, String prioridad) {
        log.info("Ejecutando función getConAnalista con idSolicitante={}, prioridad={}", idSolicitante, prioridad);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_CON_ANALISTA +
                "(?, ?) AS analistas FROM dual";

        List<PycAnalista> result = new ArrayList<>();
        PycAnalistaRowMapper mapper = new PycAnalistaRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idSolicitante (requerido)
            stmt.setInt(1, idSolicitante);

            // Establecer el parámetro prioridad (puede ser null)
            if (prioridad != null && !prioridad.trim().isEmpty()) {
                stmt.setString(2, prioridad);
            } else {
                stmt.setNull(2, java.sql.Types.VARCHAR);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("analistas");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Analistas obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getConAnalista: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los analistas", e);
        }
    }

    @Override
    public List<PycConEstado> getConEstado(Integer idSolicitante, String prioridad, Integer idAnalista) {
        log.info("Ejecutando función getConEstado con idSolicitante={}, prioridad={}, idAnalista={}",
                idSolicitante, prioridad, idAnalista);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_CON_ESTADO +
                "(?, ?, ?) AS estados FROM dual";

        List<PycConEstado> result = new ArrayList<>();
        PycConEstadoRowMapper mapper = new PycConEstadoRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idSolicitante (requerido)
            stmt.setInt(1, idSolicitante);

            // Establecer el parámetro prioridad (requerido, puede ser 'null' como string)
            if (prioridad != null && !prioridad.trim().isEmpty()) {
                stmt.setString(2, prioridad);
            } else {
                stmt.setNull(2, java.sql.Types.VARCHAR);
            }

            // Establecer el parámetro idAnalista (opcional)
            if (idAnalista != null) {
                stmt.setInt(3, idAnalista);
            } else {
                stmt.setNull(3, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("estados");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Estados obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getConEstado: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los estados", e);
        }
    }

    @Override
    public List<PycPrioridad> getConPrioridad(Integer idSolicitante, Integer idArea, Integer idDepartamento) {
        log.info("Ejecutando función getConPrioridad con idSolicitante={}, idArea={}, idDepartamento={}",
                idSolicitante, idArea, idDepartamento);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_CON_PRIORIDAD +
                "(?, ?, ?) AS prioridades FROM dual";

        List<PycPrioridad> result = new ArrayList<>();
        PycPrioridadRowMapper mapper = new PycPrioridadRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idSolicitante (puede ser null)
            if (idSolicitante != null) {
                stmt.setInt(1, idSolicitante);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            // Establecer el parámetro idArea (puede ser null)
            if (idArea != null) {
                stmt.setInt(2, idArea);
            } else {
                stmt.setNull(2, java.sql.Types.INTEGER);
            }

            // Establecer el parámetro idDepartamento (puede ser null)
            if (idDepartamento != null) {
                stmt.setInt(3, idDepartamento);
            } else {
                stmt.setNull(3, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("prioridades");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Prioridades obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getConPrioridad: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las prioridades", e);
        }
    }

    @Override
    public List<PycTabAnios> getTabAnios(Integer idProceso) {
        log.info("Ejecutando función getTabAnios con idProceso={}", idProceso);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_TAB_ANIOS +
                "(?) AS anios_tabla FROM dual";

        List<PycTabAnios> result = new ArrayList<>();
        PycTabAniosRowMapper mapper = new PycTabAniosRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idProceso (requerido)
            stmt.setInt(1, idProceso);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("anios_tabla");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Años obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getTabAnios: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los años", e);
        }
    }

    @Override
    public List<PycTabArea> getTabArea(Integer idProceso, String anio) {
        log.info("Ejecutando función getTabArea con idProceso={}, anio={}", idProceso, anio);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_TAB_AREA +
                "(?, ?) AS areas_tabla FROM dual";

        List<PycTabArea> result = new ArrayList<>();
        PycTabAreaRowMapper mapper = new PycTabAreaRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idProceso (requerido)
            stmt.setInt(1, idProceso);

            // Establecer el parámetro anio (opcional)
            if (anio != null && !anio.trim().isEmpty()) {
                stmt.setString(2, anio);
            } else {
                stmt.setNull(2, java.sql.Types.VARCHAR);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("areas_tabla");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Áreas obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getTabArea: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las áreas", e);
        }
    }

    @Override
    public List<PycTabEstado> getTabEstado(Integer idProceso, String anio, Integer idArea) {
        log.info("Ejecutando función getTabEstado con idProceso={}, anio={}, idArea={}", idProceso, anio, idArea);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_TAB_ESTADO +
                "(?, ?, ?) AS estados_tabla FROM dual";

        List<PycTabEstado> result = new ArrayList<>();
        PycTabEstadoRowMapper mapper = new PycTabEstadoRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idProceso (requerido)
            stmt.setInt(1, idProceso);

            // Establecer el parámetro anio (opcional)
            if (anio != null && !anio.trim().isEmpty()) {
                stmt.setString(2, anio);
            } else {
                stmt.setNull(2, java.sql.Types.VARCHAR);
            }

            // Establecer el parámetro idArea (opcional)
            if (idArea != null) {
                stmt.setInt(3, idArea);
            } else {
                stmt.setNull(3, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("estados_tabla");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Estados obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getTabEstado: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los estados", e);
        }
    }

    @Override
    public List<PycUsuPerfilPeti> getUsuariosPerfilPeticion(Integer idPeticion, Integer idPerfil, Integer idAplicacion) {
        log.info("Ejecutando función getUsuariosPerfilPeticion con idPeticion={}, idPerfil={}, idAplicacion={}",
                idPeticion, idPerfil, idAplicacion);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_USUARIOS_PERFIL_PETICION +
                "(?, ?, ?) AS usuarios_perfil_peticion FROM dual";

        List<PycUsuPerfilPeti> result = new ArrayList<>();
        PycUsuPerfilPetiRowMapper mapper = new PycUsuPerfilPetiRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros (todos requeridos)
            stmt.setInt(1, idPeticion);
            stmt.setInt(2, idPerfil);
            stmt.setInt(3, idAplicacion);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("usuarios_perfil_peticion");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Usuarios por perfil y petición obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getUsuariosPerfilPeticion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los usuarios por perfil y petición", e);
        }
    }

    @Override
    public List<PycPetiFiltro> getPeticionFiltros(Integer idArea, Integer idDepartamento, Integer idPerfil,
                                                  Integer idEstado, Integer idUsuarioSolicitante, Integer idTipo,
                                                  String fechaInicio, String fechaFin, Integer idProceso) {
        log.info("Ejecutando función getPeticionFiltros con idArea={}, idDepartamento={}, idPerfil={}, idEstado={}, idUsuarioSolicitante={}, idTipo={}, fechaInicio={}, fechaFin={}, idProceso={}",
                idArea, idDepartamento, idPerfil, idEstado, idUsuarioSolicitante, idTipo, fechaInicio, fechaFin, idProceso);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_PETICION_FILTROS +
                "(?, ?, ?, ?, ?, ?, ?, ?, ?) AS peticion_filtros FROM dual";

        List<PycPetiFiltro> result = new ArrayList<>();
        PycPetiFiltroRowMapper mapper = new PycPetiFiltroRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros (todos opcionales)
            if (idArea != null) {
                stmt.setInt(1, idArea);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            if (idDepartamento != null) {
                stmt.setInt(2, idDepartamento);
            } else {
                stmt.setNull(2, java.sql.Types.INTEGER);
            }

            if (idPerfil != null) {
                stmt.setInt(3, idPerfil);
            } else {
                stmt.setNull(3, java.sql.Types.INTEGER);
            }

            if (idEstado != null) {
                stmt.setInt(4, idEstado);
            } else {
                stmt.setNull(4, java.sql.Types.INTEGER);
            }

            if (idUsuarioSolicitante != null) {
                stmt.setInt(5, idUsuarioSolicitante);
            } else {
                stmt.setNull(5, java.sql.Types.INTEGER);
            }

            if (idTipo != null) {
                stmt.setInt(6, idTipo);
            } else {
                stmt.setNull(6, java.sql.Types.INTEGER);
            }

            // Manejo de fechas
            if (fechaInicio != null && !fechaInicio.trim().isEmpty()) {
                try {
                    java.sql.Date sqlFechaInicio = java.sql.Date.valueOf(fechaInicio);
                    stmt.setDate(7, sqlFechaInicio);
                } catch (IllegalArgumentException e) {
                    log.warn("Formato de fecha inicio inválido: {}, se establece como null", fechaInicio);
                    stmt.setNull(7, java.sql.Types.DATE);
                }
            } else {
                stmt.setNull(7, java.sql.Types.DATE);
            }

            if (fechaFin != null && !fechaFin.trim().isEmpty()) {
                try {
                    java.sql.Date sqlFechaFin = java.sql.Date.valueOf(fechaFin);
                    stmt.setDate(8, sqlFechaFin);
                } catch (IllegalArgumentException e) {
                    log.warn("Formato de fecha fin inválido: {}, se establece como null", fechaFin);
                    stmt.setNull(8, java.sql.Types.DATE);
                }
            } else {
                stmt.setNull(8, java.sql.Types.DATE);
            }

            if (idProceso != null) {
                stmt.setInt(9, idProceso);
            } else {
                stmt.setNull(9, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("peticion_filtros");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Peticiones filtradas obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getPeticionFiltros: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las peticiones filtradas", e);
        }
    }

    @Override
    public List<PycReportePeti> getReportePeticion(Integer idArea, Integer idDepartamento, Integer idSolicitante,
                                                   String prioridad, Integer idAnalista, String estado) {
        log.info("Ejecutando función getReportePeticion con idArea={}, idDepartamento={}, idSolicitante={}, prioridad={}, idAnalista={}, estado={}",
                idArea, idDepartamento, idSolicitante, prioridad, idAnalista, estado);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_REPORTE_PETICION +
                "(?, ?, ?, ?, ?, ?) AS reporte_peticion FROM dual";

        List<PycReportePeti> result = new ArrayList<>();
        PycReportepetiRowMapper mapper = new PycReportepetiRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros (todos opcionales)
            if (idArea != null) {
                stmt.setInt(1, idArea);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            if (idDepartamento != null) {
                stmt.setInt(2, idDepartamento);
            } else {
                stmt.setNull(2, java.sql.Types.INTEGER);
            }

            if (idSolicitante != null) {
                stmt.setInt(3, idSolicitante);
            } else {
                stmt.setNull(3, java.sql.Types.INTEGER);
            }

            if (prioridad != null) {
                stmt.setString(4, prioridad);
            } else {
                stmt.setNull(4, java.sql.Types.VARCHAR);
            }

            if (idAnalista != null) {
                stmt.setInt(5, idAnalista);
            } else {
                stmt.setNull(5, java.sql.Types.INTEGER);
            }

            if (estado != null) {
                stmt.setString(6, estado);
            } else {
                stmt.setNull(6, java.sql.Types.VARCHAR);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("reporte_peticion");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Reporte de peticiones obtenido: {} registros", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getReportePeticion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el reporte de peticiones", e);
        }
    }

    @Override
    public Integer insertUserProceso(PycInsertUserPro userData) {
        log.info("Ejecutando función insertUserProceso con idUsuario={}, idProceso={}, estado={}",
                userData.getIdUsuario(), userData.getIdProceso(), userData.getEstado());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_INSERT_USERPRO +
                "(?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de retorno
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada
            stmt.setInt(2, userData.getIdUsuario());
            stmt.setInt(3, userData.getIdProceso());
            stmt.setString(4, userData.getEstado());

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer idUsuario = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID Usuario procesado: {}", idUsuario);
            return idUsuario;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función insertUserProceso: {}", e.getMessage(), e);
            throw new RuntimeException("Error al insertar/actualizar usuario proceso", e);
        }
    }

    @Override
    public Integer insertUsuario(PycInsertUsuario usuarioData) {
        log.info("Ejecutando función insertUsuario con primerNombre={}, primerApellido={}, numaOUsuario={}, email={}",
                usuarioData.getPrimerNombre(), usuarioData.getPrimerApellido(),
                usuarioData.getNumaOUsuario(), usuarioData.getEmail());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_INSERT_USUARIO +
                "(?, ?, ?, ?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de retorno
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada
            stmt.setString(2, usuarioData.getPrimerNombre());

            if (usuarioData.getSegundoNombre() != null) {
                stmt.setString(3, usuarioData.getSegundoNombre());
            } else {
                stmt.setNull(3, Types.VARCHAR);
            }

            stmt.setString(4, usuarioData.getPrimerApellido());

            if (usuarioData.getSegundoApellido() != null) {
                stmt.setString(5, usuarioData.getSegundoApellido());
            } else {
                stmt.setNull(5, Types.VARCHAR);
            }

            stmt.setString(6, usuarioData.getNumaOUsuario());
            stmt.setString(7, usuarioData.getEmail());
            stmt.setString(8, usuarioData.getGenero());

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer idUsuario = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID Usuario creado: {}", idUsuario);
            return idUsuario;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función insertUsuario: {}", e.getMessage(), e);
            throw new RuntimeException("Error al insertar usuario", e);
        }
    }

    @Override
    public Integer actDocumento(PycActDocumento documentoData) {
        log.info("Ejecutando función actDocumento con idPeticion={}, idDocumento={}, localizacion={}, numaOUsuario={}",
                documentoData.getIdPeticion(), documentoData.getIdDocumento(), documentoData.getLocalizacion(), documentoData.getNumaOUsuario());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_ACT_DOCUMENTO +
                "(?, ?, ?, ?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de retorno
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada requeridos
            stmt.setInt(2, documentoData.getIdPeticion());
            stmt.setInt(3, documentoData.getIdDocumento());
            stmt.setString(4, documentoData.getLocalizacion());

            // Establecer los parámetros opcionales
            if (documentoData.getDocumento() != null && !documentoData.getDocumento().trim().isEmpty()) {
                stmt.setString(5, documentoData.getDocumento());
            } else {
                stmt.setNull(5, Types.VARCHAR);
            }

            if (documentoData.getFtpWeb() != null && !documentoData.getFtpWeb().trim().isEmpty()) {
                stmt.setString(6, documentoData.getFtpWeb());
            } else {
                stmt.setString(6, "N"); // Valor por defecto
            }

            if (documentoData.getNomArch() != null && !documentoData.getNomArch().trim().isEmpty()) {
                stmt.setString(7, documentoData.getNomArch());
            } else {
                stmt.setNull(7, Types.VARCHAR);
            }

            // Parámetro opcional numaOUsuario
            if (documentoData.getNumaOUsuario() != null && !documentoData.getNumaOUsuario().trim().isEmpty()) {
                stmt.setString(8, documentoData.getNumaOUsuario());
            } else {
                stmt.setNull(8, Types.VARCHAR);
            }

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer idPeticion = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID Petición retornada: {}", idPeticion);
            return idPeticion;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función actDocumento: {}", e.getMessage(), e);
            throw new RuntimeException("Error al actualizar documento", e);
        }
    }

    @Override
    public List<PycObserPet> getObserPeticion(Integer idPeticion, String numaOUsuario) {
        log.info("Ejecutando función getObserPeticion con idPeticion={}, numaOUsuario={}", idPeticion, numaOUsuario);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_OBSER_PETICION +
                "(?, ?) AS observaciones FROM dual";

        List<PycObserPet> result = new ArrayList<>();
        PycObserPetRowMapper mapper = new PycObserPetRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idPeticion
            stmt.setInt(1, idPeticion);

            // Establecer el parámetro numaOUsuario (opcional)
            if (numaOUsuario != null && !numaOUsuario.trim().isEmpty()) {
                stmt.setString(2, numaOUsuario);
            } else {
                stmt.setNull(2, java.sql.Types.VARCHAR);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("observaciones");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Observaciones obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getObserPeticion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las observaciones de la petición", e);
        }
    }

    @Override
    public List<PycResumenActi> getResumenActividad(Integer idPeticion, Integer idCategoria) {
        log.info("Ejecutando función getResumenActividad con idPeticion={}, idCategoria={}", idPeticion, idCategoria);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_RESUMEN_ACTIVIDAD +
                "(?, ?) AS resumen FROM dual";

        List<PycResumenActi> result = new ArrayList<>();
        PycResumenActiRowMapper mapper = new PycResumenActiRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros
            stmt.setInt(1, idPeticion);
            stmt.setInt(2, idCategoria);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("resumen");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Registros de resumen obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getResumenActividad: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el resumen de actividades", e);
        }
    }

    @Override
    public List<PycPetAvance> getPeticionAvance(Integer idProceso, Integer idArea, String estado, String codcia, Integer anio) {
        log.info("Ejecutando función getPeticionAvance con idProceso={}, idArea={}, estado={}, codcia={}, anio={}",
                idProceso, idArea, estado, codcia, anio);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_PETICION_AVANCE +
                "(?, ?, ?, ?, ?) AS peticion_avance FROM dual";

        List<PycPetAvance> result = new ArrayList<>();
        PycPetAvanceRowMapper mapper = new PycPetAvanceRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros (todos opcionales)
            if (idProceso != null) {
                stmt.setInt(1, idProceso);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            if (idArea != null) {
                stmt.setInt(2, idArea);
            } else {
                stmt.setNull(2, java.sql.Types.INTEGER);
            }

            if (estado != null && !estado.trim().isEmpty()) {
                stmt.setString(3, estado);
            } else {
                stmt.setNull(3, java.sql.Types.VARCHAR);
            }

            if (codcia != null && !codcia.trim().isEmpty()) {
                stmt.setString(4, codcia);
            } else {
                stmt.setNull(4, java.sql.Types.VARCHAR);
            }

            if (anio != null) {
                stmt.setInt(5, anio);
            } else {
                stmt.setNull(5, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("peticion_avance");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Avances de peticiones obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getPeticionAvance: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el avance de peticiones", e);
        }
    }

    @Override
    public List<PycPetAvanceSig> getPeticionAvanceSiguiente(Integer idProceso, Integer idArea, String estado, String codcia, Integer anio) {
        log.info("Ejecutando función getPeticionAvanceSiguiente con idProceso={}, idArea={}, estado={}, codcia={}, anio={}",
                idProceso, idArea, estado, codcia, anio);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_PETICION_AVANCE_SIGUIENTE +
                "(?, ?, ?, ?, ?) AS peticion_avance_siguiente FROM dual";

        List<PycPetAvanceSig> result = new ArrayList<>();
        PycPetAvanceSigRowMapper mapper = new PycPetAvanceSigRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros
            if (idProceso != null) {
                stmt.setInt(1, idProceso);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            if (idArea != null) {
                stmt.setInt(2, idArea);
            } else {
                stmt.setNull(2, java.sql.Types.INTEGER);
            }

            if (estado != null && !estado.trim().isEmpty()) {
                stmt.setString(3, estado);
            } else {
                stmt.setNull(3, java.sql.Types.VARCHAR);
            }

            if (codcia != null && !codcia.trim().isEmpty()) {
                stmt.setString(4, codcia);
            } else {
                stmt.setNull(4, java.sql.Types.VARCHAR);
            }

            if (anio != null) {
                stmt.setInt(5, anio);
            } else {
                stmt.setNull(5, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("peticion_avance_siguiente");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Peticiones con avance siguiente obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getPeticionAvanceSiguiente: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las peticiones con avance siguiente", e);
        }
    }

    @Override
    public List<PycListVal> getListaVal(String tipo, Integer idProceso) {
        log.info("Ejecutando función getListaVal con tipo={}, idProceso={}", tipo, idProceso);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_LISTA_VAL +
                "(?, ?) AS lista_valores FROM dual";

        List<PycListVal> result = new ArrayList<>();
        PycListValRowMapper mapper = new PycListValRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros (ambos requeridos)
            stmt.setString(1, tipo);
            stmt.setInt(2, idProceso);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("lista_valores");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Valores obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getListaVal: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la lista de valores", e);
        }
    }

    @Override
    public List<PycOficina> getOficinas(Integer idProceso, Integer idUsuario) {
        log.info("Ejecutando función getOficinas con idProceso={}, idUsuario={}", idProceso, idUsuario);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_GET_OFICINAS +
                "(?, ?) AS oficinas_disponibles FROM dual";

        List<PycOficina> result = new ArrayList<>();
        PycOficinaRowMapper mapper = new PycOficinaRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros (ambos requeridos)
            stmt.setInt(1, idProceso);
            stmt.setInt(2, idUsuario);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("oficinas_disponibles");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Oficinas obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getOficinas: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las oficinas", e);
        }
    }

    @Override
    public List<PycCanal> getCanales(Integer idProceso, Integer idUsuario, Integer idOficina) {
        log.info("Ejecutando función getCanales con idProceso={}, idUsuario={}, idOficina={}",
                idProceso, idUsuario, idOficina);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_GET_CANALES +
                "(?, ?, ?) AS canales_disponibles FROM dual";

        List<PycCanal> result = new ArrayList<>();
        PycCanalRowMapper mapper = new PycCanalRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros (todos requeridos)
            stmt.setInt(1, idProceso);
            stmt.setInt(2, idUsuario);
            stmt.setInt(3, idOficina);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("canales_disponibles");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Canales obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getCanales: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los canales", e);
        }
    }

    @Override
    public List<PycSubordinados> getSubordinados(String canal, String oficina, String idSupervisor, String indLogin, String proceso) {
        log.info("Ejecutando función getSubordinados con canal={}, oficina={}, idSupervisor={}, indLogin={}, proceso={}",
                canal, oficina, idSupervisor, indLogin, proceso);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.F_GET_SUBORDINADOS +
                "(?, ?, ?, ?, ?) AS subordinados_disponibles FROM dual";

        List<PycSubordinados> result = new ArrayList<>();
        PycSubordinadosRowMapper mapper = new PycSubordinadosRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros (todos requeridos como String)
            stmt.setString(1, canal);
            stmt.setString(2, oficina);
            stmt.setString(3, idSupervisor);
            stmt.setString(4, indLogin);
            stmt.setString(5, proceso);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("subordinados_disponibles");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Subordinados obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getSubordinados: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los subordinados", e);
        }
    }

    @Override
    public List<PycInfoUsuario> getInfoUsuario(String usuario) {
        log.info("Ejecutando función getInfoUsuario con usuario={}", usuario);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_AUTH_MGT + "." +
                DatabaseConstants.FN_INFO_USUARIO +
                "(?) AS info_usuario FROM dual";

        List<PycInfoUsuario> result = new ArrayList<>();
        PycInfoUsuarioRowMapper mapper = new PycInfoUsuarioRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro usuario (requerido como String)
            stmt.setString(1, usuario);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("info_usuario");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Registros de información de usuario obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getInfoUsuario: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la información del usuario", e);
        }
    }

    @Override
    public List<PycRamo> obtenerRamo(String codRamo, Integer codModalidad, String codCia) {
        log.info("Ejecutando función obtenerRamo con codRamo={}, codModalidad={}, codCia={}",
                codRamo, codModalidad, codCia);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_OBTENER_RAMO +
                "(?, ?, ?) AS ramo_info FROM dual";

        List<PycRamo> result = new ArrayList<>();
        PycRamoRowMapper mapper = new PycRamoRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro codRamo (obligatorio)
            stmt.setString(1, codRamo);

            // Establecer el parámetro codModalidad (opcional, default 999, ahora es Integer)
            if (codModalidad != null) {
                stmt.setInt(2, codModalidad);
            } else {
                stmt.setInt(2, 999); // Valor por defecto
            }

            // Establecer el parámetro codCia (obligatorio)
            stmt.setString(3, codCia);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("ramo_info");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Registros de ramo obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función obtenerRamo: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la información del ramo", e);
        }
    }

    @Override
    public List<PycDatoVarEquiv> obtenerDatVarEquiv(String codRamo, Integer codModalidad, String codCia) {
        log.info("Ejecutando función obtenerDatVarEquiv con codRamo={}, codModalidad={}, codCia={}",
                codRamo, codModalidad, codCia);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_OBTENER_DAT_VAR_EQUIV +
                "(?, ?, ?) AS dat_var_equiv_info FROM dual";

        List<PycDatoVarEquiv> result = new ArrayList<>();
        PycDatoVarEquivRowMapper mapper = new PycDatoVarEquivRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro codRamo (obligatorio)
            stmt.setString(1, codRamo);

            // Establecer el parámetro codModalidad (opcional, default 999, ahora es Integer)
            if (codModalidad != null) {
                stmt.setInt(2, codModalidad);
            } else {
                stmt.setInt(2, 999); // Valor por defecto
            }

            // Establecer el parámetro codCia (obligatorio)
            stmt.setString(3, codCia);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("dat_var_equiv_info");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Registros de datos variables equivalentes obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función obtenerDatVarEquiv: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los datos de variables equivalentes", e);
        }
    }

    @Override
    public List<PycPerfilUsu> perfilesUsuario(Integer idUsuario, Integer idProceso) {
        log.info("Ejecutando función perfilesUsuario con idUsuario={}, idProceso={}",
                idUsuario, idProceso);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_PERFILES_USUARIO +
                "(?, ?) AS perfiles_usuario FROM dual";

        List<PycPerfilUsu> result = new ArrayList<>();
        PycPerfilUsuRowMapper mapper = new PycPerfilUsuRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idUsuario (obligatorio)
            stmt.setInt(1, idUsuario);

            // Establecer el parámetro idProceso (obligatorio)
            stmt.setInt(2, idProceso);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("perfiles_usuario");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Perfiles de usuario obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función perfilesUsuario: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los perfiles del usuario", e);
        }
    }

    @Override
    public List<PycCategoriaPet> getCategoriaPeticion(Integer idPeticion) {
        log.info("Ejecutando función getCategoriaPeticion con idPeticion={}", idPeticion);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_OBTENER_CATE_X_PET +
                "(?) AS categoria_peticion FROM dual";

        List<PycCategoriaPet> result = new ArrayList<>();
        PycCategoriaPetRowMapper mapper = new PycCategoriaPetRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idPeticion (requerido)
            stmt.setInt(1, idPeticion);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("categoria_peticion");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Categorías de petición obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getCategoriaPeticion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las categorías de la petición", e);
        }
    }

    @Override
    public List<PycPeticionTab> getPeticionesTablero() {
        log.info("Ejecutando función getPeticionesTablero");

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_OBTENER_PETICIONES_TABLERO +
                "() AS peticiones_tablero FROM dual";

        List<PycPeticionTab> result = new ArrayList<>();
        PycPeticionTabRowMapper mapper = new PycPeticionTabRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("peticiones_tablero");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Peticiones de tablero obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getPeticionesTablero: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las peticiones del tablero", e);
        }
    }

    @Override
    public List<PycOpcionUsu> getOpcionUsuario(Integer idPerfil, Integer idProceso, String numaOUsuario) {
        log.info("Ejecutando función getOpcionUsuario con idPerfil={}, idProceso={}, numaOUsuario={}", idPerfil, idProceso, numaOUsuario);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_AUTH_MGT + "." +
                DatabaseConstants.FN_OPCION_USUARIO +
                "(?, ?, ?) AS opcion_usuario FROM dual";

        List<PycOpcionUsu> result = new ArrayList<>();
        PycOpcionUsuRowMapper mapper = new PycOpcionUsuRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idPerfil (requerido)
            stmt.setInt(1, idPerfil);

            // Establecer el parámetro idProceso (requerido)
            stmt.setInt(2, idProceso);

            // Establecer el parámetro numaOUsuario (requerido)
            stmt.setString(3, numaOUsuario);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("opcion_usuario");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Opciones de usuario obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getOpcionUsuario: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las opciones del usuario", e);
        }
    }

    @Override
    public List<PycReportePetProg> getReportePeticionProgramacion(Integer anio, Integer idProceso) {
        log.info("Ejecutando función getReportePeticionProgramacion con anio={}, idProceso={}", anio, idProceso);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_REPORTE_PETICION_PROG +
                "(?, ?) AS reporte_peticion_prog FROM dual";

        List<PycReportePetProg> result = new ArrayList<>();
        PycReportePetProgRowMapper mapper = new PycReportePetProgRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro anio (requerido)
            stmt.setInt(1, anio);

            // Establecer el parámetro idProceso (requerido)
            stmt.setInt(2, idProceso);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("reporte_peticion_prog");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Registros de reporte de peticiones obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getReportePeticionProgramacion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el reporte de peticiones con programación", e);
        }
    }

    @Override
    public List<PycEstadisticaEstado> getEstadisticaEstados(Integer proceso, Integer area, Integer anio, String estado, String codCia) {
        log.info("Ejecutando función getEstadisticaEstados con proceso={}, area={}, estado={}, anio={},  codCia={}",
                proceso, area, anio, estado, codCia);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_ESTADISTICA_ESTADOS +
                "(?, ?, ?, ?, ?) AS estadistica_estados FROM dual";

        List<PycEstadisticaEstado> result = new ArrayList<>();
        PycEstadisticaEstadoRowMapper mapper = new PycEstadisticaEstadoRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro proceso (requerido)
            stmt.setInt(1, proceso);

            // Establecer el parámetro area (requerido)
            stmt.setInt(2, area);

            // Establecer el parámetro estado (puede ser null)
            if (estado != null && !estado.trim().isEmpty() && !"null".equalsIgnoreCase(estado.trim())) {
                stmt.setString(3, estado);
            } else {
                stmt.setNull(3, Types.VARCHAR);
            }

            // Establecer el parámetro anio (requerido)
            stmt.setInt(4, anio);


            // Establecer el parámetro codCia (opcional, puede ser null)
            if (codCia != null) {
                stmt.setString(5, codCia);
            } else {
                stmt.setNull(5, Types.VARCHAR);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("estadistica_estados");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Estadísticas de estados obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getEstadisticaEstados: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las estadísticas de estados", e);
        }
    }

    @Override
    public List<PycEstadistEstadoSig> getEstadisticaEstadosSiguientes(Integer proceso, Integer area, Integer anio, String estado, String codCia) {
        log.info("Ejecutando función getEstadisticaEstadosSiguientes con proceso={}, area={},  estado={},anio={}, codCia={}",
                proceso, area, anio, estado, codCia);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_ESTADISTICA_ESTADOS_SIGU +
                "(?, ?, ?, ?, ?) AS estadistica_estados_sigu FROM dual";

        List<PycEstadistEstadoSig> result = new ArrayList<>();
        PycEstadistEstadoSigRowMapper mapper = new PycEstadistEstadoSigRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro proceso (requerido)
            stmt.setInt(1, proceso);

            // Establecer el parámetro area (requerido)
            stmt.setInt(2, area);

            // Establecer el parámetro estado (puede ser null)
            if (estado != null && !estado.trim().isEmpty() && !"null".equalsIgnoreCase(estado.trim())) {
                stmt.setString(3, estado);
            } else {
                stmt.setNull(3, Types.VARCHAR);
            }

            // Establecer el parámetro anio (requerido)
            stmt.setInt(4, anio);



            // Establecer el parámetro codCia (opcional, puede ser null)
            if (codCia != null) {
                stmt.setString(5, codCia);
            } else {
                stmt.setNull(5, Types.VARCHAR);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("estadistica_estados_sigu");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Estadísticas de estados siguientes obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getEstadisticaEstadosSiguientes: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las estadísticas de estados siguientes", e);
        }
    }

    @Override
    public Integer updateEstadoPeticion(PycUpdEstadoPet estadoData) {
        log.info("Ejecutando función updateEstadoPeticion con idPeticion={}, nuevoEstado={}, observacion={}, numaOUsuario={}",
                estadoData.getIdPeticion(), estadoData.getNuevoEstado(), estadoData.getObservacion(), estadoData.getNumaOUsuario());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_UPDATE_PETICION_ESTADO +
                "(?, ?, ?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de retorno
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada requeridos
            stmt.setString(2, estadoData.getIdPeticion());
            stmt.setInt(3, estadoData.getNuevoEstado());
            stmt.setString(4, estadoData.getObservacion());

            // Establecer los parámetros opcionales
            if (estadoData.getNuevoEstadoApi() != null && !estadoData.getNuevoEstadoApi().trim().isEmpty()) {
                stmt.setString(5, estadoData.getNuevoEstadoApi());
            } else {
                stmt.setNull(5, Types.VARCHAR);
            }

            if (estadoData.getMotivo() != null && !estadoData.getMotivo().trim().isEmpty()) {
                stmt.setString(6, estadoData.getMotivo());
            } else {
                stmt.setNull(6, Types.VARCHAR);
            }

            // Nuevo parámetro numaOUsuario (opcional)
            if (estadoData.getNumaOUsuario() != null && !estadoData.getNumaOUsuario().trim().isEmpty()) {
                stmt.setString(7, estadoData.getNumaOUsuario());
            } else {
                stmt.setNull(7, Types.VARCHAR);
            }

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer resultado = stmt.getInt(1);

            log.info("Función ejecutada correctamente. Resultado: {}", resultado);
            return resultado;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función updateEstadoPeticion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al actualizar el estado de la petición", e);
        }
    }

    @Override
    public Integer updatePathPerfil(PycUpdPathPerfil perfilData) {
        log.info("Ejecutando función updatePathPerfil para usuario ID: {}", perfilData.getIdUsuario());

        String sqlCall = "{? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_AUTH_MGT + "." +
                DatabaseConstants.FN_ACTUALIZA_PERFIL +
                "(?, ?, ?)}";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlCall)) {

            // Registrar el parámetro de salida (resultado de la función)
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada
            stmt.setInt(2, perfilData.getIdUsuario());
            stmt.setString(3, perfilData.getUrlPerfil());
            stmt.setString(4, perfilData.getPathPerfil());

            log.debug("Parámetros: idUsuario={}, urlPerfil={}, pathPerfil={}",
                    perfilData.getIdUsuario(), perfilData.getUrlPerfil(), perfilData.getPathPerfil());

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer resultado = stmt.getInt(1);

            log.info("Función updatePathPerfil ejecutada correctamente. Resultado: {}", resultado);
            return resultado;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función updatePathPerfil: {}", e.getMessage(), e);
            throw new RuntimeException("Error al actualizar el path del perfil del usuario", e);
        }
    }

    @Override
    public Integer updateSesion(PycUpdSesion sesionData) {
        log.info("Ejecutando función updateSesion para usuario ID: {}, estado: {}",
                sesionData.getIdUsuario(), sesionData.getEstado());

        String sqlCall = "{? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_AUTH_MGT + "." +
                DatabaseConstants.FN_ACTUALIZA_SESION +
                "(?, ?)}";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlCall)) {

            // Registrar el parámetro de salida (resultado de la función)
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada
            stmt.setInt(2, sesionData.getIdUsuario());
            stmt.setInt(3, sesionData.getEstado());

            log.debug("Parámetros: idUsuario={}, estado={}",
                    sesionData.getIdUsuario(), sesionData.getEstado());

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer resultado = stmt.getInt(1);

            log.info("Función updateSesion ejecutada correctamente. Resultado: {}", resultado);
            return resultado;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función updateSesion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al actualizar la sesión del usuario", e);
        }
    }

    @Override
    public boolean bitacoraAccion(PycBitacoraAccion bitacoraData) {
        log.info("Ejecutando procedimiento bitacoraAccion para proceso ID: {}, acción: {}, usuario: {}",
                bitacoraData.getIdProceso(), bitacoraData.getAccion(), bitacoraData.getNumaOUsuario());

        String sqlCall = "{call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_AUTH_MGT + "." +
                DatabaseConstants.P_BITACORA_ACCION +
                "(?, ?, ?, ?, ?, ?, ?)}";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlCall)) {

            // Establecer los parámetros de entrada
            stmt.setInt(1, bitacoraData.getIdProceso());
            stmt.setString(2, bitacoraData.getAccion());
            stmt.setString(3, bitacoraData.getDescripcion());
            stmt.setString(4, bitacoraData.getEstado());
            stmt.setString(5, bitacoraData.getRefEntidad());
            stmt.setString(6, bitacoraData.getRefEntidadId());
            stmt.setString(7, bitacoraData.getNumaOUsuario());

            log.debug("Parámetros: idProceso={}, accion={}, descripcion={}, estado={}, refEntidad={}, refEntidadId={}, numaOUsuario={}",
                    bitacoraData.getIdProceso(), bitacoraData.getAccion(), bitacoraData.getDescripcion(),
                    bitacoraData.getEstado(), bitacoraData.getRefEntidad(), bitacoraData.getRefEntidadId(),
                    bitacoraData.getNumaOUsuario());

            // Ejecutar el procedimiento
            stmt.execute();

            log.info("Procedimiento bitacoraAccion ejecutado correctamente");
            return true;

        } catch (SQLException e) {
            log.error("Error al ejecutar el procedimiento bitacoraAccion: {}", e.getMessage(), e);
            // No lanzamos excepción, retornamos false para indicar fallo
            return false;
        }
    }

    @Override
    public boolean gestionUsrAsignacionAuto(PycGestUsrAsigAuto asignacionData) {
        log.info("Ejecutando procedimiento gestionUsrAsignacionAuto para proceso ID: {}, perfil: {}, aplicación: {}, estado: {}, usuario: {}",
                asignacionData.getIdProceso(), asignacionData.getIdPerfil(), asignacionData.getIdAplicacion(),
                asignacionData.getIdEstado(), asignacionData.getIdUsuario());

        String sqlCall = "{call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_GESTION_USR_ASIGNACION_AUTO +
                "(?, ?, ?, ?, ?, ?, ?, ?)}";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlCall)) {

            // Establecer los parámetros de entrada obligatorios
            stmt.setInt(1, asignacionData.getIdProceso());
            stmt.setInt(2, asignacionData.getIdPerfil());
            stmt.setInt(3, asignacionData.getIdAplicacion());
            stmt.setInt(4, asignacionData.getIdEstado());
            stmt.setString(5, asignacionData.getEstado());
            stmt.setInt(6, asignacionData.getIdUsuario());

            // Parámetro opcional codinter
            if (asignacionData.getCodinter() != null && !asignacionData.getCodinter().trim().isEmpty()) {
                stmt.setString(7, asignacionData.getCodinter());
            } else {
                stmt.setNull(7, Types.VARCHAR);
            }

            // Parámetro opcional numaOUsuario
            if (asignacionData.getNumaOUsuario() != null && !asignacionData.getNumaOUsuario().trim().isEmpty()) {
                stmt.setString(8, asignacionData.getNumaOUsuario());
            } else {
                stmt.setNull(8, Types.VARCHAR);
            }

            // Ejecutar el procedimiento
            stmt.execute();

            log.info("Procedimiento gestionUsrAsignacionAuto ejecutado correctamente");
            return true;

        } catch (SQLException e) {
            log.error("Error al ejecutar el procedimiento gestionUsrAsignacionAuto: {}", e.getMessage(), e);
            // Para procedimientos de gestión, no lanzamos excepción, retornamos false
            return false;
        }
    }

    @Override
    public List<PycEstadoUsu> getEstadoUsuario(String usuario, String clave) {
        log.info("Ejecutando función getEstadoUsuario con usuario={}", usuario);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_ESTADO_USUARIO +
                "(?, ?) AS estado_usuario FROM dual";

        List<PycEstadoUsu> result = new ArrayList<>();
        PycEstadoUsuRowMapper mapper = new PycEstadoUsuRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros
            stmt.setString(1, usuario);
            stmt.setString(2, clave);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("estado_usuario");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Estados de usuario obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getEstadoUsuario: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el estado del usuario", e);
        }
    }

    @Override
    public Integer insertObservacion(PycInsertObservacion observacionData) {
        log.info("Ejecutando función insertObservacion con idPeticion={}, usuarioPet={}, estado={}, numaOUsuario={}",
                observacionData.getIdPeticion(), observacionData.getUsuarioPet(), observacionData.getEstado(), observacionData.getNumaOUsuario());

        String sqlQuery = "{? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_INSERT_OBSERVACION +
                "(?, ?, ?, ?, ?, ?)}";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de salida (función retorna NUMBER)
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada
            stmt.setInt(2, observacionData.getIdPeticion());
            stmt.setString(3, observacionData.getObservacion());
            stmt.setInt(4, observacionData.getUsuarioPet());
            stmt.setString(5, observacionData.getEstado());

            // Parámetro opcional publico con valor por defecto
            if (observacionData.getPublico() != null && !observacionData.getPublico().trim().isEmpty()) {
                stmt.setString(6, observacionData.getPublico());
            } else {
                stmt.setString(6, "S"); // Valor por defecto
            }

            // Parámetro opcional numaOUsuario con valor por defecto
            if (observacionData.getNumaOUsuario() != null && !observacionData.getNumaOUsuario().trim().isEmpty()) {
                stmt.setString(7, observacionData.getNumaOUsuario());
            } else {
                stmt.setNull(7, Types.VARCHAR);
            }

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado (ID de la observación insertada)
            Integer idObservacion = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID Observación insertada: {}", idObservacion);
            return idObservacion;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función insertObservacion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al insertar la observación", e);
        }
    }

    @Override
    public List<PycAnalistaActi> getAnalistaActividad(Integer peticion, Integer actividad) {
        log.info("Ejecutando función getAnalistaActividad con peticion={}, actividad={}", peticion, actividad);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_ANALISTA_ACTIVIDAD +
                "(?, ?) AS analista_actividad FROM dual";

        List<PycAnalistaActi> result = new ArrayList<>();
        PycAnalistaActiRowMapper mapper = new PycAnalistaActiRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros
            stmt.setInt(1, peticion);
            stmt.setInt(2, actividad);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("analista_actividad");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Analistas por actividad obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getAnalistaActividad: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los analistas por actividad", e);
        }
    }

    @Override
    public List<PycGetDatVarFormSec> getDatVarFormSecc(Integer idProceso, Integer idFormulario, Integer idSeccion, Integer idPeticion) {
        log.info("Ejecutando función getDatVarFormSecc con idProceso={}, idFormulario={}, idSeccion={}, idPeticion={}",
                idProceso, idFormulario, idSeccion, idPeticion);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_GET_DAT_VAR_FORM_SECC +
                "(?, ?, ?, ?) AS dat_var_form_secc FROM dual";

        List<PycGetDatVarFormSec> result = new ArrayList<>();
        PycGetDatVarFormSecRowMapper mapper = new PycGetDatVarFormSecRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros obligatorios
            stmt.setInt(1, idProceso);
            stmt.setInt(2, idFormulario);
            stmt.setInt(3, idSeccion);

            // Establecer el parámetro opcional idPeticion
            if (idPeticion != null) {
                stmt.setInt(4, idPeticion);
            } else {
                stmt.setNull(4, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("dat_var_form_secc");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Datos variables de formulario obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getDatVarFormSecc: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los datos variables del formulario", e);
        }
    }

    @Override
    public List<PycGetBitacoraPet> getBitacoraPeticion(Integer idPeticion) {
        log.info("Ejecutando función getBitacoraPeticion con idPeticion={}", idPeticion);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_OBTENER_BITACORA_PETICION +
                "(?) AS bitacora_peticion FROM dual";

        List<PycGetBitacoraPet> result = new ArrayList<>();
        PycGetBitacoraPetRowMapper mapper = new PycGetBitacoraPetRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idPeticion (requerido)
            stmt.setInt(1, idPeticion);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("bitacora_peticion");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Registros de bitácora obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getBitacoraPeticion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la bitácora de la petición", e);
        }
    }

    @Override
    public List<PycTipoPeticion> getTipoPeticion(Integer idProceso) {
        log.info("Ejecutando función getTipoPeticion con idProceso={}", idProceso);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_TIPO_PETICION +
                "(?) AS tipos_peticion FROM dual";

        List<PycTipoPeticion> result = new ArrayList<>();
        PycTipoPeticionRowMapper mapper = new PycTipoPeticionRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idProceso (requerido)
            stmt.setInt(1, idProceso);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("tipos_peticion");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Tipos de petición obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getTipoPeticion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los tipos de petición", e);
        }
    }

    @Override
    public List<PycEstadoTransc> getCambioEstado(Integer idPerfil, Integer idProceso, Integer idEstado) {
        log.info("Ejecutando función getCambioEstado con idPerfil={}, idProceso={}, idEstado={}", idPerfil, idProceso, idEstado);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_CAMBIO_ESTADO +
                "(?, ?, ?) AS estados_transicion FROM dual";

        List<PycEstadoTransc> result = new ArrayList<>();
        PycEstadoTranscRowMapper mapper = new PycEstadoTranscRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros (todos requeridos)
            stmt.setInt(1, idPerfil);
            stmt.setInt(2, idProceso);
            stmt.setInt(3, idEstado);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("estados_transicion");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Estados de transición obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getCambioEstado: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los estados de transición", e);
        }
    }

    @Override
    public List<PycDocPeticion> getDocumentoPeticion(Integer idPeticion, Integer perfilAct) {
        log.info("Ejecutando función getDocumentoPeticion con idPeticion={}, perfilAct={}", idPeticion, perfilAct);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_DOCUMENTO_PETICION +
                "(?, ?) AS documentos_peticion FROM dual";

        List<PycDocPeticion> result = new ArrayList<>();
        PycDocPeticionRowMapper mapper = new PycDocPeticionRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idPeticion (requerido)
            stmt.setInt(1, idPeticion);

            // Establecer el parámetro perfilAct (opcional)
            if (perfilAct != null) {
                stmt.setInt(2, perfilAct);
            } else {
                stmt.setNull(2, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("documentos_peticion");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Documentos de petición obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getDocumentoPeticion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los documentos de la petición", e);
        }
    }

    @Override
    public List<PycDocXPeticion> getDocXPeticion(Integer idTipo) {
        log.info("Ejecutando función getDocXPeticion con idTipo={}", idTipo);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_DOC_X_PETICION +
                "(?) AS documentos_x_peticion FROM dual";

        List<PycDocXPeticion> result = new ArrayList<>();
        PycDocXPeticionRowMapper mapper = new PycDocXPeticionRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idTipo (requerido)
            stmt.setInt(1, idTipo);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("documentos_x_peticion");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Documentos por tipo de petición obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getDocXPeticion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los documentos por tipo de petición", e);
        }
    }

    @Override
    public List<PycDatVarForSeccTippe> getDatVarFormSeccTippe(Integer idProceso, Integer idTipo) {
        log.info("Ejecutando función getDatVarFormSeccTippe con idProceso={}, idTipo={}", idProceso, idTipo);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_GET_DAT_VAR_FORM_SECC_TIPPE +
                "(?, ?) AS dat_var_form_secc_tippe FROM dual";

        List<PycDatVarForSeccTippe> result = new ArrayList<>();
        PycDatVarForSeccTippeRowMapper mapper = new PycDatVarForSeccTippeRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros obligatorios
            stmt.setInt(1, idProceso);
            stmt.setInt(2, idTipo);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("dat_var_form_secc_tippe");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Datos variables de formulario por tipo obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getDatVarFormSeccTippe: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los datos variables del formulario por tipo", e);
        }
    }

    @Override
    public Integer newDatVarPeticion(PycNewDatVarPet datVarData) {
        log.info("Ejecutando función newDatVarPeticion con idPeticion={}, idFormulario={}, idSeccion={}, idDatoVar={}, valor={}",
                datVarData.getIdPeticion(), datVarData.getIdFormulario(), datVarData.getIdSeccion(),
                datVarData.getIdDatoVar(), datVarData.getValor());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_NEW_DAT_VAR_PETICION +
                "(?, ?, ?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de retorno
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada obligatorios
            stmt.setInt(2, datVarData.getIdPeticion());
            stmt.setInt(3, datVarData.getIdFormulario());
            stmt.setInt(4, datVarData.getIdSeccion());
            stmt.setInt(5, datVarData.getIdDatoVar());
            stmt.setString(6, datVarData.getValor());

            // Establecer el parámetro opcional descripcion
            if (datVarData.getDescripcion() != null && !datVarData.getDescripcion().trim().isEmpty()) {
                stmt.setString(7, datVarData.getDescripcion());
            } else {
                stmt.setNull(7, Types.VARCHAR);
            }

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer resultado = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID del registro: {}", resultado);
            return resultado;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función newDatVarPeticion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al crear/actualizar el dato variable de la petición", e);
        }
    }

    @Override
    public List<PycGetPerfiles> getPerfilesPorProceso(Integer idProceso) {
        log.info("Ejecutando función getPerfilesPorProceso con idProceso={}", idProceso);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_GET_PERFIL +
                "(?) AS perfiles_proceso FROM dual";

        List<PycGetPerfiles> result = new ArrayList<>();
        PycGetPerfilesRowMapper mapper = new PycGetPerfilesRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idProceso (requerido)
            stmt.setInt(1, idProceso);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("perfiles_proceso");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Perfiles por proceso obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getPerfilesPorProceso: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los perfiles por proceso", e);
        }
    }

    @Override
    public List<PycGetEstadoPerfilCod> getEstadoPerfilCod(Integer idProceso, Integer idPerfil) {
        log.info("Ejecutando función getEstadoPerfilCod con idProceso={}, idPerfil={}", idProceso, idPerfil);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_GET_ESTADO_PERFIL +
                "(?, ?) AS estado_perfil_cod FROM dual";

        List<PycGetEstadoPerfilCod> result = new ArrayList<>();
        PycGetEstadoPerfilCodRowMapper mapper = new PycGetEstadoPerfilCodRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros obligatorios
            stmt.setInt(1, idProceso);
            stmt.setInt(2, idPerfil);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("estado_perfil_cod");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Estados por perfil obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getEstadoPerfilCod: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los estados por perfil", e);
        }
    }

    @Override
    public Integer asignaPeticionDesa(PycAsignaPeticionDesa asignacionData) {
        log.info("Ejecutando función asignaPeticionDesa con idAplicacion={}, idDesarrollador={}, idPeticion={}, idPerfil={}, numaOUsuario={}",
                asignacionData.getIdAplicacion(), asignacionData.getIdDesarrollador(),
                asignacionData.getIdPeticion(), asignacionData.getIdPerfil(), asignacionData.getNumaOUsuario());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_ASIGNA_PETICION_DESA +
                "(?, ?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de retorno
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada obligatorios
            stmt.setInt(2, asignacionData.getIdAplicacion());
            stmt.setInt(3, asignacionData.getIdDesarrollador());
            stmt.setInt(4, asignacionData.getIdPeticion());
            stmt.setInt(5, asignacionData.getIdPerfil());

            // Establecer el parámetro opcional numaOUsuario
            if (asignacionData.getNumaOUsuario() != null && !asignacionData.getNumaOUsuario().trim().isEmpty()) {
                stmt.setString(6, asignacionData.getNumaOUsuario());
            } else {
                stmt.setNull(6, Types.VARCHAR);
            }

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer idPeticion = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID de la petición procesada: {}", idPeticion);
            return idPeticion;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función asignaPeticionDesa: {}", e.getMessage(), e);
            throw new RuntimeException("Error al asignar la petición al desarrollador", e);
        }
    }

    @Override
    public List<PycRespPeti> getResponsablePeticion(Integer idPeticion, String numaOUsuario) {
        log.info("Ejecutando función getResponsablePeticion con idPeticion={}, numaOUsuario={}",
                idPeticion, numaOUsuario);

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_RESPONSABLE_PETICION +
                "(?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de retorno como cursor
            stmt.registerOutParameter(1, OracleTypes.CURSOR);

            // Establecer el parámetro de entrada obligatorio
            stmt.setInt(2, idPeticion);

            // Establecer el parámetro opcional numaOUsuario
            if (numaOUsuario != null && !numaOUsuario.trim().isEmpty()) {
                stmt.setString(3, numaOUsuario);
            } else {
                stmt.setNull(3, Types.VARCHAR);
            }

            // Ejecutar la función
            stmt.execute();

            // Obtener el cursor de resultados
            try (ResultSet rs = (ResultSet) stmt.getObject(1)) {
                List<PycRespPeti> responsables = new ArrayList<>();
                PycRespPetiRowMapper mapper = new PycRespPetiRowMapper();

                int rowNum = 0;
                while (rs.next()) {
                    responsables.add(mapper.mapRow(rs, rowNum++));
                }

                log.info("Función ejecutada correctamente. Se obtuvieron {} responsables para la petición {}, usuario: {}",
                        responsables.size(), idPeticion, numaOUsuario);
                return responsables;
            }

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getResponsablePeticion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener responsables de petición", e);
        }
    }

    @Override
    public List<PycActPeticion> getActPeticion(Integer idPeticion, Integer idActividad) {
        log.info("Ejecutando función getActPeticion con idPeticion={}, idActividad={}", idPeticion, idActividad);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_ACT_PETICION +
                "(?, ?) AS actividades_peticion FROM dual";

        List<PycActPeticion> result = new ArrayList<>();
        PycActPeticionRowMapper mapper = new PycActPeticionRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idPeticion (requerido)
            stmt.setInt(1, idPeticion);

            // Establecer el parámetro idActividad (opcional)
            if (idActividad != null) {
                stmt.setInt(2, idActividad);
            } else {
                stmt.setNull(2, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("actividades_peticion");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Actividades de petición obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getActPeticion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las actividades de la petición", e);
        }
    }

    @Override
    public Integer updatePeticion(PycUpdPeticion peticionData) {
        log.info("Ejecutando función updatePeticion con idPeticion={}, nomPeticion={}, idSolUser={}, numaOUsuario={}",
                peticionData.getIdPeticion(), peticionData.getNomPeticion(),
                peticionData.getIdSolUser(), peticionData.getNumaOUsuario());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_UPDATE_PETICION +
                "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de retorno
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros obligatorios
            stmt.setInt(2, peticionData.getIdPeticion());
            stmt.setInt(3, peticionData.getIdSolUser());
            stmt.setString(4, peticionData.getNomPeticion());
            stmt.setString(5, peticionData.getDescPeticion());
            stmt.setInt(6, peticionData.getIdTipo());
            stmt.setString(7, peticionData.getObservacion());
            stmt.setString(8, peticionData.getCodCia());

            // Establecer los parámetros opcionales - Datos del cliente
            if (peticionData.getNomClie() != null && !peticionData.getNomClie().trim().isEmpty()) {
                stmt.setString(9, peticionData.getNomClie());
            } else {
                stmt.setNull(9, Types.VARCHAR);
            }

            if (peticionData.getTelClie() != null && !peticionData.getTelClie().trim().isEmpty()) {
                stmt.setString(10, peticionData.getTelClie());
            } else {
                stmt.setNull(10, Types.VARCHAR);
            }

            if (peticionData.getEmailClie() != null && !peticionData.getEmailClie().trim().isEmpty()) {
                stmt.setString(11, peticionData.getEmailClie());
            } else {
                stmt.setNull(11, Types.VARCHAR);
            }

            // Establecer los parámetros opcionales - Información adicional
            if (peticionData.getOrigen() != null && !peticionData.getOrigen().trim().isEmpty()) {
                stmt.setString(12, peticionData.getOrigen());
            } else {
                stmt.setNull(12, Types.VARCHAR);
            }

            if (peticionData.getClarity() != null && !peticionData.getClarity().trim().isEmpty()) {
                stmt.setString(13, peticionData.getClarity());
            } else {
                stmt.setNull(13, Types.VARCHAR);
            }

            if (peticionData.getPrioridad() != null && !peticionData.getPrioridad().trim().isEmpty()) {
                stmt.setString(14, peticionData.getPrioridad());
            } else {
                stmt.setNull(14, Types.VARCHAR);
            }

            // Establecer los parámetros opcionales - Parámetros específicos
            if (peticionData.getTipoCliente() != null && !peticionData.getTipoCliente().trim().isEmpty()) {
                stmt.setString(15, peticionData.getTipoCliente());
            } else {
                stmt.setNull(15, Types.VARCHAR);
            }

            if (peticionData.getCodCliente() != null && !peticionData.getCodCliente().trim().isEmpty()) {
                stmt.setString(16, peticionData.getCodCliente());
            } else {
                stmt.setNull(16, Types.VARCHAR);
            }

            if (peticionData.getNoPoliza() != null && !peticionData.getNoPoliza().trim().isEmpty()) {
                stmt.setString(17, peticionData.getNoPoliza());
            } else {
                stmt.setNull(17, Types.VARCHAR);
            }

            if (peticionData.getTipServicio() != null && !peticionData.getTipServicio().trim().isEmpty()) {
                stmt.setString(18, peticionData.getTipServicio());
            } else {
                stmt.setNull(18, Types.VARCHAR);
            }

            if (peticionData.getCausa() != null && !peticionData.getCausa().trim().isEmpty()) {
                stmt.setString(19, peticionData.getCausa());
            } else {
                stmt.setNull(19, Types.VARCHAR);
            }

            if (peticionData.getGravedad() != null && !peticionData.getGravedad().trim().isEmpty()) {
                stmt.setString(20, peticionData.getGravedad());
            } else {
                stmt.setNull(20, Types.VARCHAR);
            }

            // Establecer el parámetro opcional numaOUsuario
            if (peticionData.getNumaOUsuario() != null && !peticionData.getNumaOUsuario().trim().isEmpty()) {
                stmt.setString(21, peticionData.getNumaOUsuario());
            } else {
                stmt.setNull(21, Types.VARCHAR);
            }

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer idPeticion = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID Petición actualizada: {}", idPeticion);
            return idPeticion;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función updatePeticion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al actualizar la petición", e);
        }
    }

    @Override
    public Integer creaSolicitudRH(PycCreaSoliRH solicitudData) {
        log.info("Ejecutando función creaSolicitudRH con nomCli={}, telCli={}, emailCli={}, dpi={}",
                solicitudData.getNomCli(), solicitudData.getTelCli(),
                solicitudData.getEmailCli(), solicitudData.getDpi());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_RRHH_MGT + "." +
                DatabaseConstants.P_CREA_SOLICITUD_RH +
                "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de retorno
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer todos los parámetros obligatorios
            stmt.setString(2, solicitudData.getNomCli());
            stmt.setString(3, solicitudData.getTelCli());
            stmt.setString(4, solicitudData.getEmailCli());
            stmt.setString(5, solicitudData.getFecNac());
            stmt.setString(6, solicitudData.getDpi());
            stmt.setString(7, solicitudData.getNit());
            stmt.setString(8, solicitudData.getDescEsc());
            stmt.setString(9, solicitudData.getCodEsc());
            stmt.setString(10, solicitudData.getTitulo());
            stmt.setString(11, solicitudData.getDireccion());
            stmt.setString(12, solicitudData.getCodDpto());
            stmt.setString(13, solicitudData.getDescDpto());
            stmt.setString(14, solicitudData.getCodTrb());
            stmt.setString(15, solicitudData.getDescTrb());
            stmt.setString(16, solicitudData.getCodSeg());
            stmt.setString(17, solicitudData.getDescSeg());
            stmt.setString(18, solicitudData.getPreten());
            stmt.setString(19, solicitudData.getCodPlaza());
            stmt.setString(20, solicitudData.getDescPlaz());

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer idSolicitud = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID Solicitud RH creada: {}", idSolicitud);
            return idSolicitud;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función creaSolicitudRH: {}", e.getMessage(), e);
            throw new RuntimeException("Error al crear la solicitud de RH", e);
        }
    }

    @Override
    public List<PycDocNoObli> getDocNoObligatorios(Integer idPeticion) {
        log.info("Ejecutando función getDocNoObligatorios con idPeticion={}", idPeticion);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_DOC_NOOBLIGATORIOS +
                "(?) AS documentos_no_obligatorios FROM dual";

        List<PycDocNoObli> result = new ArrayList<>();
        PycDocNoObliRowMapper mapper = new PycDocNoObliRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idPeticion
            stmt.setInt(1, idPeticion);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("documentos_no_obligatorios");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Documentos no obligatorios obtenidos: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getDocNoObligatorios: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los documentos no obligatorios", e);
        }
    }

    @Override
    public List<PycCatAct> getCategoriaAct(Integer idProceso) {
        log.info("Ejecutando función getCategoriaAct con idProceso={}", idProceso);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_OBTENER_CATEGORIA_ACT +
                "(?) AS categorias_activas FROM dual";

        List<PycCatAct> result = new ArrayList<>();
        PycCatActRowMapper mapper = new PycCatActRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer el parámetro idProceso
            stmt.setInt(1, idProceso);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("categorias_activas");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Categorías activas obtenidas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getCategoriaAct: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las categorías activas", e);
        }
    }

    @Override
    public Integer insertActividad(PycInserAct actividadData) {
        log.info("Ejecutando función insertActividad con idPeti={}, nombAct={}, userPet={}",
                actividadData.getIdPeti(), actividadData.getNombAct(), actividadData.getUserPet());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_INSERT_ACTIVIDAD +
                "(?, ?, ?, ?, ?, ?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de salida
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada obligatorios
            stmt.setInt(2, actividadData.getIdPeti());
            stmt.setString(3, actividadData.getNombAct());
            stmt.setString(4, actividadData.getDescAct());
            stmt.setString(5, actividadData.getHrsBase());
            stmt.setString(6, actividadData.getFecIni());
            stmt.setString(7, actividadData.getFecFin());
            stmt.setInt(8, actividadData.getUserPet());
            stmt.setString(9, actividadData.getCate());

            // Establecer el parámetro opcional
            if (actividadData.getAsigna() != null && !actividadData.getAsigna().isEmpty()) {
                stmt.setString(10, actividadData.getAsigna());
            } else {
                stmt.setNull(10, Types.VARCHAR);
            }

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer idActividad = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID Actividad creada: {}", idActividad);
            return idActividad;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función insertActividad: {}", e.getMessage(), e);
            throw new RuntimeException("Error al insertar la actividad", e);
        }
    }

    @Override
    public Integer editActividad(PycEditAct actividadData) {
        log.info("Ejecutando función editActividad con idPeticion={}, idActividad={}, nomActividad={}",
                actividadData.getIdPeticion(), actividadData.getIdActividad(), actividadData.getNomActividad());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_EDIT_ACTIVIDAD +
                "(?, ?, ?, ?, ?, ?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de salida
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada obligatorios
            stmt.setInt(2, actividadData.getIdPeticion());
            stmt.setInt(3, actividadData.getIdActividad());
            stmt.setString(4, actividadData.getNomActividad());
            stmt.setString(5, actividadData.getDescActividad());
            stmt.setString(6, actividadData.getHrsBase());
            stmt.setString(7, actividadData.getCategoria());
            stmt.setString(8, actividadData.getFecIni());
            stmt.setString(9, actividadData.getFecFin());

            // Establecer el parámetro opcional
            if (actividadData.getAsigna() != null && !actividadData.getAsigna().isEmpty()) {
                stmt.setString(10, actividadData.getAsigna());
            } else {
                stmt.setNull(10, Types.VARCHAR);
            }

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer idPeticion = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID Petición actualizada: {}", idPeticion);
            return idPeticion;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función editActividad: {}", e.getMessage(), e);
            throw new RuntimeException("Error al editar la actividad", e);
        }
    }

    @Override
    public Integer updateActividad(PycUpdAct actividadData) {
        log.info("Ejecutando función updateActividad con idPeticion={}, idActividad={}, hrsReal={}, ind={}",
                actividadData.getIdPeticion(), actividadData.getIdActividad(),
                actividadData.getHrsReal(), actividadData.getInd());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_UPDATE_ACTIVIDAD +
                "(?, ?, ?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de salida
            stmt.registerOutParameter(1, Types.INTEGER);

            // Establecer los parámetros de entrada obligatorios
            stmt.setInt(2, actividadData.getIdPeticion());
            stmt.setInt(3, actividadData.getIdActividad());
            stmt.setString(4, actividadData.getHrsReal());
            stmt.setString(5, actividadData.getObservacion());
            stmt.setString(6, actividadData.getInd().toString());

            // Establecer el parámetro opcional numaOUsuario
            if (actividadData.getNumaOUsuario() != null && !actividadData.getNumaOUsuario().isEmpty()) {
                stmt.setString(7, actividadData.getNumaOUsuario());
            } else {
                stmt.setNull(7, Types.VARCHAR);
            }

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            Integer idPeticion = stmt.getInt(1);

            log.info("Función ejecutada correctamente. ID Petición actualizada: {}", idPeticion);
            return idPeticion;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función updateActividad: {}", e.getMessage(), e);
            throw new RuntimeException("Error al actualizar la actividad", e);
        }
    }

    @Override
    public String updateEncuesta(PycUpdEnc encuestaData) {
        log.info("Ejecutando función updateEncuesta con idPeticion={}, encuCalifi={}, encuComent={}",
                encuestaData.getIdPeticion(), encuestaData.getEncuCalifi(), encuestaData.getEncuComent());

        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_PYC_PETICIONES_MGT + "." +
                DatabaseConstants.FN_UPDATE_ENCUESTA +
                "(?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetro de retorno
            stmt.registerOutParameter(1, Types.VARCHAR);

            // Configurar parámetros de entrada
            stmt.setString(2, encuestaData.getEncuCalifi());
            stmt.setString(3, encuestaData.getEncuComent());

            // Parámetro opcional encuComentAdi
            if (encuestaData.getEncuComentAdi() != null && !encuestaData.getEncuComentAdi().trim().isEmpty()) {
                stmt.setString(4, encuestaData.getEncuComentAdi());
            } else {
                stmt.setNull(4, Types.VARCHAR);
            }

            stmt.setString(5, encuestaData.getIdPeticion());

            // Ejecutar la función
            stmt.execute();

            // Obtener el resultado
            String idPeticion = stmt.getString(1);

            log.info("Función updateEncuesta ejecutada exitosamente. ID Petición retornado: {}", idPeticion);
            return idPeticion;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función updateEncuesta: {}", e.getMessage(), e);
            throw new RuntimeException("Error al actualizar la encuesta", e);
        }
    }
}
