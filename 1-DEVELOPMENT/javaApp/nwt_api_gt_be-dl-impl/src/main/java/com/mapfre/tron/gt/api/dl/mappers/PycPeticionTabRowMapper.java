package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycPeticionTab;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycPeticionTab.
 */
@Slf4j
public class PycPeticionTabRowMapper implements RowMapper<PycPeticionTab> {

    @Override
    public PycPeticionTab mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycPeticionTab", rowNum);
        
        PycPeticionTab peticionTab = new PycPeticionTab();
        
        try {
            peticionTab.setIdPeticion(rs.getInt("ID_PETICION"));
            peticionTab.setNombrePeticion(rs.getString("NOMBRE_PETICION"));
            peticionTab.setDescripcionTipoPeticion(rs.getString("DESCRIPCION_TIPO_PETICION"));
            peticionTab.setNombreEstado(rs.getString("NOMBRE_ESTADO"));
            peticionTab.setPrimerNombre(rs.getString("PRIMER_NOMBRE"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return peticionTab;
    }
}
