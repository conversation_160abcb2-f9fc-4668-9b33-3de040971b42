package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycEstadisticaEstado;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycEstadisticaEstado.
 */
@Slf4j
public class PycEstadisticaEstadoRowMapper implements RowMapper<PycEstadisticaEstado> {

    @Override
    public PycEstadisticaEstado mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycEstadisticaEstado", rowNum);
        
        PycEstadisticaEstado estadisticaEstado = new PycEstadisticaEstado();
        
        try {
            estadisticaEstado.setNombreEstado(rs.getString("NOMBRE_ESTADO"));
            estadisticaEstado.setIdEstado(rs.getInt("ID_ESTADO"));
            estadisticaEstado.setColorEstado(rs.getString("COLOR_ESTADO"));
            estadisticaEstado.setCantidadEstado(rs.getInt("CANTIDAD_ESTADO"));
            estadisticaEstado.setTotalEstado(rs.getInt("TOTAL_ESTADO"));
            estadisticaEstado.setPorcentajeEstado(rs.getDouble("PORCENTAJE_ESTADO"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return estadisticaEstado;
    }
}
