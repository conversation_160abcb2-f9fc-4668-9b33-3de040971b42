package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycControlProc;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycControlProc.
 */
@Slf4j
public class PycControlProcRowMapper implements RowMapper<PycControlProc> {

    @Override
    public PycControlProc mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycControlProc", rowNum);
        
        PycControlProc controlProc = new PycControlProc();
        
        try {
            controlProc.setTipo(rs.getString("TIPO"));
            controlProc.setIdControl(rs.getInt("ID_CONTROL"));
            controlProc.setIdHtml(rs.getString("ID_HTML"));
            controlProc.setDataJs(rs.getString("DATA_JS"));
            controlProc.setNombre(rs.getString("NOMBRE"));
            controlProc.setIcono(rs.getString("ICONO"));
            controlProc.setDescripcion(rs.getString("DESCRIPCION"));
            controlProc.setLval(rs.getString("LVAL"));
            controlProc.setVisible(rs.getString("VISIBLE"));
            controlProc.setObligatorio(rs.getString("OBLIGATORIO"));
            controlProc.setDefaultVal(rs.getString("DEFAULT"));
            
            // Manejo de orden que puede ser null
            Integer orden = rs.getObject("ORDEN", Integer.class);
            controlProc.setOrden(orden);
            
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return controlProc;
    }
}
