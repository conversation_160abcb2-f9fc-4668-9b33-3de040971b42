package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycTabArea;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycTabArea.
 */
@Slf4j
public class PycTabAreaRowMapper implements RowMapper<PycTabArea> {

    @Override
    public PycTabArea mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycTabArea", rowNum);
        
        PycTabArea tabArea = new PycTabArea();
        
        try {
            tabArea.setIdArea(rs.getInt("ID_AREA"));
            tabArea.setNombreArea(rs.getString("NOMBRE_AREA"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return tabArea;
    }
}
