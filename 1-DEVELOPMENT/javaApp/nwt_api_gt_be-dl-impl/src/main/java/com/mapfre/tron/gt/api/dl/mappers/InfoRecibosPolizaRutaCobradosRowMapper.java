package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.InfoRecibosPolizaRutaCobradosResponse;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper para convertir resultados de la base de datos a objetos InfoRecibosPolizaRutaCobradosResponse.
 */
public class InfoRecibosPolizaRutaCobradosRowMapper implements RowMapper<InfoRecibosPolizaRutaCobradosResponse> {

    @Override
    public InfoRecibosPolizaRutaCobradosResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        InfoRecibosPolizaRutaCobradosResponse response = new InfoRecibosPolizaRutaCobradosResponse();

        // Mapear los campos según la definición en InfoRecibosPolizaRutaCobradosResponse
        response.setIdRuta(rs.getInt("idRuta"));
        response.setCodPol(rs.getString("codPol"));
        response.setNumPol(rs.getString("numPol"));
        response.setCertificado(rs.getString("certificado"));
        response.setNumeroRequerimiento(rs.getString("numeroRequerimiento"));
        response.setMoneda(rs.getString("moneda"));
        response.setTotalRequerimiento(rs.getString("totalRequerimiento"));
        response.setNumeroCuota(rs.getString("numeroCuota"));
        response.setSistema(rs.getString("sistema"));
        response.setVencimientoRequerimiento(rs.getString("vencimientoRequerimiento"));
        response.setNombrePagador(rs.getString("nombrePagador"));
        response.setEstado(rs.getString("estado"));
        response.setNomEstado(rs.getString("nomEstado"));
        response.setIdPol(rs.getString("IdPol"));
        response.setEsAviso(rs.getString("esAviso"));


        return response;
    }
}