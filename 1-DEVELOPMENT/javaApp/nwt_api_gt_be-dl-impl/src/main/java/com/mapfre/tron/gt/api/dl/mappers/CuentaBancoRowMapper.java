package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PLE_CuentaBanco;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PLE_CuentaBanco.
 */
@Slf4j
public class CuentaBancoRowMapper implements RowMapper<PLE_CuentaBanco> {

    @Override
    public PLE_CuentaBanco mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PLE_CuentaBanco", rowNum);
        
        PLE_CuentaBanco response = new PLE_CuentaBanco();
        
        try {
            response.setCodigo(rs.getString("codigo"));
            response.setCuenta(rs.getString("cuenta"));
            response.setTronCode(rs.getString("tronCode"));
            response.setAcselCode(rs.getString("acselCode"));
            response.setNombre(rs.getString("nombre"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
