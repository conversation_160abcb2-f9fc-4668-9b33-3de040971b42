package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.Entidad;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class EntidadRowMapper implements RowMapper<Entidad> {

    @Override
    public Entidad mapRow(ResultSet resultSet, int i) throws SQLException {
        Entidad entidad = new Entidad();

        entidad.setCodigo(resultSet.getString("codigo"));
        entidad.setNombre(resultSet.getString("nombre"));
        return entidad;
    }
}
