package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycUsuProcesos;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycUsuProcesos.
 */
@Slf4j
public class PycUsuProcesosRowMapper implements RowMapper<PycUsuProcesos> {

    @Override
    public PycUsuProcesos mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycUsuProcesos", rowNum);
        
        PycUsuProcesos usuProcesos = new PycUsuProcesos();
        
        try {
            usuProcesos.setIdUsuario(rs.getInt("ID_USUARIO"));
            usuProcesos.setNombreUsuario(rs.getString("NOMBRE_USUARIO"));
            usuProcesos.setUsuario(rs.getString("USUARIO"));
            usuProcesos.setIdProceso(rs.getInt("ID_PROCESO"));
            usuProcesos.setNombreProceso(rs.getString("NOMBRE_PROCESO"));
            usuProcesos.setEstado(rs.getString("ESTADO"));
            usuProcesos.setIcono(rs.getString("ICONO"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return usuProcesos;
    }
}
