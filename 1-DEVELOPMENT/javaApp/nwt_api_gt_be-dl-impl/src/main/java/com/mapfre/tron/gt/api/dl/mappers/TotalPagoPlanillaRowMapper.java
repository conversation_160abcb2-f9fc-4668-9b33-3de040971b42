package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PLE_TotalPagoPlanilla;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PLE_TotalPagoPlanilla.
 */
@Slf4j
public class TotalPagoPlanillaRowMapper implements RowMapper<PLE_TotalPagoPlanilla> {

    @Override
    public PLE_TotalPagoPlanilla mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PLE_TotalPagoPlanilla", rowNum);
        
        PLE_TotalPagoPlanilla response = new PLE_TotalPagoPlanilla();
        
        try {
            response.setMedioPago(rs.getString("medio_pago"));
            response.setTipoMoneda(rs.getString("tipo_moneda"));
            response.setMonto(rs.getBigDecimal("monto"));
            response.setDocumento(rs.getString("documento"));
            response.setBanco(rs.getString("banco"));
            response.setNombreBanco(rs.getString("nombre_banco"));
            response.setFechaCheque(rs.getDate("fecha_cheque"));
            response.setFechaDeposito(rs.getDate("fecha_deposito"));
            response.setEstado(rs.getString("estado"));
            response.setProcesado(rs.getBigDecimal("procesado"));
            response.setNoProcesado(rs.getBigDecimal("no_procesado"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
