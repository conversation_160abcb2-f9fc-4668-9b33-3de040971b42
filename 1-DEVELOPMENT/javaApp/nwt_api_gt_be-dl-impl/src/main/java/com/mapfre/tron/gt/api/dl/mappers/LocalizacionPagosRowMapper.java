package com.mapfre.tron.gt.api.dl.mappers;

import com.mapfre.tron.gt.api.model.LocalizacionPagosResponse;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper para convertir resultados de la base de datos a objetos LocalizacionPagosResponse.
 */
public class LocalizacionPagosRowMapper implements RowMapper<LocalizacionPagosResponse> {

    @Override
    public LocalizacionPagosResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        LocalizacionPagosResponse response = new LocalizacionPagosResponse();

        // Mapear los campos según la definición en LocalizacionPagosResponse
        response.setIdDetalle(rs.getInt("IdDetalle"));
        response.setAsegurado(rs.getString("Asegurado"));
        response.setIdRuta(rs.getInt("IdRuta"));
        response.setIdePol(rs.getString("IdePol"));
        response.setRecibo(rs.getString("Recibo"));
        response.setCertificado(rs.getString("Certificado"));
        response.setMoneda(rs.getString("Moneda"));
        response.setTotal(rs.getString("Total"));
        response.setCuota(rs.getString("Cuota"));
        response.setLatitud(rs.getString("Latitud"));
        response.setLongitud(rs.getString("Longitud"));
        response.setNombre(rs.getString("Nombre"));
        response.setFechaCobro(rs.getString("FechaCobro"));

        return response;
    }
}