package com.mapfre.tron.gt.api.dl;

import com.mapfre.tron.gt.api.commons.DatabaseConstants;
import com.mapfre.tron.gt.api.dl.mappers.*;
import com.mapfre.tron.gt.api.model.Cobro;
import com.mapfre.tron.gt.api.model.Entidad;
import com.mapfre.tron.gt.api.model.InsertarDetalleRutaDiarioRequest;
import com.mapfre.tron.gt.api.model.InsertarDetalleRuta2Request;
import com.mapfre.tron.gt.api.model.TipoMedioPagoRequest;
import com.mapfre.tron.gt.api.model.AvisoReciboRutaRequest;
import com.mapfre.tron.gt.api.model.ReciboRutaRequest;
import com.mapfre.tron.gt.api.model.AutenticarUsuarioResponseUsuario;
import com.mapfre.tron.gt.api.model.ObtenerRolesUsuarioResponseRoles;
import com.mapfre.tron.gt.api.model.CajeroUsuarioResponseCajeros;
import com.mapfre.tron.gt.api.model.AccionesPorRolesResponseAcciones;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataRetrievalFailureException;
import org.springframework.stereotype.Repository;

import com.mapfre.tron.gt.api.commons.DatabaseConstants;
import com.mapfre.tron.gt.api.model.*;

import lombok.extern.slf4j.Slf4j;
import oracle.jdbc.internal.OracleTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.Null;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;


@Repository
@Slf4j
public class DlCobroMovilImpl implements IDlCobroMovil {

    @Autowired
    private DataSource dataSource;

    @Override
    public ArrayList<Entidad> ListarEntidades(Integer codigoSistema, String codigoMoneda) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, DatabaseConstants.F_LISTAR_ENTIDADES));

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_LISTAR_ENTIDADES +
                "(?, ?) AS resultado FROM dual";

        ArrayList<Entidad> entidades = new ArrayList<>();
        EntidadRowMapper mapper = new EntidadRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            log.info(String.format(DatabaseConstants.LOG_LAUNCH_DESC,
                    DatabaseConstants.SCHEMA_NAME,
                    "-",
                    DatabaseConstants.F_LISTAR_ENTIDADES));

            stmt.setInt(1, codigoSistema);
            stmt.setString(2, codigoMoneda);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("resultado");
                        int rowNum = 0;
                        while (rs.next()) {
                            entidades.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }
            log.info("Función ejecutada correctamente. Registros encontrados: {}", entidades.size());
            return entidades;
        } catch (
                Exception ex) {
            log.error("Error al ejecutar fn_listarEntidades", ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.F_LISTAR_ENTIDADES, ex);
        }
    }

    @Override
    public ArrayList<Cobro> ListarOpcioensCobro() {
        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_LISTAR_OPCION_COBRO +
                "() AS resultado FROM dual";

        ArrayList<Cobro> opcionesCobro = new ArrayList<>();
        CobroRowMapper mapper = new CobroRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("resultado");
                        int rowNum = 0;
                        while (rs.next()) {
                            opcionesCobro.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }
            log.info("Función ejecutada correctamente. Registros encontrados: {}", opcionesCobro.size());
            return opcionesCobro;
        } catch (
                Exception ex) {
            log.error("Error al ejecutar fn_listarEntidades", ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.F_LISTAR_OPCION_COBRO, ex);
        }
    }

    @Override
    public Integer CrearRutaDiaria(Integer idUsuarioCrea, Integer idUsuarioAsignado) {
        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_INSERTAR_RUTA_DIARIA + "(?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {
            stmt.registerOutParameter(1, java.sql.Types.INTEGER);
            stmt.setInt(2, idUsuarioCrea);
            stmt.setInt(3, idUsuarioAsignado);
            stmt.execute();
            Integer idRuta = stmt.getInt(1);
            if (stmt.wasNull()) {
                return null;
            }
            return idRuta;
        } catch (Exception ex) {
            log.error("Error al ejecutar fnInsertarRutaDiaria", ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.F_INSERTAR_RUTA_DIARIA, ex);
        }
    }

    @Override
    public Integer CrearRuta(Integer idUsuarioCrea, Integer idUsuarioAsignado) {
        String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_INSERTAR_RUTA + "(?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {
            stmt.registerOutParameter(1, java.sql.Types.INTEGER);
            stmt.setInt(2, idUsuarioCrea);
            stmt.setInt(3, idUsuarioAsignado);
            stmt.execute();
            Integer idRuta = stmt.getInt(1);
            if (stmt.wasNull()) {
                return null;
            }
            return idRuta;
        } catch (Exception ex) {
            log.error("Error al ejecutar fnInsertRuta", ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.F_INSERTAR_RUTA, ex);
        }
    }

    @Override
    public Integer ModificarRuta(Integer idUsuarioCrea, Integer idUsuarioAsignado, Integer idRuta) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, DatabaseConstants.SP_MODIFICAR_RUTA));
        log.info("Parámetros de entrada - idUsuarioCrea: {}, idUsuarioAsignado: {}, idRuta: {}",
                idUsuarioCrea, idUsuarioAsignado, idRuta);

        String sqlQuery = "{ call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.SP_MODIFICAR_RUTA + "(?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            stmt.setInt(1, idUsuarioCrea);
            stmt.setInt(2, idUsuarioAsignado);
            stmt.setInt(3, idRuta);

            stmt.execute();

            log.info("Stored procedure {} ejecutado correctamente para idRuta: {}",
                    DatabaseConstants.SP_MODIFICAR_RUTA, idRuta);

            return idRuta;
        } catch (Exception ex) {
            log.error("Error al ejecutar {}: {}", DatabaseConstants.SP_MODIFICAR_RUTA, ex.getMessage(), ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.SP_MODIFICAR_RUTA, ex);
        }
    }

    @Override
    public boolean InsertarDetalleRutaDiario(InsertarDetalleRutaDiarioRequest requestParams) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, DatabaseConstants.SP_INSERT_DETALLE_RUTA_DIARIO));

        String sqlQuery = "{ call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.SP_INSERT_DETALLE_RUTA_DIARIO + "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            stmt.setInt(1, requestParams.getRuta());
            stmt.setString(2, requestParams.getIdpol());
            stmt.setString(3, requestParams.getCodpol());
            stmt.setString(4, requestParams.getNumpol());
            if (requestParams.getNumcert() == null){
                stmt.setString(5, null);
            }else{
                stmt.setString(5, requestParams.getNumcert());
            }
            stmt.setString(6, requestParams.getNumreq());
            stmt.setString(7, requestParams.getMoneda());
            stmt.setString(8, requestParams.getTotal());
            stmt.setString(9, requestParams.getCuota());
            stmt.setString(10, requestParams.getSistema());
            stmt.setString(11, requestParams.getAsegurado());
            stmt.setString(12, requestParams.getDireccion());
            String fechaFormatoEntrada = "dd/MM/yyyy";
            SimpleDateFormat sdf = new SimpleDateFormat(fechaFormatoEntrada);
            java.util.Date utilDate = sdf.parse(requestParams.getFechaVencimiento().toString());
            java.sql.Date fechaVencimiento = new java.sql.Date(utilDate.getTime());
            stmt.setDate(13, fechaVencimiento);
            stmt.setString(14, requestParams.getEsAviso());

            stmt.execute();
            log.info("Stored procedure {} ejecutado correctamente para ruta: {}",
                    DatabaseConstants.SP_INSERT_DETALLE_RUTA_DIARIO, requestParams.getRuta());
            return true;
        } catch (Exception ex) {
            log.error("Error al ejecutar {}: {}", DatabaseConstants.SP_INSERT_DETALLE_RUTA_DIARIO, ex.getMessage(), ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.SP_INSERT_DETALLE_RUTA_DIARIO, ex);
        }
    }

    @Override
    public boolean InsertarDetalleRuta2(InsertarDetalleRuta2Request requestParams) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, DatabaseConstants.SP_INSERT_DETALLE_RUTA2));
        log.info("Parámetros de entrada - ruta: {}, idPol: {}, codPol: {}, numPol: {}, numCert: {}, numReq: {}, moneda: {}, total: {}, cuota: {}, sistema: {}, asegurado: {}, direccion: {}, correo: {}, fechaVencimiento: {}",
                requestParams.getRuta(), requestParams.getIdPol(), requestParams.getCodPol(), requestParams.getNumPol(),
                requestParams.getNumCert(), requestParams.getNumReq(), requestParams.getMoneda(), requestParams.getTotal(),
                requestParams.getCuota(), requestParams.getSistema(), requestParams.getAsegurado(), requestParams.getDireccion(),
                requestParams.getCorreo(), requestParams.getFechaVencimiento());

        String sqlQuery = "{ call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.SP_INSERT_DETALLE_RUTA2 + "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            stmt.setInt(1, requestParams.getRuta());
            stmt.setString(2, requestParams.getIdPol());
            stmt.setString(3, requestParams.getCodPol());
            stmt.setString(4, requestParams.getNumPol());
            stmt.setString(5, requestParams.getNumCert());
            stmt.setString(6, requestParams.getNumReq());
            stmt.setString(7, requestParams.getMoneda());
            stmt.setString(8, requestParams.getTotal());
            stmt.setString(9, requestParams.getCuota());
            stmt.setString(10, requestParams.getSistema());
            stmt.setString(11, requestParams.getAsegurado());
            stmt.setString(12, requestParams.getDireccion());
            stmt.setString(13, requestParams.getCorreo());
            String fechaFormatoEntrada = "dd/MM/yyyy";
            SimpleDateFormat sdf = new SimpleDateFormat(fechaFormatoEntrada);
            java.util.Date utilDate = sdf.parse(requestParams.getFechaVencimiento());

            java.sql.Date fechaVencimiento = new java.sql.Date(utilDate.getTime());
            stmt.setDate(14, fechaVencimiento);
            stmt.setString(15, requestParams.getEsAviso());

            stmt.execute();
            log.info("Stored procedure {} ejecutado correctamente para ruta: {}",
                    DatabaseConstants.SP_INSERT_DETALLE_RUTA2, requestParams.getRuta());
            return true;

        } catch (Exception ex) {
            log.error("Error al ejecutar {}: {}", DatabaseConstants.SP_INSERT_DETALLE_RUTA2, ex.getMessage(), ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.SP_INSERT_DETALLE_RUTA2, ex);
        }
    }

    @Override
    public boolean InsertarTipoMedioPago(TipoMedioPagoRequest requestParams) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, DatabaseConstants.SP_INSERTAR_TIPO_MEDIO_PAGO));
        log.info("Parámetros de entrada - idRuta: {}, tipo: {}, entidadFinanciera: {}, entidadFinancieraDesc: {}, numeroDocumento: {}, entidadTarjeta: {}, fechaDeposito: {}, fechaVencimiento: {}, noAutorizacion: {}, noReferencia: {}, moneda: {}, monto: {}, comentario: {}, idUsuarioCobra: {}",
                requestParams.getIdRuta(), requestParams.getTipo(), requestParams.getEntidadFinanciera(), requestParams.getEntidadFinancieraDesc(),
                requestParams.getNumeroDocumento(), requestParams.getEntidadTarjeta(), requestParams.getFechaDeposito(), requestParams.getFechaVencimiento(),
                requestParams.getNoAutorizacion(), requestParams.getNoReferencia(), requestParams.getMoneda(), requestParams.getMonto(),
                requestParams.getComentario(), requestParams.getIdUsuarioCobra());


        String sqlQuery = "{ call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.SP_INSERTAR_TIPO_MEDIO_PAGO + "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            stmt.setInt(1, requestParams.getIdRuta());
            stmt.setString(2, requestParams.getTipo());
            stmt.setString(3, requestParams.getEntidadFinanciera());
            stmt.setString(4, requestParams.getEntidadFinancieraDesc());
            stmt.setString(5, requestParams.getNumeroDocumento());
            stmt.setString(6, requestParams.getEntidadTarjeta());
            stmt.setString(7, requestParams.getFechaDeposito());
            stmt.setString(8, requestParams.getFechaVencimiento());
            stmt.setString(9, requestParams.getNoAutorizacion());
            stmt.setString(10, requestParams.getNoReferencia());
            stmt.setString(11, requestParams.getMoneda());
            stmt.setString(12, requestParams.getMonto());
            stmt.setString(13, requestParams.getComentario());
            stmt.setInt(14, requestParams.getIdUsuarioCobra());

            stmt.execute();
            log.info("Stored procedure {} ejecutado correctamente para ruta: {}",
                    DatabaseConstants.SP_INSERTAR_TIPO_MEDIO_PAGO, requestParams.getIdRuta());
            return true;

        } catch (Exception ex) {
            log.error("Error al ejecutar {}: {}", DatabaseConstants.SP_INSERTAR_TIPO_MEDIO_PAGO, ex.getMessage(), ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.SP_INSERTAR_TIPO_MEDIO_PAGO, ex);
        }
    }

    @Override
    public boolean UpdateAvisoReciboRuta(AvisoReciboRutaRequest requestParams) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, DatabaseConstants.SP_UPDATE_AVISO_RECIBO_RUTA));
        log.info("Parámetros de entrada - ruta: {}, recibo: {}, usuario: {}, comentario: {}, fecha: {}, latitud: {}, longitud: {}",
                requestParams.getRuta(), requestParams.getRecibo(), requestParams.getUsuario(),
                requestParams.getComentario(), requestParams.getFecha(), requestParams.getLatitud(), requestParams.getLongitud());

        String sqlQuery = "{ call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.SP_UPDATE_AVISO_RECIBO_RUTA + "(?, ?, ?, ?, ?, ?, ?, ?) }";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            stmt.setString(1, requestParams.getRuta());
            stmt.setString(2, requestParams.getRecibo());
            stmt.setString(3, requestParams.getUsuario());

            if (requestParams.getFirma() != null) {
                byte[] firmaBytes = java.util.Base64.getDecoder().decode(requestParams.getFirma());
                stmt.setBytes(4, firmaBytes);
            } else {
                stmt.setBytes(4, null);
            }
            stmt.setString(5, requestParams.getComentario());

            String fechaFormatoEntrada = "dd/MM/yyyy";
            SimpleDateFormat sdf = new SimpleDateFormat(fechaFormatoEntrada);
            java.util.Date utilDate = sdf.parse(requestParams.getFecha().toString());
            java.sql.Date fecha = new java.sql.Date(utilDate.getTime());

            stmt.setDate(6, fecha);
            stmt.setString(7, requestParams.getLatitud());
            stmt.setString(8, requestParams.getLongitud());

            stmt.execute();
            log.info("Stored procedure {} ejecutado correctamente para ruta: {}",
                    DatabaseConstants.SP_UPDATE_AVISO_RECIBO_RUTA, requestParams.getRuta());
            return true;

        } catch (Exception ex) {
            log.error("Error al ejecutar {}: {}", DatabaseConstants.SP_UPDATE_AVISO_RECIBO_RUTA, ex.getMessage(), ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.SP_UPDATE_AVISO_RECIBO_RUTA, ex);
        }
    }

    @Override
    public String UpdateReciboRuta(ReciboRutaRequest requestParams) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, DatabaseConstants.SP_UPDATE_RECIBO_RUTA));
        log.info("Parámetros de entrada - ruta: {}, recibo: {}, latitud: {}, longitud: {}, usuario: {}",
                requestParams.getRuta(), requestParams.getRecibo(), requestParams.getLatitud(),
                requestParams.getLongitud(), requestParams.getUsuario());

        String sqlQuery = "{ call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.SP_UPDATE_RECIBO_RUTA + "(?, ?, ?, ?, ?,?) }";

        ReciboRutaRowMapper mapper = new ReciboRutaRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            stmt.setInt(1, requestParams.getRuta());
            stmt.setString(2, requestParams.getRecibo());
            stmt.setString(3, requestParams.getLatitud());
            stmt.setString(4, requestParams.getLongitud());
            stmt.setString(5, requestParams.getUsuario());
            stmt.registerOutParameter(6, Types.VARCHAR);

            stmt.execute();
            log.info("Procedimiento Almacenado {} ejecutado correctamente para ruta: {}",
                    DatabaseConstants.SP_UPDATE_RECIBO_RUTA, requestParams.getRuta());
            String resultado = stmt.getString(6);
            return resultado;

        } catch (Exception ex) {
            log.error("Error al ejecutar {}: {}", DatabaseConstants.SP_UPDATE_RECIBO_RUTA, ex.getMessage(), ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.SP_UPDATE_RECIBO_RUTA, ex);
        }
    }

    @Override
    public AutenticarUsuarioResponseUsuario AutenticarUsuario(String usuario, String clave) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, DatabaseConstants.FN_AUTENTICAR_USUARIO));
        log.info("Parámetros de entrada - usuario: {}", usuario);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.FN_AUTENTICAR_USUARIO +
                "(?, ?) AS resultado FROM dual";

        AutenticarUsuarioResponseUsuario usuarioResponse = null;
        AutenticarUsuarioRowMapper mapper = new AutenticarUsuarioRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {
            stmt.setString(1, usuario);
            stmt.setString(2, clave);
            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("resultado");
                        if (rs != null && rs.next()) {
                            int rouNum = 0;
                            usuarioResponse = mapper.mapRow(rs, rouNum++);
                            log.info("Usuario autenticado exitosamente - ID: {}, Usuario: {}",
                                    usuarioResponse.getIdUsuario(), usuarioResponse.getNombreUnicoUsuario());
                        } else {
                            log.warn("No se encontró usuario con las credenciales proporcionadas - usuario: {}", usuario);
                        }
                    }
                }
            }
            return usuarioResponse;

        } catch (Exception ex) {
            log.error("Error al ejecutar {}: {}", DatabaseConstants.FN_AUTENTICAR_USUARIO, ex.getMessage(), ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.FN_AUTENTICAR_USUARIO, ex);
        }
    }

    @Override
    public AutenticarUsuarioResponseUsuario AutenticarUsuarioAD(String usuario) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, DatabaseConstants.FN_AUTENTICAR_USUARIO_AD));
        log.info("Parámetros de entrada - usuario: {}", usuario);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.FN_AUTENTICAR_USUARIO_AD +
                "(?) AS resultado FROM dual";

        AutenticarUsuarioResponseUsuario usuarioResponse = null;
        AutenticarUsuarioRowMapper mapper = new AutenticarUsuarioRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {
            stmt.setString(1, usuario);
            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("resultado");
                        if (rs != null && rs.next()) {
                            int rouNum = 0;
                            usuarioResponse = mapper.mapRow(rs, rouNum++);
                            log.info("Usuario autenticado exitosamente - ID: {}, Usuario: {}",
                                    usuarioResponse.getIdUsuario(), usuarioResponse.getNombreUnicoUsuario());
                        } else {
                            log.warn("No se encontró usuario con las credenciales proporcionadas - usuario: {}", usuario);
                        }
                    }
                }
            }
            return usuarioResponse;
        } catch (Exception ex) {
            log.error("Error al ejecutar {}: {}", DatabaseConstants.FN_AUTENTICAR_USUARIO_AD, ex.getMessage(), ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.FN_AUTENTICAR_USUARIO_AD, ex);
        }
    }

    @Override
    public ArrayList<ObtenerRolesUsuarioResponseRoles> ObtenerRolesUsuario(Integer idUsuario) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, DatabaseConstants.FN_OBTENER_ROLES_USUARIO));
        log.info("Parámetros de entrada - idUsuario: {}", idUsuario);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.FN_OBTENER_ROLES_USUARIO +
                "(?) AS resultado FROM dual";

        ArrayList<ObtenerRolesUsuarioResponseRoles> listaRoles = new ArrayList<>();
        ObtenerRolesUsuarioRowMapper mapper = new ObtenerRolesUsuarioRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {
            stmt.setInt(1, idUsuario);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("resultado");
                        if (rs != null) {
                            while (rs.next()) {
                                ObtenerRolesUsuarioResponseRoles rol = mapper.mapRow(rs, 0);
                                listaRoles.add(rol);
                            }
                            log.info("Se obtuvieron {} roles para el usuario ID: {}", listaRoles.size(), idUsuario);
                        } else {
                            log.info("No se encontraron roles para el usuario ID: {}", idUsuario);
                        }
                    }
                }
            }

            return listaRoles;

        } catch (Exception ex) {
            log.error("Error al ejecutar {}: {}", DatabaseConstants.FN_OBTENER_ROLES_USUARIO, ex.getMessage(), ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.FN_OBTENER_ROLES_USUARIO, ex);
        }
    }

    @Override
    public ArrayList<CajeroUsuarioResponseCajeros> ListarCajeroUsuario(Integer usuario) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, DatabaseConstants.FN_LISTAR_CAJERO_USUARIO));
        log.info("Parámetros de entrada - usuario: {}", usuario);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.FN_LISTAR_CAJERO_USUARIO +
                "(?) AS resultado FROM dual";

        ArrayList<CajeroUsuarioResponseCajeros> listaCajeros = new ArrayList<>();
        CajeroUsuarioRowMapper mapper = new CajeroUsuarioRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            stmt.setInt(1, usuario);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("resultado");
                        if (rs != null) {
                            while (rs.next()) {
                                CajeroUsuarioResponseCajeros cajero = mapper.mapRow(rs, 0);
                                listaCajeros.add(cajero);
                            }
                            log.info("Se obtuvieron {} cajeros para el usuario ID: {}", listaCajeros.size(), usuario);
                        } else {
                            log.info("No se encontraron cajeros para el usuario ID: {}", usuario);
                        }
                    }
                }
            }
            return listaCajeros;
        } catch (Exception ex) {
            log.error("Error al ejecutar {}: {}", DatabaseConstants.FN_LISTAR_CAJERO_USUARIO, ex.getMessage(), ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.FN_LISTAR_CAJERO_USUARIO, ex);
        }
    }

    @Override
    public ArrayList<AccionesPorRolesResponseAcciones> ObtenerAccionesPorRoles(String roles) {
        log.info(String.format(DatabaseConstants.LOG_PROC_CALL, DatabaseConstants.FN_OBTENER_ACCIONES_POR_ROLES));
        log.info("Parámetros de entrada - roles: {}", roles);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.FN_OBTENER_ACCIONES_POR_ROLES +
                "(?) AS resultado FROM dual";

        ArrayList<AccionesPorRolesResponseAcciones> listaAcciones = new ArrayList<>();
        AccionesPorRolesRowMapper mapper = new AccionesPorRolesRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {
            stmt.setString(1, roles);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("resultado");
                        if (rs != null) {
                            while (rs.next()) {
                                AccionesPorRolesResponseAcciones accion = mapper.mapRow(rs, 0);
                                listaAcciones.add(accion);
                            }
                            log.info("Se obtuvieron {} acciones para los roles: {}", listaAcciones.size(), roles);
                        } else {
                            log.info("No se encontraron acciones para los roles: {}", roles);
                        }
                    }
                }
            }
            return listaAcciones;
        } catch (Exception ex) {
            log.error("Error al ejecutar {}: {}", DatabaseConstants.FN_OBTENER_ACCIONES_POR_ROLES, ex.getMessage(), ex);
            throw new DataRetrievalFailureException("Error en " + DatabaseConstants.FN_OBTENER_ACCIONES_POR_ROLES, ex);
        }
    }

    @Override
    public RutasCobradasResponse getRutasCobradas() {
        log.info("Ejecutando función getRutasCobradas");

        String sqlCall = "{? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_OBTENER_RUTAS_UBICACION + "()}";
        List<String> result = new ArrayList<>();
        RutasCobradasRowMapper mapper = new RutasCobradasRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlCall)) {

            stmt.registerOutParameter(1, OracleTypes.CURSOR);
            stmt.execute();

            try (ResultSet rs = (ResultSet) stmt.getObject(1)) {
                int rowNum = 0;
                while (rs.next()) {
                    result.add(mapper.mapRow(rs, rowNum++));
                }
            }

            log.info("Función ejecutada correctamente. Rutas encontradas: {}", result.size());

            // Crear una nueva instancia de respuesta y establecer la lista de rutas
            RutasCobradasResponse response = new RutasCobradasResponse();
            response.setIdRutas(result);

            // Log para depuración
            log.debug("Respuesta creada: {}", response);

            return response;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getRutasCobradas: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las rutas cobradas", e);
        }
    }

    @Override
    public List<RutaLocalizacionResponse> obtenerRutasLocalizacion(
            LocalDate fechaInicio, LocalDate fechaFin, Integer idRuta) {

        log.info("Ejecutando función obtenerRutasLocalizacion con fechaInicio={}, fechaFin={}, idRuta={}",
                fechaInicio, fechaFin, idRuta);

        // Formatear las fechas al formato esperado por la función (DD-MM-YYYY)
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        String fechaInicioStr = fechaInicio != null ? fechaInicio.format(formatter) : null;
        String fechaFinStr = fechaFin != null ? fechaFin.format(formatter) : null;

        log.info("Fechas formateadas: fechaInicioStr={}, fechaFinStr={}", fechaInicioStr, fechaFinStr);

        // Construir la consulta SQL
        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_OBTENER_RUTAS_LOCALIZACION +
                "(?, ?, ?) AS rutas FROM dual";

        log.info("SQL Query: {}", sqlQuery);

        List<RutaLocalizacionResponse> result = new ArrayList<>();
        RutaLocalizacionRowMapper mapper = new RutaLocalizacionRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros como strings con el formato correcto
            if (fechaInicioStr != null) {
                stmt.setString(1, fechaInicioStr);
            } else {
                stmt.setNull(1, java.sql.Types.VARCHAR);
            }

            if (fechaFinStr != null) {
                stmt.setString(2, fechaFinStr);
            } else {
                stmt.setNull(2, java.sql.Types.VARCHAR);
            }

            if (idRuta != null) {
                stmt.setInt(3, idRuta);
            } else {
                stmt.setNull(3, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("rutas");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función obtenerRutasLocalizacion: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener las rutas con localización", e);
        }
    }

    @Override
    public List<LocalizacionPagosResponse> obtenerLocalizacionPagos(
            LocalDate fechaInicio, LocalDate fechaFin, Integer idRuta) {

        log.info("Ejecutando función obtenerLocalizacionPagos con fechaInicio={}, fechaFin={}, idRuta={}",
                fechaInicio, fechaFin, idRuta);

        // Formatear las fechas al formato esperado por la función (DD-MM-YYYY)
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        String fechaInicioStr = fechaInicio != null ? fechaInicio.format(formatter) : null;
        String fechaFinStr = fechaFin != null ? fechaFin.format(formatter) : null;

        log.info("Fechas formateadas: fechaInicioStr={}, fechaFinStr={}", fechaInicioStr, fechaFinStr);

        // Construir la consulta SQL
        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_OBTENER_LOCALIZACION_PAGOS +
                "(?, ?, ?) AS pagos FROM dual";

        log.info("SQL Query: {}", sqlQuery);

        List<LocalizacionPagosResponse> result = new ArrayList<>();
        LocalizacionPagosRowMapper mapper = new LocalizacionPagosRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros como strings con el formato correcto
            if (fechaInicioStr != null) {
                stmt.setString(1, fechaInicioStr);
            } else {
                stmt.setNull(1, java.sql.Types.VARCHAR);
            }

            if (fechaFinStr != null) {
                stmt.setString(2, fechaFinStr);
            } else {
                stmt.setNull(2, java.sql.Types.VARCHAR);
            }

            if (idRuta != null) {
                stmt.setInt(3, idRuta);
            } else {
                stmt.setNull(3, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("pagos");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Pagos encontrados: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función obtenerLocalizacionPagos: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la localización de pagos", e);
        }
    }

    @Override
    public List<LlenarDetalleCierreCajaResponse> llenarDetalleCierreCaja(Integer idUsuario) {
        log.info("Ejecutando función llenarDetalleCierreCaja con idUsuario={}", idUsuario);

        // Construir la consulta SQL
        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_INFO_RUTA_CIERRE_CAJA_DETALLE +
                "(?) AS detalles FROM dual";

        log.info("SQL Query: {}", sqlQuery);

        List<LlenarDetalleCierreCajaResponse> result = new ArrayList<>();
        LlenarDetalleCierreCajaRowMapper mapper = new LlenarDetalleCierreCajaRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            if (idUsuario != null) {
                stmt.setInt(1, idUsuario);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("detalles");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Detalles encontrados: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función llenarDetalleCierreCaja: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el detalle de cierre de caja", e);
        }
    }

    @Override
    public List<InfoRutaCierreCajaResponse> getInfoRutaCierreCaja(Integer idUsuario) {
        log.info("Ejecutando función getInfoRutaCierreCaja con idUsuario={}", idUsuario);

        // Construir la consulta SQL
        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_INFO_RUTA_CIERRE_CAJA +
                "(?) AS rutas FROM dual";

        log.info("SQL Query: {}", sqlQuery);

        List<InfoRutaCierreCajaResponse> result = new ArrayList<>();
        InfoRutaCierreCajaRowMapper mapper = new InfoRutaCierreCajaRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            if (idUsuario != null) {
                stmt.setInt(1, idUsuario);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("rutas");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Rutas encontradas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getInfoRutaCierreCaja: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener información de rutas para cierre de caja", e);
        }
    }

    @Override
    public List<InfoRutaResponse> getInfoRuta(Integer idUsuario, String fecha) {
        log.info("Ejecutando función getInfoRuta con idUsuario={}, fecha={}", idUsuario, fecha);

        // Validar formato de fecha (esperamos DD/MM/YYYY)
        if (!fecha.matches("\\d{2}/\\d{2}/\\d{4}")) {
            log.error("Formato de fecha incorrecto: {}. Debe ser DD/MM/YYYY", fecha);
            throw new IllegalArgumentException("Formato de fecha incorrecto. Debe ser DD/MM/YYYY");
        }

        // Construir la consulta SQL para llamar a la función
        String sqlQuery = "{? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_INFO_MOD_RUTA + "(?, ?)}";

        log.info("SQL Query: {}", sqlQuery);
        log.info("Parámetros: idUsuario={}, fecha={}", idUsuario, fecha);

        List<InfoRutaResponse> result = new ArrayList<>();

        try (Connection conn = dataSource.getConnection();
             CallableStatement cs = conn.prepareCall(sqlQuery)) {

            // Registrar el parámetro de salida como REF_CURSOR
            cs.registerOutParameter(1, OracleTypes.CURSOR);

            // Establecer parámetros de entrada
            cs.setInt(2, idUsuario);

            // Asegurarse de que la fecha se pasa exactamente como se espera
            cs.setString(3, fecha);  // Pasar la fecha en formato DD/MM/YYYY

            log.info("Ejecutando consulta con parámetros exactos: idUsuario={}, fecha='{}' (longitud: {})",
                    idUsuario, fecha, fecha.length());

            // Ejecutar la consulta
            cs.execute();

            // Obtener el resultado como ResultSet
            try (ResultSet rs = (ResultSet) cs.getObject(1)) {
                InfoRutaRowMapper mapper = new InfoRutaRowMapper();
                int rowNum = 0;
                while (rs.next()) {
                    InfoRutaResponse response = mapper.mapRow(rs, rowNum++);
                    result.add(response);
                    log.debug("Fila procesada: {}", response);
                }
            }

            log.info("Consulta ejecutada con éxito. Filas obtenidas: {}", result.size());
            return result;
        } catch (SQLException e) {
            log.error("Error SQL al ejecutar la función getInfoRuta: {}", e.getMessage(), e);
            log.error("SQLState: {}, ErrorCode: {}", e.getSQLState(), e.getErrorCode());
            throw new RuntimeException("Error al obtener información de rutas", e);
        } catch (Exception e) {
            log.error("Error general al ejecutar la función getInfoRuta: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener información de rutas", e);
        }
    }

    @Override
    public InfoPromedioResponse getPromedios(Integer idUsuario, Integer ruta, Integer tipo, String fecha) {
        log.info("Ejecutando función getPromedios con idUsuario={}, ruta={}, tipo={}, fecha={}",
                idUsuario, ruta, tipo, fecha);

        // Construir la consulta SQL
        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_PROMEDIOS +
                "(?, ?, ?, ?) AS promedio FROM dual";


        InfoPromedioResponse result = new InfoPromedioResponse();

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sqlQuery)) {

            // Configurar parámetros
            if (idUsuario != null) {
                stmt.setInt(1, idUsuario);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            if (ruta != null) {
                stmt.setInt(2, ruta);
            } else {
                stmt.setNull(2, java.sql.Types.INTEGER);
            }

            if (tipo != null) {
                stmt.setInt(3, tipo);
            } else {
                stmt.setNull(3, java.sql.Types.INTEGER);
            }

//            if (fecha != null) {
//                stmt.setString(4, fecha);
//            } else {
//                stmt.setNull(4, java.sql.Types.VARCHAR);
//            }
            // Fecha es opcional
            if (fecha != null && !fecha.trim().isEmpty()) {
                stmt.setString(4, fecha);
            } else {
                stmt.setNull(4, java.sql.Types.VARCHAR);
            }


            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    result.setPromedio(rs.getString("promedio"));
                } else {
                    result.setPromedio("0");
                }
            }

            log.info("Función ejecutada correctamente. Promedio: {}", result.getPromedio());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getPromedios: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el promedio", e);
        }
    }

    @Override
    public List<ModRutaPolizaFacturasResponse> getModRutaPolizaFacturas(Integer idRuta) {
        log.info("Ejecutando función getModRutaPolizaFacturas con idRuta={}", idRuta);

        // Construir la consulta SQL
        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_MOD_RUTA_POLIZAFACTURAS +
                "(?) AS polizas FROM dual";

        log.info("SQL Query: {}", sqlQuery);

        List<ModRutaPolizaFacturasResponse> result = new ArrayList<>();
        ModRutaPolizaFacturasRowMapper mapper = new ModRutaPolizaFacturasRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            if (idRuta != null) {
                stmt.setInt(1, idRuta);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("polizas");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Pólizas encontradas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getModRutaPolizaFacturas: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener pólizas y facturas de la ruta", e);
        }
    }

    @Override
    public List<ModRutaPolizaResponse> getModRutaPoliza(Integer idRuta) {
        log.info("Ejecutando función getModRutaPoliza con idRuta={}", idRuta);

        // Construir la consulta SQL
        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_MOD_RUTA_POLIZA +
                "(?) AS polizas FROM dual";

        log.info("SQL Query: {}", sqlQuery);

        List<ModRutaPolizaResponse> result = new ArrayList<>();
        ModRutaPolizaRowMapper mapper = new ModRutaPolizaRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            if (idRuta != null) {
                stmt.setInt(1, idRuta);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("polizas");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Pólizas encontradas: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getModRutaPoliza: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener pólizas de la ruta", e);
        }
    }

    @Override
    public List<InfoRecibosPolizaRutaCobradosResponse> getInfoRecibosPolizaRutaCobrados(Integer idRuta, String idePol, String numCertis) {
        log.info("Ejecutando función getInfoRecibosPolizaRutaCobrados con idRuta={}, idePol={}, numCertis={}", idRuta, idePol, numCertis);

        // Construir la consulta SQL
        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_RUTA_POLIZA_RECIBO_COBRADOS +
                "(?, ?, ?) AS recibos FROM dual";

        log.info("SQL Query: {}", sqlQuery);

        List<InfoRecibosPolizaRutaCobradosResponse> result = new ArrayList<>();
        InfoRecibosPolizaRutaCobradosRowMapper mapper = new InfoRecibosPolizaRutaCobradosRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            if (idRuta != null) {
                stmt.setInt(1, idRuta);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            if (idePol != null && !idePol.isEmpty()) {
                stmt.setString(2, idePol);
            } else {
                stmt.setNull(2, java.sql.Types.VARCHAR);
            }

            if (numCertis != null && !numCertis.isEmpty()) {
                stmt.setString(3, numCertis);
            } else {
                stmt.setNull(3, java.sql.Types.VARCHAR);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("recibos");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Recibos encontrados: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getInfoRecibosPolizaRutaCobrados: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener recibos cobrados de la póliza en la ruta", e);
        }
    }

    @Override
    public List<InfoRecibosPolizaRutaResponse> getInfoRecibosPolizaRuta(Integer idRuta, String idePol, String numCertis) {
        log.info("Ejecutando función getInfoRecibosPolizaRuta con idRuta={}, idePol={}, numCertis={}", idRuta, idePol, numCertis);

        // Construir la consulta SQL
        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_RUTA_POLIZA_RECIBO +
                "(?, ?, ?) AS recibos FROM dual";

        log.info("SQL Query: {}", sqlQuery);

        List<InfoRecibosPolizaRutaResponse> result = new ArrayList<>();
        InfoRecibosPolizaRutaRowMapper mapper = new InfoRecibosPolizaRutaRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            if (idRuta != null) {
                stmt.setInt(1, idRuta);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
            }

            if (idePol != null && !idePol.isEmpty()) {
                stmt.setString(2, idePol);
            } else {
                stmt.setNull(2, java.sql.Types.VARCHAR);
            }

            if (numCertis != null && !numCertis.isEmpty()) {
                stmt.setString(3, numCertis);
            } else {
                stmt.setNull(3, java.sql.Types.VARCHAR);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("recibos");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función ejecutada correctamente. Recibos encontrados: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getInfoRecibosPolizaRuta: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener recibos de la póliza en la ruta", e);
        }
    }

    @Override
    public List<InfoFirmaReciboResponse> getInfoFirmaRecibo(String idRuta, String recibo) {
        log.info("Ejecutando función getInfoFirmaRecibo con idRuta={}, recibo={}", idRuta, recibo);

        // Construir la consulta SQL
        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                DatabaseConstants.F_OBTENER_FIRMA +
                "(?, ?) AS firma FROM dual";

        log.info("SQL Query: {}", sqlQuery);

        List<InfoFirmaReciboResponse> result = new ArrayList<>();
        InfoFirmaReciboRowMapper mapper = new InfoFirmaReciboRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Configurar parámetros
            if (idRuta != null && !idRuta.isEmpty()) {
                stmt.setString(1, idRuta);
            } else {
                stmt.setNull(1, java.sql.Types.VARCHAR);
            }

            if (recibo != null && !recibo.isEmpty()) {
                stmt.setString(2, recibo);
            } else {
                stmt.setNull(2, java.sql.Types.VARCHAR);
            }

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("firma");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                        rs.close();
                    }
                }
            }

            log.info("Función ejecutada correctamente. Registros encontrados: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getInfoFirmaRecibo: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener información de firma del recibo", e);
        }
    }

    @Override
    public List<CobradoresResponse> getCobradores() {
        log.info("Ejecutando función getCobradores");

        List<CobradoresResponse> result = new ArrayList<>();

        try (Connection conn = dataSource.getConnection()) {
            // Usar la sintaxis correcta para llamar a una función Oracle que devuelve un cursor
            String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                    DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                    DatabaseConstants.F_LISTAR_COBRADOR + "() }";

            log.info("SQL Query: {}", sqlQuery);

            try (CallableStatement stmt = conn.prepareCall(sqlQuery)) {
                // Registrar el parámetro de salida como un cursor Oracle
                stmt.registerOutParameter(1, oracle.jdbc.OracleTypes.CURSOR);

                // Ejecutar la llamada
                stmt.execute();

                // Obtener el cursor resultante
                try (ResultSet rs = (ResultSet) stmt.getObject(1)) {
                    CobradoresRowMapper mapper = new CobradoresRowMapper();
                    int rowNum = 0;

                    while (rs != null && rs.next()) {
                        result.add(mapper.mapRow(rs, rowNum++));
                    }
                }
            }

            log.info("Función ejecutada correctamente. Cobradores encontrados: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getCobradores: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la lista de cobradores", e);
        }
    }

    @Override
    public List<TipoPagoResponse> getTipoPago() {
        log.info("Ejecutando función getTipoPago");

        List<TipoPagoResponse> result = new ArrayList<>();

        try (Connection conn = dataSource.getConnection()) {
            // Usar la sintaxis correcta para llamar a una función Oracle que devuelve un cursor
            String sqlQuery = "{ ? = call " + DatabaseConstants.SCHEMA_NAME + "." +
                    DatabaseConstants.GC_K_COBRO_MOVIL_MGT + "." +
                    DatabaseConstants.F_LISTAR_TIPO_PAGO + "() }";

            log.info("SQL Query: {}", sqlQuery);

            try (CallableStatement stmt = conn.prepareCall(sqlQuery)) {
                // Registrar el parámetro de salida como un cursor Oracle
                stmt.registerOutParameter(1, oracle.jdbc.OracleTypes.CURSOR);

                // Ejecutar la llamada
                stmt.execute();

                // Obtener el cursor resultante
                try (ResultSet rs = (ResultSet) stmt.getObject(1)) {
                    TipoPagoRowMapper mapper = new TipoPagoRowMapper();
                    int rowNum = 0;

                    while (rs != null && rs.next()) {
                        result.add(mapper.mapRow(rs, rowNum++));
                    }
                }
            }

            log.info("Función ejecutada correctamente. Tipos de pago encontrados: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getTipoPago: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la lista de tipos de pago", e);
        }
    }
}
