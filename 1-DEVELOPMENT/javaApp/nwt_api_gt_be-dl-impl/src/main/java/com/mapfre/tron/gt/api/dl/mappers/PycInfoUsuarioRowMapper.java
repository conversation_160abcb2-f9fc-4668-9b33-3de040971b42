package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycInfoUsuario;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycInfoUsuario.
 */
@Slf4j
public class PycInfoUsuarioRowMapper implements RowMapper<PycInfoUsuario> {

    @Override
    public PycInfoUsuario mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycInfoUsuario", rowNum);
        
        PycInfoUsuario infoUsuario = new PycInfoUsuario();
        
        try {
            // Datos básicos del usuario
            infoUsuario.setIdUsuario(rs.getInt("ID_USUARIO"));
            infoUsuario.setPrimerNombre(rs.getString("PRIMER_NOMBRE"));
            infoUsuario.setSegundoNombre(rs.getString("SEGUNDO_NOMBRE"));
            infoUsuario.setPrimerApellido(rs.getString("PRIMER_APELLIDO"));
            infoUsuario.setSegundoApellido(rs.getString("SEGUNDO_APELLIDO"));
            infoUsuario.setFechaCreacion(rs.getString("FECHA_CREACION"));
            infoUsuario.setEstado(rs.getString("ESTADO"));
            infoUsuario.setFechaBaja(rs.getString("FECHA_BAJA"));
            infoUsuario.setNumaOUsuario(rs.getString("NUMA_O_USUARIO"));
            infoUsuario.setEmail(rs.getString("EMAIL"));
            infoUsuario.setGenero(rs.getString("GENERO"));
            
            // Información de área y departamento
            infoUsuario.setIdArea(rs.getInt("ID_AREA"));
            infoUsuario.setArea(rs.getString("AREA"));
            infoUsuario.setIdDepartamento(rs.getInt("ID_DEPARTAMENTO"));
            infoUsuario.setDepartamento(rs.getString("DEPARTAMENTO"));
            
            // Información de perfil y proceso
            infoUsuario.setIdPerfil(rs.getInt("ID_PERFIL"));
            infoUsuario.setNombrePerfil(rs.getString("NOMBRE_PERFIL"));
            infoUsuario.setIndDefault(rs.getString("IND_DEFAULT"));
            infoUsuario.setIdProceso(rs.getInt("ID_PROCESO"));
            
            // URLs y configuraciones
            infoUsuario.setUrlPerfil(rs.getString("URL_PERFIL"));
            infoUsuario.setPathPerfil(rs.getString("PATH_PERFIL"));
            infoUsuario.setUrlInicio(rs.getString("URL_INICIO"));
            
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return infoUsuario;
    }
}
