package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycEstadoUsu;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycEstadoUsu.
 */
@Slf4j
public class PycEstadoUsuRowMapper implements RowMapper<PycEstadoUsu> {

    @Override
    public PycEstadoUsu mapRow(ResultSet rs, int rowNum) throws SQLException {

        PycEstadoUsu estadoUsuario = new PycEstadoUsu();

        estadoUsuario.setNumaOUsuario(rs.getString("NUMA_O_USUARIO"));
        estadoUsuario.setEstado(rs.getString("ESTADO"));
        
        return estadoUsuario;
    }
}
