package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PLE_DepositoPlanilla;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PLE_DepositoPlanilla.
 */
@Slf4j
public class DepositoPlanillaRowMapper implements RowMapper<PLE_DepositoPlanilla> {

    @Override
    public PLE_DepositoPlanilla mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PLE_DepositoPlanilla", rowNum);
        
        PLE_DepositoPlanilla response = new PLE_DepositoPlanilla();
        
        try {
            response.setId(rs.getInt("id"));
            response.setIdplanilla(rs.getInt("idplanilla"));
            response.setCodmedio(rs.getString("codmedio"));
            response.setMedioDeposito(rs.getString("medio_deposito"));
            response.setMonto(rs.getBigDecimal("monto"));
            response.setDocumento(rs.getString("documento"));
            response.setFechaDeposito(rs.getDate("fecha_deposito"));
            response.setCodbanco(rs.getString("codbanco"));
            response.setNombreBanco(rs.getString("nombre_banco"));
            response.setDescarga(rs.getString("descarga"));
            response.setRegistro(rs.getTimestamp("registro"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
