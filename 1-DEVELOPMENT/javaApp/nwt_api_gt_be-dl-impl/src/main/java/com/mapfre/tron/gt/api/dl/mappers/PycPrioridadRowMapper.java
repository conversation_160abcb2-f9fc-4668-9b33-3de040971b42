package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycPrioridad;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycPrioridad.
 */
@Slf4j
public class PycPrioridadRowMapper implements RowMapper<PycPrioridad> {

    @Override
    public PycPrioridad mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycPrioridad", rowNum);
        
        PycPrioridad prioridad = new PycPrioridad();
        
        try {
            prioridad.setCodigo(rs.getString("CODIGO"));
            prioridad.setPrioridad(rs.getString("PRIORIDAD"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return prioridad;
    }
}
