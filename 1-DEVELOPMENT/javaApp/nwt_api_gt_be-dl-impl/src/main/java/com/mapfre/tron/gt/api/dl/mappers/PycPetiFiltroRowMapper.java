package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycPetiFiltro;
import static com.mapfre.tron.gt.api.sr.CacheApi.log;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycPetiFiltro.
 */
@Slf4j
public class PycPetiFiltroRowMapper implements RowMapper<PycPetiFiltro> {

    @Override
    public PycPetiFiltro mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycPetiFiltro", rowNum);

        PycPetiFiltro petiFiltro = new PycPetiFiltro();

        try {
            petiFiltro.setIdPeticion(rs.getInt("ID_PETICION"));
            petiFiltro.setNombrePeticion(rs.getString("NOMBRE_PETICION"));
            petiFiltro.setDescripcionPeticion(rs.getString("DESCRIPCION_PETICION"));

            // Mapear fechas como String (siguiendo el patrón de otros mappers)
            petiFiltro.setFechaCreacion(rs.getString("FECHA_CREACION"));

            petiFiltro.setIdTipo(rs.getInt("ID_TIPO"));
            petiFiltro.setIdUsuarioSolicitante(rs.getInt("ID_USUARIO_SOLICITANTE"));
            petiFiltro.setUsuario(rs.getString("USUARIO"));

            // Mapear fechas como String
            petiFiltro.setFechaInicio(rs.getString("FECHA_INICIO"));
            petiFiltro.setFechaFin(rs.getString("FECHA_FIN"));

            petiFiltro.setTotalHoras(rs.getDouble("TOTAL_HORAS"));
            petiFiltro.setPorcentajeBase(rs.getDouble("PORCENTAJE_BASE"));
            petiFiltro.setPorcentajeReal(rs.getDouble("PORCENTAJE_REAL"));
            petiFiltro.setIdEstado(rs.getInt("ID_ESTADO"));
            petiFiltro.setIdPerfil(rs.getInt("ID_PERFIL"));
            petiFiltro.setNombreDepartamento(rs.getString("NOMBRE_DEPARTAMENTO"));
            petiFiltro.setNombreArea(rs.getString("NOMBRE_AREA"));
            petiFiltro.setNombrePerfil(rs.getString("NOMBRE_PERFIL"));
            petiFiltro.setNombreEstado(rs.getString("NOMBRE_ESTADO"));
            petiFiltro.setDescripcionTipoPeticion(rs.getString("DESCRIPCION_TIPO_PETICION"));
            petiFiltro.setOrigen(rs.getString("ORIGEN"));
            petiFiltro.setCodClarity(rs.getString("COD_CLARITY"));
            petiFiltro.setCodcia(rs.getString("CODCIA"));
            petiFiltro.setPrioridad(rs.getString("PRIORIDAD"));
            petiFiltro.setIdPeticionSiguiente(rs.getInt("ID_PETICION_SIGUIENTE"));

        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }

        return petiFiltro;
    }
}
