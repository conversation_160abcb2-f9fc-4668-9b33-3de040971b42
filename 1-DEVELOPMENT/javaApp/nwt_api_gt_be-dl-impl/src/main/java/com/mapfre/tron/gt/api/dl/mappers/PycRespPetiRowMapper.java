package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycRespPeti;

import lombok.extern.slf4j.Slf4j;

/**
 * RowMapper para mapear los resultados de la función FN_RESPONSABLE_PETICION a objetos PycRespPeti.
 * 
 * Esta clase se encarga de convertir cada fila del ResultSet retornado por la función Oracle
 * TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_RESPONSABLE_PETICION en un objeto PycRespPeti.
 * 
 * La función retorna información de responsables de petición incluyendo:
 * - Datos del solicitante de la petición
 * - Usuarios asignados con sus perfiles y aplicaciones
 * - Estado de conexión de cada usuario
 * - Información detallada de asignaciones y fechas
 */
@Slf4j
public class PycRespPetiRowMapper implements RowMapper<PycRespPeti> {

    @Override
    public PycRespPeti mapRow(ResultSet rs, int rowNum) throws SQLException {
        PycRespPeti respPeti = new PycRespPeti();
        
        try {
            // Mapear USUARIO (usuario base de datos del responsable)
            respPeti.setUsuario(rs.getString("USUARIO"));
            
            // Mapear NOMBRE (nombre completo del responsable)
            respPeti.setNombre(rs.getString("NOMBRE"));
            
            // Mapear CARGO (cargo o rol del responsable en la petición)
            respPeti.setCargo(rs.getString("CARGO"));
            
            // Mapear TITLE (información detallada del responsable con fechas y contexto)
            respPeti.setTitle(rs.getString("TITLE"));
            
            // Mapear URL_PERFIL (URL de la imagen de perfil del usuario)
            respPeti.setUrlPerfil(rs.getString("URL_PERFIL"));
            
            // Mapear GENERO (género del usuario M/F)
            respPeti.setGenero(rs.getString("GENERO"));
            
            // Mapear INDICADOR (indicador de posición en la interfaz right/left)
            respPeti.setIndicador(rs.getString("INDICADOR"));
            
            // Mapear FECHA (fecha de registro o asignación) - siguiendo el patrón de otros mappers
            Timestamp fechaTimestamp = rs.getTimestamp("FECHA");
            if (fechaTimestamp != null) {
                respPeti.setFecha(fechaTimestamp.toString());
            } else {
                respPeti.setFecha(null);
            }
            
            // Mapear CONNECTED (estado de conexión del usuario on/off)
            respPeti.setConnected(rs.getString("CONNECTED"));
            
        } catch (SQLException e) {
            log.error("Error al mapear columna en PycRespPetiRowMapper: {}", e.getMessage(), e);
            throw e;
        }
        
        return respPeti;
    }
}
