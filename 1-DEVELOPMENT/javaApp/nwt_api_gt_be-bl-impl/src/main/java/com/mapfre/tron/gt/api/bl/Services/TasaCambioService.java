package com.mapfre.tron.gt.api.bl.Services;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;

import org.w3c.dom.Document;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.net.HttpURLConnection;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;

@Service
@Slf4j
public class TasaCambioService implements ITasaCambioService {

    @Value("${app.env.soap.banguat.endpoint}")
    private String SOAP_ENDPOINT;

    @Value("${app.env.soap.banguat.action}")
    private String SOAP_ACTION;

    @Override
    public double getTipoCambioDia() {
        log.info("The getTipoCambioDia service has been called!");
        try {
            String soapRequest =
                    "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                            "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" " +
                            "xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" " +
                            "xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
                            "  <soap:Body>\n" +
                            "    <TipoCambioDia xmlns=\"http://www.banguat.gob.gt/variables/ws/\" />\n" +
                            "  </soap:Body>\n" +
                            "</soap:Envelope>";

            URL url = new URL(SOAP_ENDPOINT);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();

            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "text/xml; charset=utf-8");
            conn.setRequestProperty("SOAPAction", SOAP_ACTION);
            conn.setDoOutput(true);

            try (OutputStream os = conn.getOutputStream()) {
                os.write(soapRequest.getBytes("UTF-8"));
            }

            StringBuilder response = new StringBuilder();
            try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
                String line;
                while ((line = br.readLine()) != null) {
                    response.append(line).append("\n");
                }
            }

            String tipoCambio = parseTipoCambio(response.toString());
            return Double.parseDouble(tipoCambio);

        } catch (IllegalStateException e) {
            log.warn("Error de lógica: {}", e.getMessage());
            throw e;
        } catch (Exception ex) {
            log.error("Error inesperado al obtener el tipo de cambio del día", ex);
            throw new RuntimeException("Error inesperado al obtener el tipo de cambio del día", ex);
        }

    }

    private String parseTipoCambio(String xml) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);

            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(xml.getBytes("UTF-8")));

            NodeList nodes = doc.getElementsByTagName("referencia");
            if (nodes.getLength() > 0) {
                return nodes.item(0).getTextContent();
            } else {
                log.warn("No se encontró el nodo <referencia> en el XML");
                return null;
            }
        } catch (Exception ex) {
            log.error("Error al parsear el tipo de cambio desde el XML", ex);
            throw new RuntimeException("Error al parsear el tipo de cambio desde el XML", ex);
        }
    }

}
