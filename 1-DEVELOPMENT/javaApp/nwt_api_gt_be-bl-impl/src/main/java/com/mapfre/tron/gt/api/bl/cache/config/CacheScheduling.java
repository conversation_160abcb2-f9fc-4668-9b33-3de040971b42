package com.mapfre.tron.gt.api.bl.cache.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * lass to schedule cache cleaning.
 */
@Slf4j
@Component
public class CacheScheduling {

	@Autowired
	private ICacheService iCacheService;

	/**
	 * Cleans caches every day at 02:00
	 */
	@Scheduled(cron = "${cache.clean.cron.expression:0 0 2 * * ?}")
	private void evictAllCachesWithCron() {
		try {
			iCacheService.evictAllCaches();
		} catch (Exception e) {
			log.warn("Scheduled Cache Eviction Failed", e);
		}
		log.warn("Scheduled Cache Eviction Succesful");
	}
}