package com.mapfre.tron.gt.api.bl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapfre.tron.gt.api.bl.BaseService;
import com.mapfre.tron.gt.api.bl.IBlFirmaElectronica;
import com.mapfre.tron.gt.api.dl.IDlFirmaElectronica;
import com.mapfre.tron.gt.api.model.PLE_MedioPagoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_DepositoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_IdentificadorMedioPago;
import com.mapfre.tron.gt.api.model.PLE_DetallePlanilla;
import com.mapfre.tron.gt.api.model.PLE_CuentaBanco;
import com.mapfre.tron.gt.api.model.PLE_TotalPagoPlanilla;
import com.mapfre.tron.gt.api.model.PLE_MedioPago;
import com.mapfre.tron.gt.api.model.PLE_TipoPlanilla;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class BlFirmaElectronicaImpl extends BaseService implements IBlFirmaElectronica {
	
	@Autowired
	private IDlFirmaElectronica iDlFirmaElectronica;

	@Override
	public List<PLE_MedioPagoPlanilla> buscarMediosPagoPlanilla(Integer idPlanilla) {
		log.info("The buscarMediosPagoPlanilla service had been called!");
		
		resetSession();

		return iDlFirmaElectronica.buscarMediosPagoPlanilla(idPlanilla);
	}

	@Override
	public List<PLE_DepositoPlanilla> buscarDepositosPlanilla(Integer idPlanilla) {
		log.info("The buscarDepositosPlanilla service had been called!");
		
		resetSession();

		return iDlFirmaElectronica.buscarDepositosPlanilla(idPlanilla);
	}

	@Override
	public List<PLE_IdentificadorMedioPago> buscarIdentificadorMedioPago(String sistema, String moneda, String medio, String tipo) {
		log.info("The buscarIdentificadorMedioPago service had been called!");
		
		resetSession();

		return iDlFirmaElectronica.buscarIdentificadorMedioPago(sistema, moneda, medio, tipo);
	}

	@Override
	public List<PLE_DetallePlanilla> buscarDetallePlanilla(Integer idPlanilla) {
		log.info("The buscarDetallePlanilla service had been called!");
		
		resetSession();

		return iDlFirmaElectronica.buscarDetallePlanilla(idPlanilla);
	}

	@Override
	public List<PLE_CuentaBanco> getCuentasBancos(String moneda, String entidad) {
		log.info("The getCuentasBancos service had been called!");
		
		resetSession();

		return iDlFirmaElectronica.getCuentasBancos(moneda, entidad);
	}

	@Override
	public List<PLE_TotalPagoPlanilla> obtenerTotalPagosPlanilla(Integer idPlanilla) {
		log.info("The obtenerTotalPagosPlanilla service had been called!");
		
		resetSession();

		return iDlFirmaElectronica.obtenerTotalPagosPlanilla(idPlanilla);
	}

	@Override
	public List<PLE_MedioPago> obtenerMedioPago() {
		log.info("The obtenerMedioPago service had been called!");
		
		resetSession();

		return iDlFirmaElectronica.obtenerMedioPago();
	}

	@Override
	public List<PLE_TipoPlanilla> obtenerTipoPlanilla() {
		log.info("The obtenerTipoPlanilla service had been called!");
		
		resetSession();

		return iDlFirmaElectronica.obtenerTipoPlanilla();
	}

}
