package com.mapfre.tron.gt.api.bl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapfre.tron.gt.api.bl.BaseService;
import com.mapfre.tron.gt.api.bl.IBlOficinaDigital;
import com.mapfre.tron.gt.api.dl.IDlOficinaDigital;
import com.mapfre.tron.gt.api.model.BusquedaAsegurado;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class BlOficinaDigitalImpl extends BaseService implements IBlOficinaDigital {
	
	@Autowired
	private IDlOficinaDigital iDlOficinaDigital;

	@Override
	public List<BusquedaAsegurado> busquedaAsegurados(Integer codCia, String nombreAseg, Integer codAgente) {
		log.info("The busquedaAsegurados service had been called!");
		
		resetSession();

		//return iDlOficinaDigital.busquedaAsegurados(codCia, nombreAseg, codAgente);
		return iDlOficinaDigital.busquedaAseguradosQuery(codCia, nombreAseg, codAgente);
	}

}
