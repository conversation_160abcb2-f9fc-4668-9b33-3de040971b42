package com.mapfre.tron.gt.api.bl.cache.config;

import java.util.Collection;
import java.util.function.Supplier;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.concurrent.ConcurrentMapCache;
import org.springframework.stereotype.Component;

import net.sf.ehcache.CacheException;

/**
 * Class for managing cache cleaning
 * 
 * <AUTHOR>
 *
 */
@Component("gtCacheServiceImpl")
public class CacheServiceImpl implements ICacheService {

	@Autowired
	private CacheManager cacheManager;
	
	private static final String CACHE_ERROR = "Cache with name '%s' not found.";

	/**
	 * Evicts a specific cache value by its key.
	 * 
	 * @param cacheName the name of the cache
	 * @param cacheKey  the key of the cache value to evict
	 * @throws CacheException if the cache with the given name is not found
	 */
	@Override
	public void evictSingleCacheValue(String cacheName, String cacheKey) {
		Cache cache = cacheManager.getCache(cacheName);
		if (cache != null) {
			cache.evict(cacheKey);
		} else {
			throw new CacheException(String.format(CACHE_ERROR, cacheName));
		}
	}

	/**
	 * Evicts all values in a specific cache.
	 * 
	 * @param cacheName the name of the cache to clear
	 * @throws CacheException if the cache with the given name is not found
	 */
	@Override
	public void evictAllCacheValues(String cacheName) {
		Cache cache = cacheManager.getCache(cacheName);
		if (cache != null) {
			cache.clear();
		} else {
			throw new CacheException(String.format(CACHE_ERROR, cacheName));
		}
	}

	/**
	 * Evicts all caches managed by the cache manager.
	 */
	@Override
	public void evictAllCaches() {
		cacheManager.getCacheNames().parallelStream().forEach(cacheName -> {
			Cache cache = cacheManager.getCache(cacheName);
			if (cache != null) {
				cache.clear();
			}
		});
	}

	/**
	 * Retrieves all cache names.
	 * 
	 * @return a collection of all cache names
	 */
	@Override
	public Collection<String> getAllCacheNames() {
		return cacheManager.getCacheNames();
	}

	/**
	 * Retrieves a value from a specific cache by its key.
	 * 
	 * @param cacheName the name of the cache
	 * @param cacheKey  the key of the cache value to retrieve
	 * @return the cache value, or null if the key does not exist
	 * @throws CacheException if the cache with the given name is not found
	 */
	@Override
	public Object getCacheValue(String cacheName, String cacheKey) {
		Cache cache = cacheManager.getCache(cacheName);
		if (cache != null) {
			return cache.get(cacheKey, Object.class);
		} else {
			throw new CacheException(String.format(CACHE_ERROR, cacheName));
		}
	}

	/**
	 * Puts a value into a specific cache.
	 * 
	 * @param cacheName the name of the cache
	 * @param cacheKey  the key for the value
	 * @param value     the value to store in the cache
	 */
	@Override
	public void putCacheValue(String cacheName, String cacheKey, Object value) {
		Cache cache = cacheManager.getCache(cacheName);
		if (cache != null && cacheKey != null && value != null) {
			cache.put(cacheKey, value);
		} else {
			if (cache == null) {
				throw new CacheException(String.format(CACHE_ERROR, cacheName));
			} else {
				throw new CacheException("Cache key or value cannot be null.");
			}
		}
	}

	/**
	 * Checks if a specific cache contains a given key.
	 * 
	 * @param cacheName the name of the cache
	 * @param cacheKey  the key to check
	 * @return true if the cache contains the key, false otherwise
	 * @throws CacheException if the cache with the given name is not found
	 */
	@Override
	public boolean containsCacheValue(String cacheName, String cacheKey) {
		Cache cache = cacheManager.getCache(cacheName);
		if (cache != null) {
			return cache.get(cacheKey) != null;
		} else {
			throw new CacheException(String.format(CACHE_ERROR, cacheName));
		}
	}

	/**
	 * Retrieves the size of a specific cache.
	 * 
	 * @param cacheName the name of the cache
	 * @return the size of the cache, or -1 if the size cannot be determined
	 * @throws CacheException if the cache with the given name is not found
	 */
	@Override
	public int getCacheSize(String cacheName) {
		Cache cache = cacheManager.getCache(cacheName);
		if (cache instanceof ConcurrentMapCache) {
			return ((ConcurrentMapCache) cache).getNativeCache().size();
		}
		throw new CacheException(String.format(CACHE_ERROR, cacheName));
	}

	/**
	 * Refreshes the cache with new data using a data loader function.
	 * 
	 * @param cacheName  the name of the cache to refresh
	 * @param dataLoader a function that loads new data for the cache
	 * @throws CacheException if the cache with the given name is not found
	 */
	@Override
	public void refreshCache(String cacheName, Supplier<Object> dataLoader) {
		Cache cache = cacheManager.getCache(cacheName);
		if (cache != null && dataLoader != null) {
			Object data = dataLoader.get();
			cache.clear();
			cache.put("key", data);
		} else {
			throw new CacheException(String.format(CACHE_ERROR, cacheName));
		}
	}

}