package com.mapfre.tron.gt.api.bl;

import com.mapfre.tron.gt.api.bl.Services.ITasaCambioService;
import com.mapfre.tron.gt.api.bl.Services.ICargaArchivoService;
import com.mapfre.tron.gt.api.dl.IDlCommons;
import com.mapfre.tron.gt.api.model.Message;
import com.mapfre.tron.gt.api.model.CargaArchivoRequest;
import com.mapfre.tron.gt.api.model.CargaArchivoResponse;
import com.mapfre.tron.gt.api.model.ArchivoCargado;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;

@Service
@Slf4j
public class BlCommonsImpl extends BaseService implements IBlCommons {

    @Autowired
    private IDlCommons iDlCommons;



    @Autowired
    private ITasaCambioService tasaCambioService;

    @Autowired
    private ICargaArchivoService cargaArchivoService;

    @Override
    public Message actualizacionDeTasaDeCambio() {
        log.info("The actualizacionDeTasaDeCambio service has been called!");

        Message response = new Message();
        Date fechaHoy = new Date();
        Date fechaEquivalente = new Date();

        try {
            resetSession();

            double tasaCambio = tasaCambioService.getTipoCambioDia();

            if (tasaCambio <= 0) {
                log.warn("No se pudo obtener una tasa de cambio válida.");
                response.setCode("002");
                response.setTitle("Tasa inválida");
                response.setMessage("No se pudo actualizar la tasa de cambio porque el valor obtenido no es válido.");
                response.setType("warning");
                return response;
            }

            iDlCommons.actualizacionDeTasaDeCambio(fechaHoy, tasaCambio);
            iDlCommons.actualizacionFechaEquivalente(fechaEquivalente);

            response.setCode("200");
            response.setTitle("Actualización exitosa");
            response.setMessage("La tasa de cambio y la fecha equivalente fueron actualizadas correctamente.");
            response.setType("success");

        } catch (Exception e) {
            log.error("Error inesperado en actualizacionDeTasaDeCambio: {}", e.getMessage(), e);
            response.setCode("500");
            response.setTitle("Error inesperado");
            response.setMessage("Ocurrió un error en : " + e.getMessage());
            response.setType("error");
        }

        return response;
    }

    @Override
    public CargaArchivoResponse cargarArchivos(CargaArchivoRequest request) {
        log.info("The cargarArchivos service has been called with idConexion: {} and {} files",
                request.getIdConexion(), request.getArchivos().size());

        CargaArchivoResponse response = new CargaArchivoResponse();

        try {
            resetSession();

            // Validar request
            if (request.getIdConexion() == null || request.getIdConexion().trim().isEmpty()) {
                response.setCodigo("400");
                response.setMensaje("El ID de conexión es requerido");
                response.setArchivos(new ArrayList<>());
                return response;
            }

            if (request.getIdDirectorio() == null || request.getIdDirectorio().trim().isEmpty()) {
                response.setCodigo("400");
                response.setMensaje("El ID de directorio es requerido");
                response.setArchivos(new ArrayList<>());
                return response;
            }

            if (request.getArchivos() == null || request.getArchivos().isEmpty()) {
                response.setCodigo("400");
                response.setMensaje("Al menos un archivo es requerido");
                response.setArchivos(new ArrayList<>());
                return response;
            }



            // Llamar al servicio SOAP
            String jsonResponse = cargaArchivoService.cargarArchivos(request);

            // Parsear la respuesta JSON
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(jsonResponse);

            response.setCodigo(jsonNode.has("codigo") ? jsonNode.get("codigo").asText() : "200");
            response.setMensaje(jsonNode.has("mensaje") ? jsonNode.get("mensaje").asText() : "Operación completada");

            List<ArchivoCargado> archivos = new ArrayList<>();

            if (jsonNode.has("respuesta") && jsonNode.get("respuesta").isArray()) {
                for (JsonNode archivoNode : jsonNode.get("respuesta")) {
                    ArchivoCargado archivo = new ArchivoCargado();
                    archivo.setNombre(archivoNode.has("nombre") ? archivoNode.get("nombre").asText() : "");
                    archivo.setRuta(archivoNode.has("ruta") ? archivoNode.get("ruta").asText() : "");
                    archivo.setEstado(archivoNode.has("estado") ? archivoNode.get("estado").asText() : "SUCCESS");
                    archivo.setError(archivoNode.has("error") ? archivoNode.get("error").asText() : null);
                    archivos.add(archivo);
                }
            }

            response.setArchivos(archivos);

            log.info("Successfully processed {} files", archivos.size());

        } catch (Exception e) {
            log.error("Error inesperado en cargarArchivos: {}", e.getMessage(), e);
            response.setCodigo("500");
            response.setMensaje("Error inesperado: " + e.getMessage());
            response.setArchivos(new ArrayList<>());
        }

        return response;
    }
}