package com.mapfre.tron.gt.api.bl.cache;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import com.mapfre.tron.api.cmn.client.api.CommonApi;
import com.mapfre.tron.api.cmn.client.model.OCmnLngS;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@Component
public class CacheableMethodsImpl implements ICacheableMethods {

	@Autowired
	private CommonApi commonApi;

	@Override
	@Cacheable("getLanguagesCacheable")
	public List<OCmnLngS> getLanguagesCacheable(String lngVal, String usrVal) {
		log.debug("CACHE - Calling CommonApi getLanguages service...");
		return commonApi.getLanguages(lngVal, usrVal);
	}

}
