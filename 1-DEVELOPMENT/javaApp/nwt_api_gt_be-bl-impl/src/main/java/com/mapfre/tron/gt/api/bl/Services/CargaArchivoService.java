package com.mapfre.tron.gt.api.bl.Services;

import com.mapfre.tron.gt.api.model.CargaArchivoRequest;
import com.mapfre.tron.gt.api.model.ArchivoParaCarga;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;

import org.w3c.dom.Document;
import org.w3c.dom.NodeList;
import org.w3c.dom.Node;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.net.HttpURLConnection;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;

@Service
@Slf4j
public class CargaArchivoService implements ICargaArchivoService {

    @Value("${app.env.soap.carga.endpoint}")
    private String SOAP_ENDPOINT;

    @Value("${app.env.soap.carga.action}")
    private String SOAP_ACTION;

    @Override
    public String cargarArchivos(CargaArchivoRequest request) {
        log.info("The cargarArchivos service has been called with idConexion: {} and {} files", 
                request.getIdConexion(), request.getArchivos().size());
        
        try {
            // Construir el array de archivos para el SOAP con el formato correcto
            StringBuilder archivosXml = new StringBuilder();
            for (ArchivoParaCarga archivo : request.getArchivos()) {
                archivosXml.append("<sys:TupleOfstringstringstring>")
                          .append("<sys:m_Item1>").append(escapeXml(archivo.getItem1())).append("</sys:m_Item1>")
                          .append("<sys:m_Item2>").append(escapeXml(archivo.getItem2())).append("</sys:m_Item2>")
                          .append("<sys:m_Item3>").append(escapeXml(archivo.getItem3())).append("</sys:m_Item3>")
                          .append("</sys:TupleOfstringstringstring>");
            }

            String soapRequest =
                    "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" " +
                    "xmlns:tem=\"http://tempuri.org/\" " +
                    "xmlns:wcf=\"http://schemas.datacontract.org/2004/07/WcfFile.Estructuras\" " +
                    "xmlns:sys=\"http://schemas.datacontract.org/2004/07/System\">\n" +
                    "   <soapenv:Header/>\n" +
                    "   <soapenv:Body>\n" +
                    "      <tem:doArchivo>\n" +
                    "         <tem:datos>\n" +
                    "            <wcf:archivo>\n" +
                    archivosXml.toString() +
                    "            </wcf:archivo>\n" +
                    "            <wcf:id_conexion>" + escapeXml(request.getIdConexion()) + "</wcf:id_conexion>\n" +
                    "            <wcf:id_directorio>" + escapeXml(request.getIdDirectorio()) + "</wcf:id_directorio>\n" +
                    "         </tem:datos>\n" +
                    "      </tem:doArchivo>\n" +
                    "   </soapenv:Body>\n" +
                    "</soapenv:Envelope>";

            log.debug("SOAP Request: {}", soapRequest);

            URL url = new URL(SOAP_ENDPOINT);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();

            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "text/xml; charset=utf-8");
            conn.setRequestProperty("SOAPAction", SOAP_ACTION);
            conn.setDoOutput(true);
            conn.setConnectTimeout(30000); // 30 segundos
            conn.setReadTimeout(120000);   // 120 segundos (más tiempo para carga)

            try (OutputStream os = conn.getOutputStream()) {
                os.write(soapRequest.getBytes("UTF-8"));
            }

            StringBuilder response = new StringBuilder();
            int responseCode = conn.getResponseCode();
            
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        response.append(line).append("\n");
                    }
                }
            } else {
                log.error("HTTP Error Code: {}", responseCode);
                try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getErrorStream()))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        response.append(line).append("\n");
                    }
                }
                throw new RuntimeException("HTTP Error: " + responseCode + " - " + response.toString());
            }

            log.debug("SOAP Response: {}", response.toString());

            String jsonResponse = parseDoArchivoResponse(response.toString());
            log.info("Successfully processed doArchivo response for file upload");
            
            return jsonResponse;

        } catch (Exception ex) {
            log.error("Error calling doArchivo SOAP service for upload", ex);
            throw new RuntimeException("Error calling doArchivo SOAP service for upload: " + ex.getMessage(), ex);
        }
    }

    private String parseDoArchivoResponse(String xml) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);

            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(xml.getBytes("UTF-8")));

            // Buscar el nodo que contiene la respuesta JSON
            NodeList nodes = doc.getElementsByTagName("doArchivoResult");
            if (nodes.getLength() == 0) {
                nodes = doc.getElementsByTagName("return");
            }
            
            if (nodes.getLength() > 0) {
                Node resultNode = nodes.item(0);
                String jsonResult = resultNode.getTextContent();
                
                if (jsonResult != null && !jsonResult.trim().isEmpty()) {
                    return jsonResult.trim();
                } else {
                    log.warn("Empty response from doArchivo upload service");
                    return "{\"codigo\":\"204\",\"mensaje\":\"Respuesta vacía del servicio\",\"archivos\":[]}";
                }
            } else {
                log.warn("No se encontró el nodo de respuesta en el XML");
                return "{\"codigo\":\"500\",\"mensaje\":\"Formato de respuesta inválido\",\"archivos\":[]}";
            }
        } catch (Exception ex) {
            log.error("Error al parsear la respuesta del servicio doArchivo upload", ex);
            return "{\"codigo\":\"500\",\"mensaje\":\"Error al procesar la respuesta: " + 
                   escapeJsonString(ex.getMessage()) + "\",\"archivos\":[]}";
        }
    }

    private String escapeXml(String input) {
        if (input == null) return "";
        return input.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&apos;");
    }

    private String escapeJsonString(String input) {
        if (input == null) return "";
        return input.replace("\\", "\\\\")
                   .replace("\"", "\\\"")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\t", "\\t");
    }
}
