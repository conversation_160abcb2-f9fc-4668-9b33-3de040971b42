package com.mapfre.tron.gt.api.bl;

import java.util.List;

import com.mapfre.tron.gt.api.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapfre.tron.gt.api.dl.IDlPycges;

import lombok.extern.slf4j.Slf4j;
import static com.mapfre.tron.gt.api.sr.CacheApi.log;

/**
 * Implementación de la interfaz IBlPycges para operaciones de Pycges en la capa de negocio.
 */
@Service
@Slf4j
public class BlPycgesImpl extends BaseService implements IBlPycges {

    @Autowired
    private IDlPycges iDlPycges;

    @Override
    public List<PycUsuario> getUsuarios() {
        log.info("Iniciando obtención de usuarios");

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycUsuario> listaUsuarios = iDlPycges.getUsuarios();

            log.info("Obtención de usuarios completada. Total de usuarios encontrados: {}", listaUsuarios.size());
            return listaUsuarios;

        } catch (Exception e) {
            log.error("Error al obtener los usuarios: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycAplicacion> getAplicaciones(Integer idProceso) {
        log.info("Iniciando obtención de aplicaciones para el proceso {}", idProceso);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycAplicacion> listaAplicaciones = iDlPycges.getAplicaciones(idProceso);

            log.info("Obtención de aplicaciones completada. Total de aplicaciones encontradas: {}", listaAplicaciones.size());
            return listaAplicaciones;

        } catch (Exception e) {
            log.error("Error al obtener las aplicaciones: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycInfoProceso> getInfoProceso(Integer idProceso) {
        log.info("Iniciando obtención de información del proceso {}", idProceso);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycInfoProceso> infoProceso = iDlPycges.getInfoProceso(idProceso);

            log.info("Obtención de información del proceso completada. Total de registros encontrados: {}", infoProceso.size());
            return infoProceso;

        } catch (Exception e) {
            log.error("Error al obtener la información del proceso: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycControlProc> getControlesProceso(Integer idProceso, String tipoControl) {
        log.info("Iniciando obtención de controles del proceso {} con tipo de control {}", idProceso, tipoControl);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycControlProc> controlesProceso = iDlPycges.getControlesProceso(idProceso, tipoControl);

            log.info("Obtención de controles del proceso completada. Total de registros encontrados: {}", controlesProceso.size());
            return controlesProceso;

        } catch (Exception e) {
            log.error("Error al obtener los controles del proceso: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycEstadoPerfil> getEstadoPerfil(Integer idPerfil, Integer idProceso) {
        log.info("Iniciando obtención de estados del perfil {} en el proceso {}", idPerfil, idProceso);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycEstadoPerfil> estadosPerfil = iDlPycges.getEstadoPerfil(idPerfil, idProceso);

            log.info("Obtención de estados del perfil completada. Total de registros encontrados: {}", estadosPerfil.size());
            return estadosPerfil;

        } catch (Exception e) {
            log.error("Error al obtener los estados del perfil: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycProcesoPorTipo> getProcesosPorTipo(Integer idTipo) {
        log.info("Iniciando obtención de procesos para el tipo de petición {}", idTipo);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycProcesoPorTipo> listaProcesos = iDlPycges.getProcesosPorTipo(idTipo);

            log.info("Obtención de procesos completada. Total de procesos encontrados: {}", listaProcesos.size());
            return listaProcesos;

        } catch (Exception e) {
            log.error("Error al obtener los procesos por tipo: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycPerfil> getPerfiles() {
        log.info("Iniciando obtención de perfiles");

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycPerfil> listaPerfiles = iDlPycges.getPerfiles();

            log.info("Obtención de perfiles completada. Total de perfiles encontrados: {}", listaPerfiles.size());
            return listaPerfiles;

        } catch (Exception e) {
            log.error("Error al obtener los perfiles: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycUsuarioPerfil> getUsuariosPerfil(Integer idPerfil) {
        log.info("Iniciando obtención de usuarios por perfil. ID Perfil: {}", idPerfil);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycUsuarioPerfil> listaUsuariosPerfil = iDlPycges.getUsuariosPerfil(idPerfil);

            log.info("Obtención de usuarios por perfil completada. Total de registros encontrados: {}", listaUsuariosPerfil.size());
            return listaUsuariosPerfil;

        } catch (Exception e) {
            log.error("Error al obtener los usuarios por perfil: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycListadoAsigna> getListadoAsigna(Integer idPerfil) {
        log.info("Iniciando obtención de listado de asignaciones por perfil. ID Perfil: {}", idPerfil);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycListadoAsigna> listadoAsigna = iDlPycges.getListadoAsigna(idPerfil);

            log.info("Obtención de listado de asignaciones completada. Total de registros encontrados: {}", listadoAsigna.size());
            return listadoAsigna;

        } catch (Exception e) {
            log.error("Error al obtener el listado de asignaciones por perfil: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public PycGetQueryDatVar getQueryDatVar(Integer idProceso, Integer idSeccion, Integer idDatoVar) {
        log.info("Iniciando obtención de consulta SQL para dato variable. ID Proceso: {}, ID Sección: {}, ID Dato Variable: {}",
                idProceso, idSeccion, idDatoVar);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            PycGetQueryDatVar queryDatVar = iDlPycges.getQueryDatVar(idProceso, idSeccion, idDatoVar);

            log.info("Obtención de consulta SQL completada. Query: {}", queryDatVar.getQuery());
            return queryDatVar;

        } catch (Exception e) {
            log.error("Error al obtener la consulta SQL para el dato variable: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public PycSrcMultipleOption getSrcMultipleOption(Integer idPerfil) {
        log.info("Iniciando verificación de opción de selección múltiple para el perfil {}", idPerfil);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            PycSrcMultipleOption srcMultipleOption = iDlPycges.getSrcMultipleOption(idPerfil);

            log.info("Verificación de opción de selección múltiple completada. Resultado: {}", srcMultipleOption.getHasMultipleOption());
            return srcMultipleOption;

        } catch (Exception e) {
            log.error("Error al verificar la opción de selección múltiple: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycGetUsuariosAsig> getUsuariosAsig(Integer idProceso, Integer idPerfil, Integer idAplicacion, Integer idEstado) {
        log.info("Iniciando obtención de usuarios asignados. ID Proceso: {}, ID Perfil: {}, ID Aplicación: {}, ID Estado: {}",
                idProceso, idPerfil, idAplicacion, idEstado);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycGetUsuariosAsig> usuariosAsig = iDlPycges.getUsuariosAsig(idProceso, idPerfil, idAplicacion, idEstado);

            log.info("Obtención de usuarios asignados completada. Total de usuarios encontrados: {}", usuariosAsig.size());
            return usuariosAsig;

        } catch (Exception e) {
            log.error("Error al obtener los usuarios asignados: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycUsuPerfilProceso> getUsuPerfilProceso(Integer idPerfil, Integer idProceso) {
        log.info("Iniciando obtención de usuarios por perfil y proceso. ID Perfil: {}, ID Proceso: {}", idPerfil, idProceso);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycUsuPerfilProceso> usuariosPerfilProceso = iDlPycges.getUsuPerfilProceso(idPerfil, idProceso);

            log.info("Obtención de usuarios por perfil y proceso completada. Total de usuarios encontrados: {}", usuariosPerfilProceso.size());
            return usuariosPerfilProceso;

        } catch (Exception e) {
            log.error("Error al obtener los usuarios por perfil y proceso: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycUsuProcesos> getUsuarioProcesos(String usuario) {
        log.info("Iniciando obtención de procesos para el usuario: {}", usuario);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycUsuProcesos> usuarioProcesos = iDlPycges.getUsuarioProcesos(usuario);

            log.info("Obtención de procesos para el usuario completada. Total de procesos encontrados: {}", usuarioProcesos.size());
            return usuarioProcesos;

        } catch (Exception e) {
            log.error("Error al obtener los procesos para el usuario: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer updatePerfil(PycUpdPerfil perfilData) {
        log.info("Iniciando actualización de perfil. ID Perfil: {}, ID Usuario: {}", perfilData.getIdPerfil(), perfilData.getIdUsuario());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer idUsuario = iDlPycges.updatePerfil(perfilData);

            log.info("Actualización de perfil completada. ID Usuario: {}", idUsuario);
            return idUsuario;

        } catch (Exception e) {
            log.error("Error al actualizar el perfil: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer updateUsuario(PycUpdUsuario usuarioData) {
        log.info("Iniciando actualización de usuario. Nombre: {}, Numa o Usuario: {}",
                usuarioData.getPrimerNombre(), usuarioData.getNumaOUsuario());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer idUsuario = iDlPycges.updateUsuario(usuarioData);

            log.info("Actualización de usuario completada. ID Usuario: {}", idUsuario);
            return idUsuario;

        } catch (Exception e) {
            log.error("Error al actualizar el usuario: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer creaSolicitud(PycCreaSolicitud solicitudData) {
        log.info("Iniciando creación de solicitud. Plataforma: {}, Tipo Seguro: {}, Cliente: {} {}",
                solicitudData.getPlataforma(), solicitudData.getTipoSeguro(),
                solicitudData.getNombres(), solicitudData.getApellidos());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar el procedimiento
            Integer idSolicitud = iDlPycges.creaSolicitud(solicitudData);

            log.info("Creación de solicitud completada. ID Solicitud: {}", idSolicitud);
            return idSolicitud;

        } catch (Exception e) {
            log.error("Error al crear la solicitud: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public Integer insertPeticion(PycInsertPeticion peticionData) {
        log.info("Iniciando inserción de petición. Nombre: {}, Descripción: {}, Usuario Solicitante: {}",
                peticionData.getNombrePeticion(), peticionData.getDescripcionPeticion(),
                peticionData.getIdUsuarioSolicitante());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer idPeticion = iDlPycges.insertPeticion(peticionData);

            log.info("Inserción de petición completada. ID Petición: {}", idPeticion);
            return idPeticion;

        } catch (Exception e) {
            log.error("Error al insertar la petición: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycPeticionPerfil> getPeticionPerfil(Integer idPerfil, Integer idProceso, String idEstado,
                                                     Integer idPeticion, Integer idCanal, Integer idUsuario,
                                                     String indSubs, Integer idOficina, String numaOUsuario) {
        log.info("Iniciando obtención de peticiones por perfil. ID Perfil: {}, ID Proceso: {}, ID Estado: {}, ID Petición: {}, ID Canal: {}, ID Usuario: {}, Ind Subs: {}, ID Oficina: {}, Numa O Usuario: {}",
                idPerfil, idProceso, idEstado, idPeticion, idCanal, idUsuario, indSubs, idOficina, numaOUsuario);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycPeticionPerfil> peticionesPerfil = iDlPycges.getPeticionPerfil(idPerfil, idProceso, idEstado,
                    idPeticion, idCanal, idUsuario, indSubs, idOficina, numaOUsuario);

            log.info("Obtención de peticiones por perfil completada. Total de peticiones encontradas: {}", peticionesPerfil.size());
            return peticionesPerfil;

        } catch (Exception e) {
            log.error("Error al obtener las peticiones por perfil: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycPetSinProgramador> getPeticionSinProgramador() {
        log.info("Iniciando obtención de peticiones sin programador");

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycPetSinProgramador> peticionesSinProgramador = iDlPycges.getPeticionSinProgramador();

            log.info("Obtención de peticiones sin programador completada. Total de peticiones encontradas: {}", peticionesSinProgramador.size());
            return peticionesSinProgramador;

        } catch (Exception e) {
            log.error("Error al obtener las peticiones sin programador: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycAreaPeti> getConArea() {
        log.info("Iniciando obtención de áreas con peticiones");

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycAreaPeti> areasConPeticiones = iDlPycges.getConArea();

            log.info("Obtención de áreas con peticiones completada. Total de áreas encontradas: {}", areasConPeticiones.size());
            return areasConPeticiones;

        } catch (Exception e) {
            log.error("Error al obtener las áreas con peticiones: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycDepartamento> getConDepartamento(Integer idArea) {
        log.info("Iniciando obtención de departamentos. ID Área: {}", idArea);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycDepartamento> departamentos = iDlPycges.getConDepartamento(idArea);

            log.info("Obtención de departamentos completada. Total de departamentos encontrados: {}", departamentos.size());
            return departamentos;

        } catch (Exception e) {
            log.error("Error al obtener los departamentos: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycAreaUsuario> getConSolicitante(Integer idArea, Integer idDepartamento) {
        log.info("Iniciando obtención de usuarios solicitantes. ID Área: {}, ID Departamento: {}", idArea, idDepartamento);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycAreaUsuario> usuariosSolicitantes = iDlPycges.getConSolicitante(idArea, idDepartamento);

            log.info("Obtención de usuarios solicitantes completada. Total de usuarios encontrados: {}", usuariosSolicitantes.size());
            return usuariosSolicitantes;

        } catch (Exception e) {
            log.error("Error al obtener los usuarios solicitantes: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycAnalista> getConAnalista(Integer idSolicitante, String prioridad) {
        log.info("Iniciando obtención de analistas. ID Solicitante: {}, Prioridad: {}", idSolicitante, prioridad);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycAnalista> analistas = iDlPycges.getConAnalista(idSolicitante, prioridad);

            log.info("Obtención de analistas completada. Total de analistas encontrados: {}", analistas.size());
            return analistas;

        } catch (Exception e) {
            log.error("Error al obtener los analistas: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycConEstado> getConEstado(Integer idSolicitante, String prioridad, Integer idAnalista) {
        log.info("Iniciando obtención de estados. ID Solicitante: {}, Prioridad: {}, ID Analista: {}",
                idSolicitante, prioridad, idAnalista);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycConEstado> estados = iDlPycges.getConEstado(idSolicitante, prioridad, idAnalista);

            log.info("Obtención de estados completada. Total de estados encontrados: {}", estados.size());
            return estados;

        } catch (Exception e) {
            log.error("Error al obtener los estados: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycPrioridad> getConPrioridad(Integer idSolicitante, Integer idArea, Integer idDepartamento) {
        log.info("Iniciando obtención de prioridades. ID Solicitante: {}, ID Área: {}, ID Departamento: {}",
                idSolicitante, idArea, idDepartamento);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycPrioridad> prioridades = iDlPycges.getConPrioridad(idSolicitante, idArea, idDepartamento);

            log.info("Obtención de prioridades completada. Total de prioridades encontradas: {}", prioridades.size());
            return prioridades;

        } catch (Exception e) {
            log.error("Error al obtener las prioridades: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycTabAnios> getTabAnios(Integer idProceso) {
        log.info("Iniciando obtención de años. ID Proceso: {}", idProceso);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycTabAnios> anios = iDlPycges.getTabAnios(idProceso);

            log.info("Obtención de años completada. Total de años encontrados: {}", anios.size());
            return anios;

        } catch (Exception e) {
            log.error("Error al obtener los años: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycTabArea> getTabArea(Integer idProceso, String anio) {
        log.info("Iniciando obtención de áreas. ID Proceso: {}, Año: {}", idProceso, anio);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycTabArea> areas = iDlPycges.getTabArea(idProceso, anio);

            log.info("Obtención de áreas completada. Total de áreas encontradas: {}", areas.size());
            return areas;

        } catch (Exception e) {
            log.error("Error al obtener las áreas: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycTabEstado> getTabEstado(Integer idProceso, String anio, Integer idArea) {
        log.info("Iniciando obtención de estados. ID Proceso: {}, Año: {}, ID Área: {}", idProceso, anio, idArea);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycTabEstado> estados = iDlPycges.getTabEstado(idProceso, anio, idArea);

            log.info("Obtención de estados completada. Total de estados encontrados: {}", estados.size());
            return estados;

        } catch (Exception e) {
            log.error("Error al obtener los estados: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycUsuPerfilPeti> getUsuariosPerfilPeticion(Integer idPeticion, Integer idPerfil, Integer idAplicacion) {
        log.info("Iniciando obtención de usuarios por perfil y petición. ID Petición: {}, ID Perfil: {}, ID Aplicación: {}",
                idPeticion, idPerfil, idAplicacion);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycUsuPerfilPeti> usuarios = iDlPycges.getUsuariosPerfilPeticion(idPeticion, idPerfil, idAplicacion);

            log.info("Obtención de usuarios por perfil y petición completada. Total de usuarios encontrados: {}", usuarios.size());
            return usuarios;

        } catch (Exception e) {
            log.error("Error al obtener los usuarios por perfil y petición: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycPetiFiltro> getPeticionFiltros(Integer idArea, Integer idDepartamento, Integer idPerfil,
                                                  Integer idEstado, Integer idUsuarioSolicitante, Integer idTipo,
                                                  String fechaInicio, String fechaFin, Integer idProceso) {
        log.info("Iniciando obtención de peticiones filtradas. ID Área: {}, ID Departamento: {}, ID Perfil: {}, ID Estado: {}, ID Usuario Solicitante: {}, ID Tipo: {}, Fecha Inicio: {}, Fecha Fin: {}, ID Proceso: {}",
                idArea, idDepartamento, idPerfil, idEstado, idUsuarioSolicitante, idTipo, fechaInicio, fechaFin, idProceso);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycPetiFiltro> peticionesFiltradas = iDlPycges.getPeticionFiltros(idArea, idDepartamento, idPerfil,
                    idEstado, idUsuarioSolicitante, idTipo, fechaInicio, fechaFin, idProceso);

            log.info("Obtención de peticiones filtradas completada. Total de peticiones encontradas: {}", peticionesFiltradas.size());
            return peticionesFiltradas;

        } catch (Exception e) {
            log.error("Error al obtener las peticiones filtradas: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycReportePeti> getReportePeticion(Integer idArea, Integer idDepartamento, Integer idSolicitante,
                                                   String prioridad, Integer idAnalista, String estado) {
        log.info("Iniciando obtención del reporte de peticiones. ID Área: {}, ID Departamento: {}, ID Solicitante: {}, Prioridad: {}, ID Analista: {}, Estado: {}",
                idArea, idDepartamento, idSolicitante, prioridad, idAnalista, estado);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycReportePeti> reportePeticiones = iDlPycges.getReportePeticion(idArea, idDepartamento, idSolicitante,
                    prioridad, idAnalista, estado);

            log.info("Obtención del reporte de peticiones completada. Total de registros encontrados: {}", reportePeticiones.size());
            return reportePeticiones;

        } catch (Exception e) {
            log.error("Error al obtener el reporte de peticiones: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer insertUserProceso(PycInsertUserPro userData) {
        log.info("Iniciando inserción/actualización de usuario proceso. ID Usuario: {}, ID Proceso: {}, Estado: {}",
                userData.getIdUsuario(), userData.getIdProceso(), userData.getEstado());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer idUsuario = iDlPycges.insertUserProceso(userData);

            log.info("Inserción/actualización de usuario proceso completada. ID Usuario: {}", idUsuario);
            return idUsuario;

        } catch (Exception e) {
            log.error("Error al insertar/actualizar usuario proceso: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer insertUsuario(PycInsertUsuario usuarioData) {
        log.info("Iniciando inserción de usuario. Primer Nombre: {}, Primer Apellido: {}, Numa o Usuario: {}, Email: {}",
                usuarioData.getPrimerNombre(), usuarioData.getPrimerApellido(),
                usuarioData.getNumaOUsuario(), usuarioData.getEmail());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer idUsuario = iDlPycges.insertUsuario(usuarioData);

            log.info("Inserción de usuario completada. ID Usuario: {}", idUsuario);
            return idUsuario;

        } catch (Exception e) {
            log.error("Error al insertar usuario: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer actDocumento(PycActDocumento documentoData) {
        log.info("Iniciando actualización de documento. ID Petición: {}, ID Documento: {}, Localización: {}",
                documentoData.getIdPeticion(), documentoData.getIdDocumento(), documentoData.getLocalizacion());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer idPeticion = iDlPycges.actDocumento(documentoData);

            log.info("Actualización de documento completada. ID Petición: {}", idPeticion);
            return idPeticion;

        } catch (Exception e) {
            log.error("Error al actualizar documento: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycObserPet> getObserPeticion(Integer idPeticion, String numaOUsuario) {
        log.info("Iniciando obtención de observaciones para la petición {}. Numa O Usuario: {}", idPeticion, numaOUsuario);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycObserPet> observaciones = iDlPycges.getObserPeticion(idPeticion, numaOUsuario);

            log.info("Obtención de observaciones completada. Total de observaciones encontradas: {}", observaciones.size());
            return observaciones;

        } catch (Exception e) {
            log.error("Error al obtener las observaciones de la petición: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycResumenActi> getResumenActividad(Integer idPeticion, Integer idCategoria) {
        log.info("Iniciando obtención de resumen de actividades para petición {} y categoría {}", idPeticion, idCategoria);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycResumenActi> resumen = iDlPycges.getResumenActividad(idPeticion, idCategoria);

            log.info("Obtención de resumen de actividades completada. Registros encontrados: {}", resumen.size());
            return resumen;

        } catch (Exception e) {
            log.error("Error al obtener el resumen de actividades: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycPetAvance> getPeticionAvance(Integer idProceso, Integer idArea, String estado, String codcia, Integer anio) {
        log.info("Iniciando obtención del avance de peticiones. ID Proceso: {}, ID Área: {}, Estado: {}, Codcia: {}, Año: {}",
                idProceso, idArea, estado, codcia, anio);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycPetAvance> avancePeticiones = iDlPycges.getPeticionAvance(idProceso, idArea, estado, codcia, anio);

            log.info("Obtención del avance de peticiones completada. Total de registros encontrados: {}", avancePeticiones.size());
            return avancePeticiones;

        } catch (Exception e) {
            log.error("Error al obtener el avance de peticiones: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycPetAvanceSig> getPeticionAvanceSiguiente(Integer idProceso, Integer idArea, String estado, String codcia, Integer anio) {
        log.info("Iniciando obtención de peticiones con avance siguiente. ID Proceso: {}, ID Área: {}, Estado: {}, Codcia: {}, Año: {}",
                idProceso, idArea, estado, codcia, anio);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycPetAvanceSig> peticionesAvanceSiguiente = iDlPycges.getPeticionAvanceSiguiente(idProceso, idArea, estado, codcia, anio);

            log.info("Obtención de peticiones con avance siguiente completada. Total de registros encontrados: {}", peticionesAvanceSiguiente.size());
            return peticionesAvanceSiguiente;

        } catch (Exception e) {
            log.error("Error al obtener las peticiones con avance siguiente: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycListVal> getListaVal(String tipo, Integer idProceso) {
        log.info("Iniciando obtención de lista de valores. Tipo: {}, ID Proceso: {}", tipo, idProceso);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycListVal> listaValores = iDlPycges.getListaVal(tipo, idProceso);

            log.info("Obtención de lista de valores completada. Total de valores encontrados: {}", listaValores.size());
            return listaValores;

        } catch (Exception e) {
            log.error("Error al obtener la lista de valores: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycOficina> getOficinas(Integer idProceso, Integer idUsuario) {
        log.info("Iniciando obtención de oficinas. ID Proceso: {}, ID Usuario: {}", idProceso, idUsuario);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycOficina> oficinas = iDlPycges.getOficinas(idProceso, idUsuario);

            log.info("Obtención de oficinas completada. Total de oficinas encontradas: {}", oficinas.size());
            return oficinas;

        } catch (Exception e) {
            log.error("Error al obtener las oficinas: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycCanal> getCanales(Integer idProceso, Integer idUsuario, Integer idOficina) {
        log.info("Iniciando obtención de canales. ID Proceso: {}, ID Usuario: {}, ID Oficina: {}",
                idProceso, idUsuario, idOficina);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycCanal> canales = iDlPycges.getCanales(idProceso, idUsuario, idOficina);

            log.info("Obtención de canales completada. Total de canales encontrados: {}", canales.size());
            return canales;

        } catch (Exception e) {
            log.error("Error al obtener los canales: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycSubordinados> getSubordinados(String canal, String oficina, String idSupervisor, String indLogin, String proceso) {
        log.info("Iniciando obtención de subordinados. Canal: {}, Oficina: {}, ID Supervisor: {}, Ind Login: {}, Proceso: {}",
                canal, oficina, idSupervisor, indLogin, proceso);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycSubordinados> subordinados = iDlPycges.getSubordinados(canal, oficina, idSupervisor, indLogin, proceso);

            log.info("Obtención de subordinados completada. Total de subordinados encontrados: {}", subordinados.size());
            return subordinados;

        } catch (Exception e) {
            log.error("Error al obtener los subordinados: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycInfoUsuario> getInfoUsuario(String usuario) {
        log.info("Iniciando obtención de información del usuario. Usuario: {}", usuario);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycInfoUsuario> infoUsuario = iDlPycges.getInfoUsuario(usuario);

            log.info("Obtención de información del usuario completada. Total de registros encontrados: {}", infoUsuario.size());
            return infoUsuario;

        } catch (Exception e) {
            log.error("Error al obtener la información del usuario: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycRamo> obtenerRamo(String codRamo, Integer codModalidad, String codCia) {
        log.info("Iniciando obtención de información del ramo. Código Ramo: {}, Código Modalidad: {}, Código Compañía: {}",
                codRamo, codModalidad, codCia);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycRamo> ramos = iDlPycges.obtenerRamo(codRamo, codModalidad, codCia);

            log.info("Obtención de información del ramo completada. Total de registros encontrados: {}", ramos.size());
            return ramos;

        } catch (Exception e) {
            log.error("Error al obtener la información del ramo: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycDatoVarEquiv> obtenerDatVarEquiv(String codRamo, Integer codModalidad, String codCia) {
        log.info("Iniciando obtención de datos de variables equivalentes. Código Ramo: {}, Código Modalidad: {}, Código Compañía: {}",
                codRamo, codModalidad, codCia);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycDatoVarEquiv> datosVarEquiv = iDlPycges.obtenerDatVarEquiv(codRamo, codModalidad, codCia);

            log.info("Obtención de datos de variables equivalentes completada. Total de registros encontrados: {}", datosVarEquiv.size());
            return datosVarEquiv;

        } catch (Exception e) {
            log.error("Error al obtener los datos de variables equivalentes: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycPerfilUsu> perfilesUsuario(Integer idUsuario, Integer idProceso) {
        log.info("Iniciando obtención de perfiles de usuario. ID Usuario: {}, ID Proceso: {}",
                idUsuario, idProceso);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycPerfilUsu> perfilesUsuario = iDlPycges.perfilesUsuario(idUsuario, idProceso);

            log.info("Obtención de perfiles de usuario completada. Total de perfiles encontrados: {}", perfilesUsuario.size());
            return perfilesUsuario;

        } catch (Exception e) {
            log.error("Error al obtener los perfiles del usuario: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycCategoriaPet> getCategoriaPeticion(Integer idPeticion) {
        log.info("Iniciando obtención de categorías de petición. ID Petición: {}", idPeticion);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycCategoriaPet> categoriasPeticion = iDlPycges.getCategoriaPeticion(idPeticion);

            log.info("Obtención de categorías de petición completada. Total de categorías encontradas: {}", categoriasPeticion.size());
            return categoriasPeticion;

        } catch (Exception e) {
            log.error("Error al obtener las categorías de la petición: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycPeticionTab> getPeticionesTablero() {
        log.info("Iniciando obtención de peticiones para tablero");

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycPeticionTab> peticionesTablero = iDlPycges.getPeticionesTablero();

            log.info("Obtención de peticiones para tablero completada. Total de peticiones encontradas: {}", peticionesTablero.size());
            return peticionesTablero;

        } catch (Exception e) {
            log.error("Error al obtener las peticiones del tablero: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycOpcionUsu> getOpcionUsuario(Integer idPerfil, Integer idProceso, String numaOUsuario) {
        log.info("Iniciando obtención de opciones de usuario. ID Perfil: {}, ID Proceso: {}, Numa O Usuario: {}", idPerfil, idProceso, numaOUsuario);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycOpcionUsu> opcionesUsuario = iDlPycges.getOpcionUsuario(idPerfil, idProceso, numaOUsuario);

            log.info("Obtención de opciones de usuario completada. Total de opciones encontradas: {}", opcionesUsuario.size());
            return opcionesUsuario;

        } catch (Exception e) {
            log.error("Error al obtener las opciones del usuario: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycReportePetProg> getReportePeticionProgramacion(Integer anio, Integer idProceso) {
        log.info("Iniciando obtención de reporte de peticiones con programación. Año: {}, ID Proceso: {}", anio, idProceso);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycReportePetProg> reportePeticiones = iDlPycges.getReportePeticionProgramacion(anio, idProceso);

            log.info("Obtención de reporte de peticiones con programación completada. Total de registros encontrados: {}", reportePeticiones.size());
            return reportePeticiones;

        } catch (Exception e) {
            log.error("Error al obtener el reporte de peticiones con programación: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public List<PycEstadisticaEstado> getEstadisticaEstados(Integer proceso, Integer area, Integer anio, String estado, String codCia) {
        log.info("Iniciando obtención de estadísticas de estados. Proceso: {}, Área: {}, Estado: {}, Año: {}, CodCia: {}",
                proceso, area, estado, anio, codCia);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycEstadisticaEstado> estadisticasEstados = iDlPycges.getEstadisticaEstados(proceso, area, anio, estado, codCia);

            log.info("Obtención de estadísticas de estados completada. Total de registros encontrados: {}", estadisticasEstados.size());
            return estadisticasEstados;

        } catch (Exception e) {
            log.error("Error al obtener las estadísticas de estados: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycEstadistEstadoSig> getEstadisticaEstadosSiguientes(Integer proceso, Integer area, Integer anio, String estado, String codCia) {
        log.info("Iniciando obtención de estadísticas de estados siguientes. Proceso: {}, Área: {}, Año: {}, Estado: {}, CodCia: {}",
                proceso, area, anio, estado, codCia);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycEstadistEstadoSig> estadisticasEstadosSiguientes = iDlPycges.getEstadisticaEstadosSiguientes(proceso, area, anio, estado, codCia);

            log.info("Obtención de estadísticas de estados siguientes completada. Total de registros encontrados: {}", estadisticasEstadosSiguientes.size());
            return estadisticasEstadosSiguientes;

        } catch (Exception e) {
            log.error("Error al obtener las estadísticas de estados siguientes: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public Integer updateEstadoPeticion(PycUpdEstadoPet estadoData) {
        log.info("Iniciando actualización de estado de petición. ID Petición: {}, Nuevo Estado: {}, Observación: {}",
                estadoData.getIdPeticion(), estadoData.getNuevoEstado(), estadoData.getObservacion());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer resultado = iDlPycges.updateEstadoPeticion(estadoData);

            log.info("Actualización de estado de petición completada. Resultado: {}", resultado);
            return resultado;

        } catch (Exception e) {
            log.error("Error al actualizar el estado de la petición: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public Integer updatePathPerfil(PycUpdPathPerfil perfilData) {
        log.info("Iniciando actualización de path del perfil. ID Usuario: {}, URL Perfil: {}, Path Perfil: {}",
                perfilData.getIdUsuario(), perfilData.getUrlPerfil(), perfilData.getPathPerfil());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer resultado = iDlPycges.updatePathPerfil(perfilData);

            log.info("Actualización de path del perfil completada. Resultado: {}", resultado);
            return resultado;

        } catch (Exception e) {
            log.error("Error al actualizar el path del perfil: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer updateSesion(PycUpdSesion sesionData) {
        log.info("Iniciando actualización de sesión. ID Usuario: {}, Estado: {}",
                sesionData.getIdUsuario(), sesionData.getEstado());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer resultado = iDlPycges.updateSesion(sesionData);

            log.info("Actualización de sesión completada. Resultado: {}", resultado);
            return resultado;

        } catch (Exception e) {
            log.error("Error al actualizar la sesión: {}", e.getMessage(), e);
            throw e;
        }
    }
    @Override
    public boolean bitacoraAccion(PycBitacoraAccion bitacoraData) {
        log.info("Iniciando registro en bitácora. ID Proceso: {}, Acción: {}, Estado: {}",
                bitacoraData.getIdProceso(), bitacoraData.getAccion(), bitacoraData.getEstado());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar el procedimiento
            boolean resultado = iDlPycges.bitacoraAccion(bitacoraData);

            log.info("Registro en bitácora completado. Resultado: {}", resultado);
            return resultado;

        } catch (Exception e) {
            log.error("Error al registrar en bitácora: {}", e.getMessage(), e);
            // Para procedimientos de bitácora, no lanzamos excepción, retornamos false
            return false;
        }
    }

    @Override
    public boolean gestionUsrAsignacionAuto(PycGestUsrAsigAuto asignacionData) {
        log.info("Iniciando gestión de asignación automática de usuario. ID Proceso: {}, ID Perfil: {}, ID Aplicación: {}, ID Estado: {}, Estado: {}, ID Usuario: {}",
                asignacionData.getIdProceso(), asignacionData.getIdPerfil(), asignacionData.getIdAplicacion(),
                asignacionData.getIdEstado(), asignacionData.getEstado(), asignacionData.getIdUsuario());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar el procedimiento
            boolean resultado = iDlPycges.gestionUsrAsignacionAuto(asignacionData);

            log.info("Gestión de asignación automática completada. Resultado: {}", resultado);
            return resultado;

        } catch (Exception e) {
            log.error("Error al gestionar la asignación automática de usuario: {}", e.getMessage(), e);
            // Para procedimientos de gestión, no lanzamos excepción, retornamos false
            return false;
        }
    }

    @Override
    public List<PycEstadoUsu> getEstadoUsuario(String usuario, String clave) {
        log.info("Iniciando obtención de estado de usuario. Usuario: {}", usuario);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycEstadoUsu> estadoUsuario = iDlPycges.getEstadoUsuario(usuario, clave);

            log.info("Obtención de estado de usuario completada. Total de registros encontrados: {}", estadoUsuario.size());
            return estadoUsuario;

        } catch (Exception e) {
            log.error("Error al obtener el estado del usuario: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer insertObservacion(PycInsertObservacion observacionData) {
        log.info("Iniciando inserción de observación. ID Petición: {}, Usuario: {}, Estado: {}, NumaOUsuario: {}",
                observacionData.getIdPeticion(), observacionData.getUsuarioPet(), observacionData.getEstado(), observacionData.getNumaOUsuario());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer idObservacion = iDlPycges.insertObservacion(observacionData);

            log.info("Inserción de observación completada. ID Observación: {}", idObservacion);
            return idObservacion;

        } catch (Exception e) {
            log.error("Error al insertar la observación: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycAnalistaActi> getAnalistaActividad(Integer peticion, Integer actividad) {
        log.info("Iniciando obtención de analistas por actividad. Petición: {}, Actividad: {}", peticion, actividad);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycAnalistaActi> analistas = iDlPycges.getAnalistaActividad(peticion, actividad);

            log.info("Obtención de analistas por actividad completada. Total de analistas: {}", analistas.size());
            return analistas;

        } catch (Exception e) {
            log.error("Error al obtener los analistas por actividad: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycGetDatVarFormSec> getDatVarFormSecc(Integer idProceso, Integer idFormulario, Integer idSeccion, Integer idPeticion) {
        log.info("Iniciando obtención de datos variables de formulario por sección. ID Proceso: {}, ID Formulario: {}, ID Sección: {}, ID Petición: {}",
                idProceso, idFormulario, idSeccion, idPeticion);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycGetDatVarFormSec> datosVariables = iDlPycges.getDatVarFormSecc(idProceso, idFormulario, idSeccion, idPeticion);

            log.info("Obtención de datos variables de formulario completada. Total de datos variables encontrados: {}", datosVariables.size());
            return datosVariables;

        } catch (Exception e) {
            log.error("Error al obtener los datos variables del formulario: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycGetBitacoraPet> getBitacoraPeticion(Integer idPeticion) {
        log.info("Iniciando obtención de bitácora de petición. ID Petición: {}", idPeticion);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycGetBitacoraPet> bitacoraPeticion = iDlPycges.getBitacoraPeticion(idPeticion);

            log.info("Obtención de bitácora de petición completada. Total de registros de bitácora encontrados: {}", bitacoraPeticion.size());
            return bitacoraPeticion;

        } catch (Exception e) {
            log.error("Error al obtener la bitácora de la petición: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycTipoPeticion> getTipoPeticion(Integer idProceso) {
        log.info("Iniciando obtención de tipos de petición. ID Proceso: {}", idProceso);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycTipoPeticion> tiposPeticion = iDlPycges.getTipoPeticion(idProceso);

            log.info("Obtención de tipos de petición completada. Total de tipos de petición encontrados: {}", tiposPeticion.size());
            return tiposPeticion;

        } catch (Exception e) {
            log.error("Error al obtener los tipos de petición: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycEstadoTransc> getCambioEstado(Integer idPerfil, Integer idProceso, Integer idEstado) {
        log.info("Iniciando obtención de estados de transición. ID Perfil: {}, ID Proceso: {}, ID Estado: {}", idPerfil, idProceso, idEstado);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycEstadoTransc> estadosTransicion = iDlPycges.getCambioEstado(idPerfil, idProceso, idEstado);

            log.info("Obtención de estados de transición completada. Total de estados de transición encontrados: {}", estadosTransicion.size());
            return estadosTransicion;

        } catch (Exception e) {
            log.error("Error al obtener los estados de transición: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycDocPeticion> getDocumentoPeticion(Integer idPeticion, Integer perfilAct) {
        log.info("Iniciando obtención de documentos de petición. ID Petición: {}, Perfil Actual: {}", idPeticion, perfilAct);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycDocPeticion> documentosPeticion = iDlPycges.getDocumentoPeticion(idPeticion, perfilAct);

            log.info("Obtención de documentos de petición completada. Total de documentos encontrados: {}", documentosPeticion.size());
            return documentosPeticion;

        } catch (Exception e) {
            log.error("Error al obtener los documentos de la petición: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycDocXPeticion> getDocXPeticion(Integer idTipo) {
        log.info("Iniciando obtención de documentos por tipo de petición. ID Tipo: {}", idTipo);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycDocXPeticion> documentosXPeticion = iDlPycges.getDocXPeticion(idTipo);

            log.info("Obtención de documentos por tipo de petición completada. Total de documentos encontrados: {}", documentosXPeticion.size());
            return documentosXPeticion;

        } catch (Exception e) {
            log.error("Error al obtener los documentos por tipo de petición: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycDatVarForSeccTippe> getDatVarFormSeccTippe(Integer idProceso, Integer idTipo) {
        log.info("Iniciando obtención de datos variables de formulario por tipo. ID Proceso: {}, ID Tipo: {}", idProceso, idTipo);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycDatVarForSeccTippe> datosVariablesTippe = iDlPycges.getDatVarFormSeccTippe(idProceso, idTipo);

            log.info("Obtención de datos variables de formulario por tipo completada. Total de datos variables encontrados: {}", datosVariablesTippe.size());
            return datosVariablesTippe;

        } catch (Exception e) {
            log.error("Error al obtener los datos variables del formulario por tipo: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer newDatVarPeticion(PycNewDatVarPet datVarData) {
        log.info("Iniciando creación/actualización de dato variable de petición. ID Petición: {}, ID Formulario: {}, ID Sección: {}, ID Dato Variable: {}",
                datVarData.getIdPeticion(), datVarData.getIdFormulario(), datVarData.getIdSeccion(), datVarData.getIdDatoVar());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer idRegistro = iDlPycges.newDatVarPeticion(datVarData);

            log.info("Creación/actualización de dato variable de petición completada. ID Registro: {}", idRegistro);
            return idRegistro;

        } catch (Exception e) {
            log.error("Error al crear/actualizar el dato variable de la petición: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycGetPerfiles> getPerfilesPorProceso(Integer idProceso) {
        log.info("Iniciando obtención de perfiles por proceso. ID Proceso: {}", idProceso);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycGetPerfiles> perfilesProceso = iDlPycges.getPerfilesPorProceso(idProceso);

            log.info("Obtención de perfiles por proceso completada. Total de perfiles encontrados: {}", perfilesProceso.size());
            return perfilesProceso;

        } catch (Exception e) {
            log.error("Error al obtener los perfiles por proceso: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycGetEstadoPerfilCod> getEstadoPerfilCod(Integer idProceso, Integer idPerfil) {
        log.info("Iniciando obtención de estados por perfil. ID Proceso: {}, ID Perfil: {}", idProceso, idPerfil);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycGetEstadoPerfilCod> estadosPerfil = iDlPycges.getEstadoPerfilCod(idProceso, idPerfil);

            log.info("Obtención de estados por perfil completada. Total de estados encontrados: {}", estadosPerfil.size());
            return estadosPerfil;

        } catch (Exception e) {
            log.error("Error al obtener los estados por perfil: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer asignaPeticionDesa(PycAsignaPeticionDesa asignacionData) {
        log.info("Iniciando asignación de petición a desarrollador. ID Aplicación: {}, ID Desarrollador: {}, ID Petición: {}, ID Perfil: {}, numaOUsuario: {}",
                asignacionData.getIdAplicacion(), asignacionData.getIdDesarrollador(),
                asignacionData.getIdPeticion(), asignacionData.getIdPerfil(), asignacionData.getNumaOUsuario());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer idPeticion = iDlPycges.asignaPeticionDesa(asignacionData);

            log.info("Asignación de petición a desarrollador completada. ID Petición procesada: {}", idPeticion);
            return idPeticion;

        } catch (Exception e) {
            log.error("Error al asignar la petición al desarrollador: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycRespPeti> getResponsablePeticion(Integer idPeticion, String numaOUsuario) {
        log.info("Iniciando consulta de responsables de petición. ID Petición: {}, Usuario: {}",
                idPeticion, numaOUsuario);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycRespPeti> responsables = iDlPycges.getResponsablePeticion(idPeticion, numaOUsuario);

            log.info("Consulta de responsables de petición completada. ID Petición: {}, Usuario: {}, Responsables encontrados: {}",
                    idPeticion, numaOUsuario, responsables.size());
            return responsables;

        } catch (Exception e) {
            log.error("Error al obtener responsables de petición. ID Petición: {}. Error: {}",
                    idPeticion, e.getMessage(), e);
            throw new RuntimeException("Error al obtener responsables de petición: " + e.getMessage(), e);
        }
    }

    @Override
    public List<PycActPeticion> getActPeticion(Integer idPeticion, Integer idActividad) {
        log.info("Iniciando obtención de actividades de petición. ID Petición: {}, ID Actividad: {}", idPeticion, idActividad);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycActPeticion> actividadesPeticion = iDlPycges.getActPeticion(idPeticion, idActividad);

            log.info("Obtención de actividades de petición completada. Total de actividades encontradas: {}", actividadesPeticion.size());
            return actividadesPeticion;

        } catch (Exception e) {
            log.error("Error al obtener las actividades de la petición: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer updatePeticion(PycUpdPeticion peticionData) {
        log.info("Iniciando actualización de petición. ID Petición: {}, Nombre: {}, ID Usuario Solicitante: {}, numaOUsuario: {}",
                peticionData.getIdPeticion(), peticionData.getNomPeticion(),
                peticionData.getIdSolUser(), peticionData.getNumaOUsuario());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer idPeticion = iDlPycges.updatePeticion(peticionData);

            log.info("Actualización de petición completada. ID Petición: {}", idPeticion);
            return idPeticion;

        } catch (Exception e) {
            log.error("Error al actualizar la petición: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer actualizarPeticion(PycUpdPeticion peticionData) {
        log.info("Iniciando actualización de petición (POST). ID Petición: {}, Nombre: {}, ID Usuario Solicitante: {}, numaOUsuario: {}",
                peticionData.getIdPeticion(), peticionData.getNomPeticion(),
                peticionData.getIdSolUser(), peticionData.getNumaOUsuario());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función (reutiliza la misma implementación)
            Integer idPeticion = iDlPycges.updatePeticion(peticionData);

            log.info("Actualización de petición (POST) completada. ID Petición: {}", idPeticion);
            return idPeticion;

        } catch (Exception e) {
            log.error("Error al actualizar la petición (POST): {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer creaSolicitudRH(PycCreaSoliRH solicitudData) {
        log.info("Iniciando creación de solicitud RH. Cliente: {}, Teléfono: {}, Email: {}, DPI: {}",
                solicitudData.getNomCli(), solicitudData.getTelCli(),
                solicitudData.getEmailCli(), solicitudData.getDpi());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer idSolicitud = iDlPycges.creaSolicitudRH(solicitudData);

            log.info("Creación de solicitud RH completada. ID Solicitud: {}", idSolicitud);
            return idSolicitud;

        } catch (Exception e) {
            log.error("Error al crear la solicitud RH: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycDocNoObli> getDocNoObligatorios(Integer idPeticion) {
        log.info("Iniciando obtención de documentos no obligatorios para la petición {}", idPeticion);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycDocNoObli> documentosNoObligatorios = iDlPycges.getDocNoObligatorios(idPeticion);

            log.info("Obtención de documentos no obligatorios completada. Total de documentos encontrados: {}", documentosNoObligatorios.size());
            return documentosNoObligatorios;

        } catch (Exception e) {
            log.error("Error al obtener los documentos no obligatorios: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PycCatAct> getCategoriaAct(Integer idProceso) {
        log.info("Iniciando obtención de categorías activas del tablero. ID Proceso: {}", idProceso);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<PycCatAct> categoriasActivas = iDlPycges.getCategoriaAct(idProceso);

            log.info("Obtención de categorías activas completada. Total de categorías encontradas: {}", categoriasActivas.size());
            return categoriasActivas;

        } catch (Exception e) {
            log.error("Error al obtener las categorías activas: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer insertActividad(PycInserAct actividadData) {
        log.info("Iniciando inserción de actividad. ID Petición: {}, Nombre: {}, Usuario: {}",
                actividadData.getIdPeti(), actividadData.getNombAct(), actividadData.getUserPet());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer idActividad = iDlPycges.insertActividad(actividadData);

            log.info("Inserción de actividad completada. ID Actividad creada: {}", idActividad);
            return idActividad;

        } catch (Exception e) {
            log.error("Error al insertar la actividad: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer editActividad(PycEditAct actividadData) {
        log.info("Iniciando edición de actividad. ID Petición: {}, ID Actividad: {}, Nombre: {}",
                actividadData.getIdPeticion(), actividadData.getIdActividad(), actividadData.getNomActividad());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer idPeticion = iDlPycges.editActividad(actividadData);

            log.info("Edición de actividad completada. ID Petición actualizada: {}", idPeticion);
            return idPeticion;

        } catch (Exception e) {
            log.error("Error al editar la actividad: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer updateActividad(PycUpdAct actividadData) {
        log.info("Iniciando actualización de actividad. ID Petición: {}, ID Actividad: {}, Horas Reales: {}, Indicador: {}",
                actividadData.getIdPeticion(), actividadData.getIdActividad(),
                actividadData.getHrsReal(), actividadData.getInd());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            Integer idPeticion = iDlPycges.updateActividad(actividadData);

            log.info("Actualización de actividad completada. ID Petición actualizada: {}", idPeticion);
            return idPeticion;

        } catch (Exception e) {
            log.error("Error al actualizar la actividad: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public String updateEncuesta(PycUpdEnc encuestaData) {
        log.info("Iniciando actualización de encuesta. ID Petición: {}, Calificación: {}, Comentario: {}",
                encuestaData.getIdPeticion(), encuestaData.getEncuCalifi(), encuestaData.getEncuComent());

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            String idPeticion = iDlPycges.updateEncuesta(encuestaData);

            log.info("Actualización de encuesta completada. ID Petición actualizada: {}", idPeticion);
            return idPeticion;

        } catch (Exception e) {
            log.error("Error al actualizar la encuesta: {}", e.getMessage(), e);
            throw e;
        }
    }
}