package com.mapfre.tron.gt.api.bl;

import java.time.LocalDate;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapfre.tron.gt.api.dl.IDlTracking;
import com.mapfre.tron.gt.api.model.DetalleSiniestro;
import com.mapfre.tron.gt.api.model.EncuestaSiniestro;
import com.mapfre.tron.gt.api.model.EtapaSiniestro;
import com.mapfre.tron.gt.api.model.Siniestro;
import com.mapfre.tron.gt.api.model.SiniestroNit;
import com.mapfre.tron.gt.api.model.SiniestroVehiculo;
import com.mapfre.tron.gt.api.model.UrlSiniestro;

import lombok.extern.slf4j.Slf4j;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;

/**
 * Implementación de la interfaz IBlTracking para operaciones de tracking en la capa de negocio.
 */
@Service
@Slf4j
public class BlTrackingImpl extends BaseService implements IBlTracking {

    @Autowired
    private IDlTracking iDlTracking;

    @Override
    public List<Siniestro> getSiniestros(Integer codRamo, Integer codInter, String numPoliza,
                                         String numSini, LocalDate fechaInicio, LocalDate fechaFin) {
        log.info("Iniciando búsqueda de siniestros con codRamo={}, codInter={}, numPoliza={}, numSini={}, fechaInicio={}, fechaFin={}",
                 codRamo, codInter, numPoliza, numSini, fechaInicio, fechaFin);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (codRamo == null) {
                log.error("El código de ramo es obligatorio");
                throw new IllegalArgumentException("El código de ramo es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            List<Siniestro> siniestros = iDlTracking.getSiniestros(
                    codRamo, codInter, numPoliza, numSini, fechaInicio, fechaFin);

            log.info("Búsqueda de siniestros completada. Siniestros encontrados: {}", siniestros.size());
            return siniestros;

        } catch (Exception e) {
            log.error("Error al buscar siniestros: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer getRamoSiniestro(String numSiniestro) {
        log.info("Iniciando búsqueda de ramo para el siniestro: {}", numSiniestro);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (numSiniestro == null || numSiniestro.trim().isEmpty()) {
                log.error("El número de siniestro es obligatorio");
                throw new IllegalArgumentException("El número de siniestro es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            Integer ramo = iDlTracking.getRamoSiniestro(numSiniestro);

            if (ramo != null) {
                log.info("Búsqueda de ramo completada. Ramo encontrado: {}", ramo);
            } else {
                log.info("No se encontró ramo para el siniestro: {}", numSiniestro);
            }

            return ramo;

        } catch (Exception e) {
            log.error("Error al buscar el ramo del siniestro: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<EtapaSiniestro> getTrackingEtapaSiniestro(String numSiniestro, Integer codRamo) {
        log.info("Iniciando búsqueda de etapas para el siniestro: {} con ramo: {}", numSiniestro, codRamo);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (numSiniestro == null || numSiniestro.trim().isEmpty()) {
                log.error("El número de siniestro es obligatorio");
                throw new IllegalArgumentException("El número de siniestro es obligatorio");
            }

            if (codRamo == null) {
                log.error("El código de ramo es obligatorio");
                throw new IllegalArgumentException("El código de ramo es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            List<EtapaSiniestro> etapas = iDlTracking.getTrackingEtapaSiniestro(numSiniestro, codRamo);

            log.info("Búsqueda de etapas completada. Etapas encontradas: {}", etapas.size());
            return etapas;

        } catch (Exception e) {
            log.error("Error al buscar las etapas del siniestro: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<SiniestroNit> getSiniestroByDoc(String numSiniestro, String numDoc, String tipoDoc) {
        log.info("Iniciando búsqueda de siniestro por documento: {} de tipo: {} con número de siniestro: {}", numDoc, tipoDoc, numSiniestro);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (numSiniestro == null || numSiniestro.trim().isEmpty()) {
                log.error("El número de siniestro es obligatorio");
                throw new IllegalArgumentException("El número de siniestro es obligatorio");
            }

            if (numDoc == null || numDoc.trim().isEmpty()) {
                log.error("El número de documento es obligatorio");
                throw new IllegalArgumentException("El número de documento es obligatorio");
            }

            if (tipoDoc == null || tipoDoc.trim().isEmpty()) {
                log.error("El tipo de documento es obligatorio");
                throw new IllegalArgumentException("El tipo de documento es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            List<SiniestroNit> siniestros = iDlTracking.getSiniestroByDoc(numSiniestro, numDoc, tipoDoc);

            log.info("Búsqueda de siniestro por documento completada. Siniestros encontrados: {}", siniestros.size());
            return siniestros;

        } catch (Exception e) {
            log.error("Error al buscar el siniestro por documento: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public UrlSiniestro getUrlSiniestro(String url, String numSiniestro, String tipoCliente) {
        log.info("Iniciando generación de URL encriptada con url={}, numSiniestro={}, tipoCliente={}", url, numSiniestro, tipoCliente);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (url == null || url.trim().isEmpty()) {
                log.error("La URL base es obligatoria");
                throw new IllegalArgumentException("La URL base es obligatoria");
            }

            if (numSiniestro == null || numSiniestro.trim().isEmpty()) {
                log.error("El número de siniestro es obligatorio");
                throw new IllegalArgumentException("El número de siniestro es obligatorio");
            }

            if (tipoCliente == null || tipoCliente.trim().isEmpty()) {
                log.error("El tipo de cliente es obligatorio");
                throw new IllegalArgumentException("El tipo de cliente es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            UrlSiniestro urlSiniestro = iDlTracking.getUrlSiniestro(url, numSiniestro, tipoCliente);

            if (urlSiniestro != null) {
                log.info("Generación de URL encriptada completada: {}", urlSiniestro.getUrl());
            } else {
                log.info("No se pudo generar la URL encriptada");
            }

            return urlSiniestro;

        } catch (Exception e) {
            log.error("Error al generar la URL encriptada: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<DetalleSiniestro> getTrackingDetalleSiniestro(String numSiniestro, Integer codRamo) {
        log.info("Iniciando obtención de detalle de siniestro con numSiniestro={}, codRamo={}", numSiniestro, codRamo);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (numSiniestro == null || numSiniestro.trim().isEmpty()) {
                log.error("El número de siniestro es obligatorio");
                throw new IllegalArgumentException("El número de siniestro es obligatorio");
            }

            if (codRamo == null) {
                log.error("El código de ramo es obligatorio");
                throw new IllegalArgumentException("El código de ramo es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            List<DetalleSiniestro> detalles = iDlTracking.getTrackingDetalleSiniestro(numSiniestro, codRamo);

            log.info("Obtención de detalle de siniestro completada. Detalles encontrados: {}", detalles.size());
            return detalles;

        } catch (Exception e) {
            log.error("Error al obtener el detalle del siniestro: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<EncuestaSiniestro> getEncuestaSiniestro() {
        log.info("Iniciando obtención de encuesta de siniestro");

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<EncuestaSiniestro> encuestas = iDlTracking.getEncuestaSiniestro();

            log.info("Obtención de encuesta de siniestro completada. Encuestas encontradas: {}", encuestas.size());
            return encuestas;

        } catch (Exception e) {
            log.error("Error al obtener la encuesta de siniestro: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public String saveEncuesta(String numSiniestro, Integer idEncuesta, String archXml) {
        log.info("Iniciando guardado de encuesta de siniestro con numSiniestro={}, idEncuesta={}", numSiniestro, idEncuesta);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (numSiniestro == null || numSiniestro.trim().isEmpty()) {
                log.error("El número de siniestro es obligatorio");
                throw new IllegalArgumentException("El número de siniestro es obligatorio");
            }

            if (idEncuesta == null) {
                log.error("El identificador de encuesta es obligatorio");
                throw new IllegalArgumentException("El identificador de encuesta es obligatorio");
            }

            if (archXml == null || archXml.trim().isEmpty()) {
                log.error("El XML con las respuestas es obligatorio");
                throw new IllegalArgumentException("El XML con las respuestas es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            String resultado = iDlTracking.saveEncuesta(numSiniestro, idEncuesta, archXml);

            log.info("Guardado de encuesta de siniestro completado. Resultado: {}", resultado);
            return resultado;

        } catch (Exception e) {
            log.error("Error al guardar la encuesta de siniestro: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public String validaIngresoEncuesta(String numSiniestro) {
        log.info("Iniciando validación de ingreso de encuesta con numSiniestro={}", numSiniestro);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (numSiniestro == null || numSiniestro.trim().isEmpty()) {
                log.error("El número de siniestro es obligatorio");
                throw new IllegalArgumentException("El número de siniestro es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            String resultado = iDlTracking.validaIngresoEncuesta(numSiniestro);

            log.info("Validación de ingreso de encuesta completada. Resultado: {}", resultado);
            return resultado;

        } catch (Exception e) {
            log.error("Error al validar el ingreso de encuesta: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<SiniestroVehiculo> getSiniestroVehiculo(String numSiniestro, Integer codCia, String numPoliza, String codFase, Integer codRamo) {
        log.info("Iniciando obtención de información de siniestro de vehículo con numSiniestro={}, codCia={}, numPoliza={}, codFase={}, codRamo={}",
                 numSiniestro, codCia, numPoliza, codFase, codRamo);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (numSiniestro == null || numSiniestro.trim().isEmpty()) {
                log.error("El número de siniestro es obligatorio");
                throw new IllegalArgumentException("El número de siniestro es obligatorio");
            }

            if (codCia == null) {
                log.error("El código de compañía es obligatorio");
                throw new IllegalArgumentException("El código de compañía es obligatorio");
            }

            if (numPoliza == null || numPoliza.trim().isEmpty()) {
                log.error("El número de póliza es obligatorio");
                throw new IllegalArgumentException("El número de póliza es obligatorio");
            }

            if (codFase == null || codFase.trim().isEmpty()) {
                log.error("El código de fase es obligatorio");
                throw new IllegalArgumentException("El código de fase es obligatorio");
            }

            if (codRamo == null) {
                log.error("El código de ramo es obligatorio");
                throw new IllegalArgumentException("El código de ramo es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            List<SiniestroVehiculo> siniestros = iDlTracking.getSiniestroVehiculo(numSiniestro, codCia, numPoliza, codFase, codRamo);

            log.info("Obtención de información de siniestro de vehículo completada. Siniestros encontrados: {}", siniestros.size());
            return siniestros;

        } catch (Exception e) {
            log.error("Error al obtener la información de siniestro de vehículo: {}", e.getMessage(), e);
            throw e;
        }
    }
}