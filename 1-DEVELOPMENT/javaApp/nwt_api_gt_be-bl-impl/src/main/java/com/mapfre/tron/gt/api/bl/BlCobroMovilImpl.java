package com.mapfre.tron.gt.api.bl;

import com.mapfre.tron.gt.api.dl.IDlCobroMovil;
import com.mapfre.tron.gt.api.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;


@Service
@Slf4j
public class BlCobroMovilImpl extends BaseService implements IBlCobroMovil {

    @Autowired
    private IDlCobroMovil iDlCobroMovil;

    @Override
    public EntidadResponse ListaEntidad(Integer codigoSistema, String codigoMoneda) {
        log.info("Iniciando proceso de ListarEntidades - codigoSistema: {}, codigoMoneda:{}",
                codigoSistema, codigoMoneda);

        EntidadResponse response = new EntidadResponse();
        ArrayList<Entidad> listaEntidades = iDlCobroMovil.ListarEntidades(codigoSistema, codigoMoneda);
        try {
            if (codigoSistema == null || codigoMoneda == null || codigoMoneda.isEmpty()) {
                response.setCodigo("400");
                response.mensaje("Solicitud incorrecta, parametros no validos");
                response.setListaEntidades(null);
                return response;
            }
            if (listaEntidades != null & !listaEntidades.isEmpty()) {
                response.setCodigo("200");
                response.setMensaje("Solicitud exitosa");
                response.setListaEntidades(listaEntidades);
            } else {
                log.info("Parametros boy incorrectos estatus 400 - endidades: {}",
                        listaEntidades);
                response.setCodigo("404");
                response.setMensaje("Recurso no encontrado en el servidor");
                response.setListaEntidades(listaEntidades);
            }
        } catch (Exception ex) {
            log.error("Error interno al listar entidades bancarias: {}, error: {}", listaEntidades, ex.getMessage(), ex);
            response.setCodigo("500");
            response.setMensaje("Error interno del servidor");
            response.setListaEntidades(null);
        }
        log.info("Proceso de listar entidades financieras completado - código respuesta: {}", response.getCodigo());
        return response;
    }

    @Override
    public CobroResponse ListaOpcionesCobro() {
        log.info("Iniciando procesado que recupera ListaOpcionesCobro");
        CobroResponse response = new CobroResponse();
        ArrayList<Cobro> listaOcpcionesCobro = iDlCobroMovil.ListarOpcioensCobro();
        try {
            if (listaOcpcionesCobro != null & !listaOcpcionesCobro.isEmpty()) {
                log.info("Endpoint procesado correctamente para ListaOpcionesCobro con estatus 200");
                response.setCodigo("200");
                response.setMensaje("Solicitud exitosa");
                response.setListaOpcionCobro(listaOcpcionesCobro);
            } else {
                log.info("Recursos no encontrados para la peticion ListarOpcionesCobro error 400");
                response.setCodigo("404");
                response.setMensaje("Recurso no encontrado en el servidor");
                response.setListaOpcionCobro(listaOcpcionesCobro);
            }
        } catch (Exception ex) {
            log.error("Error interno al Listar opciones de cobro {}, error: {}", listaOcpcionesCobro, ex.getMessage(), ex);
            response.setCodigo("500");
            response.setMensaje("Error interno del servidor");
            response.setListaOpcionCobro(null);
        }
        log.info("Proceso de listar opciones de cobro completado - código respuesta: {}", response.getCodigo());
        return response;
    }

    @Override
    public RutaDiaria CrearRutaDiaria(Integer idUsuarioCrea, Integer idUsuarioAsignado) {
        log.info("Iniciando solicitud para crearRutaDiaria idUsuarioCrea{}, idUsuarioAsignado{},",
                idUsuarioCrea, idUsuarioAsignado);
        RutaDiaria response = new RutaDiaria();
        Integer idRutaDiaria = iDlCobroMovil.CrearRutaDiaria(idUsuarioCrea, idUsuarioAsignado);
        try {
            if (idUsuarioAsignado == null || idUsuarioCrea == null) {
                response.setCodigo("400");
                response.mensaje("Solicitud incorrecta, parametros no validos");
                response.setIdRutaCreada(null);
                return response;
            }
            if (idRutaDiaria != null) {
                log.info("crearRutaDiaria creada correctamente idrutaCreada{}", idRutaDiaria );
                response.setCodigo("201");
                response.setMensaje("Solicitud exitosa");
                response.setIdRutaCreada(idRutaDiaria);
            } else {
                log.info("Error interno del servidior" );
                response.setCodigo("500");
                response.setMensaje("Error interno del servidior");
                response.setIdRutaCreada(null);
            }
        } catch (Exception ex) {
            log.error("Error interno al crear la ruta diaria - idRuta: {}, error: {}", idRutaDiaria, ex.getMessage(), ex);
            response.setCodigo("500");
            response.setMensaje("Error interno del servidor");
            response.setIdRutaCreada(null);
        }
        log.info("Proceso de creacion de ruta diaria completado - código respuesta: {}", response.getCodigo());
        return response;
    }

    @Override
    public RutaDiaria CrearRuta(Integer idUsuarioCrea, Integer idUsuarioAsignado) {
        log.info("Iniciando solicitud para crearRuta idUsuarioCrea{}, idUsuarioAsignado{},",
                idUsuarioCrea, idUsuarioAsignado);
        RutaDiaria response = new RutaDiaria();
        Integer idRuta = iDlCobroMovil.CrearRuta(idUsuarioCrea, idUsuarioAsignado);
        try {
            if (idUsuarioAsignado == null || idUsuarioCrea == null) {
                response.setCodigo("400");
                response.mensaje("Solicitud incorrecta, parametros no validos");
                response.setIdRutaCreada(null);
                return response;
            }
            if (idRuta != null) {
                log.info("crearRuta creada correctamente. idRuta {}", idRuta );
                response.setCodigo("201");
                response.setMensaje("Solicitud exitosa");
                response.setIdRutaCreada(idRuta);
            } else {
                log.info("Error interno del servidior. idRuta {}", idRuta );
                response.setCodigo("500");
                response.setMensaje("Error interno del servidior");
                response.setIdRutaCreada(null);
            }
        } catch (Exception ex) {
            log.error("Error interno al crear la ruta - idRuta: {}, error: {}", idRuta, ex.getMessage(), ex);
            response.setCodigo("500");
            response.setMensaje("Error interno del servidor");
            response.setIdRutaCreada(null);
        }
        log.info("Proceso de creacion de ruta completado - código respuesta: {}", response.getCodigo());
        return response;
    }

    @Override
    public ModificarRutaResponse ModificarRuta(Integer idUsuarioCrea, Integer idUsuarioAsignado, Integer idRuta) {
        log.info("Iniciando proceso de modificación de ruta - idUsuarioCrea: {}, idUsuarioAsignado: {}, idRuta: {}",
                idUsuarioCrea, idUsuarioAsignado, idRuta);

        ModificarRutaResponse response = new ModificarRutaResponse();

        if (idUsuarioCrea == null || idUsuarioAsignado == null || idRuta == null) {
            log.warn("Parámetros inválidos - idUsuarioCrea: {}, idUsuarioAsignado: {}, idRuta: {}",
                    idUsuarioCrea, idUsuarioAsignado, idRuta);
            response.setCodigo("400");
            response.setMensaje("Solicitud incorrecta, parámetros no válidos");
            response.setIdRuta(null);
            return response;
        }
        try {
            log.info("Llamando a la capa DL para modificar la ruta");
            Integer idRutaModificada = iDlCobroMovil.ModificarRuta(idUsuarioCrea, idUsuarioAsignado, idRuta);

            if (idRutaModificada != null) {
                log.info("Ruta modificada exitosamente - idRuta: {}", idRutaModificada);
                response.setCodigo("200");
                response.setMensaje("Ruta modificada exitosamente");
                response.setIdRuta(idRutaModificada);
            } else {
                log.warn("No se pudo modificar la ruta - idRuta: {}", idRuta);
                response.setCodigo("404");
                response.setMensaje("No se encontró la ruta especificada o no se pudo modificar");
                response.setIdRuta(null);
            }
        } catch (Exception ex) {
            log.error("Error interno al modificar la ruta - idRuta: {}, error: {}", idRuta, ex.getMessage(), ex);
            response.setCodigo("500");
            response.setMensaje("Error interno del servidor");
            response.setIdRuta(null);
        }
        log.info("Proceso de modificación de ruta completado - código respuesta: {}", response.getCodigo());
        return response;
    }

    @Override
    public InsertarDetalleRutaDiarioResponse InsertarDetalleRutaDiario(InsertarDetalleRutaDiarioRequest request) {
        log.info("Iniciando proceso de inserción de detalle de ruta diario - ruta: {}, idpol: {}, numpol: {}",
                request.getRuta(), request.getIdpol(), request.getNumpol());
        InsertarDetalleRutaDiarioResponse response = new InsertarDetalleRutaDiarioResponse();

        if (request == null) {
            log.warn("Request es nulo");
            response.setCodigo("400");
            response.setMensaje("Solicitud incorrecta, request no puede ser nulo");
            return response;
        }

        if (request.getCodpol().trim().isEmpty()
                || request.getNumpol().trim().isEmpty()
                || request.getNumreq().trim().isEmpty() || request.getMoneda().trim().isEmpty()
                || request.getTotal().trim().isEmpty() || request.getCuota().trim().isEmpty()
                || request.getSistema().trim().isEmpty()
                || request.getEsAviso().trim().isEmpty())
             {

            log.warn("Campos de texto no pueden estar vacíos");
            response.setCodigo("400");
            response.setMensaje("Los campos de texto no pueden estar vacíos");
            return response;
        }
        try {
            log.info("Llamando a la capa DL para insertar detalle de ruta diario");
            boolean insercionCorrecta = iDlCobroMovil.InsertarDetalleRutaDiario(request);
            if (insercionCorrecta) {
                log.info("Detalle de ruta diario insertado exitosamente - ruta: {}", request.getRuta());
                response.setCodigo("201");
                response.setMensaje("Detalle de ruta diario insertado exitosamente");
            } else {
                log.info("No se pudo realizar la insercion correctamente: {}", request.getRuta());
                response.setCodigo("404");
                response.setMensaje("No se pudo realiza la insercion correctamente");
            }
        } catch (Exception ex) {
            log.error("Error interno al insertar detalle de ruta diario - ruta: {}, error: {}", request.getRuta(), ex.getMessage(), ex);
            response.setCodigo("500");
            response.setMensaje("Error interno del servidor");
        }
        log.info("Proceso de inserción de detalle de ruta diario completado - código respuesta: {}", response.getCodigo());
        return response;
    }

    @Override
    public InsertarDetalleRuta2Response InsertarDetalleRuta2(InsertarDetalleRuta2Request request) {
        log.info("Iniciando proceso de inserción de detalle de ruta2 - ruta: {}, idPol: {}, numPol: {}, correo: {}",
                request.getRuta(), request.getIdPol(), request.getNumPol(), request.getCorreo());
        InsertarDetalleRuta2Response response = new InsertarDetalleRuta2Response();

        // Validaciones de parámetros de entrada
        if (request == null) {
            log.warn("Request es nulo");
            response.setCodigo("400");
            response.setMensaje("Solicitud incorrecta, request no puede ser nulo");
            return response;
        }
        // Validaciones de campos obligatorios
        if (request.getRuta() == null
                || request.getIdPol() == null || request.getIdPol().trim().isEmpty()
                || request.getFechaVencimiento() == null || request.getFechaVencimiento().trim().isEmpty()
                || request.getEsAviso() == null || request.getEsAviso().isEmpty()) {
            log.warn("el campo es obligatoria");
            response.setCodigo("400");
            response.setMensaje("Solicitud incorrecta, los datos no pueden ser nullos o vacios");
            return response;
        }
        try {
            log.info("Llamando a la capa DL para insertar detalle de ruta2");
            boolean insercionCorrecta = iDlCobroMovil.InsertarDetalleRuta2(request);

            if (insercionCorrecta) {
                response.setCodigo("201");
                response.setMensaje("Detalle de ruta diario insertado exitosamente");
            } else {
                response.setCodigo("404");
                response.setMensaje("No se pudo realiza la insercion correctamente");
            }

        } catch (Exception ex) {
            log.error("Error interno al insertar detalle de ruta2 - ruta: {}, error: {}", request.getRuta(), ex.getMessage(), ex);
            response.setCodigo("500");
            response.setMensaje("Error interno del servidor");
        }
        log.info("Proceso de inserción de detalle de ruta2 completado - código respuesta: {}", response.getCodigo());
        return response;
    }

    @Override
    public TipoMedioPagoResponse InsertarTipoMedioPago(TipoMedioPagoRequest request) {
        log.info("Iniciando proceso de inserción de tipo de medio de pago - idRuta: {}, tipo: {}, entidadFinanciera: {}, monto: {}",
                request.getIdRuta(), request.getTipo(), request.getEntidadFinanciera(), request.getMonto());

        TipoMedioPagoResponse response = new TipoMedioPagoResponse();
        if (request == null || request.getIdRuta() == null
                || request.getTipo() == null || request.getTipo().trim().isEmpty()
                || request.getMoneda() == null || request.getMoneda().trim().isEmpty()
                || request.getMonto() == null || request.getMonto().trim().isEmpty()
                || request.getIdUsuarioCobra() == null) {
            log.warn("los valores del body son vacios o null");
            response.setCodigo("400");
            response.setMensaje("Solicitud incorrecta, valores obligatorios en null");
            return response;
        }
        try {
            log.info("Llamando a la capa DL para insertar tipo de medio de pago");
            boolean insertadoCorrectamente = iDlCobroMovil.InsertarTipoMedioPago(request);
            if (insertadoCorrectamente) {
                log.info("Tipo de medio de pago insertado exitosamente - idRuta: {}", request.getIdRuta());
                response.setCodigo("201");
                response.setMensaje("Tipo de medio de pago insertado exitosamente");
            } else {
                log.info("Tipo de medio de pago no fue insertado correctamente - idRuta: {}", request.getIdRuta());
                response.setCodigo("404");
                response.setMensaje("Tipo de medio de pago no fue insertado correctamente");
            }
        } catch (Exception ex) {
            log.error("Error interno al insertar tipo de medio de pago - idRuta: {}, error: {}",
                    request.getIdRuta(), ex.getMessage(), ex);
            response.setCodigo("500");
            response.setMensaje("Error interno del servidor");
        }
        log.info("Proceso de inserción de tipo de medio de pago completado - código respuesta: {}", response.getCodigo());
        return response;
    }

    @Override
    public AvisoReciboRutaResponse UpdateAvisoReciboRuta(AvisoReciboRutaRequest request) {
        log.info("Iniciando proceso de actualización de aviso de recibo de ruta - ruta: {}, recibo: {}, usuario: {}",
                request.getRuta(), request.getRecibo(), request.getUsuario());
        AvisoReciboRutaResponse response = new AvisoReciboRutaResponse();

        // Validaciones de parámetros de entrada
        if (request == null) {
            log.warn("Request es nulo");
            response.setCodigo("400");
            response.setMensaje("Solicitud incorrecta, request no puede ser nulo");
            return response;
        }
        if (request.getRuta() == null || request.getRuta().trim().isEmpty()
                || request.getRecibo() == null || request.getRecibo().trim().isEmpty()
                || request.getUsuario() == null || request.getUsuario().trim().isEmpty()
                || request.getFecha() == null
                || request.getFecha().trim().isEmpty()) {
            log.warn("datos obligatorios no pueden ser null o vacios");
            response.setCodigo("400");
            response.setMensaje("Solicitud incorrecta,los datos no deben ser null o vacios");
            return response;
        }
        try {
            log.info("Llamando a la capa DL para actualizar aviso de recibo de ruta");
            boolean actualizacionCorrecta = iDlCobroMovil.UpdateAvisoReciboRuta(request);
            if (actualizacionCorrecta) {
                log.info("Aviso de recibo de ruta actualizado exitosamente - ruta: {}", request.getRuta());
                response.setCodigo("200");
                response.setMensaje("Aviso de recibo de ruta actualizado exitosamente");
            } else {
                log.warn("No se pudo actualizar el aviso de recibo de ruta - ruta: {}", request.getRuta());
                response.setCodigo("404");
                response.setMensaje("No se pudo actualizar el aviso de recibo de ruta");
            }
        } catch (Exception ex) {
            log.error("Error interno al actualizar aviso de recibo de ruta - ruta: {}, error: {}",
                    request.getRuta(), ex.getMessage(), ex);
            response.setCodigo("500");
            response.setMensaje("Error interno del servidor");
        }

        log.info("Proceso de actualización de aviso de recibo de ruta completado - código respuesta: {}", response.getCodigo());
        return response;
    }

    @Override
    public UpdateReciboRutaResponse UpdateReciboRuta(ReciboRutaRequest request) {
        log.info("Iniciando proceso de actualización de recibo de ruta - ruta: {}, recibo: {}, usuario: {}",
                request.getRuta(), request.getRecibo(), request.getUsuario());

        UpdateReciboRutaResponse response = new UpdateReciboRutaResponse();

        // Validaciones de parámetros de entrada
        if (request == null
                || request.getRuta() == null
                || request.getRecibo() == null || request.getRecibo().trim().isEmpty()
                || request.getUsuario() == null || request.getUsuario().trim().isEmpty()
                || request.getLatitud() == null || request.getLatitud().trim().isEmpty()
                || request.getLongitud() == null || request.getLongitud().trim().isEmpty()) {
            log.warn("Request es nulo");
            response.setCodigo("400");
            response.setMensaje("Solicitud incorrecta, request no puede ser nulo");
            response.setCorreos(null);
            return response;
        }

        try {
            log.info("Llamando a la capa DL para actualizar recibo de ruta");

            String correos = iDlCobroMovil.UpdateReciboRuta(request);

            if (correos != null) {
                log.info("Recibo de ruta actualizado exitosamente - ruta: {}", request.getRuta());
                response.setCorreos(correos);
                response.setCodigo("200");
                response.setMensaje("Recibo de ruta actualizado exitosamente");
            } else {
                response.setCorreos("");
                response.setCodigo("200");
                response.setMensaje("Recibo de ruta actualizado exitosamente");
            }

        } catch (Exception ex) {
            log.error("Error interno al actualizar recibo de ruta - ruta: {}, error: {}",
                    request.getRuta(), ex.getMessage(), ex);
            response.setCodigo("500");
            response.setMensaje("Error interno del servidor");
        }

        log.info("Proceso de actualización de recibo de ruta completado - código respuesta: {}", response.getCodigo());
        return response;
    }

    private static String doubleBase64Decode(String encodedText) {
        byte[] firstDecode = Base64.getDecoder().decode(encodedText);
        byte[] secondDecode = Base64.getDecoder().decode(new String(firstDecode, StandardCharsets.UTF_8));
        return new String(secondDecode, StandardCharsets.UTF_8);
    }


    @Override
    public AutenticarUsuarioResponse AutenticarUsuario(String usuario, String clave) {
        log.info("Iniciando proceso de autenticación de usuario - usuario: {}", usuario);

        AutenticarUsuarioResponse response = new AutenticarUsuarioResponse();
        usuario = doubleBase64Decode(usuario);

        // Validaciones de parámetros de entrada
        if (usuario == null || usuario.trim().isEmpty()){
            log.warn("Usuario es obligatorio");
            response.setCodigo("400");
            response.setMensaje("Solicitud incorrecta, usuario es obligatorio");
            return response;
        }
        try {
            log.info("Llamando a la capa DL para autenticar usuario");
            if(clave == null || clave.trim().isEmpty()){
                AutenticarUsuarioResponseUsuario usuarioAutenticadoAD = iDlCobroMovil.AutenticarUsuarioAD(usuario);
                if (usuarioAutenticadoAD != null) {
                    log.info("Usuario autenticado exitosamente - ID: {}, Usuario: {}",
                            usuarioAutenticadoAD.getIdUsuario(), usuarioAutenticadoAD.getNombreUnicoUsuario());
                    response.setCodigo("200");
                    response.setMensaje("Usuario autenticado exitosamente");
                    response.setUsuario(usuarioAutenticadoAD);
                } else {
                    log.warn("Credenciales inválidas para usuario: {}", usuario);
                    response.setCodigo("404");
                    response.setMensaje("Usuario no encontrado");
                    response.setUsuario(usuarioAutenticadoAD);
                }
            }else{
                clave = doubleBase64Decode(clave);
                AutenticarUsuarioResponseUsuario usuarioAutenticado = iDlCobroMovil.AutenticarUsuario(usuario, clave);
                if (usuarioAutenticado != null) {
                    log.info("Usuario autenticado exitosamente - ID: {}, Usuario: {}",
                            usuarioAutenticado.getIdUsuario(), usuarioAutenticado.getNombreUnicoUsuario());
                    response.setCodigo("200");
                    response.setMensaje("Usuario autenticado exitosamente");
                    response.setUsuario(usuarioAutenticado);
                } else {
                    log.warn("Credenciales inválidas para usuario: {}", usuario);
                    response.setCodigo("401");
                    response.setMensaje("Usuario no autorizado");
                    response.setUsuario(usuarioAutenticado);
                }
            }
        } catch (Exception ex) {
            log.error("Error interno al autenticar usuario - usuario: {}, error: {}",
                    usuario, ex.getMessage(), ex);
            response.setCodigo("500");
            response.setMensaje("Error interno del servidor");
            response.setUsuario(null);
        }
        log.info("Proceso de autenticación de usuario completado - código respuesta: {}", response.getCodigo());
        return response;
    }

    @Override
    public ObtenerRolesUsuarioResponse ObtenerRolesUsuario(Integer idUsuario) {
        log.info("Iniciando proceso de obtención de roles de usuario - idUsuario: {}", idUsuario);
        ObtenerRolesUsuarioResponse response = new ObtenerRolesUsuarioResponse();

        // Validaciones de parámetros de entrada
        if (idUsuario == null || idUsuario <= 0) {
            log.warn("ID de usuario es obligatorio");
            response.setCodigo("400");
            response.setMensaje("Solicitud incorrecta, ID de usuario es obligatorio");
            return response;
        }
        try {
            log.info("Llamando a la capa DL para obtener roles del usuario");

            ArrayList<ObtenerRolesUsuarioResponseRoles> listaRoles = iDlCobroMovil.ObtenerRolesUsuario(idUsuario);

            if (listaRoles != null) {
                log.info("Se obtuvieron {} roles para el usuario ID: {}", listaRoles.size(), idUsuario);
                response.setCodigo("200");
                response.setMensaje("Roles obtenidos exitosamente");
                response.setRoles(listaRoles);
            } else {
                log.info("No se encontraron roles para el usuario ID: {}", idUsuario);
                response.setCodigo("200");
                response.setMensaje("No se encontraron roles para el usuario");
                response.setRoles(new ArrayList<>());
            }

        } catch (Exception ex) {
            log.error("Error interno al obtener roles del usuario - idUsuario: {}, error: {}",
                    idUsuario, ex.getMessage(), ex);
            response.setCodigo("500");
            response.setMensaje("Error interno del servidor");
            response.setRoles(new ArrayList<>());
        }

        log.info("Proceso de obtención de roles de usuario completado - código respuesta: {}", response.getCodigo());
        return response;
    }

    @Override
    public CajeroUsuarioResponse ListarCajeroUsuario(Integer usuario) {
        log.info("Iniciando proceso de listado de cajeros de usuario - usuario: {}", usuario);
        CajeroUsuarioResponse response = new CajeroUsuarioResponse();

        // Validaciones de parámetros de entrada
        if (usuario == null || usuario <= 0) {
            log.warn("ID de usuario es obligatorio");
            response.setCodigo("400");
            response.setMensaje("Solicitud incorrecta, ID de usuario es obligatorio");
            return response;
        }

        try {
            log.info("Llamando a la capa DL para listar cajeros del usuario");

            ArrayList<CajeroUsuarioResponseCajeros> listaCajeros = iDlCobroMovil.ListarCajeroUsuario(usuario);

            if (listaCajeros != null & !listaCajeros.isEmpty()) {
                log.info("Se obtuvieron {} cajeros para el usuario ID: {}", listaCajeros.size(), usuario);
                response.setCodigo("200");
                response.setMensaje("Cajeros obtenidos exitosamente");
                response.setCajeros(listaCajeros);
            } else {
                log.info("No se encontraron cajeros para el usuario ID: {}", usuario);
                response.setCodigo("200");
                response.setMensaje("No se encontraron cajeros para el usuario");
                response.setCajeros(new ArrayList<>());
            }

        } catch (Exception ex) {
            log.error("Error interno al listar cajeros del usuario - usuario: {}, error: {}",
                    usuario, ex.getMessage(), ex);
            response.setCodigo("500");
            response.setMensaje("Error interno del servidor");
            response.setCajeros(new ArrayList<>());
        }

        log.info("Proceso de listado de cajeros de usuario completado - código respuesta: {}", response.getCodigo());
        return response;
    }

    @Override
    public AccionesPorRolesResponse ObtenerAccionesPorRoles(String roles) {
        log.info("Iniciando proceso de obtención de acciones por roles - roles: {}", roles);

        AccionesPorRolesResponse response = new AccionesPorRolesResponse();
        if (roles == null || roles.trim().isEmpty()
                || !roles.matches("^[0-9,\\s]+$")) {
            log.warn("Roles es obligatorio");
            response.setCodigo("400");
            response.setMensaje("Solicitud incorrecta, roles es obligatorio");
            return response;
        }
        try {
            log.info("Llamando a la capa DL para obtener acciones por roles");

            ArrayList<AccionesPorRolesResponseAcciones> listaAcciones = iDlCobroMovil.ObtenerAccionesPorRoles(roles);

            if (listaAcciones != null & !listaAcciones.isEmpty()) {
                log.info("Se obtuvieron {} acciones para los roles: {}", listaAcciones.size(), roles);
                response.setCodigo("200");
                response.setMensaje("Acciones obtenidas exitosamente");
                response.setAcciones(listaAcciones);
            } else {
                log.info("No se encontraron acciones para los roles: {}", roles);
                response.setCodigo("200");
                response.setMensaje("No se encontraron acciones para los roles especificados");
                response.setAcciones(new ArrayList<>());
            }

        } catch (Exception ex) {
            log.error("Error interno al obtener acciones por roles - roles: {}, error: {}",
                    roles, ex.getMessage(), ex);
            response.setCodigo("500");
            response.setMensaje("Error interno del servidor");
            response.setAcciones(new ArrayList<>());
        }

        log.info("Proceso de obtención de acciones por roles completado - código respuesta: {}", response.getCodigo());
        return response;
    }

    @Override
    public RutasCobradasResponse getRutasCobradas() {
        log.info("Iniciando obtención de rutas cobradas");

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            RutasCobradasResponse response = iDlCobroMovil.getRutasCobradas();

            // Asegurarse de que idRutas nunca sea null
            if (response.getIdRutas() == null) {
                response.setIdRutas(new ArrayList<>());
            }

            log.info("Obtención de rutas cobradas completada. Rutas encontradas: {}", response.getIdRutas().size());
            return response;

        } catch (Exception e) {
            log.error("Error al obtener rutas cobradas: {}", e.getMessage(), e);
            // Propagar la excepción para que el controlador la maneje
            throw e;
        }
    }

    @Override
    public List<RutaLocalizacionResponse> obtenerRutasLocalizacion(
            LocalDate fechaInicio, LocalDate fechaFin, Integer idRuta) {

        log.info("Iniciando búsqueda de rutas con localización con fechaInicio={}, fechaFin={}, idRuta={}",
                fechaInicio, fechaFin, idRuta);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (fechaInicio == null) {
                log.error("La fecha de inicio es obligatoria");
                throw new IllegalArgumentException("La fecha de inicio es obligatoria");
            }

            if (fechaFin == null) {
                log.error("La fecha de fin es obligatoria");
                throw new IllegalArgumentException("La fecha de fin es obligatoria");
            }

            if (idRuta == null) {
                log.error("El ID de ruta es obligatorio");
                throw new IllegalArgumentException("El ID de ruta es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            List<RutaLocalizacionResponse> rutas = iDlCobroMovil.obtenerRutasLocalizacion(
                    fechaInicio, fechaFin, idRuta);

            log.info("Búsqueda de rutas completada. Rutas encontradas: {}",
                    rutas != null ? rutas.size() : 0);
            return rutas;

        } catch (Exception e) {
            log.error("Error al buscar rutas: {}", e.getMessage(), e);
            throw e;
        }
    }


    @Override
    public List<LocalizacionPagosResponse> obtenerLocalizacionPagos(
            LocalDate fechaInicio, LocalDate fechaFin, Integer idRuta) {

        log.info("Iniciando búsqueda de localización de pagos con fechaInicio={}, fechaFin={}, idRuta={}",
                fechaInicio, fechaFin, idRuta);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (fechaInicio == null) {
                log.error("La fecha de inicio es obligatoria");
                throw new IllegalArgumentException("La fecha de inicio es obligatoria");
            }

            if (fechaFin == null) {
                log.error("La fecha de fin es obligatoria");
                throw new IllegalArgumentException("La fecha de fin es obligatoria");
            }

            if (idRuta == null) {
                log.error("El ID de ruta es obligatorio");
                throw new IllegalArgumentException("El ID de ruta es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            List<LocalizacionPagosResponse> pagos = iDlCobroMovil.obtenerLocalizacionPagos(
                    fechaInicio, fechaFin, idRuta);

            // Verificar el resultado
            if (pagos == null) {
                log.info("No se encontraron pagos, devolviendo lista vacía");
                return new ArrayList<>();
            }

            log.info("Búsqueda de localización de pagos completada. Pagos encontrados: {}", pagos.size());
            return pagos;

        } catch (Exception e) {
            log.error("Error al obtener localización de pagos: {}", e.getMessage(), e);
            // Propagar la excepción para que el controlador la maneje
            throw e;
        }
    }

    @Override
    public List<LlenarDetalleCierreCajaResponse> llenarDetalleCierreCaja(Integer idUsuario) {
        log.info("Iniciando obtención de detalle de cierre de caja para idUsuario={}", idUsuario);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (idUsuario == null) {
                log.error("El ID de usuario es obligatorio");
                throw new IllegalArgumentException("El ID de usuario es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            List<LlenarDetalleCierreCajaResponse> detalles = iDlCobroMovil.llenarDetalleCierreCaja(idUsuario);

            // Verificar el resultado
            if (detalles == null) {
                log.info("No se encontraron detalles, devolviendo lista vacía");
                return new ArrayList<>();
            }

            log.info("Obtención de detalle de cierre de caja completada. Detalles encontrados: {}", detalles.size());
            return detalles;

        } catch (Exception e) {
            log.error("Error al obtener detalle de cierre de caja: {}", e.getMessage(), e);
            // Propagar la excepción para que el controlador la maneje
            throw e;
        }
    }

    @Override
    public List<InfoRutaCierreCajaResponse> getInfoRutaCierreCaja(Integer idUsuario) {
        log.info("Iniciando obtención de información de rutas para cierre de caja para idUsuario={}", idUsuario);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (idUsuario == null) {
                log.error("El ID de usuario es obligatorio");
                throw new IllegalArgumentException("El ID de usuario es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            List<InfoRutaCierreCajaResponse> rutas = iDlCobroMovil.getInfoRutaCierreCaja(idUsuario);

            // Verificar el resultado
            if (rutas == null) {
                log.info("No se encontraron rutas, devolviendo lista vacía");
                return new ArrayList<>();
            }

            log.info("Obtención de información de rutas para cierre de caja completada. Rutas encontradas: {}", rutas.size());
            return rutas;

        } catch (Exception e) {
            log.error("Error al obtener información de rutas para cierre de caja: {}", e.getMessage(), e);
            // Propagar la excepción para que el controlador la maneje
            throw e;
        }
    }

    @Override
    public List<InfoRutaResponse> getInfoRuta(Integer idUsuario, String fecha) {
        log.info("Iniciando obtención de información de rutas para idUsuario={}, fecha={}", idUsuario, fecha);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (idUsuario == null) {
                log.error("El ID de usuario es obligatorio");
                throw new IllegalArgumentException("El ID de usuario es obligatorio");
            }

            if (fecha == null || fecha.trim().isEmpty()) {
                log.error("La fecha es obligatoria");
                throw new IllegalArgumentException("La fecha es obligatoria");
            }

            // Convertir formato de fecha de YYYY-MM-DD a DD/MM/YYYY
            String fechaFormateada;
            try {
                LocalDate fechaDate = LocalDate.parse(fecha);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                fechaFormateada = fechaDate.format(formatter);
                log.info("Fecha convertida: {}", fechaFormateada);
            } catch (Exception e) {
                log.error("Error al convertir la fecha: {}", e.getMessage(), e);
                throw new IllegalArgumentException("Formato de fecha inválido. Debe ser YYYY-MM-DD", e);
            }

            // Llamar a la capa de datos para ejecutar la función
            List<InfoRutaResponse> rutas = iDlCobroMovil.getInfoRuta(idUsuario, fechaFormateada);

            // Verificar el resultado
            if (rutas == null) {
                log.info("No se encontraron rutas, devolviendo lista vacía");
                return new ArrayList<>();
            }

            log.info("Obtención de información de rutas completada. Rutas encontradas: {}", rutas.size());
            return rutas;

        } catch (Exception e) {
            log.error("Error al obtener información de rutas: {}", e.getMessage(), e);
            // Propagar la excepción para que el controlador la maneje
            throw e;
        }
    }

    @Override
    public InfoPromedioResponse getPromedios(Integer idUsuario, Integer ruta, Integer tipo, String fecha) {
        log.info("Iniciando obtención de promedios para idUsuario={}, ruta={}, tipo={}, fecha={}",
                idUsuario, ruta, tipo, fecha);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (idUsuario == null) {
                log.error("El ID de usuario es obligatorio");
                throw new IllegalArgumentException("El ID de usuario es obligatorio");
            }

            if (ruta == null) {
                log.error("El ID de ruta es obligatorio");
                throw new IllegalArgumentException("El ID de ruta es obligatorio");
            }

            if (tipo == null) {
                log.error("El tipo es obligatorio");
                throw new IllegalArgumentException("El tipo es obligatorio");
            }



            // Convertir formato de fecha de YYYY-MM-DD a DD/MM/YYYY
            String fechaFormateada = null;
            if (fecha != null && !fecha.trim().isEmpty()) {
                try {
                    LocalDate fechaDate = LocalDate.parse(fecha);
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    fechaFormateada = fechaDate.format(formatter);
                    log.info("Fecha convertida: {}", fechaFormateada);
                } catch (Exception e) {
                    log.error("Error al convertir la fecha: {}", e.getMessage(), e);
                    throw new IllegalArgumentException("Formato de fecha inválido. Debe ser YYYY-MM-DD", e);
                }

            }


            // Llamar a la capa de datos para ejecutar la función
            InfoPromedioResponse response = iDlCobroMovil.getPromedios(idUsuario, ruta, tipo, fechaFormateada);

            // Verificar el resultado
            if (response == null) {
                log.info("No se obtuvo respuesta, creando respuesta por defecto");
                response = new InfoPromedioResponse();
                response.setPromedio("0");
            }

            log.info("Obtención de promedios completada. Promedio: {}", response.getPromedio());
            return response;

        } catch (Exception e) {
            log.error("Error al obtener promedios: {}", e.getMessage(), e);
            // Propagar la excepción para que el controlador la maneje
            throw e;
        }
    }

    @Override
    public List<ModRutaPolizaFacturasResponse> getModRutaPolizaFacturas(Integer idRuta) {
        log.info("Iniciando obtención de pólizas y facturas para idRuta={}", idRuta);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (idRuta == null) {
                log.error("El ID de ruta es obligatorio");
                throw new IllegalArgumentException("El ID de ruta es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            List<ModRutaPolizaFacturasResponse> polizas = iDlCobroMovil.getModRutaPolizaFacturas(idRuta);

            // Verificar el resultado
            if (polizas == null) {
                log.info("No se encontraron pólizas, devolviendo lista vacía");
                return new ArrayList<>();
            }

            log.info("Obtención de pólizas y facturas completada. Pólizas encontradas: {}", polizas.size());
            return polizas;

        } catch (Exception e) {
            log.error("Error al obtener pólizas y facturas: {}", e.getMessage(), e);
            // Propagar la excepción para que el controlador la maneje
            throw e;
        }
    }

    @Override
    public List<ModRutaPolizaResponse> getModRutaPoliza(Integer idRuta) {
        log.info("Iniciando obtención de pólizas para idRuta={}", idRuta);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (idRuta == null) {
                log.error("El ID de ruta es obligatorio");
                throw new IllegalArgumentException("El ID de ruta es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            List<ModRutaPolizaResponse> polizas = iDlCobroMovil.getModRutaPoliza(idRuta);

            // Verificar el resultado
            if (polizas == null) {
                log.info("No se encontraron pólizas, devolviendo lista vacía");
                return new ArrayList<>();
            }

            log.info("Obtención de pólizas completada. Pólizas encontradas: {}", polizas.size());
            return polizas;

        } catch (Exception e) {
            log.error("Error al obtener pólizas: {}", e.getMessage(), e);
            // Propagar la excepción para que el controlador la maneje
            throw e;
        }
    }

    @Override
    public List<InfoRecibosPolizaRutaCobradosResponse> getInfoRecibosPolizaRutaCobrados(Integer idRuta, String idePol, String numCertis) {
        log.info("Iniciando obtención de recibos cobrados para idRuta={}, idePol={}, numCertis={}", idRuta, idePol, numCertis);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (idRuta == null) {
                log.error("El ID de ruta es obligatorio");
                throw new IllegalArgumentException("El ID de ruta es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            List<InfoRecibosPolizaRutaCobradosResponse> recibos = iDlCobroMovil.getInfoRecibosPolizaRutaCobrados(idRuta, idePol, numCertis);

            // Verificar el resultado
            if (recibos == null) {
                log.info("No se encontraron recibos, devolviendo lista vacía");
                return new ArrayList<>();
            }

            log.info("Obtención de recibos cobrados completada. Recibos encontrados: {}", recibos.size());
            return recibos;

        } catch (Exception e) {
            log.error("Error al obtener recibos cobrados: {}", e.getMessage(), e);
            // Propagar la excepción para que el controlador la maneje
            throw e;
        }
    }

    @Override
    public List<InfoRecibosPolizaRutaResponse> getInfoRecibosPolizaRuta(Integer idRuta, String idePol, String numCertis) {
        log.info("Iniciando obtención de recibos para idRuta={}, idePol={}, numCertis={}", idRuta, idePol, numCertis);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (idRuta == null) {
                log.error("El ID de ruta es obligatorio");
                throw new IllegalArgumentException("El ID de ruta es obligatorio");
            }

            if (idePol == null || idePol.isEmpty()) {
                log.error("El ID de póliza es obligatorio");
                throw new IllegalArgumentException("El ID de póliza es obligatorio");
            }

            if (numCertis == null || numCertis.isEmpty()) {
                log.error("El número de certificado es obligatorio");
                throw new IllegalArgumentException("El número de certificado es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            List<InfoRecibosPolizaRutaResponse> recibos = iDlCobroMovil.getInfoRecibosPolizaRuta(idRuta, idePol, numCertis);

            // Verificar el resultado
            if (recibos == null) {
                log.info("No se encontraron recibos, devolviendo lista vacía");
                return new ArrayList<>();
            }

            log.info("Obtención de recibos completada. Recibos encontrados: {}", recibos.size());
            return recibos;

        } catch (Exception e) {
            log.error("Error al obtener recibos: {}", e.getMessage(), e);
            // Propagar la excepción para que el controlador la maneje
            throw e;
        }
    }

    @Override
    public List<InfoFirmaReciboResponse> getInfoFirmaRecibo(String idRuta, String recibo) {
        log.info("Iniciando obtención de información de firma para idRuta={}, recibo={}", idRuta, recibo);

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (idRuta == null || idRuta.trim().isEmpty()) {
                log.error("El ID de ruta es obligatorio");
                throw new IllegalArgumentException("El ID de ruta es obligatorio");
            }

            if (recibo == null || recibo.trim().isEmpty()) {
                log.error("El número de recibo es obligatorio");
                throw new IllegalArgumentException("El número de recibo es obligatorio");
            }

            // Llamar a la capa de datos para ejecutar la función
            List<InfoFirmaReciboResponse> firmas = iDlCobroMovil.getInfoFirmaRecibo(idRuta, recibo);

            // Verificar el resultado
            if (firmas == null) {
                log.info("No se encontró información de firma, devolviendo lista vacía");
                return new ArrayList<>();
            }

            log.info("Obtención de información de firma completada. Registros encontrados: {}", firmas.size());
            return firmas;

        } catch (Exception e) {
            log.error("Error al obtener información de firma: {}", e.getMessage(), e);
            // Propagar la excepción para que el controlador la maneje
            throw e;
        }
    }

    @Override
    public List<CobradoresResponse> getCobradores() {
        log.info("Iniciando obtención de lista de cobradores");

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<CobradoresResponse> cobradores = iDlCobroMovil.getCobradores();

            // Verificar el resultado
            if (cobradores == null) {
                log.info("No se encontraron cobradores, devolviendo lista vacía");
                return new ArrayList<>();
            }

            log.info("Obtención de lista de cobradores completada. Cobradores encontrados: {}", cobradores.size());
            return cobradores;

        } catch (Exception e) {
            log.error("Error al obtener lista de cobradores: {}", e.getMessage(), e);
            // Propagar la excepción para que el controlador la maneje
            throw e;
        }
    }

    @Override
    public List<TipoPagoResponse> getTipoPago() {
        log.info("Iniciando obtención de lista de tipos de pago");

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Llamar a la capa de datos para ejecutar la función
            List<TipoPagoResponse> tiposPago = iDlCobroMovil.getTipoPago();

            // Verificar el resultado
            if (tiposPago == null) {
                log.info("No se encontraron tipos de pago, devolviendo lista vacía");
                return new ArrayList<>();
            }

            log.info("Obtención de lista de tipos de pago completada. Tipos encontrados: {}", tiposPago.size());
            return tiposPago;

        } catch (Exception e) {
            log.error("Error al obtener lista de tipos de pago: {}", e.getMessage(), e);
            // Propagar la excepción para que el controlador la maneje
            throw e;
        }
    }
}
