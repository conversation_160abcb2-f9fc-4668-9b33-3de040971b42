package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * InfoDeducibleResponse
 */
@Validated

public class InfoDeducibleResponse   {
  @JsonProperty("numLiq")
  private String numLiq = null;

  @JsonProperty("numSini")
  private String numSini = null;

  @JsonProperty("numExp")
  private Integer numExp = null;

  @JsonProperty("numPoliza")
  private String numPoliza = null;

  @JsonProperty("codNivel3")
  private String codNivel3 = null;

  @JsonProperty("nomNivel3")
  private String nomNivel3 = null;

  @JsonProperty("codRamo")
  private Integer codRamo = null;

  @JsonProperty("codSector")
  private Integer codSector = null;

  @JsonProperty("nomSector")
  private String nomSector = null;

  @JsonProperty("codTercero")
  private String codTercero = null;

  @JsonProperty("nomTercero")
  private String nomTercero = null;

  @JsonProperty("codActTercero")
  private Integer codActTercero = null;

  @JsonProperty("nomActTercero")
  private String nomActTercero = null;

  @JsonProperty("tipDocum")
  private String tipDocum = null;

  @JsonProperty("codDocum")
  private String codDocum = null;

  @JsonProperty("obs")
  private String obs = null;

  @JsonProperty("fecLiq")
  private String fecLiq = null;

  @JsonProperty("fecEstPago")
  private String fecEstPago = null;

  @JsonProperty("fecPago")
  private String fecPago = null;

  @JsonProperty("codMonLiq")
  private Integer codMonLiq = null;

  @JsonProperty("codMonLiqIso")
  private String codMonLiqIso = null;

  @JsonProperty("numDecimales")
  private Integer numDecimales = null;

  @JsonProperty("codMonPago")
  private Integer codMonPago = null;

  @JsonProperty("codMonPagoIso")
  private String codMonPagoIso = null;

  @JsonProperty("impLiqNeto")
  private Double impLiqNeto = null;

  @JsonProperty("impIva")
  private Double impIva = null;

  @JsonProperty("impLiq")
  private Double impLiq = null;

  @JsonProperty("valCambio")
  private Double valCambio = null;

  @JsonProperty("tipDocto")
  private String tipDocto = null;

  // Getters and Setters
  public InfoDeducibleResponse numLiq(String numLiq) {
    this.numLiq = numLiq;
    return this;
  }

  @ApiModelProperty(example = "110123003434", value = "Número de liquidación")
  public String getNumLiq() {
    return numLiq;
  }

  public void setNumLiq(String numLiq) {
    this.numLiq = numLiq;
  }

  public InfoDeducibleResponse numSini(String numSini) {
    this.numSini = numSini;
    return this;
  }

  @ApiModelProperty(example = "110130023003755", value = "Número de siniestro")
  public String getNumSini() {
    return numSini;
  }

  public void setNumSini(String numSini) {
    this.numSini = numSini;
  }

  public InfoDeducibleResponse numExp(Integer numExp) {
    this.numExp = numExp;
    return this;
  }

  @ApiModelProperty(example = "3", value = "Número de expediente")
  public Integer getNumExp() {
    return numExp;
  }

  public void setNumExp(Integer numExp) {
    this.numExp = numExp;
  }

  public InfoDeducibleResponse numPoliza(String numPoliza) {
    this.numPoliza = numPoliza;
    return this;
  }

  @ApiModelProperty(example = "0830023005036", value = "Número de póliza")
  public String getNumPoliza() {
    return numPoliza;
  }

  public void setNumPoliza(String numPoliza) {
    this.numPoliza = numPoliza;
  }

  public InfoDeducibleResponse codNivel3(String codNivel3) {
    this.codNivel3 = codNivel3;
    return this;
  }

  @ApiModelProperty(example = "1101", value = "Código de nivel 3")
  public String getCodNivel3() {
    return codNivel3;
  }

  public void setCodNivel3(String codNivel3) {
    this.codNivel3 = codNivel3;
  }

  public InfoDeducibleResponse nomNivel3(String nomNivel3) {
    this.nomNivel3 = nomNivel3;
    return this;
  }

  @ApiModelProperty(example = "CASA MATRIZ", value = "Nombre de nivel 3")
  public String getNomNivel3() {
    return nomNivel3;
  }

  public void setNomNivel3(String nomNivel3) {
    this.nomNivel3 = nomNivel3;
  }

  public InfoDeducibleResponse codRamo(Integer codRamo) {
    this.codRamo = codRamo;
    return this;
  }

  @ApiModelProperty(example = "300", value = "Código de ramo")
  public Integer getCodRamo() {
    return codRamo;
  }

  public void setCodRamo(Integer codRamo) {
    this.codRamo = codRamo;
  }

  public InfoDeducibleResponse codSector(Integer codSector) {
    this.codSector = codSector;
    return this;
  }

  @ApiModelProperty(example = "3", value = "Código de sector")
  public Integer getCodSector() {
    return codSector;
  }

  public void setCodSector(Integer codSector) {
    this.codSector = codSector;
  }

  public InfoDeducibleResponse nomSector(String nomSector) {
    this.nomSector = nomSector;
    return this;
  }

  @ApiModelProperty(example = "AUTOS / MOTOR", value = "Nombre del sector")
  public String getNomSector() {
    return nomSector;
  }

  public void setNomSector(String nomSector) {
    this.nomSector = nomSector;
  }

  public InfoDeducibleResponse codTercero(String codTercero) {
    this.codTercero = codTercero;
    return this;
  }

  @ApiModelProperty(value = "Código de tercero")
  public String getCodTercero() {
    return codTercero;
  }

  public void setCodTercero(String codTercero) {
    this.codTercero = codTercero;
  }

  public InfoDeducibleResponse nomTercero(String nomTercero) {
    this.nomTercero = nomTercero;
    return this;
  }

  @ApiModelProperty(example = "FARIÑEZ, , ANGÉLICA", value = "Nombre del tercero")
  public String getNomTercero() {
    return nomTercero;
  }

  public void setNomTercero(String nomTercero) {
    this.nomTercero = nomTercero;
  }

  public InfoDeducibleResponse codActTercero(Integer codActTercero) {
    this.codActTercero = codActTercero;
    return this;
  }

  @ApiModelProperty(example = "1", value = "Código de actividad del tercero")
  public Integer getCodActTercero() {
    return codActTercero;
  }

  public void setCodActTercero(Integer codActTercero) {
    this.codActTercero = codActTercero;
  }

  public InfoDeducibleResponse nomActTercero(String nomActTercero) {
    this.nomActTercero = nomActTercero;
    return this;
  }

  @ApiModelProperty(example = "ASEGURADOS/INSURERS", value = "Nombre de actividad del tercero")
  public String getNomActTercero() {
    return nomActTercero;
  }

  public void setNomActTercero(String nomActTercero) {
    this.nomActTercero = nomActTercero;
  }

  public InfoDeducibleResponse tipDocum(String tipDocum) {
    this.tipDocum = tipDocum;
    return this;
  }

  @ApiModelProperty(example = "CED", value = "Tipo de documento")
  public String getTipDocum() {
    return tipDocum;
  }

  public void setTipDocum(String tipDocum) {
    this.tipDocum = tipDocum;
  }

  public InfoDeducibleResponse codDocum(String codDocum) {
    this.codDocum = codDocum;
    return this;
  }

  @ApiModelProperty(example = "CED123456", value = "Código de documento")
  public String getCodDocum() {
    return codDocum;
  }

  public void setCodDocum(String codDocum) {
    this.codDocum = codDocum;
  }

  public InfoDeducibleResponse obs(String obs) {
    this.obs = obs;
    return this;
  }

  @ApiModelProperty(value = "Observaciones")
  public String getObs() {
    return obs;
  }

  public void setObs(String obs) {
    this.obs = obs;
  }

  public InfoDeducibleResponse fecLiq(String fecLiq) {
    this.fecLiq = fecLiq;
    return this;
  }

  @ApiModelProperty(example = "29/05/2023", value = "Fecha de liquidación")
  public String getFecLiq() {
    return fecLiq;
  }

  public void setFecLiq(String fecLiq) {
    this.fecLiq = fecLiq;
  }

  public InfoDeducibleResponse fecEstPago(String fecEstPago) {
    this.fecEstPago = fecEstPago;
    return this;
  }

  @ApiModelProperty(example = "29/05/2023", value = "Fecha estimada de pago")
  public String getFecEstPago() {
    return fecEstPago;
  }

  public void setFecEstPago(String fecEstPago) {
    this.fecEstPago = fecEstPago;
  }

  public InfoDeducibleResponse fecPago(String fecPago) {
    this.fecPago = fecPago;
    return this;
  }

  @ApiModelProperty(example = "27/05/2023", value = "Fecha de pago")
  public String getFecPago() {
    return fecPago;
  }

  public void setFecPago(String fecPago) {
    this.fecPago = fecPago;
  }

  public InfoDeducibleResponse codMonLiq(Integer codMonLiq) {
    this.codMonLiq = codMonLiq;
    return this;
  }

  @ApiModelProperty(example = "1", value = "Código de moneda de liquidación")
  public Integer getCodMonLiq() {
    return codMonLiq;
  }

  public void setCodMonLiq(Integer codMonLiq) {
    this.codMonLiq = codMonLiq;
  }

  public InfoDeducibleResponse codMonLiqIso(String codMonLiqIso) {
    this.codMonLiqIso = codMonLiqIso;
    return this;
  }

  @ApiModelProperty(example = "PAB", value = "Código ISO de moneda de liquidación")
  public String getCodMonLiqIso() {
    return codMonLiqIso;
  }

  public void setCodMonLiqIso(String codMonLiqIso) {
    this.codMonLiqIso = codMonLiqIso;
  }

  public InfoDeducibleResponse numDecimales(Integer numDecimales) {
    this.numDecimales = numDecimales;
    return this;
  }

  @ApiModelProperty(example = "2", value = "Número de decimales")
  public Integer getNumDecimales() {
    return numDecimales;
  }

  public void setNumDecimales(Integer numDecimales) {
    this.numDecimales = numDecimales;
  }

  public InfoDeducibleResponse codMonPago(Integer codMonPago) {
    this.codMonPago = codMonPago;
    return this;
  }

  @ApiModelProperty(example = "1", value = "Código de moneda de pago")
  public Integer getCodMonPago() {
    return codMonPago;
  }

  public void setCodMonPago(Integer codMonPago) {
    this.codMonPago = codMonPago;
  }

  public InfoDeducibleResponse codMonPagoIso(String codMonPagoIso) {
    this.codMonPagoIso = codMonPagoIso;
    return this;
  }

  @ApiModelProperty(example = "PAB", value = "Código ISO de moneda de pago")
  public String getCodMonPagoIso() {
    return codMonPagoIso;
  }

  public void setCodMonPagoIso(String codMonPagoIso) {
    this.codMonPagoIso = codMonPagoIso;
  }

  public InfoDeducibleResponse impLiqNeto(Double impLiqNeto) {
    this.impLiqNeto = impLiqNeto;
    return this;
  }

  @ApiModelProperty(example = "6000", value = "Importe de liquidación neto")
  public Double getImpLiqNeto() {
    return impLiqNeto;
  }

  public void setImpLiqNeto(Double impLiqNeto) {
    this.impLiqNeto = impLiqNeto;
  }

  public InfoDeducibleResponse impIva(Double impIva) {
    this.impIva = impIva;
    return this;
  }

  @ApiModelProperty(example = "0", value = "Importe de IVA")
  public Double getImpIva() {
    return impIva;
  }

  public void setImpIva(Double impIva) {
    this.impIva = impIva;
  }

  public InfoDeducibleResponse impLiq(Double impLiq) {
    this.impLiq = impLiq;
    return this;
  }

  @ApiModelProperty(example = "6000", value = "Importe de liquidación")
  public Double getImpLiq() {
    return impLiq;
  }

  public void setImpLiq(Double impLiq) {
    this.impLiq = impLiq;
  }

  public InfoDeducibleResponse valCambio(Double valCambio) {
    this.valCambio = valCambio;
    return this;
  }

  @ApiModelProperty(example = "1", value = "Valor de cambio")
  public Double getValCambio() {
    return valCambio;
  }

  public void setValCambio(Double valCambio) {
    this.valCambio = valCambio;
  }

  public InfoDeducibleResponse tipDocto(String tipDocto) {
    this.tipDocto = tipDocto;
    return this;
  }

  @ApiModelProperty(example = "IN", value = "Tipo de documento")
  public String getTipDocto() {
    return tipDocto;
  }

  public void setTipDocto(String tipDocto) {
    this.tipDocto = tipDocto;
  }

  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InfoDeducibleResponse infoDeducibleResponse = (InfoDeducibleResponse) o;
    return Objects.equals(this.numLiq, infoDeducibleResponse.numLiq) &&
        Objects.equals(this.numSini, infoDeducibleResponse.numSini) &&
        Objects.equals(this.numExp, infoDeducibleResponse.numExp) &&
        Objects.equals(this.numPoliza, infoDeducibleResponse.numPoliza) &&
        Objects.equals(this.codNivel3, infoDeducibleResponse.codNivel3) &&
        Objects.equals(this.nomNivel3, infoDeducibleResponse.nomNivel3) &&
        Objects.equals(this.codRamo, infoDeducibleResponse.codRamo) &&
        Objects.equals(this.codSector, infoDeducibleResponse.codSector) &&
        Objects.equals(this.nomSector, infoDeducibleResponse.nomSector) &&
        Objects.equals(this.codTercero, infoDeducibleResponse.codTercero) &&
        Objects.equals(this.nomTercero, infoDeducibleResponse.nomTercero) &&
        Objects.equals(this.codActTercero, infoDeducibleResponse.codActTercero) &&
        Objects.equals(this.nomActTercero, infoDeducibleResponse.nomActTercero) &&
        Objects.equals(this.tipDocum, infoDeducibleResponse.tipDocum) &&
        Objects.equals(this.codDocum, infoDeducibleResponse.codDocum) &&
        Objects.equals(this.obs, infoDeducibleResponse.obs) &&
        Objects.equals(this.fecLiq, infoDeducibleResponse.fecLiq) &&
        Objects.equals(this.fecEstPago, infoDeducibleResponse.fecEstPago) &&
        Objects.equals(this.fecPago, infoDeducibleResponse.fecPago) &&
        Objects.equals(this.codMonLiq, infoDeducibleResponse.codMonLiq) &&
        Objects.equals(this.codMonLiqIso, infoDeducibleResponse.codMonLiqIso) &&
        Objects.equals(this.numDecimales, infoDeducibleResponse.numDecimales) &&
        Objects.equals(this.codMonPago, infoDeducibleResponse.codMonPago) &&
        Objects.equals(this.codMonPagoIso, infoDeducibleResponse.codMonPagoIso) &&
        Objects.equals(this.impLiqNeto, infoDeducibleResponse.impLiqNeto) &&
        Objects.equals(this.impIva, infoDeducibleResponse.impIva) &&
        Objects.equals(this.impLiq, infoDeducibleResponse.impLiq) &&
        Objects.equals(this.valCambio, infoDeducibleResponse.valCambio) &&
        Objects.equals(this.tipDocto, infoDeducibleResponse.tipDocto);
  }

  @Override
  public int hashCode() {
    return Objects.hash(numLiq, numSini, numExp, numPoliza, codNivel3, nomNivel3, codRamo, codSector, nomSector, codTercero, nomTercero, codActTercero, nomActTercero, tipDocum, codDocum, obs, fecLiq, fecEstPago, fecPago, codMonLiq, codMonLiqIso, numDecimales, codMonPago, codMonPagoIso, impLiqNeto, impIva, impLiq, valCambio, tipDocto);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InfoDeducibleResponse {\n");
    
    sb.append("    numLiq: ").append(toIndentedString(numLiq)).append("\n");
    sb.append("    numSini: ").append(toIndentedString(numSini)).append("\n");
    sb.append("    numExp: ").append(toIndentedString(numExp)).append("\n");
    sb.append("    numPoliza: ").append(toIndentedString(numPoliza)).append("\n");
    sb.append("    codNivel3: ").append(toIndentedString(codNivel3)).append("\n");
    sb.append("    nomNivel3: ").append(toIndentedString(nomNivel3)).append("\n");
    sb.append("    codRamo: ").append(toIndentedString(codRamo)).append("\n");
    sb.append("    codSector: ").append(toIndentedString(codSector)).append("\n");
    sb.append("    nomSector: ").append(toIndentedString(nomSector)).append("\n");
    sb.append("    codTercero: ").append(toIndentedString(codTercero)).append("\n");
    sb.append("    nomTercero: ").append(toIndentedString(nomTercero)).append("\n");
    sb.append("    codActTercero: ").append(toIndentedString(codActTercero)).append("\n");
    sb.append("    nomActTercero: ").append(toIndentedString(nomActTercero)).append("\n");
    sb.append("    tipDocum: ").append(toIndentedString(tipDocum)).append("\n");
    sb.append("    codDocum: ").append(toIndentedString(codDocum)).append("\n");
    sb.append("    obs: ").append(toIndentedString(obs)).append("\n");
    sb.append("    fecLiq: ").append(toIndentedString(fecLiq)).append("\n");
    sb.append("    fecEstPago: ").append(toIndentedString(fecEstPago)).append("\n");
    sb.append("    fecPago: ").append(toIndentedString(fecPago)).append("\n");
    sb.append("    codMonLiq: ").append(toIndentedString(codMonLiq)).append("\n");
    sb.append("    codMonLiqIso: ").append(toIndentedString(codMonLiqIso)).append("\n");
    sb.append("    numDecimales: ").append(toIndentedString(numDecimales)).append("\n");
    sb.append("    codMonPago: ").append(toIndentedString(codMonPago)).append("\n");
    sb.append("    codMonPagoIso: ").append(toIndentedString(codMonPagoIso)).append("\n");
    sb.append("    impLiqNeto: ").append(toIndentedString(impLiqNeto)).append("\n");
    sb.append("    impIva: ").append(toIndentedString(impIva)).append("\n");
    sb.append("    impLiq: ").append(toIndentedString(impLiq)).append("\n");
    sb.append("    valCambio: ").append(toIndentedString(valCambio)).append("\n");
    sb.append("    tipDocto: ").append(toIndentedString(tipDocto)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
