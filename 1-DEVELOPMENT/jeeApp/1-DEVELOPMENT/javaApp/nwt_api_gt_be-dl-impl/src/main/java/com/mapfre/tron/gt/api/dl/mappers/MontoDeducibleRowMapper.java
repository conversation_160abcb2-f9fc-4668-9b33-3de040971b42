package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.MontoDeducibleResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos MontoDeducibleResponse.
 */
@Slf4j
public class MontoDeducibleRowMapper implements RowMapper<MontoDeducibleResponse> {

    @Override
    public MontoDeducibleResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto MontoDeducibleResponse", rowNum);

        MontoDeducibleResponse response = new MontoDeducibleResponse();

        try {
            Double monto = rs.getDouble("fp_obtiene_deducible");
            // Verificar si el valor es null en la base de datos
            if (rs.wasNull()) {
                monto = null;
            }
            response.setMonto(monto);
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }

        return response;
    }
}
