<?xml version="1.0" encoding="UTF-8" ?>
<weblogic-web-app xmlns="http://xmlns.oracle.com/weblogic/weblogic-web-app">

	<!-- For the application to be able to use GAIA Logback implementation instead of WebLogic implementation -->
    <container-descriptor>
        <prefer-application-packages>
            <package-name>org.slf4j.*</package-name>
            <package-name>org.apache.commons.*</package-name>
			<package-name>com.google.common.*</package-name>                                    
        </prefer-application-packages>
    </container-descriptor>
	
	<!-- Shared libraries used by this application -->
	<library-ref>
        <library-name>MAPFRE_GAIA_ENVCONFIG_DIST-3.1.1</library-name>
    </library-ref>
	<library-ref>
        <library-name>MAPFRE_GAIA_LOG4M_nwt_api_gt_be-ear-1.0.0</library-name>
    </library-ref>
	<library-ref>
        <library-name>nwt_api_gt_be.zeroConfig-${project.version}</library-name>
    </library-ref>
</weblogic-web-app>