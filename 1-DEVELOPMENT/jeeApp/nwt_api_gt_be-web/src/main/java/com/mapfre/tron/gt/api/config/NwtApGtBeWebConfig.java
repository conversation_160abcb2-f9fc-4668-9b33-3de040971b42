package com.mapfre.tron.gt.api.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.view.InternalResourceViewResolver;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mapfre.dgtp.gaia.webmvc.GaiaBeWebMvcConfigurerAdapter;
import com.mapfre.tron.gt.api.commons.TronApiHttpMessageConverter;

import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableWebMvc
@Configuration
@EnableSwagger2
public class NwtApGtBeWebConfig extends GaiaBeWebMvcConfigurerAdapter {

	@Value("${prop.swagger.enabled:true}")
	private boolean enableSwagger;

	@Bean
	public Docket swaggerConfig() {
		return new Docket(DocumentationType.SWAGGER_2)
				.enable(enableSwagger).select()
				.apis(RequestHandlerSelectors.basePackage("com.mapfre.tron.gt.api.cache")
						.or(RequestHandlerSelectors.basePackage("com.mapfre.tron.gt.api")))
				.paths(PathSelectors.any()).build();
	}

	@Override
	public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
		converters.add(new TronApiHttpMessageConverter());
		converters.add(new StringHttpMessageConverter());
		Jackson2ObjectMapperBuilder builder = new Jackson2ObjectMapperBuilder();
		builder.serializationInclusion(JsonInclude.Include.NON_NULL);
		converters.add(new MappingJackson2HttpMessageConverter(builder.build()));
	}

	// added to resolve Use of @EnableWebMvc in app breaks swagger-ui.html redirect
	@Bean
	public InternalResourceViewResolver defaultViewResolver() {
		return new InternalResourceViewResolver();
	}

}
