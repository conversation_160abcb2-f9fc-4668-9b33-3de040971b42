package com.mapfre.tron.gt.api.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mapfre.dgtp.gaia.dl.connector.datasource.DataSourceManager;
import com.mapfre.tron.gt.api.roles.NwtApiGtBeSecurityConfigurer;

@Configuration
public class NwtApiGtBeConfig {

	@Bean
	public NwtApiGtBeSecurityConfigurer securityConfigurer() {
		return new NwtApiGtBeSecurityConfigurer();
	}

	@Bean
	@ConditionalOnMissingBean
	DataSourceManager dataSourceManager() {
		DataSourceManager dsManager = new DataSourceManager();
		dsManager.setDefaultDataSourceName("dataSource");
		return dsManager;
	}

	@Bean
	public ObjectMapper objectMapper() {
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		mapper.configure(MapperFeature.DEFAULT_VIEW_INCLUSION, true);

		return mapper;
	}

	@Autowired
	@Qualifier("dataSource")
	private DataSource dataSource;

	@Primary
	@Bean(name = "jdbcTemplate")
	JdbcTemplate jdbcTemplate() {
		JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
		final CustomSQLErrorCodeTranslator customSQLErrorCodeTranslator = new CustomSQLErrorCodeTranslator();
		jdbcTemplate.setExceptionTranslator(customSQLErrorCodeTranslator);
		jdbcTemplate.setResultsMapCaseInsensitive(true);
		return jdbcTemplate;
	}

	@Bean
	public RestTemplate restTemplate() {
		return new RestTemplate();
	}

}
