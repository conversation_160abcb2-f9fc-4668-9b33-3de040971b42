package com.mapfre.tron.gt.api.config.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;

import com.mapfre.dgtp.gaia.commons.env.EnvironmentAttributes;
import com.mapfre.dgtp.gaia.commons.security.annotation.AbstractGaiaSecurity;
import com.mapfre.tron.gt.api.roles.NwtApiGtBeSecurityConfigurer;

/**
 * The demo security config class.
 *
 * <AUTHOR>
 * @since 1.0.0
 * @version 26/02/2024
 *
 */
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true, jsr250Enabled = true)
@Order(3)
@Profile(EnvironmentAttributes.SPRING_DEMO_SECURITY_PROFILE)
@EnableWebSecurity
public class NwtApiGtBeSecurityDemo extends AbstractGaiaSecurity {

	@Autowired
	NwtApiGtBeSecurityConfigurer securityConfigurer;

	@Autowired
	public void configureGlobal(AuthenticationManagerBuilder auth) throws Exception {
		auth.inMemoryAuthentication().withUser("APITRON").password("{noop}Mapfre2019").roles("ROLAPINWT_ALL");
	}

	@Override
	protected void configureAuthorizeRequests(HttpSecurity http) throws Exception {
		securityConfigurer.roleAuthorizations(http).and().httpBasic();
	}

}
