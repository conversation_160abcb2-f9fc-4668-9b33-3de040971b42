package com.mapfre.tron.gt.api.config.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;

import com.mapfre.dgtp.gaia.commons.env.EnvironmentAttributes;
import com.mapfre.dgtp.gaia.config.annotation.security.DefaultGaiaBackendSecurityLdap;
import com.mapfre.tron.gt.api.roles.NwtApiGtBeSecurityConfigurer;

/**
 * The real security config class.
 *
 * <AUTHOR>
 * @since 1.0.0
 * @version 26/02/2024
 *
 */
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true, jsr250Enabled = true)
@Order(3)
@Profile(EnvironmentAttributes.SPRING_REAL_SECURITY_PROFILE)
@EnableWebSecurity
public class NwtApiGtBeSecurityReal extends DefaultGaiaBackendSecurityLdap {

	@Autowired
	NwtApiGtBeSecurityConfigurer securityConfigurer;

	@Override
	protected void configureAuthorizeRequests(HttpSecurity http) throws Exception {
		securityConfigurer.roleAuthorizations(http).and().httpBasic();
	}

}
