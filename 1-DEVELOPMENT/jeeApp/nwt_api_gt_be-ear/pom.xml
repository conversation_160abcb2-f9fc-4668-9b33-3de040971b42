<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.mapfre.tron.gt</groupId>
        <artifactId>nwt_api_gt_be.jeeApp</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>nwt_api_gt_be-ear</artifactId>
    <packaging>ear</packaging>

    <name>${project.artifactId}</name>
    <description>${project.artifactId}_int:${project.version}</description>

    <dependencies>
        <dependency>
            <groupId>com.mapfre.tron.gt</groupId>
            <artifactId>nwt_api_gt_be-web-dependencies</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.mapfre.tron.gt</groupId>
            <artifactId>nwt_api_gt_be-web</artifactId>
            <type>war</type>
        </dependency>
        <!-- Required to included tomcat dependency in lib-provided-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-el</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- TODO revisar esta dependencia y su necesidad -->
        <dependency>
            <groupId>javax.el</groupId>
            <artifactId>javax.el-api</artifactId>
            <version>3.0.0</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-ear-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <filtering>true</filtering>
                    <archive>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>./ META-INF/</Class-Path>
                        </manifestEntries>
                    </archive>
                    <earSourceExcludes>**/application.xml,**/ibmconfig/**,**/MANIFEST.MF</earSourceExcludes>
                    <generateApplicationXml>true</generateApplicationXml>
                    <earSourcesDirectory>src/main/application/</earSourcesDirectory>
                    <resourcesDir>src/main/resources</resourcesDir>
                    <version>6</version>
                    <skinnyWars>true</skinnyWars>
                    <displayName>${project.groupId}.${project.artifactId}.${project.version}.app</displayName>
                    <defaultLibBundleDir>lib</defaultLibBundleDir>
                    <modules>
                        <webModule>
                            <groupId>com.mapfre.tron.gt</groupId>
                            <artifactId>nwt_api_gt_be-web</artifactId>
                            <contextRoot>${app.url.contextroot}</contextRoot>
                        </webModule>
                    </modules>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <profiles>
		<profile>
			<id>WEBLOGIC</id>
			<properties>
				<defaultLibBundleDir>.</defaultLibBundleDir>
			</properties>
		</profile>
	</profiles>
</project>
