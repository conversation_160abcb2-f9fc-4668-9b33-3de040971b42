<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.mapfre.tron.gt</groupId>
		<artifactId>nwt_api_gt_be.jeeApp</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>

	<artifactId>nwt_api_gt_be-web-dependencies</artifactId>
	<packaging>pom</packaging>

	<name>${project.artifactId}:${project.version}</name>
	<description>${project.artifactId}:${project.version}</description>

	<!-- Dependencias Web. Definir en este POM y no en el del módulo web -->
	<dependencies>
		<dependency>
			<groupId>com.mapfre.tron.gt</groupId>
			<artifactId>nwt_api_gt_be.zeroConfig</artifactId>
		</dependency>
		<dependency>
			<groupId>com.mapfre.tron.gt</groupId>
			<artifactId>nwt_api_gt_be-bo</artifactId>
		</dependency>
		<dependency>
			<groupId>com.mapfre.tron.gt</groupId>
			<artifactId>nwt_api_gt_be-commons</artifactId>
		</dependency>
		<dependency>
			<groupId>com.mapfre.tron.gt</groupId>
			<artifactId>nwt_api_gt_be-config</artifactId>
		</dependency>
		<dependency>
			<groupId>com.mapfre.tron.gt</groupId>
			<artifactId>nwt_api_gt_be-sr-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.mapfre.tron.gt</groupId>
			<artifactId>nwt_api_gt_be-sr-impl</artifactId>
		</dependency>
		<dependency>
			<groupId>com.mapfre.tron.gt</groupId>
			<artifactId>nwt_api_gt_be-bl-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.mapfre.tron.gt</groupId>
			<artifactId>nwt_api_gt_be-bl-impl</artifactId>
		</dependency>
		<dependency>
			<groupId>com.mapfre.tron.gt</groupId>
			<artifactId>nwt_api_gt_be-dl-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.mapfre.tron.gt</groupId>
			<artifactId>nwt_api_gt_be-dl-impl</artifactId>
		</dependency>

		<!-- External libraries -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
		</dependency>
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
			<version>${ojdbc-version}</version>
		</dependency>
		<dependency>
			<groupId>com.oracle.database.nls</groupId>
			<artifactId>orai18n</artifactId>
			<version>${ojdbc-version}</version>
		</dependency>
	</dependencies>
</project>