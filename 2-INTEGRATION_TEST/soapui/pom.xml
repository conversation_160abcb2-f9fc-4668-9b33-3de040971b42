<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.mapfre.dgtp.otic</groupId>
	<artifactId>nwt_api_gt_be_INTEGRATION_TEST_SOAPUI</artifactId>
	<packaging>jar</packaging>
	<version>1.0.0</version>
	
     <!-- SOAPUI: Configuración del plugin -->
	<build>
	  <testResources>
		<testResource>
           <directory>src/test/resources</directory>
		   <filtering>true</filtering>
        </testResource>
      </testResources>
		<plugins>
			<plugin>
				<groupId>eviware</groupId>
				<artifactId>maven-soapui-plugin</artifactId>
				<version>4.5.1</version>
				<configuration>
                   <outputFolder>${project.build.directory}/soapui/</outputFolder>
					<exportAll>true</exportAll>
					<soapuiProperties>
						<property>
							<name>soapui.logroot</name>
							<value>${project.build.directory}/soapui-logs/</value>
						</property>
					</soapuiProperties>
					<projectFile>${projectFile}</projectFile>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<profiles>
		<!-- Propiedad usada en los tests de integración SOAPUI -->
		<profile>
			<id>IT_ENV_IC</id>
			<properties>
				<soapui.integration.test.endpoint>les000900289:9080</soapui.integration.test.endpoint>
			</properties>
		</profile>
		<profile>
			<id>IT_ENV_LOCAL</id>
			<properties>
				<soapui.integration.test.endpoint>localhost:9080</soapui.integration.test.endpoint>
			</properties>
		</profile>
	</profiles>
</project>