<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true" scanPeriod="30 seconds">
	
	<!-- Console output -->
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger - %msg%n</pattern>
		</encoder>
	</appender>
	
	<!-- File output -->
	<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<append>true</append>
		<file>C:/TEMP/nwt_api_gt_be_int.log</file>	
		<!-- Encoder defaults to ch.qos.logback.classic.encoder.PatternLayoutEncoder -->
		<encoder>
			<pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36}  %msg%n</pattern>
		</encoder>
		<!-- ROLLING POLICY -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <!-- rollover daily -->
                <fileNamePattern>C:/TEMP/nwt_api_gt_be_int.%d{yyyy-MM-dd}.log</fileNamePattern>
                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
	                <!-- or whenever the file size reaches 64 MB -->
	                <maxFileSize>50MB</maxFileSize>    	
                </timeBasedFileNamingAndTriggeringPolicy>
				<maxHistory>10</maxHistory>
         </rollingPolicy>
	</appender>
	
	<!-- ROOT LEVEL -->
	<root level="ERROR">	
		<appender-ref ref="FILE" />
		<appender-ref ref="STDOUT" />
	</root>
	
	<logger name="com.mapfre.nwt" level="DEBUG">
		<appender-ref ref="FILE" />
		<appender-ref ref="STDOUT" />
	</logger>
	
</configuration>