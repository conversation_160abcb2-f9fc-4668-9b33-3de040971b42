# Copyright (c) MAPFRE DevOps Platform
#
# Pull Request Workflow. Triggers when:
#   - A pull request from feature to develop is created / synchronized
#   - A pull request from hotfix to main is created / synchronized
#
# Required secrets:
#   - AZURE_ARTIFACTS_USER    (Organization)
#   - AZURE_ARTIFACTS_PW      (Organization)
# Optional variables:
#   - SONARQUBE_TOKEN         (Repository)
#   - SONAR_ROOT_CERT         (Repository)

name: PR workflow
run-name: "Build and Test #${{ github.run_number }}: ${{ github.event.pull_request.title }}"

on:
  pull_request:
    types:
      - opened
      - synchronize
    branches:
      - develop
      - main
      - master

env:
  ####################################
  #   Workflow project variables
  ####################################
  # Sonar Project configuration
  SONAR_PROJECT_KEY: ${{ vars.SONAR_PROJECT_KEY }}
  SONAR_PROJECT_NAME: ${{ vars.SONAR_PROJECT_NAME }}
  # Sonar scanner CLI params
  SONAR_PARAMS: '-Dsonar.qualitygate.wait=false -Dsonar.sourceEncoding=UTF-8 -Dsonar.tests=test -Dsonar.typescript.lcov.reportPaths=test/test-reports/coverage/lcov.info -Dsonar.sources=src,sources,bin,infra'
  ########################################

  # Azure Artifacts
  AZURE_ARTIFACTS_USER: ${{secrets.AZURE_ARTIFACTS_USER}}
  AZURE_ARTIFACTS_PW: ${{secrets.AZURE_ARTIFACTS_PW}}

  # Workflow options
  JAVA_VERSION: '8.0.412+8'

jobs:
  pull-request:
    name: Build and Test
    if: ( contains(github.event.pull_request.head.ref, 'feature/') || contains(github.event.pull_request.head.ref, 'hotfix/') )
    runs-on: ubuntu-latest
    outputs:
      sonar-project-configured: ${{ env.SONAR_PROJECT_KEY != '' && env.SONAR_PROJECT_NAME != '' }}

    steps:

      - name: Setup Java
        uses: mapfre-tech/action-java-maven/setup@v1
        with:
          java-version: ${{ env.JAVA_VERSION }}

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Build
        uses: mapfre-tech/action-java-maven/package@v1
        with:
          extra-args: '-Dsource.skip -DskipTests -Dcobertura.skip'
          azure-artifact-user: ${{ env.AZURE_ARTIFACTS_USER }}
          azure-artifact-pw: ${{ env.AZURE_ARTIFACTS_PW }}

      - name: Unit Test
        uses: mapfre-tech/action-java-maven/junit@v1
        with:
          azure-artifact-user: ${{ env.AZURE_ARTIFACTS_USER }}
          azure-artifact-pw: ${{ env.AZURE_ARTIFACTS_PW }}

      - name: Setup Java 11
        uses: mapfre-tech/action-java-maven/setup@v1

      - name: SonarQube Scanner - Maven
        continue-on-error: true
        uses: mapfre-tech/action-sonarqube/scanner-maven@v1
        with:
          projectKey: ${{ env.SONAR_PROJECT_KEY }}
          projectName: ${{ env.SONAR_PROJECT_NAME }}
          projectBaseDir: '.'
          authToken: ${{ secrets.SONAR_TOKEN }}
          checkQualityGate: 'true'
          azure-artifact-user: ${{ secrets.AZURE_ARTIFACTS_USER }}
          azure-artifact-pw: ${{ secrets.AZURE_ARTIFACTS_PW }}
          scanMetadataReportFile: './target/sonar/report-task.txt'