# Copyright (c) MAPFRE DevOps Platform
#
# Merge Commit Workflow. Triggers when:
#   - A pull request to develop is closed and merged
#   - A pull request to main is closed and merged
#
# Required secrets:
#   - AZURE_ARTIFACTS_USER    (Organization)
#   - AZURE_ARTIFACTS_PW      (Organization)
#   - GH_GIT_TOKEN            (Repository)

name: Merge Commit
run-name: "Generate ${{ fromJson(github.base_ref == 'main' || github.base_ref == 'master') && 'Release' || 'Snapshot' }} Artifact #${{ github.run_number }}"

on:
  pull_request:
    types:
      - closed
    branches:
      - main
      - master
      - develop

env:

  # Azure Artifacts
  AZURE_ARTIFACTS_USER: ${{secrets.AZURE_ARTIFACTS_USER}}
  AZURE_ARTIFACTS_PW: ${{secrets.AZURE_ARTIFACTS_PW}}

  #GitHub Token for creating Pull Request to develo
  GH_GIT_TOKEN: ${{secrets.GH_GIT_TOKEN}}

  # Merge to main/master (base_ref)
  IS_RELEASE: ${{ fromJson(github.base_ref == 'main' || github.base_ref == 'master') }}
  IS_DEV: ${{ fromJson(github.base_ref == 'develop')}}

  # Workflow options
  JAVA_VERSION: '8.0.412+8'

jobs:
  merge:
    #https://docs.github.com/en/actions/using-workflows/events-that-trigger-workflows#running-your-pull_request_target-workflow-when-a-pull-request-merges
    if: ${{ github.event.pull_request.merged == true && !startsWith(github.head_ref , 'release/branch--') }}

    name: "Generate ${{ fromJson(github.base_ref == 'main' || github.base_ref == 'master') && 'Release' || 'Snapshot' }} Artifact"
    runs-on: ubuntu-latest
    steps:
      - name: Setup Java
        uses: mapfre-tech/action-java-maven/setup@v1
        with:
          java-version: ${{ env.JAVA_VERSION }}

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ env.GH_GIT_TOKEN }}

      - name: Prepare Promotion
        id: prepare-promotion
        if: fromJson(env.IS_RELEASE)
        uses: mapfre-tech/action-java-maven/promotion@v1
        with:
          branch: ${{github.base_ref}}
          workspace: ./
          azure-artifact-user: ${{ env.AZURE_ARTIFACTS_USER }}
          azure-artifact-pw: ${{ env.AZURE_ARTIFACTS_PW }}

      - name: Build
        uses: mapfre-tech/action-java-maven/package@v1
        with:
          extra-args: '-Dsource.skip -DskipTests -Dcobertura.skip'
          azure-artifact-user: ${{ env.AZURE_ARTIFACTS_USER }}
          azure-artifact-pw: ${{ env.AZURE_ARTIFACTS_PW }}

      - name: Validate Architecture
        if: fromJson(env.IS_DEV)
        run: echo "::debug::Architecture validated"

      - name: Publish
        uses: mapfre-tech/action-java-maven/publish@v1
        with:
          azure-artifact-user: ${{ env.AZURE_ARTIFACTS_USER }}
          azure-artifact-pw: ${{ env.AZURE_ARTIFACTS_PW }}

      - name: Read POM
        id: read-pom
        uses: mapfre-tech/action-java-maven/read-pom@v1
        with:
          azure-artifact-user: ${{ env.AZURE_ARTIFACTS_USER }}
          azure-artifact-pw: ${{ env.AZURE_ARTIFACTS_PW }}

      - name: Release Promotion (Commit & Push pom.xml)
        if: fromJson(env.IS_RELEASE)
        uses: mapfre-tech/action-git/commit-push@v1
        with:
          filename: '"*pom.xml"'
          message: Promotion to RELEASE completed version (${{ steps.read-pom.outputs.version }})
          branch: ${{github.base_ref}}
          github-token: ${{ env.GH_GIT_TOKEN }}

      - name: Release Promotion (Tag Git Repository)
        if: fromJson(env.IS_RELEASE)
        uses: mapfre-tech/action-git/tag-push@v1
        with:
          tag: ${{ steps.read-pom.outputs.version }}
          message: "New version tag ${{ steps.read-pom.outputs.version }}"

      - name: Next Snapshot Promotion (Set next version in pom.xml)
        id: next-snapshot-promotion
        if: fromJson(env.IS_RELEASE)
        uses: mapfre-tech/action-java-maven/promotion@v1
        with:
          branch: develop
          azure-artifact-user: ${{ env.AZURE_ARTIFACTS_USER }}
          azure-artifact-pw: ${{ env.AZURE_ARTIFACTS_PW }}

      - name: Next Snapshot Promotion (Commit & Push pom.xml)
        if: fromJson(env.IS_RELEASE)
        uses: mapfre-tech/action-git/commit-push@v1
        with:
          filename: '"*pom.xml"'
          message:  Promotion to next SNAPSHOT completed (${{ steps.next-snapshot-promotion.outputs.version }})
          branch: ${{github.base_ref}}
          create-pull-request: true
          pull-request-title: Next development version  (${{ steps.next-snapshot-promotion.outputs.version }})
          github-token: ${{ env.GH_GIT_TOKEN }}

    outputs:
      published-version: ${{ steps.read-pom.outputs.version }}
  # Deploy to dev
  deploy:
    needs: merge
    if: github.base_ref == 'develop'
    uses: ./.github/workflows/deploy.yml
    with:
      environment: 'dev'
      version: ${{ needs.merge.outputs.published-version }}
    secrets: inherit
