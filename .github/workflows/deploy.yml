# Copyright (c) MAPFRE DevOps Platform
#
# Deploy Workflow. Triggers when:
#   - Manually
#   - From Merge-commit workflow after a pull request to develop is closed and merged.
#
# Required secrets:
#   - GH_GIT_TOKEN             (Repository)
#   - JENKINS_REMOTE_TOKEN     (Repository)
#   - JENKINS_REMOTE_JOB_TOKEN (Repository)
#   - JENKINS_REMOTE_USER      (Repository)

name: Deploy
run-name: "Deploy version ${{ inputs.is_workflow_call && inputs.version || github.ref_name }} to ${{ inputs.environment }} #${{ github.run_number }}"

on:
  workflow_dispatch:
    inputs:
      environment:
        description: Environment
        required: true
        type: choice
        options:
          - dev
          - int
          - pre
          - pro
      jenkins_remote_url:
        description: 'Jenkins remote URL (not required) (default: https://mapfrearcpro.pro.devops.mapfre.com/jenkins/)'
        required: false
        default: 'https://mapfrearcpro.pro.devops.mapfre.com/jenkins/'
      jenkins_remote_job_path:
        description: '<PERSON> remote job path (not required) (default: GAIA/Utils/gaia-github-deploy)'
        required: false
        default: 'GAIA/Utils/gaia-github-deploy'
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      version:
        required: true
        type: string
      is_workflow_call:
        description: 'Internal Flag to distinguish workflow_call from workflow_dispatch'
        required: false
        type: boolean
        default: true
    secrets:
      GH_GIT_TOKEN:
        required: true
      JENKINS_REMOTE_TOKEN:
        required: true
      JENKINS_REMOTE_JOB_TOKEN:
        required: true
      JENKINS_REMOTE_USER:
        required: true

env:
  # Azure Artifacts
  AZURE_ARTIFACTS_PW: ${{ secrets.AZURE_ARTIFACTS_PW }}
  VERSION: ${{ inputs.is_workflow_call && inputs.version || github.ref_name }}

jobs:
  check:
    name: Check environment
    runs-on: ubuntu-22.04
    steps:
      - name: Summary of input parameters
        uses: actions/github-script@v7
        with:
          script: |
            const summary = `
            ## Summary of Input Parameters
            - Environment: ${{ inputs.environment }}
            - Jenkins remote URL: ${{ inputs.jenkins_remote_url }}
            - Jenkins remote job path: ${{ inputs.jenkins_remote_job_path }}
            - Version: ${{ inputs.version }}
            - Is workflow call: ${{ inputs.is_workflow_call }}
            `;
            core.summary.addRaw(summary).write();

      # workflow_dispatch (Manual Trigger) restricted to TAGS
      - name: Fail if workflow_dispatch not triggered from tag
        if: ${{ !inputs.is_workflow_call && github.ref_type != 'tag' }}
        uses: actions/github-script@v7
        with:
          script: |
            core.setFailed("Error: This workflow can only be executed from a TAG.")


  deploy:
    needs: check
    uses: mapfre-tech/arch-mar2-reusable-workflows/.github/workflows/deploy.gaia.yml@v1
    with:
      environment: ${{ inputs.environment }}
      version: ${{ inputs.is_workflow_call && inputs.version || github.ref_name }}
      snapshot: ${{ inputs.is_workflow_call && true || false }}
      #pre-deploy-enabled: true
      #pre-deploy-propagate-failure: true
      #pre-deploy-wait: true
      ########################################
      # Pre-deploy Workflows on external repo
      ########################################
      #pre-deploy-repo: example-repo
      #pre-deploy-version: v1
      #pre-deploy-workflow: pre-deploy-mar.yml
      ########################################
      #post-deploy-enabled: true
      #post-deploy-propagate-failure: true
      post-deploy-wait: false # do not wait until post-deploy is completed
      ########################################
      # Post-deploy Workflows on external repo
      ########################################
      #post-deploy-repo: example-repo
      #post-deploy-version: v1
      #post-deploy-workflow: pre-deploy-mar.yml
      ########################################
    secrets:
      GH_GIT_TOKEN: ${{ secrets.GH_GIT_TOKEN }}
      JENKINS_REMOTE_TOKEN:  ${{ secrets.JENKINS_REMOTE_TOKEN }}
      JENKINS_REMOTE_JOB_TOKEN:  ${{ secrets.JENKINS_REMOTE_JOB_TOKEN }}
      JENKINS_REMOTE_USER:  ${{ secrets.JENKINS_REMOTE_USER }}