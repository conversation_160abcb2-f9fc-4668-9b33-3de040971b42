#!groovy
import com.mapfre.pod.Container
import com.mapfre.pod.ContainerSize
import com.mapfre.pod.ContainerType

@Library(['global-pipeline-library@release/v9.2', 'security-library@release/v2.0', '<your-repo-config>']) _
// @Library('<organization>-pipeline-config')

def libModules = '.deploy/libModules.yml'
def DEVOPS_PLATFORM_ORGANIZATION = '<your org-name>' //org-xxxxx

pipeline {
    agent {
        kubernetes {
            yaml getPodTemplate(DEVOPS_PLATFORM_ORGANIZATION, [
                    ['maven', ContainerType.MAVEN_3_6_0_JDK8, ContainerSize.MEDIUM]
            ] as Container[], true)
        }
    }
    // Timeout execution
    options {
        timeout(time: 15, unit: 'MINUTES')
        timestamps()
    }
    environment {
        POM_VERSION = getPomVersion()
        ARTIFACT_ID = getArtifactId()
        ADDITIONAL_PROFILES = 'INCLUDE_DEPENDENCIES'
        APP_WEB_ARTIFACT_ID = "${ARTIFACT_ID}-web"
        DEPLOY_SERVER = '<your server>' // 'jboss' or 'was'
    }

    stages {
        stage('Prepare Environment') {
            steps {
                prepareEnvironment()
            }
        }
        //This is a security stage that must be executed before building any code or image.
        stage('Security pre-build') {
            steps {
                script {
                    secPreBuild()
                }
            }
        }
        stage('Prepare Promotion') {
            when {
                anyOf {
                    branch 'master'; branch 'hotfix/*'
                }
            }
            steps {
                container('maven') {
                    promotion env.BRANCH_NAME
                }
            }
        }
        // Compile with Maven
        stage('Build') {
            steps {
                container('maven') {
                    buildWithProfiles(getBuildProfile(env.BRANCH_NAME), ADDITIONAL_PROFILES)
                }
            }
        }
        stage('Unit Test') {
            steps {
                container('maven') {
                    mavenExecuteJUnitTest()
                }
            }
        }
        stage('SonarQube Analysis') {
            when {
                branch 'develop'
            }
            environment {
                POM_VERSION = getPomVersion()
                SONAR_ENVIRONMENT = '<your sonar environment>'
                SONAR_TOKEN = '<your sonar token>'
                SONAR_PROJECT_NAME = '<your sonar project name>'
                SONAR_PROJECT_KEY = '<your sonar project key>'
            }
            steps {
                container('maven') {
                    echo 'SonarQube Analysis --> Uncomment groovy function and configure properties needed'
                    //sonarScanner(SONAR_ENVIRONMENT, SONAR_TOKEN, SONAR_PROJECT_KEY,
                    //        SONAR_PROJECT_NAME, SonarScannerType.MAVEN)
                }
            }

        }
        // This is a security stage that must be executed after building any code and after Quality stage.
        stage('Security post-build') {
            steps {
                script {
                    secPostBuild()
                }
            }
        }
        // Publish to Artifacts Repository
        stage('Publish') {
            when {
                anyOf {
                    branch 'master'; branch 'hotfix/*'; branch 'develop'
                }
            }
            steps {
                container('maven') {
                    publish(getBuildProfile(env.BRANCH_NAME), ADDITIONAL_PROFILES)
                }
            }
        }
        stage('Release Promotion') {
            when {
                branch 'master'
            }
            steps {
                releasePromotion(getPomVersion(), env.BRANCH_NAME)
            }
        }
        stage('Next Snapshot Promotion') {
            when {
                anyOf {
                    branch 'master'
                }
            }
            environment {
                developBranch = "develop"
            }
            steps {
                container('maven') {
                    promotion developBranch
                }
                gitCommit "promotion to next SNAPSHOT completed (" + getPomVersion() + ")"
                gitPush()
            }
        }
        // This is a security stage that must be executed before deploying any app or service.
        stage('Security pre-deploy'){
            steps{
                script{
                    secPreDeploy()
                }
            }
        }
        stage('Deploy Dev') {
            when {
                branch 'develop'
            }
            steps {
                script() {
                    echo 'Deploy Dev --> Uncomment groovy function and configure properties needed'
                    // AN EXAMPLE OF DEPLOYUTILS IS GIVEN IN https://bitbucket.org/corparchitecture/devops-pipeline-config/src/master
                    /*
                    yml_dev = deployUtils.getConfig('insurance-devops', 'dev')
                    try {
                        deployGaiaApplication(
                                DEPLOY_SERVER,
                                yml_dev,
                                env.POM_VERSION,
                                env.BRANCH_NAME,
                                libModules)
                    } catch(Exception err) {
                        error('Deploy Dev Error: ' + err)
                    }
                    */
                }
            }
            post{
                always{
                    jiraSendDeploymentInfo site: 'mapfrealm.atlassian.net', environmentId: 'Develop', environmentName: 'develop', environmentType: 'staging'
                }
            }
        }
        stage('HP SM Integration') {
            when {
                anyOf {
                    branch 'master'; branch 'hotfix/*'
                }
            }
            steps {
                echo 'HP SM Integration --> TO-DO'
            }
        }
        stage('Deploy Integration') {
            when {
                branch 'master'
            }
            steps {
                script() {
                    echo 'Deploy Integration --> Uncomment groovy function and configure properties needed'
                    // AN EXAMPLE OF DEPLOYUTILS IS GIVEN IN https://bitbucket.org/corparchitecture/devops-pipeline-config/src/master
                    /*
                    yml_int = deployUtils.getConfig('insurance-devops', 'int')
                    try {
                        deployGaiaApplication(
                                DEPLOY_SERVER,
                                yml_int,
                                env.POM_VERSION,
                                env.BRANCH_NAME,
                                libModules)
                    } catch(Exception err) {
                        error('Deploy Dev Error: ' + err)
                    }
                    */
                }
            }
            post{
                always{
                    jiraSendDeploymentInfo site: 'mapfrealm.atlassian.net', environmentId: 'Integration', environmentName: 'integration', environmentType: 'staging'
                }
            }
        }
        stage('Integration Testing (SOAP UI)') {
            when {
                anyOf {
                    branch 'master'; branch 'develop'
                }
            }
            steps {
                echo 'Integration Tests (SOAP UI) --> TO-DO'
            }
        }
        stage('Functional Testing (UFTOne)') {
            when {
                branch 'master'
            }
            steps {
                echo 'Functional Testing (UFTOne) --> TO-DO'
            }
        }
        stage('Verify HP SM Quality Gates (Int)') {
            when {
                branch 'master'
            }
            steps {
                echo 'Verify HP SM Quality Gates (Int) --> TO-DO'
            }
        }

        stage('Deploy Preproduction') {
            when {
                branch 'master'
            }
            steps {
                script() {
                    echo 'Deploy Preproduction --> Uncomment groovy function and configure properties needed'
                    // AN EXAMPLE OF DEPLOYUTILS IS GIVEN IN https://bitbucket.org/corparchitecture/devops-pipeline-config/src/master
                    /*
                    yml_pre = deployUtils.getConfig('insurance-devops', 'pre')
                    try {
                        deployGaiaApplication(
                                DEPLOY_SERVER,
                                yml_pre,
                                env.POM_VERSION,
                                env.BRANCH_NAME,
                                libModules)
                    } catch(Exception err) {
                        error('Deploy Dev Error: ' + err)
                    }
                    */
                }
            }
            post{
                always{
                    jiraSendDeploymentInfo site: 'mapfrealm.atlassian.net', environmentId: 'Preproduction', environmentName: 'preproduction', environmentType: 'staging'
                }
            }
        }
        stage('Functional Testing (Pre)') {
            when {
                anyOf {
                    branch 'master'; branch 'hotfix/*'
                }
            }
            steps {
                echo 'Functional Testing (Pre) --> TO-DO'
            }
        }
        stage('Monitoring Testing (Pre)') {
            when {
                anyOf {
                    branch 'master'; branch 'hotfix/*'
                }
            }
            steps {
                echo 'Monitoring Testing (Pre) --> TO-DO'
            }
        }
        stage('Performance Testing (Pre)') {
            when {
                anyOf {
                    branch 'master'; branch 'hotfix/*'
                }
            }
            steps {
                echo 'Performance Testing (Pre) --> TO-DO'
            }
        }
        stage('Verify HP SM Quality Gates (Pre)') {
            when {
                anyOf {
                    branch 'master'; branch 'hotfix/*'
                }
            }
            steps {
                echo 'Verify HP SM Quality Gates (Pre) --> TO-DO'
            }
        }
        // This is a security stage that must be executed after deploying any image or application.
        stage('Deploy Production') {
            when {
                branch 'master'
            }
            steps {
                script() {
                    echo 'Deploy Production --> Uncomment groovy function and configure properties needed'
                    /*
                    // AN EXAMPLE OF DEPLOYUTILS IS GIVEN IN https://bitbucket.org/corparchitecture/devops-pipeline-config/src/master
                    yml_pro = deployUtils.getConfig('insurance-devops', 'pro')
                    try {
                        deployGaiaApplication(
                                DEPLOY_SERVER,
                                yml_pro,
                                env.POM_VERSION,
                                env.BRANCH_NAME,
                                libModules)
                    } catch(Exception err) {
                        error('Deploy Dev Error: ' + err)
                    }
                    */
                }
            }
            post{
                always{
                    jiraSendDeploymentInfo site: 'mapfrealm.atlassian.net', environmentId: 'Production', environmentName: 'production', environmentType: 'staging'
                }
            }
        }
        stage('Security post-deploy'){
            steps{
                script{
                    secPostDeploy()
                }
            }
        }

    }

}